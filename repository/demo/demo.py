
from artiq.experiment import *
from modules.demo_base_exp import RamanBaseExp
from copy import deepcopy
import calibrations.CaliBase as cali
from modules.check_information import decode_item_index,decode_item_name

# from repository.circuit.circuit import Rxx, Rphi
from ionctrl_pkg.utils.log import get_logger
logger = get_logger(__name__)

class RouteDemo(RamanBaseExp,EnvExperiment):

    @kernel()
    def run_level_3(self, param_list):
        """N 个扫描点"""
        self.point_now = 0
        while self.point_now < len(param_list):
            cali_list = self.check_parameters(self.point_now)
            self.run_calibration(cali_list)
            self.run_level_2(self.point_now)
            param_list = self.update_scan_list(param_list)
            # 添加优雅中止
            if self.scheduler.check_termination():
                break

    @rpc(flags={""})
    def check_parameters(self, point_index)->TList(TStr):
        """检查所有的待校准参数, 返回一个待执行的校准实验 list """
        cali_list = []
        if self.is_calibrating:
            return cali_list
        for op in self.points_list[int(point_index)]:
            for cali_item in op.check_list:
                y = decode_item_index(cali_item)
                name = decode_item_name(cali_item)
                if not cali.check0_pass(name,y) and cali_item not in cali_list:
                    cali_list.append(cali_item)
        return cali_list

    @kernel()
    def run_calibration(self, cali_list):
        """
        执行校准子程序

        Parameters
        ----------
        cali_list: route list

        Returns
        -------

        """
        if cali_list:
            self.move_data_to_temp()
            for cali_item in cali_list:
                y = decode_item_index(cali_item)  # 一个数据点
                name = decode_item_name(cali_item)  # 格式转换
                for _ in range(4):
                    cali.update(self,name , y)  #
                    if not cali.check1_pass(name, y):
                        back_check_list = cali.back_check_list(name, y)
                        # 执行一遍子校准程序
                        self.run_calibration(back_check_list)
                    else:
                        break
                else:
                    # 修复三次还每成功就报错
                    raise ValueError("can't repair ")
            self.recover_data()
            self.core.wait_until_mu(now_mu())

    @rpc(flags={""})
    def move_data_to_temp(self):
        """启动校准时, 将当前层级的所有数据保存到一个缓存中, 并将状态标记为正在校准"""
        self.iteration_layer +=1
        logger.info(f"into_iteration_layer: {self.iteration_layer}")
        if not self.is_calibrating:
            self.x_temp = self.get_dataset("x_points")
            self.y_temp = self.get_dataset("y_probability")
            self.cb_temp = self.get_dataset("computational_basis_probability")
            self.points_list_temp = deepcopy(self.points_list)
            self.ion_choice_temp = self.ion_choice
            self.is_circuit_temp = self.is_circuit
            self.is_calibrating = True

    @rpc(flags={""})
    def recover_data(self):
        """校准完成, 恢复当前层级的数据, 取消正在校准的状态标记"""
        self.iteration_layer -= 1
        if self.iteration_layer == 0:
            self.set_dataset("x_points",self.x_temp,broadcast=True)
            self.set_dataset("y_probability",self.y_temp,broadcast=True)
            self.set_dataset("computational_basis_probability",self.cb_temp,broadcast=True)
            self.points_list = deepcopy(self.points_list_temp)
            self.ion_choice = self.ion_choice_temp
            self.is_circuit = self.is_circuit_temp
            self.is_calibrating = False

    @rpc(flags={""})
    def update_condition(self,condition):
        pass
    @rpc(flags={""})
    def recover_condition(self):
        pass