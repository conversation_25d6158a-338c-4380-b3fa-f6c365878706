"""
MMC QCMOS
"""

from modules.config import Config,LOADED_PARAMETER
from modules.qcmos import QCMOS
from modules.light_369 import *
# from modules.light_554 import *
from artiq.experiment import *
from modules.RigolDZ1000_package.Rigol_DZ1000 import RigolDZ1000

class MMcompensation(EnvExperiment):
    """ Micro Motion Compensation
    """

    def build(self):
        # 1. 参数拉取
        # self.parameter = Config(DEFAULT_PARAMETER)
        self.parameter = LOADED_PARAMETER()
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.repeat = self.parameter.Experiment.Repeat

        # 2. 连接仪器
        self.setattr_device("core")
        self.l369 = Light369(self)
        # self.l554 = Light554(self)
        self.qcmos = QCMOS(self)

        # rigol scan frequency, Hz
        self.setattr_argument("X_scan_range",
                              Scannable(default=RangeScan(935000, 945000, 25), global_min=1, global_max=60000000,
                                        global_step=10, precision=0))  # MHz
        # sampling time, unit is ms
        self.setattr_argument("SamplingTime_ms", NumberValue(1, step=1, precision=1))
        # Tickle_volt of CH2, range(0.1 to 0.5)
        self.setattr_argument("Tickle_volt", NumberValue(0.2, step=1, precision=2))

        # Alpha
        self.setattr_argument("alpha", NumberValue(0.0, step=0.01, precision=2))

        # Beta
        self.setattr_argument("beta", NumberValue(0.0, step=0.01, precision=2))
        print("Experiment Registered: MMcompensation")


    def prepare(self):
        """实验准备阶段，将必要的参数从 config 文件中提取出来， artiq 不支持传递字典格式"""
        self.qcmos.prepare_for_idle()

        # self.l554.awg_device.close()  # 关闭可能开启的 AWG

        # 1. 获得参数
        self.scan_parameter = self.X_scan_range.sequence
        # 2. 准备实验数据集
        self.qcmos.prepare_for_x_scan(np.array(self.scan_parameter) / 1e6)
        print("prepare_done")
        self.CH2 = RigolDZ1000(CH=2)
        self.CH2.setAmp(self.Tickle_volt)
        print('Rigol DZ1000 connected')
        self.set_dataset("alpha", np.full(1, self.alpha), broadcast=True, archive=True)
        self.set_dataset("beta", np.full(1, self.beta), broadcast=True, archive=True)
    @rpc(flags={"async"})
    def tprint(self, data):
        print(data)

    @kernel()
    def run(self):
        # 0. initial
        self.core.reset()

        self.qcmos.initial()

        self.l369.initial()  # 369 初始化完后直接到 cooling 状态

        for scan_point in range(len(self.scan_parameter)):

            self.CH2.setFreq(self.scan_parameter[scan_point])

            self.core.break_realtime()
            # delay(200*ms)
            # 留出设置频率的时间

            for repeat in range(self.repeat):

                self.qcmos.qcmos_start_count()
                delay(self.SamplingTime_ms*ms)
                # delay(1000*us)
                self.qcmos.qcmos_stop_count()
                delay(2 * ms) #在PMT开始读数和停止读数之间添加缓冲
            self.core.wait_until_mu(now_mu())
            delay(1 * ms)
            print(1)
            self.qcmos.process_for_x_scan_counts(scan_point)
        self.CH2.stopScan()

    def analyze(self):

        print("analyze")
