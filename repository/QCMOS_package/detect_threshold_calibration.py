from modules.light_369 import *
from modules.signal_mw import *
from modules.qcmos import *

import logging
from typing import List, Sequence, Tuple

import numpy as np

logger = logging.getLogger(__name__)


def _optimal_threshold_1d(
        hist_dark: np.ndarray,
        hist_bright: np.ndarray,
) -> Tuple[int, float, float]:
    """
    根据一维直方图计算最优判决阈值。

    通过最小化暗态判亮和亮态判暗的错误概率之和来确定阈值。
    给定一个阈值 t，错误概率定义为：
    P(错误|暗态) = P(计数 > t | 暗态)
    P(错误|亮态) = P(计数 <= t | 亮态)
    目标是找到 t* 使得 P(错误|暗态) + P(错误|亮态) 最小。

    Parameters:
        hist_dark: 暗态计数的直方图 (numpy array)，数组索引代表计数值，数组值代表该计数值出现的频数。shppe=[N,1];
        hist_bright: 亮态计数的直方图 (numpy array)，格式同上。shppe=[N,1];


    Returns:
        Tuple[int, float, float]: 包含
            - t_star (int): 最优判决阈值。
            - p_dark_to_bright (float): 在最优阈值下，暗态被误判为亮态的概率 P(计数 > t* | 暗态)。
            - p_bright_to_dark (float): 在最优阈值下，亮态被误判为暗态的概率 P(计数 <= t* | 亮态)。

    Note:
        - 此方法假设输入的直方图能充分代表暗态和亮态的计数分布。
        - 寻找最优阈值的标准是最小化两种错误概率之和，这在许多场景下是合理的，
          但不一定是所有应用场景下的唯一或最佳标准。
        - 输入的两个直方图应该具有相同的 bin 边界和宽度。
    """
    # 计算暗态和亮态计数的累积分布函数 (Cumulative Distribution Function, CDF)
    # cdf_dark[i] 表示暗态计数 <= i 的总次数
    cdf_dark = np.cumsum(hist_dark)  # shape=[N, 1]
    # cdf_bright[i] 表示亮态计数 <= i 的总次数
    cdf_bright = np.cumsum(hist_bright)  # shape=[N, 1]
    # 获取暗态和亮态的总计数（总样本数）
    total_dark = cdf_dark[-1]  # 最后一个值确实是所有计数的和, 即总计数, tpye = int, shape=[1, ]
    total_bright = cdf_bright[-1]

    # 防止除零错误，如果总计数为0，则错误率为0
    if total_dark == 0:
        p_dark_to_bright = np.zeros_like(cdf_dark, dtype=float)
    else:
        # 对于每个可能的阈值 t (对应数组索引)，并行计算暗态被误判为亮态的概率
        # P(计数 > t | 暗态) = (总暗态数 - 暗态计数 <= t 的数量) / 总暗态数
        p_dark_to_bright = (total_dark - cdf_dark) / total_dark

    if total_bright == 0:
        p_bright_to_dark = np.zeros_like(cdf_bright, dtype=float)
    else:
        # 对于每个可能的阈值 t (对应数组索引)，计算亮态被误判为暗态的概率
        # P(计数 <= t | 亮态) = (亮态计数 <= t 的数量) / 总亮态数
        p_bright_to_dark = cdf_bright / total_bright

    # 计算总错误率，即两种错误概率之和
    p_error = p_dark_to_bright + p_bright_to_dark
    # print("error array", p_error)
    # 找到使总错误率最小的索引，该索引即为最优阈值 t*
    t_star = int(np.argmin(p_error))

    # 返回最优阈值 t* 以及在该阈值下的两种错误概率
    # 注意索引 t_star 对应的是计数阈值
    return (
        t_star,
        float(p_dark_to_bright[t_star]),
        float(p_bright_to_dark[t_star]),
    )


def process_for_histogram_per_ion(
        self,
        re_repeat: int = 1,
        bins: int | None = None,
) -> None:
    """
    为每个选定的离子计算独立的判决阈值，并写入 ARTIST 数据集。

    从缓存的图像数据中提取每个离子的计数，生成直方图，
    并调用 _optimal_threshold_1d 寻找最优阈值。

    Parameters
    ----------
    ion_choice : Sequence[int] | None, optional
        要分析的离子索引列表；若为 `None`，则分析所有离子。默认为 None。
    re_repeat : int, default 1
        等效的重复次数放大因子，总重复次数为 `Experiment.Repeat * re_repeat`。
    bins : int | None, optional
        生成直方图时的 bin 数量。若为 `None`，则根据每个离子的数据范围自动确定。默认为 None。
    """
    # ---------- 从缓存图像帧计算离子计数矩阵 ----------
    repeat_total = self.parameter.Experiment.Repeat * re_repeat
    # pump_img 对应暗态 (odd frames), ctrl_img 对应亮态 (even frames)
    pump_img, ctrl_img = self.odd_cache, self.even_cache

    # 使用 ROI 批量计算每个离子在每次重复中的像素总和
    pump_matrix = calculate_roi_pixel_sums_batch(
        pump_img,
        self.roi_for_ions,
        repeat=repeat_total,
        background_noise=self.parameter.QCMOS.bit16.background_noise,
    )
    ctrl_matrix = calculate_roi_pixel_sums_batch(
        ctrl_img,
        self.roi_for_ions,
        repeat=repeat_total,
        background_noise=self.parameter.QCMOS.bit16.background_noise,
    )

    # ----- 选择要处理的离子(选择所有离子) -----
    ion_choice = range(pump_matrix.shape[1])
    ion_choice = list(ion_choice)  # 转换为列表方便处理

    # 初始化用于存储结果的列表
    histograms: List[np.ndarray] = []  # 存储每个离子的直方图 (暗态, 亮态)
    thresholds: List[int] = []  # 存储每个离子的最优阈值
    errors_dark_to_bright: List[float] = []  # 存储每个离子的暗态判亮错误率
    errors_bright_to_dark: List[float] = []  # 存储每个离子的亮态判暗错误率

    # ---------- 遍历每个选定的离子 ----------
    for i in ion_choice:
        # 提取当前离子的暗态和亮态计数数据
        pump_counts = pump_matrix[:, i]
        ctrl_counts = ctrl_matrix[:, i]
        # 合并暗态和亮态数据以确定直方图的范围和 bin 数量
        all_counts = np.concatenate([pump_counts, ctrl_counts])

        # 确定直方图的 bin 数量：始终根据数据范围自动计算
        # 确保至少有一个 bin
        data_range = all_counts.max() - all_counts.min()
        nbins = int(data_range + 1) if data_range >= 0 else 1

        # 生成暗态直方图
        # 使用与 nbins 匹配的范围，确保边界正确
        hist_range = (all_counts.min(), all_counts.max() + 1)  # +1 因为 range 是 [min, max)
        hist_dark = np.histogram(
            pump_counts, bins=nbins, range=(all_counts.min(), all_counts.max())
        )[0]
        # 生成亮态直方图
        hist_bright = np.histogram(
            ctrl_counts, bins=nbins, range=(all_counts.min(), all_counts.max())
        )[0]
        # 将暗态和亮态直方图堆叠在一起
        hist_i = np.stack((hist_dark, hist_bright), axis=1)
        histograms.append(hist_i)

        # 计算当前离子的最优阈值和错误率
        t_star, p_d2b, p_b2d = _optimal_threshold_1d(hist_dark, hist_bright)
        thresholds.append(t_star)
        errors_dark_to_bright.append(p_d2b)
        errors_bright_to_dark.append(p_b2d)

    # ---------- 将结果写入 ARTIST 数据集 ----------
    # 保存所有选定离子的直方图数据
    self.set_dataset(
        "histograms", [h.tolist() for h in histograms], broadcast=True
    )
    # 保存所有选定离子的最优阈值
    self.set_dataset(
        "thresholds", thresholds, broadcast=True
    )
    # 保存所有选定离子的错误率 (结构化字典)
    self.set_dataset(
        "errors",
        {
            "dark_to_bright": errors_dark_to_bright,
            "bright_to_dark": errors_bright_to_dark,
        },
        broadcast=True,
    )


class QCMOSForHistogram(QCMOS):
    @rpc(flags={"async"})
    def prepare_for_histogram(self):
        """Histogram 数据集准备"""
        #
        self.set_dataset("histogram", [], broadcast=True)
        self.set_dataset("histogram_threshold", 0, broadcast=True)
        # error[0] 是暗态 判断成亮态的概率, error[1] 是亮态判断成暗态的概率
        self.set_dataset("error", [0, 0], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "QCMOS_Histogram",
            "${artiq_applet}histogram_double histogram --x histogram_threshold --error error",
        )

    @rpc(flags={""})
    def process_for_re_repeat(self, re_repeat_num):
        """缓存图像数据，支持清空和动态堆叠

        Args:
            re_repeat_num:
                0 表示清空缓存并用新数据初始化缓存，
                其他值将新数据堆叠到缓存
        """
        # 读取数据（无论 re_repeat_num 为何值都需读取）
        # print("00")
        odd, even = self.read_data_double()
        # print(odd.shape)

        if re_repeat_num == 0 or not hasattr(self, 'odd_cache'):
            # 清空缓存或初始化时，直接用新数据覆盖
            self.odd_cache = odd
            self.even_cache = even
        else:
            # 非清空时，堆叠数据
            self.odd_cache = np.vstack((self.odd_cache, odd))
            self.even_cache = np.vstack((self.even_cache, even))
        # print("01")
        print(f"re_repeat: {re_repeat_num}")

    @rpc(flags={""})
    def process_for_histogram(self, ion=(0,), re_repeat=1):
        """Histogram 数据处理
        流程:
        1. 读数两次, 并分割成 pump_detect 和 control_detect 图像, shape 均为 (repeat * height, width);
        2. ROI 求和, 分别得到 pump_detect 和 control_detect 对应于离子这一层的计数, shape 均为 (repeat, ion_num);
        3. 选择要做 histogram 的离子索引, 并对他们的通道进一步求和, 对 pump_detect 和 control_detect 分别得到 shape = (repeat, 1),
        本步骤假定多个离子共用同一个阈值;
        4. 将 pump_detect 和 control_detect 的两列数拼成一个矩阵分别做 histogram, 得到 shape = (bins, 2) 的数据;
        5. 根据设定的阈值分别计算二者的错误率, 得到 shape = (2,1) 的 list;
        6. 更新数据集 error 和 histogram;

        结果信息:
        得到一个矩阵: shape = [N, 3]
        """
        # 1. 读数两次, 分别获取 pumping data 和 control data
        # print("11")
        pump_detect_data_temp, control_detect_data_temp = self.odd_cache, self.even_cache
        # 2. 将 pump -> detect 和 control -> detect 的数据分开, 分别做 ROI 求和
        pump_matrix = calculate_roi_pixel_sums_batch(pump_detect_data_temp, self.roi_for_ions,
                                                     repeat=self.parameter.Experiment.Repeat * re_repeat,
                                                     background_noise=self.parameter.QCMOS.bit16.background_noise)
        print("pump_matrix", pump_matrix)
        ctrl_matrix = calculate_roi_pixel_sums_batch(control_detect_data_temp, self.roi_for_ions,
                                                     repeat=self.parameter.Experiment.Repeat * re_repeat,
                                                     background_noise=self.parameter.QCMOS.bit16.background_noise)
        print("ctrl_matrix", ctrl_matrix)

        # ----- 选择要处理的离子(选择所有离子) -----
        ion_choice = range(pump_matrix.shape[1])
        ion_choice = list(ion_choice)  # 转换为列表方便处理

        # 初始化用于存储结果的列表
        histograms: List[np.ndarray] = []  # 存储每个离子的直方图 (暗态, 亮态)
        thresholds: List[int] = []  # 存储每个离子的最优阈值
        errors_dark_to_bright: List[float] = []  # 存储每个离子的暗态判亮错误率
        errors_bright_to_dark: List[float] = []  # 存储每个离子的亮态判暗错误率
        # print("threshold 0 ", thresholds)
        # ---------- 遍历每个选定的离子 ----------
        for i in ion_choice:
            # 提取当前离子的暗态和亮态计数数据
            pump_counts = pump_matrix[:, i]
            ctrl_counts = ctrl_matrix[:, i]

            # 合并暗态和亮态数据以确定直方图的范围和 bin 数量
            all_counts = np.concatenate([pump_counts, ctrl_counts])
            # 确定直方图的 bin 数量：始终根据数据范围自动计算
            # 确保至少有一个 bin
            data_range = all_counts.max() - 0
            nbins = int(data_range + 1) if data_range >= 0 else 1

            # 生成暗态直方图
            hist_dark = np.histogram(
                pump_counts, bins=nbins, range=(0, all_counts.max())
            )[0]
            # 生成亮态直方图
            hist_bright = np.histogram(
                ctrl_counts, bins=nbins, range=(0, all_counts.max())
            )[0]

            # 将暗态和亮态直方图堆叠在一起
            hist_i = np.stack((hist_dark, hist_bright), axis=1)
            histograms.append(hist_i)

            # 计算当前离子的最优阈值和错误率
            t_star, p_d2b, p_b2d = _optimal_threshold_1d(hist_dark, hist_bright)
            thresholds.append(t_star)
            # print("Threshold", i, thresholds)
            errors_dark_to_bright.append(p_d2b)
            errors_bright_to_dark.append(p_b2d)

        # print("Threshold", thresholds)
        # print("Errors_dark_to_bright", errors_dark_to_bright)
        # print("Errors_bright_to_dark", errors_bright_to_dark)
        # print("SPAM", (np.array(errors_dark_to_bright) + np.array(errors_bright_to_dark)) / 2)
        self.parameter.QCMOS.Detect_threshold = np.array(thresholds)
        self.parameter.QCMOS.Errors.Dark_to_Bright = np.array(errors_dark_to_bright)
        self.parameter.QCMOS.Errors.Bright_to_Dark = np.array(errors_bright_to_dark)
        self.parameter.QCMOS.Errors.SPAM = (np.array(errors_dark_to_bright) + np.array(errors_bright_to_dark)) / 2
        self.set_dataset("SPAM", self.parameter.QCMOS.Errors.SPAM, broadcast=True)
        self.set_dataset("Detect_threshold", self.parameter.QCMOS.Detect_threshold, broadcast=True)
        self.set_dataset("Errors_Dark_to_Bright", self.parameter.QCMOS.Errors.Dark_to_Bright, broadcast=True)
        self.set_dataset("Errors_Bright_to_Dark", self.parameter.QCMOS.Errors.Bright_to_Dark, broadcast=True)

        errors = self.parameter.QCMOS.Errors

        # 计算Dark_to_Dark和Bright_to_Bright
        spam_matrices = []
        for i in range(len(errors.Dark_to_Bright)):
            dark_to_bright = errors.Dark_to_Bright[i]
            bright_to_dark = errors.Bright_to_Dark[i]

            # 计算互补概率
            dark_to_dark = 1 - dark_to_bright
            bright_to_bright = 1 - bright_to_dark

            # 构建2x2矩阵
            matrix = [
                [dark_to_dark, bright_to_dark],
                [dark_to_bright, bright_to_bright]
            ]
            spam_matrices.append(matrix)

        self.parameter.QCMOS.Errors.SPAM_matrix = spam_matrices

        inverse_matrices = []
        for mat in spam_matrices:
            mat_np = np.array(mat)
            inv_mat = np.linalg.inv(mat_np)  # 使用NumPy求逆
            inverse_matrices.append(inv_mat.tolist())  # 转换回Python列表

        self.parameter.QCMOS.Errors.SPAM_matrix_inverted = inverse_matrices

        if len(ion) != 1:
            print("Ion_choice should be 1")
        self.set_dataset("histogram", histograms[ion[0]].tolist(), broadcast=True)
        self.set_dataset(
            "error", [errors_dark_to_bright[ion[0]], errors_bright_to_dark[ion[0]]], broadcast=True
        )
        self.set_dataset("histogram_threshold", thresholds[ion[0]], broadcast=True)
    @rpc(flags={})
    def connect_qcmos(self, bundle_mode=1):
        if dcamcon_init():
            self.dcamcon = dcamcon_choose_and_open()
            if self.dcamcon is not None:
                # 设置相机参数
                res = setup_properties(
                    self.dcamcon,
                    x_pos=self.init_roi[0],
                    y_pos=self.init_roi[1],
                    width=self.init_roi[2],
                    height=self.init_roi[3],
                    tirg_mode=0,
                    bundle_mode=bundle_mode,
                    bundle_number=self.parameter.Experiment.Repeat * 2,
                )
                if res is False:
                    print("Parameter Configure False")
                else:
                    print("Parameter Configure Success")
            else:
                print("Device Open Failure")
        else:
            dcamcon_uninit()
            print("DCAM Initial Failure")


class HistogramMWReRepeat3(EnvExperiment):
    """MW_Histogram_QCMOS_Calibration_All"""

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        # self.parameter = Config(DEFAULT_PARAMETER)
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        # self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.operation_time = self.parameter.Signal_MW.mw_pi_2.zero
        self.mw_pi_2 = self.parameter.Signal_MW.mw_pi_2.zero
        self.pre_cooling_time = self.parameter.Experiment.Pre_Cooling_Time

        # 2. 连接仪器
        self.setattr_device("core")
        self.qcmos = QCMOSForHistogram(self)
        self.mw = SignalMW(self)

        # 3.设置参数
        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="重复次数",
            group="Experiment",
        )

        self.setattr_argument(
            "re_repeat",
            NumberValue(default=30, min=1, step=1, precision=0),
            tooltip="多次 repeat 数据的协同处理, 主要解决 QCMOS 的 bundle mode 数量不够问题",
            group="Experiment",
        )
        self.setattr_argument(
            "detecting_time",
            NumberValue(default=self.parameter.Experiment.Detecting_Time, min=1e-4, step=1e-4, precision=4),
            tooltip="探测时间",
            group="Experiment",
        )

        self.setattr_argument(
            "detecting_power",
            NumberValue(default=self.parameter.Light_369.dds_for_D_amp, min=1e-3, step=1e-3, max=1, precision=3),
            tooltip="探测功率",
            group="Experiment",
        )

        self.setattr_argument(
            "detecting_fre",
            NumberValue(default=self.parameter.Light_369.dds_for_D_fre, min=1, step=1, unit="MHz", precision=0),
            tooltip="探测频率",
            group="Experiment",
        )

        if self.detecting_power is not None:
            self.parameter.Light_369.dds_for_D_amp = self.detecting_power
        if self.detecting_fre is not None:
            self.parameter.Light_369.dds_for_D_fre = self.detecting_fre

        self.setattr_argument("ion_choice", StringValue(default="(0,)"), group="Experiment")

        self.l369 = Light369(self)
        print("Experiment Registered: MW_Histogram")

    def prepare(self):
        """准备扫描参数和数据集"""
        self.ion_choice = eval(self.ion_choice)
        print('detecting_power', self.detecting_power)
        # self.histogram_threshold = self.histogram_threshold

    @kernel()
    def run(self):
        self.qcmos.prepare_for_histogram()
        # 0. initial
        self.core.reset()
        self.l369.initial()
        self.mw.initial()
        self.qcmos.initial()
        self.core.break_realtime()
        for i in range(self.task_num):
            for re_repeat in range(self.re_repeat):
                for repeat in range(self.repeat):
                    # ---------------- dark state ---------------
                    # 1 . 切换至 cooling 状态
                    delay(self.pre_cooling_time)
                    self.l369.switch_pre_cooling_to_cooling()
                    delay(self.cooling_time)
                    # 2. 切换至 pumping 状态
                    self.l369.switch_cool_to_pump()
                    delay(self.pumping_time)
                    # ３. 切换至 detect 状态
                    with parallel:
                        self.l369.switch_pump_to_detect()
                        self.qcmos.qcmos_start_count()
                    delay(self.detecting_time)
                    with parallel:
                        self.qcmos.qcmos_stop_count()
                    delay(2 * ms)
                    self.l369.switch_detect_to_pre_cooling()
                    # ---------------- bright state ---------------
                    # 1. 切换至 cooling 状态
                    delay( 0.5 * ms)
                    delay(self.pre_cooling_time)
                    self.l369.switch_pre_cooling_to_cooling()
                    delay(self.cooling_time)
                    # 2. 切换至 pumping 状态
                    self.l369.switch_cool_to_pump()
                    delay(self.pumping_time)
                    # 3. 切换至 control 阶段
                    with parallel:
                        self.mw.mw_on()
                        self.l369.switch_pump_to_control()
                    delay(self.mw_pi_2 * 2)
                    # 4. 切换至 detecting 状态
                    with parallel:
                        self.mw.mw_off()
                        self.qcmos.qcmos_start_count()
                        self.l369.switch_control_to_detect()
                    delay(self.detecting_time)
                    # 5. 切换至 off 状态
                    with parallel:
                        self.qcmos.qcmos_stop_count()
                    delay(2 * ms)
                    self.l369.switch_detect_to_pre_cooling()
                delay(10 * ms)
                self.core.wait_until_mu(now_mu())
                self.qcmos.process_for_re_repeat(re_repeat)
                self.core.break_realtime()

            delay(10 * ms)
            self.core.wait_until_mu(now_mu())
            self.qcmos.process_for_histogram(self.ion_choice, re_repeat=self.re_repeat)

    def analyze(self):
        print("QCMOS.Detect_threshold is update to:", self.parameter.QCMOS.Detect_threshold)
        print("QCMOS.Errors.Dark_to_Bright is update to:", self.parameter.QCMOS.Errors.Dark_to_Bright)
        print("QCMOS.Errors.Bright_to_Dark is update to:", self.parameter.QCMOS.Errors.Bright_to_Dark)
        self.parameter.update_config_json()

        # print("analyze done")
