import numpy as np
from artiq.experiment import *
from modules.raman_base_exp_qcmos_for_dev import RamanBaseExp
from modules.operation_manager import Operation, Circuit
from waveform_manager import Waveform


class AOD_Scan(Operation):
    """
    AOD Scan 波形构造
    """

    def __init__(
        self,
        qubit_index: tuple,
        aod_fre: float,
        side_choice="All",
        operation_time: float = 2e-6,
    ):
        """

        Parameters
        ----------
        qubit_index: 离子索引
        aod_fre: aod 扫描频率, 主要变量
        side_choice: 单双边选择
        operation_time: 操作时间
        """
        super().__init__(qubit_index)
        self.aod_fre = aod_fre
        self.side_choice = side_choice
        self.operation_time = operation_time
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        """构造 AOD scan 的 AOM 波形"""
        # 1. 创建 AOM 波形
        amp_dict = {
            "Right": [0, self.parameters.Light_554.AOM_AWG_amp],
            "Left": [self.parameters.Light_554.AOM_AWG_amp, 0],
            "All": [
                self.parameters.Light_554.AOM_AWG_amp,
                self.parameters.Light_554.AOM_AWG_amp,
            ],
        }
        amp_lift = amp_dict[self.side_choice][0]
        amp_right = amp_dict[self.side_choice][1]
        Idle = Waveform(
            duration=self.parameters.Light_554.AOD_time_before_AOM,
            amplitude=0,
            phase=0,
            detuning=0,
        )
        Carrier_L = Waveform(
            duration=self.operation_time,  # 扫描时间从config读取
            amplitude=amp_lift,  # AWG 输出的振幅
            phase=0,
            detuning=0,  # 失谐为 0, 即默认使用载波频率
        )
        Carrier_R = Waveform(
            duration=self.operation_time,  # 扫描时间从config读取
            amplitude=amp_right,  # AWG 输出的振幅
            phase=0,
            detuning=0,  # 失谐为 0, 即默认使用载波频率
        )

        # 2. 波形赋值到属性
        self.AOM_L = Idle * Carrier_L
        self.AOM_R = Idle * Carrier_R

        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()

    def compute_AOD_wave(self):
        """构造 AOD scan 的, AOD 波形
        注意失谐为 aod_fre, 在初始化阶段传进来的扫描参数
        """
        # 1. 构造波形
        AOD_wave = Waveform(
            duration=self.duration_time, amplitude=1.0, detuning=self.aod_fre
        )

        # 2. 波形到属性
        self.AOD_L = AOD_wave
        self.AOD_R = AOD_wave


class RamanAODScan(RamanBaseExp, EnvExperiment):
    """Raman AOD Scan"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(-1e6, 1e6, 30),
                global_min=-25e6,
                global_max=25e6,
                global_step=0.1e6,
                precision=2,
                unit="MHz",
            ),
            group="Experiment",
        )
        self.setattr_argument(
            "side_choice",
            EnumerationValue(["Left", "Right", "All"], default="All"),
            tooltip="选择左侧/右侧/双边rabi",
            group="Experiment",
        )

        self.setattr_argument(
            "operation_time",
            NumberValue(default=3e-6, min=1e-7, step=1e-7,unit="us", precision=8),
            tooltip="AOD 操作的时间",
            group="Experiment",
        )
        print("Experiment Registered: Raman AOD Scan")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.qcmos.prepare_for_x_scan((np.array(self.scan_parameter) + self.parameter.Light_554.AOD_middle_freq) / 1e6)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        AOD_Scan_Operation = AOD_Scan(qubit_index=(0,),
                                      aod_fre=scan_parameter,
                                      operation_time=self.operation_time,
                                      side_choice=self.side_choice)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(AOD_Scan_Operation)
        return AOD_Scan_Operation.duration()



if __name__ == "__main__":
    AOD_scan = [
        {
            "gate": "AOD_Scan",
            "qubit_index": (0, ),
            "aod_fre": 0,
            "operation_time": 3e-6,
            "side_choice": "All",
        }
    ]

    operation_list = [AOD_scan]
    circuit = Circuit(operation_list)
    circuit.plot_circuit_wave(AOM_base_fre=10e6, AOD_base_fre=10e6, sampling_rate=1e9)
