"""
用于执行 N 次量子线路的工具

参数: 
1. 量子线路
2. 次数 shot_num
3. 比特选择
"""
import numpy as np
from artiq.experiment import *
from modules.raman_base_exp_qcmos_for_dev import RamanBaseExp
from modules.operation_manager import Circuit
from modules.qcmos import *
from modules.light_554_awg import Light554 as Light554AWG
from modules.light_554_dds import Light554 as Light554DDS


class QCMOSForCircuitShot(QCMOS):

    @rpc(flags={})
    def prepare_for_circuit(self):
        """量子线路数据集
        1. 计算基
        2. 概率
        3. PMT 32 通道数据
        """
        # 1. 计算基
        self.set_dataset("computational_basis_histogram", [], broadcast=True)
        self.ccb.issue("create_applet", "Computational Basis Histogram",
                       "${artiq_applet}computational_basis_histogram computational_basis_histogram")

        # # 2. pmt 32 通道数据
        # self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)
        # self.set_dataset("PMT_channels", np.arange(0, 32), broadcast=True)
        # self.ccb.issue("create_applet", "PMT_counts_Plot",
        #                "${artiq_applet}green_plot_hist_8 PMT_counts --x PMT_channels")
        self.set_dataset("computational_basis_probability", [], broadcast=True)
        self.ccb.issue("create_applet", "Computational Basis Probability",
                       "${artiq_applet}computational_basis_histogram computational_basis_probability")

    @rpc(flags={})
    def process_for_circuit(self):
        """电路 shot 数据处理"""
        # 1. 读取数据
        init_image = self.cache

        # 2. ---------------------- ROI Counts ---------------------
        ion_counts_matrix = calculate_roi_pixel_sums_batch(init_image,
                                                           self.roi_for_ions,
                                                           repeat=self.parameter.QCMOS.Experiment.Repeat,
                                                           background_noise=self.parameter.QCMOS.bit16.background_noise)
        print("ion_counts_matrix", ion_counts_matrix.shape)

        # 3. ---------------------- 阈值处理 ------------------------
        # threshold_data = ion_counts_matrix > np.array(
        #     self.parameter.QCMOS.Detect_threshold)  # shape=[repeat, ion_nums], type=bool
        # 确保阈值数组与离子计数矩阵的第二维匹配
        thresholds = np.array(self.parameter.QCMOS.Detect_threshold)

        # 获取实际的离子数量
        n_ions = ion_counts_matrix.shape[1]

        # 简单处理：使用离子数量作为索引截取阈值数组
        if len(thresholds) >= n_ions:
            # 如果阈值数组足够长，取前n_ions个值
            thresholds = thresholds[:n_ions]
        else:
            # 如果阈值数组较短，使用最后一个阈值填充
            thresholds = np.array([thresholds[-1]] * n_ions)

        # 执行阈值比较
        threshold_data = ion_counts_matrix > thresholds

        # 4. ---------------------- 计算基构造 ------------------------

        computational_basis_histogram = self.computational_basis_analysis_all(threshold_data)
        print("computational_basis_histogram ", computational_basis_histogram )
        # print("computational_basis_histogram", computational_basis_histogram)
        self.set_dataset("computational_basis_histogram", computational_basis_histogram, broadcast=True)

        # 5. ----------------------- 概率数据构造 ------------------------

        computational_basis_probability = np.column_stack([computational_basis_histogram[:, 0],
                                                   computational_basis_histogram[:, 1]/self.parameter.QCMOS.Experiment.Repeat],
                                                   )
        # print("computational_basis_histogram ", computational_basis_probability)
        # print("computational_basis_histogram", computational_basis_histogram)
        self.set_dataset("computational_basis_probability", computational_basis_probability, broadcast=True)



class CircuitShot(RamanBaseExp, EnvExperiment):
    """Circuit Shot QCMOS"""

    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument("circuit", StringValue(default='[[{"gate": "X", "qubit_index": (0,)}]]'),
                              tooltip="量子线路", group="Experiment")

        self.setattr_argument("repeats", NumberValue(default=100, min=1, step=1, precision=0),
                              tooltip="执行次数", group="Experiment")

        self.setattr_argument("ion_index", StringValue(default=""), tooltip="量子线路", group="Experiment")

        if self.repeats is not None:
            print("repeat_in", self.repeats)
            self.parameter.QCMOS.Experiment.Repeat = self.repeats
            self.repeat = self.repeats
            if self.repeat % self.bundle_num != 0:
                raise ValueError(f"QCMOS Repeat value error {self.repeat}, must be a multiple of 100")
            self.re_repeat = int(self.repeat / self.bundle_num)
            self.qcmos = QCMOSForCircuitShot(self)
            if self.AWG_Mode is not None:
                if self.AWG_Mode == "AWG":
                    self.l554 = Light554AWG(self)
                elif self.AWG_Mode == "DDS":
                    self.l554 = Light554DDS(self)
                else:
                    raise "Unknown AWG_Mode"

        print("Experiment Registered: Circuit Shot")

    def prepare(self):
        # super().prepare()
        # 将量子线路字符串转换为量子线路列表
        self.circuit_asm = eval(self.circuit)
        print("Circuit :", self.circuit_asm)
        if len(self.circuit_asm) == 0:
            self.circuit_asm = [[{"gate": "Idle", "qubit_index": (0,), "idle_time": 1e-6}]]
        # 将量子线路列表转换为量子线路对象
        # print("Circuit :", self.circuit_asm)

        # self.circuit = Circuit(circuit_asm)

    @rpc(flags={""})
    def wave_compute(self, circuit_asm) -> TFloat:
        """传入参数计算波形"""
        print("len", len(circuit_asm))
        print("circuit_asm", circuit_asm)

        if len(circuit_asm) == 0:
            return 10e-6
        else:
            circuit = Circuit(circuit_asm)
            self.l554.prepare_waveform(circuit)
            return circuit.duration()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 1. 准备实验数据集
        self.qcmos.prepare_for_circuit()

    @rpc(flags={""})
    def data_process(self):
        """数据处理的逻辑"""
        self.qcmos.process_for_circuit()

    @rpc(flags={})
    def check_lost(self) -> TBool:
        """检查离子是否丢失"""
        lost = self.get_dataset("ion_lost")
        print(f"Ion_State: {lost}")
        return False

    @kernel()
    def run_level_1(self, operation_time):
        """单次 repeat 中的时序"""
        for _ in range(self.bundle_num):
            # self.qcmos.qcmos_start_count()
            delay(self.pre_cooling_time)
            self.l369.switch_pre_cooling_to_cooling()
            delay(self.cooling_time)
            # self.qcmos.qcmos_stop_count()

            self.l369.switch_cool_to_eit()
            delay(self.eit_time)

            self.l369.switch_eit_to_pump()
            delay(self.pumping_time)

            with parallel:
                self.l369.switch_pump_to_control()
                self.l554.AWG_on()

            with parallel:
                # self.l369.sbc_cooling(self.enable_SBC)
                delay(operation_time)

            with parallel:
                self.l554.AWG_off()
                self.qcmos.qcmos_start_count()
                self.l369.switch_control_to_detect()

            delay(self.detecting_time)

            with parallel:
                self.qcmos.qcmos_stop_count()
            delay(1.5 * ms)
            self.l369.switch_detect_to_pre_cooling()
            # delay(2 * ms)

    @kernel()
    def run_level_2(self, scan_parameter):
        """加入一个扫描点的波形计算和数据处理"""
        while True:
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter)

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1_re_repeat(operation_time)

            self.core.wait_until_mu(now_mu())

            # 3. 数据处理
            self.data_process()
            # 4. 离子状态检查
            # if self.check_lost():
            #     self.save_ions()  # 封装救离子的逻辑
            # else:
            #     # self.scan_point += 1
            break
            # 添加优雅中止
            # if self.scheduler.check_termination():
            #     break

    @kernel
    def run(self):
        self.exp_initial()
        self.run_level_2(self.circuit_asm)


if __name__ == "__main__":
    from artiq.frontend import artiq_run

    artiq_run.run()
