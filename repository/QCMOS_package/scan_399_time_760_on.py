import numpy as np
from artiq.experiment import *
from modules.config import LOADED_PARAMETER
from modules.light_369 import Light369
from modules.qcmos import QCMOS


class Scan399Time760On(EnvExperiment):
    """Scan 399 Time 760 On
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.pre_cooling_time = self.parameter.Experiment.Pre_Cooling_Time

        # 2. 模块准备
        self.setattr_device("core")
        self.l369 = Light369(self)
        self.qcmos = QCMOS(self)

        # 3. 变量设置
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 20),
                global_min=0,
                global_max=1,
                global_step=1e-6,
                precision=2,
                unit="us",
            ), group="Experiment")

        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="扫描任务的重复次数", group="Experiment")

        self.setattr_argument(
            "time_760",
            NumberValue(default=100e-6, min=0, step=1e-6, precision=6),
            tooltip="扫描任务的重复次数", group="Experiment")

        print("Experiment Registered: Scan 399 Time 760 On")

    def prepare(self):
        self.scan_parameter = self.X_scan_range.sequence  # 变量 list
        self.scan_points = len(self.scan_parameter)  # 数据点数
        print("Prepare Done")

    @kernel()
    def run(self):
        # 准备 X 轴数据集
        self.qcmos.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)
        # 0. initial
        self.core.reset()
        self.qcmos.initial()
        self.l369.initial()
        delay(self.pre_cooling_time)

        self.l369.dds_for_760.cfg_sw(False)
        self.l369.dds_for_399.cfg_sw(False)
        self.l369.switch_pre_cooling_to_cooling()

        # 1. 给方波信号
        for task_num in range(self.task_num):
            for scan_point in range(self.scan_points):

                for repeat in range(self.repeat):
                    # 1 . 切换至 cooling 状态
                    delay(self.cooling_time)

                    # 2. 切换至 pumping 状态
                    self.l369.switch_cool_to_pump()

                    delay(self.pumping_time)  # 等待 pumping

                    # ３. 切换至 control 状态
                    with parallel:
                        self.l369.switch_pump_to_control()
                        self.l369.dds_for_760.cfg_sw(True)
                        with sequential:
                            delay(1 * us)
                            self.l369.dds_for_399.cfg_sw(True)
                            delay(self.scan_parameter[scan_point])  # delay 一个 control 阶段的时间
                            self.l369.dds_for_399.cfg_sw(False)
                        delay(self.time_760)  # delay 一个 control 阶段的时间
                    self.l369.dds_for_760.cfg_sw(False)

                    # 4. 切换至 detecting 状态
                    with parallel:
                        self.l369.switch_control_to_detect()
                        self.qcmos.qcmos_start_count()

                    delay(self.detecting_time)

                    # 5. 切换至 cooling 状态
                    with parallel:
                        self.qcmos.qcmos_stop_count()

                    delay(1 * ms)

                    self.l369.switch_detect_to_cool()

                delay(3 * ms)
                self.core.wait_until_mu(now_mu())
                self.qcmos.process_for_x_scan(scan_point)
                self.core.break_realtime()
        self.l369.dds_for_760.cfg_sw(True)
        self.l369.dds_for_399.cfg_sw(True)

    def analyze(self):
        print("Analysis Done")
