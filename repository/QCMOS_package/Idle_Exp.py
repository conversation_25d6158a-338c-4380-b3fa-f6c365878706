import numpy as np
from artiq.experiment import *
from modules.config import LOADED_PARAMETER
from modules.light_369 import Light369
from modules.qcmos import QCMOS


class IdleExp(EnvExperiment):
    """ Cooling Pumping Control Detect Idle

    用途:
    1. 控制 369 光路在四个状态间来回切换
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time

        # 2. 模块准备
        self.setattr_device("core")
        self.setattr_device("scheduler")
        self.l369 = Light369(self)
        self.qcmos = QCMOS(self)

        self.setattr_argument(
            "Stage_369",
            EnumerationValue(["Cooling", "Pumping", "Controlling", "Detecting"], default="Cooling"),
            tooltip="369 Idle 所在阶段",
            group="Experiment",
        )

        self.setattr_argument("exposure_time", NumberValue(default=0.01, min=0, step=0.001, precision=6),
                              tooltip="曝光时间", group="Experiment")

        self.setattr_argument("delay_time", NumberValue(default=0.01, min=0, step=0.001, precision=6),
                              tooltip="曝光之后的等待时间", group="Experiment")

        print("Experiment Registered: Idle Exp")

    def prepare(self):
        print("Prepare Done")

    @rpc(flags={})
    def check_scheduler(self) -> TBool:
        status = self.scheduler.get_status()
        return len(status) > 1

    @kernel()
    def run(self):
        # 0. initial
        self.qcmos.prepare_for_idle()
        self.core.reset()
        self.qcmos.initial()
        self.l369.initial()

        if self.Stage_369 == "Cooling":
            self.l369.switch_pre_cooling_to_cooling()
        elif self.Stage_369 == "Pumping":
            self.l369.switch_pre_cooling_to_cooling()
            delay(10 * us)
            self.l369.switch_cool_to_pump()
        elif self.Stage_369 == "Controlling":
            self.l369.switch_pre_cooling_to_cooling()
            delay(10 * us)
            self.l369.switch_cool_to_pump()
            delay(10 * us)
            self.l369.switch_pump_to_control()
        elif self.Stage_369 == "Detecting":
            self.l369.switch_pre_cooling_to_cooling()
            delay(10 * us)
            self.l369.switch_cool_to_pump()
            delay(10 * us)
            self.l369.switch_pump_to_control()
            delay(10 * us)
            self.l369.switch_control_to_detect()
        else:
            pass

        # 1. 给方波信号
        while True:
            self.core.break_realtime()
            for repeat in range(self.repeat):
                # self.core.break_realtime()
                # repeat 此读数循环, 总积分时间是 cooling_time * repeat
                self.qcmos.qcmos_start_count()
                delay(self.exposure_time * s)
                self.qcmos.qcmos_stop_count()
                delay(self.delay_time * s)
            delay(100 * ms)
            self.core.wait_until_mu(now_mu())
            delay(100e-6)
            self.qcmos.process_for_idle_qcmos()
            if self.check_scheduler():
                break
            if self.scheduler.check_termination():
                break

    def analyze(self):
        print("Analysis Done")