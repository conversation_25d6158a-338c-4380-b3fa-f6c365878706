from modules.light_369 import *
from modules.signal_mw import *
from modules.qcmos import * 

class RabiMWQCMOS(EnvExperiment):
    """MW_Rabi_QCMOS

    用途:
    1. 执行 MW Rabi 实验;
    2. 执行 +- <PERSON><PERSON><PERSON> 实验;
    3. 测量 MW <PERSON>bi, <PERSON><PERSON><PERSON> +- 的 pi_2 时间
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.pre_cooling_time = self.parameter.Experiment.Pre_Cooling_Time

        self.zeeman_p = self.parameter.Signal_MW.zeeman_p
        self.zeeman_n = self.parameter.Signal_MW.zeeman_n

        # 2. 连接仪器
        self.setattr_device("core")
        self.setattr_device("scheduler")
        self.l369 = Light369(self)
        self.qcmos = QCMOS(self)
        self.mw = SignalMW(self)

        # 3. 参数设置
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 20e-6, 21),
                global_min=0,
                global_max=1,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )

        # self.setattr_argument("mw_frequency", NumberValue(default=0, unit="Hz", precision=6), group="Experiment")

        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="扫描任务的重复次数",
            group="Experiment",
        )

        self.setattr_argument(
            "zeeman_choice",
            EnumerationValue(["+", "0", "-"], default="0"),
            group="Experiment",
        )
        print("Experiment Registered: MW_Rabi")

    def prepare(self):
        """准备扫描参数和数据集"""
        self.scan_parameter = self.X_scan_range.sequence
        self.scan_points = len(self.scan_parameter)

        print("prepare_done")

    @rpc(flags={"async"})
    def tprint(self, data):
        print(data)

    @kernel()
    def run(self):
        self.qcmos.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)
        # 0. initial
        self.core.reset()
        self.qcmos.initial()
        self.l369.initial()
        self.mw.initial()

        # 根据扫 Zeeman 的实验类型设置 MW 失谐
        if self.zeeman_choice == "+":
            self.mw.set_mw_parameter(detuning=self.zeeman_p)
        elif self.zeeman_choice == "-":
            self.mw.set_mw_parameter(detuning=self.zeeman_n)
        else:
            self.mw.set_mw_parameter(detuning=0.0)

        self.core.break_realtime()

        for task in range(self.task_num):
            for scan_point in range(self.scan_points):
                for repeat in range(self.repeat):
                    # 1 . 切换至 cooling 状态
                    # self.pmt.pmt_start_count()
                    delay(self.pre_cooling_time)
                    self.l369.switch_pre_cooling_to_cooling()
                    delay(self.cooling_time)
                    # self.pmt.pmt_stop_count()

                    # 2. 切换至 pumping 状态
                    self.l369.switch_cool_to_pump()

                    delay(self.pumping_time)

                    # ３. 切换至 control 状态
                    with parallel:
                        self.l369.switch_pump_to_control()
                        self.mw.mw_on()

                    delay(self.scan_parameter[scan_point])

                    # 4. 切换至 detecting 状态
                    with parallel:
                        self.mw.mw_off()
                        self.qcmos.qcmos_start_count()
                        self.l369.switch_control_to_detect()

                    delay(self.detecting_time)

                    # 5. 切换至 off 状态
                    with parallel:
                        self.qcmos.qcmos_stop_count()
                    delay(1.5 * ms)
                    self.l369.switch_detect_to_pre_cooling()
                delay(1 * ms)
                self.core.wait_until_mu(now_mu())
                self.qcmos.process_for_x_scan(scan_point)

                if self.scheduler.check_termination():
                    break
                self.core.break_realtime()

            if self.scheduler.check_termination():
                break

    def analyze(self):
        pass
