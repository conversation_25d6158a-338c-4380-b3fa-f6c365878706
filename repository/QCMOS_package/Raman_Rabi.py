import numpy as np
from artiq.experiment import *
from modules.raman_base_exp_qcmos_for_dev import RamanBaseExp
from modules.operation_manager import Circuit


class RamanRabi(RamanBaseExp, EnvExperiment):
    """Raman Rabi"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=10000e-6,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )

        self.setattr_argument("rabi_choice", EnumerationValue(['Red', 'Carrier', 'Blue'], default='Carrier'),
                              group="Experiment")

        self.setattr_argument(
            "side_choice",
            EnumerationValue(["Left", "Right", "All"], default="All"),
            tooltip="选择左侧/右侧/双边rabi",
            group="Experiment",
        )

        self.setattr_argument("phonon_index", NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="声子序号", group="Experiment")

        print("Experiment Registered: Raman Rabi")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.qcmos.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Rabi = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "rabi_time": scan_parameter,
                 "phonon_index": self.phonon_index,
                 "rabi_choice": self.rabi_choice,
                 "side_choice": self.side_choice}]
        if self.enable_SBC:
            operation_list = [SBC, Rabi]
        else:
            operation_list = [Rabi]
        circuit = Circuit(operation_list)
        print(circuit.circuit_list)
        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()


if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
