import numpy as np
from artiq.experiment import *
from modules.raman_base_exp_qcmos_for_dev import RamanBaseExp
from modules.operation_manager import  Circuit


class RamanRabiDelay(RamanBaseExp, EnvExperiment):
    """Raman Rabi Delay"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=10000e-6,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )

        self.setattr_argument("rabi_choice", EnumerationValue(['Red', 'Carrier', 'Blue'], default='Carrier'),
                              group="Experiment")

        self.setattr_argument(
            "side_choice",
            EnumerationValue(["Left", "Right", "All"], default="All"),
            tooltip="选择左侧/右侧/双边rabi",
            group="Experiment",
        )

        self.setattr_argument("phonon_index", NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="声子序号", group="Experiment")

        self.setattr_argument(
            "delay_time",
            NumberValue(
                default=0,
                min=0,
                max=10000e-6,
                step=1e-6,
                precision=2,
                unit="us",
            ),
            tooltip="Rabi过程前的延迟时间",
            group="Experiment",
        )

        print("Experiment Registered: Raman Rabi")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        (self.qcmos.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6))

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Rabi = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "rabi_time": scan_parameter,
                 "phonon_index": self.phonon_index,
                 "rabi_choice": self.rabi_choice,
                 "side_choice": self.side_choice}]
        if self.enable_SBC:
            operation_list = [SBC, Rabi]
        else:
            operation_list = [Rabi]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()

    @kernel()
    def run_level_1(self, operation_time):
        """单次 repeat 中的时序"""
        for _ in range(self.repeat):
            self.qcmos.qcmos_start_count()
            delay(self.pre_cooling_time)
            self.l369.switch_pre_cooling_to_cooling()
            delay(self.cooling_time)
            self.qcmos.qcmos_stop_count()
            # self.l369.switch_cool_to_pump()
            self.l369.switch_cool_to_eit()
            delay(self.eit_time)

            self.l369.switch_eit_to_pump()
            delay(self.pumping_time)

            self.l369.switch_pump_to_control()

            delay(self.delay_time)

            self.l554.AWG_on()

            with parallel:
                self.l369.sbc_cooling(self.enable_SBC)
                delay(operation_time)

            with parallel:
                self.l554.AWG_off()
                self.qcmos.qcmos_start_count()
                self.l369.switch_control_to_detect()

            delay(self.detecting_time)

            with parallel:
                self.qcmos.qcmos_stop_count()
            delay(1 * ms)
            self.l369.switch_detect_to_pre_cooling()


if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()