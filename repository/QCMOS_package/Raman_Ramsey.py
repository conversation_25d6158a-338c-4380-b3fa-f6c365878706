import numpy as np
from artiq.experiment import *
from modules.raman_base_exp_qcmos_for_dev import RamanBaseExp
from modules.operation_manager import  Circuit



class <PERSON><PERSON><PERSON><PERSON><PERSON>(RamanBaseExp, EnvExperiment):
    """Rama<PERSON> Ramsey"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=1000000e-6,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )
        self.setattr_argument(
            "ramsey_choice",
            EnumerationValue(["Carrier", "Red", "Blue"], default="Carrier"),
            tooltip="选择红/蓝边带还是载波",
            group="Experiment",
        )

        self.setattr_argument("phonon_index", NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="声子序号", group="Experiment")

        print("Experiment Registered: <PERSON><PERSON>")


    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.qcmos.prepare_for_x_scan(np.array(self.scan_parameter) /1e-6 )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 读取pi/2时间
        pi_2_times = {
            "Carrier": self.parameter.Light_554.pi_2.Carrier[self.qubit_index[0]],
            "Red": self.parameter.Light_554.pi_2.Red,
            "Blue": self.parameter.Light_554.pi_2.Blue
        }

        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Raman_Pi2 = [{"gate": "Raman_Rabi",
                      "qubit_index": self.qubit_index,
                      "phonon_index":self.phonon_index,
                      "rabi_time": pi_2_times[self.ramsey_choice],
                      "rabi_choice": self.ramsey_choice}]
        Idle_Ramsey = [
            {
                "gate": "Idle",
                "qubit_index": self.qubit_index,
                "idle_time": scan_parameter
            }
        ]
        if self.enable_SBC:
            operation_list = [SBC, Raman_Pi2,Idle_Ramsey,Raman_Pi2]
        else:
            operation_list = [Raman_Pi2,Idle_Ramsey,Raman_Pi2]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
