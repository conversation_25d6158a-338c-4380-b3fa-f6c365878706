import numpy as np
from artiq.experiment import *
from modules.raman_base_exp_qcmos_for_dev import RamanBaseExp
from modules.operation_manager import Operation, Circuit
from waveform_manager import Waveform


class Raman_Spectrum(Operation):
    """
    Raman Spectrum
    """

    def __init__(self, qubit_index: tuple, aom_fre: float, operation_time: float):
        super().__init__(qubit_index)

        self.aom_fre = aom_fre
        self.operation_time = operation_time
        self.compute_AOM_wave()
        self.compute_AOD_wave()


    def compute_AOM_wave(self, algorithm=None):
        """
        AOM 波形
        """
        # 1. 创建 AOM 波形

        Carrier = Waveform(
            duration=self.operation_time,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        Sideband = Waveform(
            duration=self.operation_time,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=self.aom_fre,
        )
        # 2. 波形赋值到属性
        self.AOM_L = Carrier
        self.AOM_R = Sideband
        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()


class RamanSpectrum(RamanBaseExp, EnvExperiment):
    """Raman Spectrum"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(-1e6, 1e6, 30),
                global_min=-50e6,
                global_max=50e6,
                global_step=0.1e6,
                precision=2,
                unit="MHz",
            ),
            group="Experiment",
        )

        self.setattr_argument(
            "operation_time",
            NumberValue(default=3e-6, min=1e-6, step=1e-6, precision=8),
            tooltip="Raman Rabi 操作的时间",
            group="Experiment",
        )

        self.setattr_argument("phonon_index", NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="声子序号", group="Experiment")

        print("Experiment Registered: Raman Spectrum")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.qcmos.prepare_for_x_scan(np.array(self.scan_parameter) / 1e6)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却

        Raman_Spectrum = [
            {
                "gate": "Raman_Spectrum",
                "qubit_index": self.qubit_index,
                "aom_fre": scan_parameter,
                "operation_time": self.operation_time,
            }
        ]
        if self.enable_SBC:
            operation_list = [SBC, Raman_Spectrum]
        else:
            operation_list = [Raman_Spectrum]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
