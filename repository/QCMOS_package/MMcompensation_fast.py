"""
MMC_fast QCMOS
"""

from modules.config import Config, LOADED_PARAMETER
from modules.qcmos import *
from modules.light_369 import *
# from modules.light_554 import *
from artiq.experiment import *
from modules.RigolDZ1000_package.Rigol_DZ1000 import RigolDZ1000
import socket  # 导入 socket 模块
import time


class Mysocket:
    '''
    定义与DC树莓派的socket连接
    '''

    def __init__(self, ip, port):
        self.ip = ip
        self.port = port
        self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.s.connect((ip, port))

    def send(self, data):
        # self.cleancache()
        self.s.send(data.encode())

    def recv(self):
        temp = self.s.recv(1024).decode()
        # return re.sub("Keep-Alive Packet", "", temp)
        return temp

    def close(self):
        self.s.close()

    def shutdown(self):
        self.s.shutdown(socket.SHUT_WR)

    def cleancache(self):
        data = self.s.recv(1024)
        return data

class QCMOSForMMC(QCMOS):
    def prepare_for_mmc(self, x_points):
        self.set_dataset("x_points", x_points, broadcast=True)
        self.set_dataset("y_counts", [np.full(self.ion_nums, np.nan)], broadcast=True)
        self.set_dataset("y_counts_all", [np.full(self.ion_nums, np.nan)], broadcast=True)
        self.ccb.issue("create_applet", "X-Scan",
                       "${artiq_applet}ionctrl_plot_multi_xy_8_color probability --x x_points")  # 提交绘图组件

        self.set_dataset("img", np.zeros([self.init_roi[0], self.init_roi[1]]), broadcast=True)
        self.ccb.issue("create_applet", "IMG", "${artiq_applet}qcmos_image img")

    @rpc(flags={"async"})
    def process_for_mmc(self, reset=False):
        if reset:
            self.set_dataset("y_counts", [np.full(self.ion_nums, np.nan)], broadcast=True)

        # 1. 读数 repeat 组, 并设置到绘图组件
        init_image = self.read_data()  # shape = [height * repeat, width]
        sum_image = sum_repeated_images(init_image, self.parameter.QCMOS.Experiment.Repeat)
        self.set_dataset("img", sum_image.T, broadcast=True)

        # 2. ROI Counts
        ion_counts_matrix = calculate_roi_pixel_sums_batch(init_image,
                                                           self.roi_for_ions,
                                                           repeat=self.parameter.QCMOS.Experiment.Repeat,
                                                           background_noise=self.parameter.QCMOS.bit16.background_noise)
        # print(ion_counts_matrix)
        cal_data = calc_col_stats(ion_counts_matrix)
        print("mean, std, rms,max,min", cal_data)
        # 3.1 求和并放入实时 Ion_Counts 组件

        ion_counts = np.sum(ion_counts_matrix, axis=0).tolist()
        self.set_dataset("ion_count", ion_counts, broadcast=True)

        # 3.2 更新到 x_scan_counts 的

        self.append_to_dataset("y_counts", ion_counts)
        self.append_to_dataset("y_counts_all", ion_counts)

class MMcompensation_Fast(EnvExperiment):
    """ Micro Motion Compensation Fast
    """

    def build(self):
        # 1. 参数拉取
        # self.parameter = Config(DEFAULT_PARAMETER)
        self.parameter = LOADED_PARAMETER()
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.repeat = self.parameter.Experiment.Repeat

        # 2. 连接仪器
        self.setattr_device("core")
        self.l369 = Light369(self)
        # self.l554 = Light554(self)
        self.qcmos = QCMOSForMMC(self)

        # rigol scan frequency, Hz
        self.setattr_argument("X_scan_range",
                              Scannable(default=RangeScan(935000, 945000, 25), global_min=1, global_max=60000000,
                                        global_step=10, precision=0))  # MHz
        # sampling time, unit is ms
        self.setattr_argument("SamplingTime_ms", NumberValue(1, step=1, precision=1))
        # Tickle_volt of CH2, range(0.1 to 0.5)
        self.setattr_argument("Tickle_volt", NumberValue(0.2, step=1, precision=2))

        # Alpha
        self.setattr_argument("alpha_start", NumberValue(0.0, step=0.01, precision=2), group='DC scan')
        self.setattr_argument("alpha_end", NumberValue(0.0, step=0.01, precision=2), group='DC scan')
        # Beta
        self.setattr_argument("beta_start", NumberValue(0.0, step=0.01, precision=2), group='DC scan')
        self.setattr_argument("beta_end", NumberValue(0.0, step=0.01, precision=2), group='DC scan')
        # DC scan number
        self.setattr_argument("DC_scan_number", NumberValue(1, step=1, precision=0), group='DC scan')

        # DC
        self.setattr_argument("V_A", NumberValue(0.0, step=0.000001, precision=6), group='DC')
        self.setattr_argument("V_B", NumberValue(0.0, step=0.000001, precision=6), group='DC')
        self.setattr_argument("V_RF1", NumberValue(0.0, step=0.0000001, precision=6), group='DC')
        self.setattr_argument("V_RF2", NumberValue(0.0, step=0.0000001, precision=6), group='DC')
        self.setattr_argument("scale", NumberValue(0.04, step=0.01, precision=2), group='DC')
        print("Experiment Registered: MMcompensation_fast")

    def prepare(self):
        """实验准备阶段，将必要的参数从 config 文件中提取出来， artiq 不支持传递字典格式"""
        self.qcmos.prepare_for_idle()

        # self.l554.awg_device.close()  # 关闭可能开启的 AWG

        # 1. 获得参数
        self.scan_parameter = self.X_scan_range.sequence
        # 2. 准备实验数据集
        self.qcmos.prepare_for_mmc(np.array(self.scan_parameter) / 1e6)
        print("prepare_done")
        # 3. 连接Rigol
        self.CH2 = RigolDZ1000(CH=2)
        self.CH2.setAmp(self.Tickle_volt)
        print('Rigol DZ1000 connected')
        # 4. 连接DC
        #指定连接DC树莓派的地址与端口
        self.socketA = Mysocket("192.168.1.127", 6666)
        self.socketB = Mysocket("192.168.1.127", 6666)
        self.channel_list_A = [0,1, 2, 3, 4, 5]

        self.alpha = np.linspace(self.alpha_start, self.alpha_end, self.DC_scan_number)
        self.beta = np.linspace(self.beta_start, self.beta_end, self.DC_scan_number)
        self.set_dataset("alpha", self.alpha, broadcast=True, archive=True)
        self.set_dataset("beta", self.beta, broadcast=True, archive=True)

    @rpc(flags={"async"})
    def tprint(self, data):
        print(data)

    @kernel()
    def run(self):
        # 0. initial
        self.core.reset()

        self.qcmos.initial()

        self.l369.initial()  # 369 初始化完后直接到 cooling 状态
        for dc_point in range(self.DC_scan_number):
            self.set_DC(self.alpha[dc_point], self.beta[dc_point])
            reset = True
            for scan_point in range(len(self.scan_parameter)):

                self.CH2.setFreq(self.scan_parameter[scan_point])

                self.core.break_realtime()
                # delay(200*ms)
                # 留出设置频率的时间

                for repeat in range(self.repeat):
                    self.qcmos.qcmos_start_count()
                    delay(self.SamplingTime_ms * ms)
                    # delay(1000*us)
                    self.qcmos.qcmos_stop_count()
                    delay(1 * ms)  #在PMT开始读数和停止读数之间添加缓冲
                self.core.wait_until_mu(now_mu())
                delay(0.5 * ms)
                self.qcmos.process_for_mmc(reset)
                reset = False
        self.set_DC(0, 0)
        self.CH2.stopScan()

    def analyze(self):
        print("analyze")

    @rpc(flags={"async"})
    def set_DC(self, alpha, beta, scale=0.04):
        '''
        根据 alpha 和 beta
        设置电极电压

        '''
        dVA3 = scale * (alpha + beta)
        dVB3 = -dVA3
        dVRF1 = scale * (alpha - beta)
        dVRF2 = -dVRF1
        channels = np.array([5, 1, 10, 2]).flatten()
        voltages = np.array([self.V_A + dVA3, self.V_RF1 + dVRF1, self.V_B + dVB3, self.V_RF2 + dVRF2]).flatten()
        for i, ch in enumerate(channels):
            if (voltages[i] > 10) | (voltages[i] < -10):
                print('Dangerous, Voltage on CH%2d out of range ±11' % ch)
                continue
            try:

                tempvalue = voltages[i]
                if tempvalue > 10:
                    tempvalue = 10
                elif tempvalue < -10:
                    tempvalue = -10
                if int(channels[i]) in self.channel_list_A:
                    self.socketA = Mysocket("192.168.1.127", 6666)

                    self.socketA.send(f"SetDC_{int(channels[i])}_{tempvalue}")
                    print(f"Channel_{channels[i]}:{self.socketA.recv()}")
                else:
                    self.socketB = Mysocket("192.168.1.127", 6666)
                    self.socketB.send(f"SetDC_{int(channels[i])}_{tempvalue}")
                    print(f"Channel_{channels[i]}:{self.socketB.recv()}")
            except Exception as e:
                print("TCP_error on CH%2d：" % ch, e)
                continue
