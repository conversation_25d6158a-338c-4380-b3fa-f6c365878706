import numpy as np
from artiq.experiment import *
from modules.raman_base_exp_qcmos_for_dev import RamanBaseExp
from waveform_manager import Waveform
from modules.operation_manager import Circuit,Operation
class Raman_Rabi_Operation(Operation):
    """
    Raman_Rabi 波形构造

    1. 包括载波, 红, 蓝边带 rabi, 可以选择对哪个声子的边带进行操作;
    2. 可以选择指定离子进行操作;
    3. 可以选择左侧或者右侧或者两侧的 554 打开, 注意选择单侧 Rabi 时, 由于 AOM 存在关断比, 另一侧的光会有一点影响.
    """

    def __init__(self, qubit_index: tuple, rabi_time: float,aom_amp = 1.0, phase=0.0, phonon_index=0, rabi_choice="Carrier",
                 side_choice="All"):
        super().__init__(qubit_index)

        self.rabi_time = rabi_time
        self.side_choice = side_choice
        self.rabi_choice = rabi_choice
        self.aom_amp = aom_amp
        self.phonon_index = phonon_index
        self.phase = phase
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        """AOM 波形计算"""
        # 1. 选择单边还是双边
        amp_dict = {
            "Right": [0, self.parameters.Light_554.AOM_AWG_amp],
            "Left": [self.parameters.Light_554.AOM_AWG_amp, 0],
            "All": [
                self.aom_amp,
                0.3,
            ],
        }
        amp_left, amp_right = amp_dict[self.side_choice]

        detuning_dict = {
            "Carrier": 0,
            "Red": self.parameters.Light_554.Motion_freq[self.phonon_index],
            "Blue": -self.parameters.Light_554.Motion_freq[self.phonon_index]
        }
        # 2. 左边的载波波形

        Carrier_L = Waveform(
            duration=self.rabi_time,
            amplitude=amp_left,
            phase=0,
            detuning=0,
        )
        # 3. 构造右侧波形
        # 根据边带选择决定右侧失谐

        Carrier_R = Waveform(
            duration=self.rabi_time,
            amplitude=amp_right,
            phase=self.phase,
            detuning=detuning_dict[self.rabi_choice],
        )
        # 4. 波形赋值到属性

        self.AOM_L = Carrier_L
        self.AOM_R = Carrier_R

        # 5. 计算持续时间
        self.duration_time = self.AOM_L.duration()

class RamanRabi(RamanBaseExp, EnvExperiment):
    """Raman Rabi amp"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=10000e-6,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )

        self.setattr_argument("rabi_choice", EnumerationValue(['Red', 'Carrier', 'Blue'], default='Carrier'),
                              group="Experiment")

        self.setattr_argument(
            "side_choice",
            EnumerationValue(["Left", "Right", "All"], default="All"),
            tooltip="选择左侧/右侧/双边rabi",
            group="Experiment",
        )
        self.setattr_argument("aom_amp",NumberValue(default=0, min=0, max = 1,step=0.01, precision=6),
                              tooltip="AOM左侧幅度", group="Experiment")
        self.setattr_argument("phonon_index", NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="声子序号", group="Experiment")

        print("Experiment Registered: Raman Rabi")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.qcmos.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        print("Start_compute_Waveform")
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Rabi = [{"gate": "Raman_Rabi_Operation",
                 "qubit_index": self.qubit_index,
                 "rabi_time": scan_parameter,
                 "phonon_index": self.phonon_index,
                 "rabi_choice": self.rabi_choice,
                 "side_choice": self.side_choice,
                 "aom_amp":self.aom_amp,
                 }]
        if self.enable_SBC:
            operation_list = [SBC, Rabi]
        else:
            operation_list = [Rabi]
        print("Start_compute_circuit")

        circuit = Circuit(operation_list)
        print("End_compute_Waveform")

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        print("prepare_compute_Waveform")

        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
