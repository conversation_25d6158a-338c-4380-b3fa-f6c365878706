from __future__ import annotations

from modules.RPC_Client.rf_ctrl import RFCtrl
from modules.RPC_Client.servo_motor import MotorServer
from modules.light_369 import *
from modules.signal_mw import *
from modules.qcmos import *
from typing import List, Optional, Tuple

import cv2
import numpy as np

def calculate_ion_centers(roi_for_ions):
    """
    计算每个离子的中心坐标和整个离子链的中心坐标

    参数:
        roi_for_ions: 离子ROI列表，格式为 [[[x1,y1], [x2,y2], ...], ...]

    返回:
        ion_centers: 每个离子的中心坐标数组，形状为(n_ions, 2)，保留两位小数
        chain_center: 整个离子链的中心坐标，形状为(2,)，保留两位小数
    """
    # 计算每个离子的中心坐标
    ion_centers = []
    for ion_roi in roi_for_ions:
        # 转换为numpy数组并计算均值，保留两位小数
        points = np.array(ion_roi)
        center = np.round(np.mean(points, axis=0), decimals=2)
        ion_centers.append(center)

    # 转换为numpy数组
    ion_centers = np.array(ion_centers)

    # 计算整个离子链的中心坐标
    chain_center = np.round(np.mean(ion_centers, axis=0), decimals=2)

    return ion_centers, chain_center

def pad_pixel_marks(pixel_marks: List[List[List[int]]]) -> np.ndarray:
    if not pixel_marks:  # 如果为空，返回空数组
        return np.array([], dtype=np.int32).reshape(0, 0, 2)  # 形状 (0, 0, 2)

    max_pixels = max(len(ion) for ion in pixel_marks)  # 找到最长的离子像素列表
    padded = []
    for ion in pixel_marks:
        padded_ion = ion + [[-1, -1]] * (max_pixels - len(ion))  # 用 [-1, -1] 填充
        padded.append(padded_ion)
    return np.array(padded, dtype=np.int32)  # 转换为均匀数组

# def detect_brightest_pixels_from_avg(
#         cooling_avg: np.ndarray,
#         pumping_avg: np.ndarray,
#         *,
#         min_pixel_count: int = 1,
#         brightness_threshold: float,
#         max_candidate_pixels: int = 10,
# ) -> List[List[List[int]]]:
#     """
#     利用两张预平均 cooling/pumping 图像，通过亮度阈值检测每个离子的像素点标记。
#
#     Parameters
#     ----------
#     cooling_avg : np.ndarray
#         处于冷却态（离子发光）的灰度平均图像，形状 ``[H, W]``。
#     pumping_avg : np.ndarray
#         处于抽运态（离子暗态）的灰度平均图像，与 ``cooling_avg`` 形状一致。
#     min_pixel_count : int, optional
#         连通域最小像素数阈值，默认值为 ``1``。
#     brightness_threshold : float
#         亮度阈值，从最亮的像素开始累加，直到达到此阈值的最小像素数。
#     max_candidate_pixels : int, optional
#         候选像素的最大数量，默认值为 ``10``。
#
#     Returns
#     -------
#     List[List[List[int]]]
#         像素标记列表，每个离子对应一个子列表，格式为 [[[x1, y1], [x2, y2], ...], ...]。
#     """
#     if cooling_avg.shape != pumping_avg.shape:
#         raise ValueError("cooling_avg and pumping_avg must have same shape")
#     if cooling_avg.ndim != 2:
#         raise ValueError("image must be single channel picture")
#
#     # 差分并归一化
#     diff = cv2.subtract(cooling_avg, pumping_avg)
#     diff_u8 = cv2.normalize(diff, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
#
#     # 自适应阈值分割
#     T = diff_u8.mean() + 4.0 * diff_u8.std()
#     print("threshold", T)
#     _, mask = cv2.threshold(diff_u8, T, 255, cv2.THRESH_BINARY)
#
#     # 连通域分析
#     num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(mask, 8)
#     pixel_marks_for_ions: List[List[List[int]]] = []
#
#     print("num_labels", num_labels)
#     for i in range(1, num_labels):  # 跳过背景标签 0
#         area = int(stats[i, cv2.CC_STAT_AREA])
#         print(f"area {i}: pixel_num = {area}")
#         if area < min_pixel_count:
#             continue
#
#         # 找到该连通域中的所有像素位置
#         comp_mask = (labels == i)
#         y_coords, x_coords = np.where(comp_mask)
#         pixel_values = diff[comp_mask]
#
#         # 按亮度从大到小排序
#         sorted_indices = np.argsort(pixel_values)[::-1]
#
#         # 基于亮度阈值的动态像素选择
#         cumulative_brightness = 0.0
#         selected_indices = []
#
#         # 限制候选像素数量，避免选择过多像素
#         max_pixels = min(max_candidate_pixels, len(sorted_indices))
#
#         for j in range(max_pixels):
#             idx = sorted_indices[j]
#             cumulative_brightness += float(pixel_values[idx])
#             selected_indices.append(idx)
#
#             # 达到阈值或已选择最少1个像素时检查是否停止
#             if cumulative_brightness >= brightness_threshold and len(selected_indices) >= 1:
#                 break
#
#         # 如果累积亮度仍未达到阈值，至少选择1个最亮的像素
#         if not selected_indices:
#             selected_indices = [sorted_indices[0]]
#
#         print(
#             f"ion {i}: threshold={brightness_threshold}, cumulative_brightness={cumulative_brightness:.2f}, selected_indices={len(selected_indices)}")
#
#         # 构造该离子的像素标记列表
#         ion_pixel_marks = []
#         for idx in selected_indices:
#             x, y = int(x_coords[idx]), int(y_coords[idx])
#             ion_pixel_marks.append([x, y])
#
#         pixel_marks_for_ions.append(ion_pixel_marks)
#
#     return pixel_marks_for_ions


def detect_brightest_pixels_from_avg(
        cooling_avg: np.ndarray,
        pumping_avg: np.ndarray,
        *,
        min_pixel_count: int = 1,
        brightness_threshold: float,
        max_candidate_pixels: int = 10,
) -> List[List[List[int]]]:
    """
    利用两张预平均 cooling/pumping 图像，通过亮度阈值检测每个离子的像素点标记，并按x坐标从左到右排序离子区域。

    Parameters
    ----------
    cooling_avg : np.ndarray
        处于冷却态（离子发光）的灰度平均图像，形状 ``[H, W]``。
    pumping_avg : np.ndarray
        处于抽运态（离子暗态）的灰度平均图像，与 ``cooling_avg`` 形状一致。
    min_pixel_count : int, optional
        连通域最小像素数阈值，默认值为 ``1``。
    brightness_threshold : float
        亮度阈值，从最亮的像素开始累加，直到达到此阈值的最小像素数。
    max_candidate_pixels : int, optional
        候选像素的最大数量，默认值为 ``10``。

    Returns
    -------
    List[List[List[int]]]
        像素标记列表，每个离子对应一个子列表，格式为 [[[x1, y1], [x2, y2], ...], ...]，按x坐标从左到右排序。
    """
    if cooling_avg.shape != pumping_avg.shape:
        raise ValueError("cooling_avg and pumping_avg must have same shape")
    if cooling_avg.ndim != 2:
        raise ValueError("image must be single channel picture")

    # 差分并归一化
    diff = cv2.subtract(cooling_avg, pumping_avg)
    diff_u8 = cv2.normalize(diff, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)

    # 自适应阈值分割
    T = diff_u8.mean() + 4.0 * diff_u8.std()
    print("threshold", T)
    _, mask = cv2.threshold(diff_u8, T, 255, cv2.THRESH_BINARY)

    # 连通域分析
    num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(mask, 8)
    pixel_marks_for_ions: List[List[List[int]]] = []

    print("num_labels", num_labels)

    # 收集所有离子区域信息，用于后续排序
    ion_regions = []

    for i in range(1, num_labels):  # 跳过背景标签 0
        area = int(stats[i, cv2.CC_STAT_AREA])
        # print(f"area {i}: pixel_num = {area}")
        if area < min_pixel_count:
            continue

        # 找到该连通域中的所有像素位置
        comp_mask = (labels == i)
        y_coords, x_coords = np.where(comp_mask)
        pixel_values = diff[comp_mask]

        # 按亮度从大到小排序
        sorted_indices = np.argsort(pixel_values)[::-1]

        # 基于亮度阈值的动态像素选择
        cumulative_brightness = 0.0
        selected_indices = []

        # 限制候选像素数量，避免选择过多像素
        max_pixels = min(max_candidate_pixels, len(sorted_indices))

        for j in range(max_pixels):
            idx = sorted_indices[j]
            cumulative_brightness += float(pixel_values[idx])
            selected_indices.append(idx)

            # 达到阈值或已选择最少1个像素时检查是否停止
            if cumulative_brightness >= brightness_threshold and len(selected_indices) >= 1:
                break

        # 如果累积亮度仍未达到阈值，至少选择1个最亮的像素
        if not selected_indices:
            selected_indices = [sorted_indices[0]]

        # print(
        #     f"ion {i}: threshold={brightness_threshold}, cumulative_brightness={cumulative_brightness:.2f}, selected_indices={len(selected_indices)}")

        # 构造该离子的像素标记列表
        ion_pixel_marks = []
        for idx in selected_indices:
            x, y = int(x_coords[idx]), int(y_coords[idx])
            ion_pixel_marks.append([x, y])

        # 计算该离子区域的平均x坐标用于排序
        avg_x = np.mean(x_coords[selected_indices])
        ion_regions.append((avg_x, ion_pixel_marks))

    # 按照x坐标从左到右排序离子区域
    ion_regions.sort(key=lambda x: x[0])
    pixel_marks_for_ions = [region[1] for region in ion_regions]

    return pixel_marks_for_ions



class QCMOSForROICalibration(QCMOS):
    @rpc(flags={"async"})
    def prepare_for_histogram(self):
        """ROI 数据集"""
        # 1. 差分图像显示
        self.set_dataset(
            "img", np.zeros([self.init_roi[0], self.init_roi[1]]), broadcast=True
        )
        self.ccb.issue("create_applet", "IMG", "${artiq_applet}qcmos_image img")

        # 2. ion_counts 显示
        self.set_dataset("ion_count_index", [0,1,2], broadcast=True)
        self.set_dataset("ion_count", [0,0,0], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "Ion_Counts",
            "${artiq_applet}green_plot_hist_8 ion_count --x ion_count_index",
        )

        # 3. 用于存放校准 roi 参数
        self.set_dataset("new_roi", [], broadcast=True)


    @rpc(flags={""})
    def process_for_roi_calibration(self):
        """离子 ROI 校准
        """
        # 1. 读数 cooling + pumping
        logger.info("start_roi_data_process")
        odd, even = self.read_data_double()
        logger.info("read_data_process")

        # 2. 均值计算
        cooling_frame = average_repeated_images(odd, repeat=self.parameter.Experiment.Repeat)
        # logger.info(f"cooling STD {cooling_frame.std()}")
        # logger.info(f"cooling MAX {np.max(cooling_frame)}")

        pumping_frame = average_repeated_images(even, repeat=self.parameter.Experiment.Repeat)
        # logger.info(f"pumping STD {pumping_frame.std()}")
        # logger.info(f"pumping MAX {np.max(pumping_frame)}")
        #
        # logger.info("average_repeat")
        cv2.imwrite(self.parameter.QCMOS.background_image_path, pumping_frame)
        cv2.imwrite(self.parameter.QCMOS.cooling_image_path, cooling_frame)
        np.savetxt(self.parameter.QCMOS.background_data_path, pumping_frame, delimiter=',', fmt='%d')

        logger.info("save pumping image success")

        # 构造差分图像
        diff_frame = cooling_frame - pumping_frame
        self.set_dataset(
            "img", diff_frame.T, broadcast=True
        )

        # 3. 使用亮度阈值像素标记格式
        brightness_threshold = self.parameter.QCMOS.brightness_threshold
        max_candidate_pixels = self.parameter.QCMOS.max_candidate_pixels

        print(f"ROI calibration parameter:")
        print(f"  -  brightness_threshold: {brightness_threshold}")
        print(f"  - max_candidate_pixels: {max_candidate_pixels}")

        pixel_marks = detect_brightest_pixels_from_avg(
            cooling_avg=cooling_frame,
            pumping_avg=pumping_frame,
            min_pixel_count=self.parameter.QCMOS.min_pixel_count,
            brightness_threshold=brightness_threshold,
            max_candidate_pixels=max_candidate_pixels
        )

        self.parameter.QCMOS.roi_for_ions = pixel_marks
        _, chain_center = calculate_ion_centers(pixel_marks)
        self.parameter.QCMOS.ion_chain_center = chain_center
        self.set_dataset("new_roi", pad_pixel_marks(pixel_marks), persist=True, broadcast=True)
        self.parameter.QCMOS.bit16.background_noise = [190 * len(roi) for roi in pixel_marks]  # 每个像素默认 190 的底噪
        print("Pixel marks calibration data", pixel_marks)
        print(f"Number of ions detected: {len(pixel_marks)}")

        # 打印每个离子的像素数量
        for i, ion_pixels in enumerate(pixel_marks):
            print(f"  - ion {i+1}: {len(ion_pixels)} pixel")

        # 更新 ion_counts
        ion_counts = calculate_roi_pixel_sums(
            image=diff_frame, roi_for_ions=self.roi_for_ions, background_noise=0
        )
        print("ion_count", ion_counts)
        self.set_dataset("ion_count", ion_counts[0, :], broadcast=True)
        self.set_dataset("ion_count_index", np.arange(0, self.ion_nums), broadcast=True)


    @rpc(flags={})
    def connect_qcmos(self, bundle_mode=1):
        if dcamcon_init():
            self.dcamcon = dcamcon_choose_and_open()
            if self.dcamcon is not None:
                # 设置相机参数
                res = setup_properties(
                    self.dcamcon,
                    x_pos=self.init_roi[0],
                    y_pos=self.init_roi[1],
                    width=self.init_roi[2],
                    height=self.init_roi[3],
                    tirg_mode=0,
                    bundle_mode=bundle_mode,
                    bundle_number=self.parameter.QCMOS.bundle_num * 2,
                )
                if res is False:
                    print("Parameter Configure False")
                else:
                    print("Parameter Configure Success")
            else:
                print("Device Open Failure")
        else:
            dcamcon_uninit()
            print("DCAM Initial Failure")


class IonsROIsCalibration(EnvExperiment):
    """Ions ROIs Calibration"""

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        # self.parameter = Config(DEFAULT_PARAMETER)
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.operation_time = self.parameter.Signal_MW.mw_pi_2.zero
        self.pre_cooling_time = self.parameter.Experiment.Pre_Cooling_Time

        # 2. 连接仪器
        self.setattr_device("core")
        self.l369 = Light369(self)
        self.qcmos = QCMOSForROICalibration(self)

        print("Experiment Registered: Ions ROIs Calibration")

    def prepare(self):
        """准备扫描参数和数据集"""
        self.motor = MotorServer(sever_ip=self.parameter.Motor.ip, ionization_channel=1)
        self.rf_ctrl = RFCtrl(ip=self.parameter.RFCtrl.ip)

        self.save_ions_time = 0  # 当前救离子次数
        self.max_save_time = 100  # 最大救离子次数


    @rpc(flags={""})
    def save_ions(self):
        """救离子的逻辑"""
        # 目前只加入开离子化光救离子的代码
        self.motor.open_channel(self.motor.ionization_channel)
        self.rf_ctrl.shake(self.parameter.RFCtrl.minus, self.parameter.RFCtrl.delay_time)
        time.sleep(5)
        self.motor.close_channel(self.motor.ionization_channel)
        time.sleep(2)
        # self.set_dataset("all_ion_lost", 0, broadcast=True)
        self.set_dataset("ion_lost", 0, broadcast=True)

    @kernel()
    def run(self):
        self.qcmos.prepare_for_histogram()
        # 0. initial
        self.core.reset()
        self.l369.initial()
        self.qcmos.initial()
        self.save_ions()

        self.core.break_realtime()

        for repeat in range(self.repeat):
            # ---------------- bright state ---------------
            # 1 . 切换至 cooling 状态
            delay(self.pre_cooling_time)
            self.l369.switch_pre_cooling_to_cooling()
            self.qcmos.qcmos_start_count()
            delay(self.detecting_time)
            self.qcmos.qcmos_stop_count()

            # ---------------- dark state ---------------
            delay(3 * ms)
            # 2. 切换至 pumping 状态
            self.l369.switch_cool_to_pump()
            delay(self.pumping_time * 5)

            self.qcmos.qcmos_start_count()
            delay(self.detecting_time)
            self.qcmos.qcmos_stop_count()
            delay(2 * ms)
            self.l369.switch_detect_to_pre_cooling()

        delay(10 * ms)
        self.core.wait_until_mu(now_mu())
        # 使用像素标记格式进行校准
        self.qcmos.process_for_roi_calibration()
        self.save_ions()

        self.core.break_realtime()

    def analyze(self):
        self.parameter.update_config_json()
        # print("QCMOS.roi_for_ions is updated to:", self.parameter.QCMOS.roi_for_ions)
        pass
