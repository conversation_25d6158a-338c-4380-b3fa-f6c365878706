
from modules.pmt import PMT
import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.circuit_to_waveform import ShiftedCircuit
from modules.operation_manager import Operation,Circuit
from waveform_manager import *
import dill
import json
with open('C:Users\\Administrator\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator_list.pkl','rb') as f:
    modulator_list = dill.load( f)

ms_parameter_file_path = "C:Users\\Administrator\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\modules\\gate_parameter.json"
with open(ms_parameter_file_path,'r') as json_file:
    ms_parameter = json.load(json_file)
class Rphi(Operation):
    def __init__(self, qubit_index,theta,phi):
        super().__init__(qubit_index)
        self.theta = theta
        self.phi = phi
        self.shift = self.parameters.Light_554.q1_AC_Stark_shift
        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def compute_AOM_wave(self, algorithm=None):
        pi2_time = self.parameters.Light_554.pi_2.Carrier[self.qubit_index[0]]
        delay_time = 0.358e-6
        if self.theta > 0:
            phi = 0
        else:
            phi = np.pi
        Carrier_pi_2_L = Waveform(
            duration= (pi2_time-delay_time) * (2 * abs(self.theta) / np.pi)+delay_time,
            amplitude= 0.12,
            phase=self.phi + phi,
            detuning=self.shift,
        )
        Carrier_pi_2_R = Waveform(
            duration= (pi2_time-delay_time) * (2 * abs(self.theta) / np.pi)+delay_time,
            amplitude= 0.12,
            phase= 0 ,
            detuning=0,
        )
        self.AOM_L = Carrier_pi_2_L
        self.AOM_R = Carrier_pi_2_R
        self.duration_time = self.AOM_R.duration()

class Rxx(Operation):
    def __init__(self, qubit_index,phase):
        super().__init__(qubit_index)


        self.modulator = modulator_list[qubit_index[0]][qubit_index[1]]
        self.phase = phase +ms_parameter["phase"][qubit_index[0]][qubit_index[1]]
        self.rabi_time = ms_parameter["gate_time"][qubit_index[0]][qubit_index[1]]
        self.aom_amp = ms_parameter["AOM_amp"][qubit_index[0]][qubit_index[1]]
        self.ac_stark_shift = ms_parameter["AC_Stark_shift"][qubit_index[0]][qubit_index[1]]
        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def amp_to_aom_amp(self,amp):
        sin_coeff = 1.6753
        aom_amp = np.arcsin(np.sin(sin_coeff)*amp)/sin_coeff
        return aom_amp
    def compute_AOM_wave(self, algorithm=None):
        dt = self.modulator.t_modulation
        N_len = int(np.floor(self.rabi_time/dt))
        N = self.modulator.N_modulation
        t_remain = self.rabi_time- N_len * dt
        aom_amp = self.aom_amp
        ratio = self.parameters.Light_554.ratio_AOM

        ac_stark_shift = self.ac_stark_shift

        detuning = self.modulator.mu / 2 / np.pi + 0
        # 1. 构造波形片段
        Idle = Waveform(
            duration= 0 ,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        self.AOM_L = Idle
        self.AOM_R = Idle



        for i in range(N_len):
            i_index = i % N
            amp = self.amp_to_aom_amp(abs(aom_amp*self.modulator.rabi[i_index]))
            if self.modulator.rabi[i_index] < 0:
                phase = np.pi + self.phase
            else:
                phase = 0 + self.phase
            carrier = Waveform(
                duration=dt,
                amplitude= amp,
                phase= phase,
                detuning=ac_stark_shift,
            )
            red = Waveform(
                duration=dt,
                amplitude=ratio/(ratio + 1),
                phase=0,
                detuning=detuning
            )
            blue = Waveform(
                duration=dt,
                amplitude=1/(ratio + 1),
                phase = 0,
                detuning = -detuning
            )
            self.AOM_L = self.AOM_L * carrier
            self.AOM_R = self.AOM_R * (red + blue)
        i_index = N_len % N
        amp = self.amp_to_aom_amp(abs(aom_amp * self.modulator.rabi[i_index]))
        if self.modulator.rabi[i_index] < 0:
            phase = np.pi + self.phase
        else:
            phase = 0 + self.phase
        carrier_end = Waveform(
            duration=t_remain,
            amplitude=amp,
            phase=phase,
            detuning=ac_stark_shift,
        )
        red = Waveform(
            duration=t_remain,
            amplitude=ratio/(ratio + 1),
            phase=0,
            detuning=detuning
        )
        blue = Waveform(
            duration=t_remain,
            amplitude=1/(ratio + 1),
            phase=0,
            detuning=-detuning
        )

        self.AOM_L = self.AOM_L * carrier_end
        self.AOM_R = self.AOM_R * (red + blue)
        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()


class CircuitPMT(PMT):
    def prepare_for_circuit(self):
        """量子线路数据集
        1. 计算基概率
        """
        # 1. 计算基
        self.set_dataset("computational_basis_histogram", [], broadcast=True)
        self.ccb.issue("create_applet", "Computational Basis Histogram",
                       "${artiq_applet}computational_basis_histogram computational_basis_histogram")
        # 2. pmt 32 通道数据
        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)
        self.set_dataset("PMT_channels", np.arange(0, 32), broadcast=True)
        self.ccb.issue("create_applet", "PMT_counts_Plot",
                       "${artiq_applet}green_plot_hist_8 PMT_counts --x PMT_channels")

        # 6. ion_lost
        self.set_dataset("ion_lost", 0, broadcast=True)
        self.set_dataset("one_ion_dark",0,broadcast= True)
        self.set_dataset("all_ion_lost", 0, broadcast=True)
        self.set_dataset("repeat",self.repeat,broadcast=True)

    @rpc(flags={})
    def data_process_for_circuit(self):
        """电路 shot 数据处理"""
        # 1. 读取数据
        data_temp_all = self.read_data_double()

        # 2. 处理Cooling数据，判断离子是否丢失
        data_temp_cooling = data_temp_all[0::2, :]
        self.cooling_count_data_process(data_temp_cooling)

        # 4. 处理Detect数据
        data_temp = data_temp_all[1::2, :]

        # 5.PMT32通道数据
        pmt_data_32 = np.sum(data_temp, 0)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)

        # 6. 通道选择
        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num
        # print("select_channels_counts", select_channels_counts)
        # 7. 阈值处理
        threshold_data = select_channels_counts > self.parameter.PMT.Detect_threshold
        # 8. 计算基
        computational_basis_histogram = self.computational_basis_analysis_all(threshold_data)
        print("computational_basis_histogram", computational_basis_histogram)
        self.set_dataset("computational_basis_histogram", computational_basis_histogram, broadcast=True)


class CircuitRun(RamanBaseExp,EnvExperiment):

    def build(self):
        # 1. 参数获取
        super().build()
        self.setattr_argument(
            "circuit_list", StringValue("[]"), tooltip= "线路",group="Experiment"
        )

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(1, 2, 2),
                global_min=1,
                global_max=10000,
                global_step=1,
                precision=0,
            ),
            group="Experiment",
        )

        self.pmt = CircuitPMT(self)
    def prepare(self):

        super().prepare()
        print(self.circuit_list)
        # self.circuit_list = eval(self.circuit_list)


    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan([0,1])

    @rpc(flags={""})
    def wave_compute(self,scan_parameter) -> TFloat:
        """传入参数计算波形"""
        try:
            circuit = ShiftedCircuit(self.circuit_list)
        except Exception as e:
            print(e)

        self.l554.prepare_waveform(circuit)
        circuit.AOM_L.plot_waveform(base_fre = 350e6)
        return circuit.duration()