import numpy as np
import dill
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit,Operation
from waveform_manager import Waveform
import copy

try:
    from cloud.circuit_generater import get_rb2_from_rb1,generate_rb1,get_circuit_from_rb2
except Exception as e:
    print(f"import failed:{e}")

from one_qubit_RB import R
class Rpi2(Operation):
    def __init__(self,qubit_index: tuple,rabi_time):
        super().__init__(qubit_index)
        self.rabi_time = rabi_time
        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def compute_AOM_wave(self, algorithm=None):
        t_pi2 = self.rabi_time
        aom_amp = 0.12
        Carrier_L = Waveform(
            duration=t_pi2,
            amplitude=aom_amp,
            phase=0,
            detuning=0,
        )
        # 3. 构造右侧波形
        # 根据边带选择决定右侧失谐

        Carrier_R = Waveform(
            duration= t_pi2,
            amplitude=aom_amp,
            phase=0,
            detuning=0,
        )
        # 4. 波形赋值到属性

        self.AOM_L = Carrier_L
        self.AOM_R = Carrier_R

        # 5. 计算持续时间
        self.duration_time = self.AOM_L.duration()
class RR(Operation):
    """
    R phi R phi
    """

    def __init__(self, qubit_index: tuple, theta: float, phase: float):
        super().__init__(qubit_index)
        self.theta = theta
        self.phi = phase
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        ac_stark_shift = self.parameters.Light_554.q1_AC_Stark_shift
        Carrier_L = Waveform(
            duration=self.parameters.Light_554.pi_2for2ions.Carrier  * (2 * self.theta / np.pi),
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase= self.phi,
            detuning=ac_stark_shift,
        )
        Carrier_R = Waveform(
            duration=self.parameters.Light_554.pi_2for2ions.Carrier  * (2 * self.theta / np.pi),
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        self.AOM_L = Carrier_L
        self.AOM_R = Carrier_R
        self.duration_time = self.AOM_R.duration()

class MSOperationPlus(Operation):
    def __init__(
            self,
            qubit_index: tuple,
            rabi_time: float,
            modulator,
            gate_type= "rxx",
            aom_amp=-1.0
    ):
        super().__init__(qubit_index)
        if aom_amp < 0:
            self.aom_amp = self.parameters.Light_554.AOM_AWG_amp_ms
        else:
            self.aom_amp = aom_amp
        self.rabi_time = rabi_time
        self.gate_type = gate_type
        self.modulator = modulator
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def amp_to_aom_amp(self, amp):
        sin_coeff = 1.8615
        aom_amp = np.arcsin(np.sin(sin_coeff) * amp) / sin_coeff
        return aom_amp

    def compute_AOM_wave(self, algorithm=None):
        dt = self.modulator.t_modulation
        N_len = int(np.floor(self.rabi_time / dt))
        N = self.modulator.N_modulation
        t_remain = self.rabi_time - N_len * dt
        aom_amp = self.aom_amp
        ratio = self.parameters.Light_554.ratio_AOM
        ac_stark_shift = self.parameters.Light_554.MS_AC_Stark_shift

        detuning = self.modulator.mu / 2 / np.pi - 0
        # 1. 构造波形片段
        Idle = Waveform(
            duration=0,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        self.AOM_L = Idle
        self.AOM_R = Idle

        for i in range(N_len):
            i_index = i % N
            amp = self.amp_to_aom_amp(abs(aom_amp * self.modulator.rabi[i_index]))
            if self.modulator.rabi[i_index] < 0:
                phase = np.pi
            else:
                phase = 0
            if self.gate_type == "ryy":
                phase += np.pi / 2
            phase += -0.4741572406600463/2+np.pi/2

            carrier = Waveform(
                duration=dt,
                amplitude=amp,
                phase=phase,
                detuning=ac_stark_shift,
            )
            red = Waveform(
                duration=dt,
                amplitude=ratio / (ratio + 1),
                phase=0,
                detuning=detuning
            )
            blue = Waveform(
                duration=dt,
                amplitude=1 / (ratio + 1),
                phase=0,
                detuning=-detuning
            )
            self.AOM_L = self.AOM_L * carrier
            self.AOM_R = self.AOM_R * (red + blue)
        i_index = N_len % N
        amp = self.amp_to_aom_amp(abs(aom_amp * self.modulator.rabi[i_index]))
        if self.modulator.rabi[i_index] < 0:
            phase = np.pi
        else:
            phase = 0
        carrier_end = Waveform(
            duration=t_remain,
            amplitude=amp,
            phase=phase,
            detuning=ac_stark_shift,
        )
        red = Waveform(
            duration=t_remain,
            amplitude=ratio / (ratio + 1),
            phase=0,
            detuning=detuning
        )
        blue = Waveform(
            duration=t_remain,
            amplitude=1 / (ratio + 1),
            phase=0,
            detuning=-detuning
        )

        self.AOM_L = self.AOM_L * carrier_end
        self.AOM_R = self.AOM_R * (red + blue)
        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()



class TwoRB(RamanBaseExp,EnvExperiment):
    """RB two qubit AM"""

    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(1, 101, 21),
                global_min=1,
                global_max=10000,
                global_step=1,
                precision=0,
            ),
            group="Experiment",
        )

        self.setattr_argument(
            "initial_state", StringValue("00"), tooltip="RB线路初态", group="Experiment"
        )
        print("Experiment Registered: single qubit RB")

    def prepare(self):
        super().prepare()
        with open('D:\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator.pkl',
                  'rb') as f:
            self.modulator = dill.load(f)
    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={})
    def wave_compute(self,scan_parameter) -> TFloat:
        """传入参数计算波形"""
        qc = generate_rb1(int(scan_parameter))
        qc2 = get_rb2_from_rb1(qc)
        circuit = get_circuit_from_rb2(qc)
        # circuit = ['-rx', '-ry', 'rxx',"-rx",]
        print(circuit)
        rabi_time0 = self.parameter.Light_554.pi_2.Carrier[self.qubit_index[0]]
        rabi_time1 = self.parameter.Light_554.pi_2.Carrier[self.qubit_index[1]]
        # 1. 定义操作
        inite_circuit = {
            "11":[
                [{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": 0}],
                [{"gate": "Idle", "qubit_index": self.qubit_index, "idle_time":0.5e-6}],
                [{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi / 2, "phase": 0}]
            ],
            "10":[
                [{"gate": "Rpi2", "qubit_index": (self.qubit_index[0],),"rabi_time": rabi_time0}],
                [{"gate": "Idle", "qubit_index": (self.qubit_index[0],), "idle_time": 0.5e-6}],
                [{"gate": "Rpi2", "qubit_index": (self.qubit_index[0],),"rabi_time": rabi_time0}],
                [{"gate": "Idle", "qubit_index": self.qubit_index}],

            ],
            "01":[
                [{"gate": "Rpi2", "qubit_index": (self.qubit_index[1],),"rabi_time":rabi_time1}],
                [{"gate": "Idle", "qubit_index": (self.qubit_index[1],), "idle_time": 0.5e-6}],
                [{"gate": "Rpi2", "qubit_index": (self.qubit_index[1],),"rabi_time": rabi_time1}],
                [{"gate": "Idle", "qubit_index": self.qubit_index}],
                  ],

            "00":[]
        }

        circuit_list = inite_circuit.get(self.initial_state,[])
        shift_q1 = self.parameter.Light_554.q1_AC_Stark_shift * 2 * np.pi
        ms_time = self.parameter.Light_554.MS_time
        gate_dict = {
            "rx":[{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": 0}],
            "ry":[{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": np.pi / 2}],
            "-rx":[{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": np.pi }],
            "-ry":[{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": -np.pi / 2}],
            "rxx":[{"gate": "MSOperationPlus",
                    "qubit_index": self.qubit_index,
                    "rabi_time": ms_time,
                    "modulator":self.modulator,
                    "gate_type": "rxx"}],
            "ryy":[{"gate": "MSOperationPlus",
                    "qubit_index": self.qubit_index,
                    "rabi_time": ms_time,
                    "modulator":self.modulator,
                    "gate_type": "ryy"}],
            "Idle":[{"gate": "Idle",
                    "qubit_index": self.qubit_index,
                    "idle_time":ms_time
                                  }]
        }

        for gate in circuit:
            circuit_list.append([{"gate": "Idle",
                                  "qubit_index": self.qubit_index,
                                  "idle_time":0.5e-6
                                  }])
            circuit_list.append(copy.deepcopy(gate_dict[gate]))
            if gate == "Idle":
                for single_gate in ["rx","ry",'-rx','-ry']:
                    gate_dict[single_gate][0]["phase"] += gate_dict[gate][0]["idle_time"]*(0-shift_q1)

        # circuit_list  = [
        #     [{"gate": "MSOperation", "qubit_index": self.qubit_index,}]
        # ]
        print(circuit_list)
        operation = Circuit(circuit_list)
        # 2. 将波形导入 awg
        self.l554.prepare_waveform(operation)
        # 3. 传回操作时间
        return operation.duration()

