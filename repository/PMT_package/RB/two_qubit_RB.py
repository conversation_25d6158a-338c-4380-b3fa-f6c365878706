import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit,Operation,MSOperation
from waveform_manager import Waveform


try:
    from cloud.circuit_generater import get_rb2_from_rb1,generate_rb1,get_circuit_from_rb2
except Exception as e:
    print(f"import failed:{e}")

from one_qubit_RB import R

class RR(Operation):
    """
    R phi R phi
    """

    def __init__(self, qubit_index: tuple, theta: float, phase: float):
        super().__init__(qubit_index)
        self.theta = theta
        self.phi = phase
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        Carrier_L = Waveform(
            duration=self.parameters.Light_554.pi_2for2ions.Carrier  * (2 * self.theta / np.pi),
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        Carrier_R = Waveform(
            duration=self.parameters.Light_554.pi_2for2ions.Carrier  * (2 * self.theta / np.pi),
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=self.phi,
            detuning=0,
        )
        self.AOM_L = Carrier_L
        self.AOM_R = Carrier_R
        self.duration_time = self.AOM_R.duration()

class MSOperationPlus(MSOperation):
    def __init__(self,
                 qubit_index: tuple,
                 rabi_choice='Carrier',
                 gate_type = "rxx",
                **kwargs):
        self.gate_type = gate_type
        print(kwargs)
        super().__init__(qubit_index=qubit_index,rabi_choice=rabi_choice,**kwargs)

    def compute_AOD_wave(self):
        if len(self.qubit_index) == 2:
            # 在AOD上加相位以实现负的角度旋转
            phase_dict = {
                "rxx":(0,0),
                "ryy":(np.pi/2,np.pi/2),
                "-rxx":(np.pi,0),
                "-ryy":(3*np.pi/2,np.pi/2),
            }
            phase_list = phase_dict.get(self.gate_type,(0,0))
            # 1. 提取该离子对对应的 AOD Ratio
            ratio = self.parameters.Light_554.AOD_AWG_ratio[self.qubit_index[0]][self.qubit_index[1]]
            amp = [ratio / (1 + ratio), 1 / (1 + ratio)]
            # 2. 构造两离子的波形
            ion_i_wave = Waveform(duration=self.duration_time,
                                  amplitude=amp[0],
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]])
            ion_j_wave = Waveform(duration=self.duration_time,
                                  amplitude=amp[1],
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[1]])
            temp_wave = ion_i_wave + ion_j_wave

            ion_i_wave_R = Waveform(duration=self.duration_time,
                                  amplitude=amp[0],
                                  phase=phase_list[0],
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]])
            ion_j_wave_R = Waveform(duration=self.duration_time,
                                  amplitude=amp[1],
                                  phase=phase_list[1],
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[1]])
            temp_wave_R = ion_i_wave_R + ion_j_wave_R

            self.AOD_L = temp_wave
            self.AOD_R = temp_wave_R
        else:
            super().compute_AOD_wave()


class TwoRB(RamanBaseExp,EnvExperiment):
    """RB two qubit"""

    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(1, 101, 21),
                global_min=1,
                global_max=10000,
                global_step=1,
                precision=0,
            ),
            group="Experiment",
        )

        self.setattr_argument(
            "initial_state", StringValue("00"), tooltip="RB线路初态", group="Experiment"
        )
        print("Experiment Registered: single qubit RB")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={})
    def wave_compute(self,scan_parameter) -> TFloat:
        """传入参数计算波形"""
        qc = generate_rb1(int(scan_parameter))
        qc2 = get_rb2_from_rb1(qc)
        circuit = get_circuit_from_rb2(qc2)
        circuit = ["rxx","rx"]
        # 1. 定义操作
        inite_circuit = {
            "00":[
                [{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": 0}],
                [{"gate": "Idle", "qubit_index": self.qubit_index, "idle_time":0.5e-6}],
                [{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi / 2, "phase": 0}]
            ],
            "01":[
                [{"gate": "R", "qubit_index": (self.qubit_index[0],), "theta": np.pi/2, "phase": 0}],
                [{"gate": "Idle", "qubit_index": (self.qubit_index[0],), "idle_time": 0.5e-6}],
                [{"gate": "R", "qubit_index": (self.qubit_index[0],), "theta": np.pi / 2, "phase": 0}],
                [{"gate": "Idle", "qubit_index": self.qubit_index}],

            ],
            "10":[
                [{"gate": "R", "qubit_index": (self.qubit_index[1],), "theta": np.pi / 2, "phase": 0}],
                [{"gate": "Idle", "qubit_index": (self.qubit_index[1],), "idle_time": 0.5e-6}],
                [{"gate": "R", "qubit_index": (self.qubit_index[1],), "theta": np.pi / 2, "phase": 0}],
                [{"gate": "Idle", "qubit_index": self.qubit_index}],
                  ],

            "11":[]
        }

        circuit_list = inite_circuit.get(self.initial_state,[])


        gate_dict = {
            "rx":[{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": 0}],
            "ry":[{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": np.pi / 2}],
            "-rx":[{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": np.pi }],
            "-ry":[{"gate": "RR", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": -np.pi / 2}],
            "rxx":[{"gate": "MSOperationPlus", "qubit_index": self.qubit_index, "gate_type": "rxx"}],
            "ryy":[{"gate": "MSOperationPlus", "qubit_index": self.qubit_index, "gate_type": "ryy"}],
            "-rxx":[{"gate": "MSOperationPlus", "qubit_index": self.qubit_index, "gate_type": "-rxx"}],
            "-ryy":[{"gate": "MSOperationPlus", "qubit_index": self.qubit_index, "gate_type": "-ryy"}],
        }

        for gate in circuit:
            circuit_list.append([{"gate": "Idle",
                                  "qubit_index": self.qubit_index,
                                  "idle_time": 0.5e-6
                                  }])
            circuit_list.append(gate_dict[gate])
        # circuit_list  = [
        #     [{"gate": "MSOperation", "qubit_index": self.qubit_index,}]
        # ]
        operation = Circuit(circuit_list)
        # 2. 将波形导入 awg
        self.l554.prepare_waveform(operation)
        # 3. 传回操作时间
        return operation.duration()

