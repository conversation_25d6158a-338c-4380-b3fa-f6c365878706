import numpy as np
import dill
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit,Operation
from waveform_manager import Waveform
from modules.circuit_to_waveform import string2circuit_list
import copy
try:
    from cloud.circuit_generater import get_rb2_from_rb1,generate_rb1,get_circuit_from_rb2
except Exception as e:
    print(f"import failed:{e}")

from one_qubit_RB import R

MODULATOR_PATH = 'D:\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator.pkl'
class Rpi2(Operation):
    def __init__(self,qubit_index: tuple,rabi_time):
        super().__init__(qubit_index)
        self.rabi_time = rabi_time
        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def compute_AOM_wave(self, algorithm=None):
        t_pi2 = self.rabi_time
        aom_amp = 0.12
        Carrier_L = Waveform(
            duration=t_pi2,
            amplitude=aom_amp,
            phase=0,
            detuning=0,
        )
        # 3. 构造右侧波形
        # 根据边带选择决定右侧失谐

        Carrier_R = Waveform(
            duration= t_pi2,
            amplitude=aom_amp,
            phase=0,
            detuning=0,
        )
        # 4. 波形赋值到属性

        self.AOM_L = Carrier_L
        self.AOM_R = Carrier_R

        # 5. 计算持续时间
        self.duration_time = self.AOM_L.duration()
class RR(Operation):
    """
    R phi R phi
    """

    def __init__(self, qubit_index: tuple, theta: float, phase: float):
        super().__init__(qubit_index)
        self.theta = theta
        self.phi = phase
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        ac_stark_shift = self.parameters.Light_554.q1_AC_Stark_shift

        Carrier_L = Waveform(
            duration=self.parameters.Light_554.pi_2for2ions.Carrier  * (2 * self.theta / np.pi),
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=self.phi,
            detuning= ac_stark_shift,
        )
        Carrier_R = Waveform(
            duration=self.parameters.Light_554.pi_2for2ions.Carrier  * (2 * self.theta / np.pi),
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        self.AOM_L = Carrier_L
        self.AOM_R = Carrier_R
        self.duration_time = self.AOM_R.duration()

class MSOperationPlus(Operation):
    def __init__(
            self,
            qubit_index: tuple,
            rabi_time: float,
            modulator,
            gate_type= "rxx",
            aom_amp=-1.0,
            ms_phase = 0.0
    ):
        super().__init__(qubit_index)
        if aom_amp < 0:
            self.aom_amp = self.parameters.Light_554.AOM_AWG_amp_ms
        else:
            self.aom_amp = aom_amp
        self.rabi_time = rabi_time
        self.gate_type = gate_type
        self.modulator = modulator
        self.ms_phase = ms_phase
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def amp_to_aom_amp(self, amp):
        sin_coeff = 1.6753
        aom_amp = np.arcsin(np.sin(sin_coeff) * amp) / sin_coeff
        return aom_amp

    def compute_AOM_wave(self, algorithm=None):
        dt = self.modulator.t_modulation
        N_len = int(np.floor(self.rabi_time / dt))
        N = self.modulator.N_modulation
        t_remain = self.rabi_time - N_len * dt
        aom_amp = self.aom_amp
        ratio = self.parameters.Light_554.ratio_AOM
        ac_stark_shift = self.parameters.Light_554.MS_AC_Stark_shift

        detuning = self.modulator.mu / 2 / np.pi - 0
        # 1. 构造波形片段
        Idle = Waveform(
            duration=0,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        self.AOM_L = Idle
        self.AOM_R = Idle

        for i in range(N_len):
            i_index = i % N
            amp = self.amp_to_aom_amp(abs(aom_amp * self.modulator.rabi[i_index]))
            if self.modulator.rabi[i_index] < 0:
                phase = np.pi
            else:
                phase = 0
            if self.gate_type == "ryy":
                phase += np.pi / 2
            phase += self.ms_phase

            carrier = Waveform(
                duration=dt,
                amplitude=amp,
                phase=phase,
                detuning=ac_stark_shift,
            )
            red = Waveform(
                duration=dt,
                amplitude=ratio / (ratio + 1),
                phase=0,
                detuning=detuning
            )
            blue = Waveform(
                duration=dt,
                amplitude=1 / (ratio + 1),
                phase=0,
                detuning=-detuning
            )
            self.AOM_L = self.AOM_L * carrier
            self.AOM_R = self.AOM_R * (red + blue)
        i_index = N_len % N
        amp = self.amp_to_aom_amp(abs(aom_amp * self.modulator.rabi[i_index]))
        if self.modulator.rabi[i_index] < 0:
            phase = np.pi
        else:
            phase = 0
        if self.gate_type == "ryy":
            phase += np.pi / 2
        phase += self.ms_phase
        carrier_end = Waveform(
            duration=t_remain,
            amplitude=amp,
            phase=phase,
            detuning=ac_stark_shift,
        )
        red = Waveform(
            duration=t_remain,
            amplitude=ratio / (ratio + 1),
            phase=0,
            detuning=detuning
        )
        blue = Waveform(
            duration=t_remain,
            amplitude=1 / (ratio + 1),
            phase=0,
            detuning=-detuning
        )

        self.AOM_L = self.AOM_L * carrier_end
        self.AOM_R = self.AOM_R * (red + blue)
        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()



class TwoRB(RamanBaseExp,EnvExperiment):
    """RB two qubit AM"""

    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(1, 101, 21),
                global_min=1,
                global_max=10000,
                global_step=1,
                precision=0,
            ),
            group="Experiment",
        )

        self.setattr_argument(
            "initial_state", StringValue("00"), tooltip="RB线路初态", group="Experiment"
        )

        self.setattr_argument(
            "ms_phase",
            NumberValue(default=0.0, precision=6),
            tooltip="MS 门初始相位",
            group="Experiment",
        )

        self.setattr_argument(
            "shift_phase_1q",
            NumberValue(default=0.0, precision=6),
            tooltip="做MS后单比特门的坐标系旋转角度",
            group="Experiment",
        )
        self.setattr_argument(
            "shift_phase_MS",
            NumberValue(default=0.0, precision=6),
            tooltip="做MS后MS门的坐标系旋转角度",
            group="Experiment",
        )
        print("Experiment Registered: two qubit RB")

    def prepare(self):
        super().prepare()
        with open(MODULATOR_PATH,
                  'rb') as f:
            self.modulator = dill.load(f)


    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={})
    def wave_compute(self,scan_parameter) -> TFloat:
        """传入参数计算波形"""
        qc = generate_rb1(int(scan_parameter))
        qc2 = get_rb2_from_rb1(qc)
        circuit = get_circuit_from_rb2(qc2)
        print(circuit)

        parameter= self.parameter
        qubit_index = self.qubit_index
        initial_state = self.initial_state
        modulator = self.modulator
        ms_phase = self.ms_phase
        rabi_time0 = parameter.Light_554.pi_2.Carrier[qubit_index[0]]
        rabi_time1 = parameter.Light_554.pi_2.Carrier[qubit_index[1]]
        rabi_time = parameter.Light_554.pi_2for2ions.Carrier

        idle_AOD = parameter.Light_554.AOD_time_before_AOM
        idle_time = 0.5e-6

        # 1. 定义操作
        inite_circuit = {
            "11": [
                [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": 0}],
                [{"gate": "Idle", "qubit_index": qubit_index, "idle_time": idle_time}],
                [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": 0}]
            ],
            "10": [
                [{"gate": "Rpi2", "qubit_index": (qubit_index[0],), "rabi_time": rabi_time0}],
                [{"gate": "Idle", "qubit_index": (qubit_index[0],), "idle_time": idle_time}],
                [{"gate": "Rpi2", "qubit_index": (qubit_index[0],), "rabi_time": rabi_time0}],
            ],
            "01": [
                [{"gate": "Rpi2", "qubit_index": (qubit_index[1],), "rabi_time": rabi_time1}],
                [{"gate": "Idle", "qubit_index": (qubit_index[1],), "idle_time": idle_time}],
                [{"gate": "Rpi2", "qubit_index": (qubit_index[1],), "rabi_time": rabi_time1}],
            ],

            "00": []
        }
        circuit_list = inite_circuit.get(initial_state, [])
        circuit_list.append([{"gate": "Idle",
                              "qubit_index": qubit_index,
                              "idle_time": idle_time
                              }])

        inite_time = {
            "00": idle_AOD + idle_time,
            "01": idle_AOD * 2 + idle_time * 2 + rabi_time0 * 2,
            "10": idle_AOD * 2 + idle_time * 2 + rabi_time0 * 2,
            "11": idle_AOD + idle_time * 2 + rabi_time * 2
        }

        shift_ms = parameter.Light_554.MS_AC_Stark_shift * 2 * np.pi
        shift_q1 = parameter.Light_554.q1_AC_Stark_shift * 2 * np.pi

        phase0_MS = (0 - shift_ms) * inite_time.get(initial_state, 0)
        phase0_1q = (0 - shift_q1) * inite_time.get(initial_state, 0)

        ms_time = parameter.Light_554.MS_time

        gate_dict = {
            "rx": [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": 0 + phase0_1q}],
            "ry": [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": -np.pi / 2 + phase0_1q}],
            "-rx": [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": np.pi + phase0_1q}],
            "-ry": [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": np.pi / 2 + phase0_1q}],
            "rxx": [{"gate": "MSOperationPlus",
                     "qubit_index": qubit_index,
                     "rabi_time": ms_time,
                     "modulator": modulator,
                     "gate_type": "rxx",
                     "ms_phase": ms_phase + phase0_MS
                     }],
            "ryy": [{"gate": "MSOperationPlus",
                     "qubit_index": qubit_index,
                     "rabi_time": ms_time,
                     "modulator": modulator,
                     "gate_type": "ryy",
                     "ms_phase": ms_phase + phase0_MS
                     }],
        }

        for gate in circuit:
            if gate == "Idle":
                continue

            circuit_list.append(copy.deepcopy(gate_dict[gate]))

            circuit_list.append([{"gate": "Idle",
                                  "qubit_index": qubit_index,
                                  "idle_time": idle_time
                                  }])


            if gate in ["rxx", "ryy"]:  # 修正因做两比特门产生的相位,具体相位由实验校正
                circuit_list.append(
                    copy.deepcopy((gate_dict["ry"]))
                )
                circuit_list.append([{"gate": "Idle",
                                      "qubit_index": qubit_index,
                                      "idle_time": idle_time
                                      }])
                circuit_list.append(
                    copy.deepcopy((gate_dict["ry"]))
                )
                circuit_list.append([{"gate": "Idle",
                                      "qubit_index": qubit_index,
                                      "idle_time": idle_time
                                      }])
                circuit_list.append(copy.deepcopy(gate_dict[gate]))
                circuit_list.append([{"gate": "Idle",
                                      "qubit_index": qubit_index,
                                      "idle_time": idle_time
                                      }])
                circuit_list.append(
                    copy.deepcopy((gate_dict["-ry"]))
                )
                circuit_list.append([{"gate": "Idle",
                                      "qubit_index": qubit_index,
                                      "idle_time": idle_time
                                      }])
                circuit_list.append(
                    copy.deepcopy((gate_dict["-ry"]))
                )
                circuit_list.append([{"gate": "Idle",
                                      "qubit_index": qubit_index,
                                      "idle_time": idle_time
                                      }])


                for single_gate in ["rx", "-rx", "ry", "-ry"]:
                    gate_dict[single_gate][0]["phase"] = gate_dict[single_gate][0]["phase"] + ( 0 - shift_q1) * (
                            ms_time * 2 +rabi_time * 4 + idle_time * 6)
                for twoqubit_gate in ["rxx", "ryy"]:
                    gate_dict[twoqubit_gate][0]["ms_phase"] = gate_dict[twoqubit_gate][0]["ms_phase"] + 0

            if gate in ["rx", "-rx", "ry", "-ry"]:
                for single_gate in ["rx", "-rx", "ry", "-ry"]:  # 单比特无相位偏移
                    gate_dict[single_gate][0]["phase"] = gate_dict[single_gate][0]["phase"]
                for twoqubit_gate in ["rxx", "ryy"]:  # 两比特相位偏移为 (shift_q1-shift_ms) * 单比特门时间
                    gate_dict[twoqubit_gate][0]["ms_phase"] = gate_dict[twoqubit_gate][0]["ms_phase"] + (
                            shift_q1 - shift_ms) * (rabi_time + idle_time)
        operation = Circuit(circuit_list)
        # 2. 将波形导入 awg
        self.l554.prepare_waveform(operation)
        # 3. 传回操作时间
        print(operation.duration())
        return operation.duration()

