"""
randomized benchmarking 实验，单离子
"""

import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit,Operation
from waveform_manager import Waveform


try:
    from cloud.circuit_generater import CircuitGenerator
except Exception as e:
    print(e)


class R(Operation):
    """
    R phi
    """

    def __init__(self, qubit_index: tuple, theta: float, phase: float):
        super().__init__(qubit_index)
        self.theta = theta
        self.phi = phase
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        if len(self.qubit_index)== 1:
            pi2_time = self.parameters.Light_554.pi_2.Carrier[self.qubit_index[0]]
        else:
            pi2_time = self.parameters.Light_554.pi_2for2ions.Carrier

        Carrier_L = Waveform(
            duration= pi2_time * (2 * self.theta / np.pi),
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        Carrier_R = Waveform(
            duration= pi2_time * (2 * self.theta / np.pi),
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=self.phi,
            detuning=0,
        )
        self.AOM_L = Carrier_L
        self.AOM_R = Carrier_R
        self.duration_time = self.AOM_R.duration()



class OneRB(RamanBaseExp,EnvExperiment):
    """RB one qubit"""

    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(1, 101, 21),
                global_min=1,
                global_max=10000,
                global_step=1,
                precision=0,
            ),
            group="Experiment",
        )
        print("Experiment Registered: single qubit RB")

    def prepare(self):
        super().prepare()
        self.generator = CircuitGenerator(10)

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={})
    def wave_compute(self,scan_parameter) -> TFloat:
        """传入参数计算波形"""
        self.generator.set_length(int(scan_parameter))
        # 1. 定义操作
        circuit = self.generator.random_generate_1qubit()
        # circuit = ['ry', ""]

        # print(circuit)
        circuit_list = []
        circuit_list.append([{"gate": "R", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": 0}])

        circuit_list.append([{"gate": "Idle",
                              "qubit_index": self.qubit_index,
                              "idle_time": 0.5e-6
                              }])

        circuit_list.append([{"gate": "R", "qubit_index": self.qubit_index, "theta": np.pi/2, "phase": 0}])

        phase_dict = {
            "rx":0,
            "ry":np.pi / 2,
            "-rx":np.pi ,
            "-ry":-np.pi / 2
            }

        for gate in circuit:
            circuit_list.append([{"gate": "Idle",
                                  "qubit_index": self.qubit_index,
                                  "idle_time": 0.5e-6
                                  }])
            circuit_list.append([{"gate": "R", "qubit_index": self.qubit_index,
                                  "theta": np.pi / 2, "phase": phase_dict[gate]}])
        # circuit_list = [[{"gate":"Idle","qubit_index":self.qubit_index}],[{"gate":"R","qubit_index":self.qubit_index,"theta":np.pi/2,"phase":0}]]
        operation = Circuit(circuit_list)
        # 2. 将波形导入 awg
        self.l554.prepare_waveform(operation)
        # 3. 传回操作时间
        return operation.duration()

    
