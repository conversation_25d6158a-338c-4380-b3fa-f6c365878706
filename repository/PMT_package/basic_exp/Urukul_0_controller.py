from artiq.experiment import *  # 从artiq实验库导入所有内容


# 该代码在urukul的一个通道上输出单一频率，幅度固定
# 以下内容需要从仪表板上输入：
# 频率(单位MHz)
# 幅度(作为幅度缩放因子，所以在0和1之间)
# 衰减(单位db，介于0和31.5之间)
# 脉冲长度(单位s)

class Urukul0_Programmable(EnvExperiment):
    """Urukul_0 DDS 控制"""

    def build(self):  # 这段代码在宿主设备上运行

        self.setattr_device("core")  # 将核心设备驱动设置为属性
        self.setattr_device("dds_for_cooling_2")  # 将urukul0的通道1设备驱动设置为属性
        self.setattr_device("dds_for_CPD")  # 将urukul0的通道1设备驱动设置为属性
        self.setattr_device("dds_for_EIT_sigma")  # 将urukul0的通道1设备驱动设置为属性
        self.setattr_device("dds_for_EIT_pi")  # 将urukul0的通道1设备驱动设置为属性

        # 所有可调参数都可以单提出来调整
        self.setattr_argument("freq_0", NumberValue(precision=0, unit="MHz", step=1), group="Cooling_1")  # 指导仪表板以MHz的形式输入并将其设置为名为freq的属性
        self.setattr_argument("amp_0", NumberValue(precision=2, step=1), group="Cooling_1")  # 指导仪表板输入并将其设置为名为amp的属性
        self.setattr_argument("atten_0", NumberValue(default=25, precision=2, step=1, min=5), group="Cooling_1")  # 指导仪表板输入并将其设置为名为atten的属性
        # self.setattr_argument("t_pulse_0", NumberValue(precision=2, unit="s", step=1))  # 指导仪表板输入并将其设置为名为t_pulse的属性

        self.setattr_argument("freq_1", NumberValue(precision=0, unit="MHz", step=1), group="Cooling_2")  # 指导仪表板以MHz的形式输入并将其设置为名为freq的属性
        self.setattr_argument("amp_1", NumberValue(precision=2, step=1), group="Cooling_2")  # 指导仪表板输入并将其设置为名为amp的属性
        self.setattr_argument("atten_1", NumberValue(default=25,precision=2, step=1, min=5), group="Cooling_2")  # 指导仪表板输入并将其设置为名为atten的属性
        # self.setattr_argument("t_pulse_1", NumberValue(precision=2, unit="s", step=1))  # 指导仪表板输入并将其设置为名为t_pulse的属性

        self.setattr_argument("freq_2", NumberValue(precision=0, unit="MHz", step=1), group="EIT_sigma")  # 指导仪表板以MHz的形式输入并将其设置为名为freq的属性
        self.setattr_argument("amp_2", NumberValue(precision=2, step=1), group="EIT_sigma")  # 指导仪表板输入并将其设置为名为amp的属性
        self.setattr_argument("atten_2", NumberValue(default=25, precision=2, step=1, min=5), group="EIT_sigma")  # 指导仪表板输入并将其设置为名为atten的属性
        # self.setattr_argument("t_pulse_2", NumberValue(precision=2, unit="s", step=1))  # 指导仪表板输入并将其设置为名为t_pulse的属性

        self.setattr_argument("freq_3", NumberValue(precision=0, unit="MHz", step=1), group="EIT_pi")  # 指导仪表板以MHz的形式输入并将其设置为名为freq的属性
        self.setattr_argument("amp_3", NumberValue(precision=2, step=1), group="EIT_pi")  # 指导仪表板输入并将其设置为名为amp的属性
        self.setattr_argument("atten_3", NumberValue(default=25, precision=2, step=1, min=5), group="EIT_pi")  # 指导仪表板输入并将其设置为名为atten的属性
        self.setattr_argument("t_pulse", NumberValue(precision=2, unit="s", step=1))  # 指导仪表板输入并将其设置为名为t_pulse的属性
        print("Experiment Registered: Urukul 0 Controller")

    @rpc(flags={"async"})
    def tprint(self, data):
        print(data)

    def prepare(self):
        print("prepare")

    @kernel  # 这段代码在FPGA上运行
    def run(self):
        self.core.reset()  # 重置核心设备
        self.dds_for_cooling_2.cpld.init()  # 初始化通道1上的CPLD

        delay(10 * ms)  # 延迟10毫秒

        self.dds_for_cooling_2.init()
        self.dds_for_CPD.init()
        self.dds_for_EIT_sigma.init()
        self.dds_for_EIT_pi.init()

        delay(10 * ms)  # 延迟10毫秒
        self.dds_for_CPD.set_att(self.atten_0)  # 将衰减写入urukul通道
        self.dds_for_CPD.set(self.freq_0, amplitude=self.amp_0)  # 将频率和幅度属性写入urukul通道，从而输出函数

        self.dds_for_cooling_2.set_att(self.atten_1)  # 将衰减写入urukul通道
        self.dds_for_cooling_2.set(self.freq_1, amplitude=self.amp_1)  # 将频率和幅度属性写入urukul通道，从而输出函数

        delay(100 * us)  # 延迟10毫秒
        self.dds_for_EIT_sigma.set_att(self.atten_2)  # 将衰减写入urukul通道
        self.dds_for_EIT_sigma.set(self.freq_2, amplitude=self.amp_2)  # 将频率和幅度属性写入urukul通道，从而输出函数

        self.dds_for_EIT_pi.set_att(self.atten_3)  # 将衰减写入urukul通道
        self.dds_for_EIT_pi.set(self.freq_3, amplitude=self.amp_3)  # 将频率和幅度属性写入urukul通道，从而输出函数


        delay(100 * us)  # 延迟10毫秒
        self.dds_for_cooling_2.cfg_sw(True)  # 打开urukul通道
        self.dds_for_CPD.cfg_sw(True)  # 打开urukul通道
        self.dds_for_EIT_sigma.cfg_sw(True)  # 打开urukul通道
        self.dds_for_EIT_pi.cfg_sw(True)  # 打开urukul通道
        # delay(self.t_pulse_0 * s)  # 延迟由用户输入决定
        # self.urukul0_ch0.cfg_sw(False)  # 打开urukul通道
        # self.urukul0_ch1.cfg_sw(False)  # 打开urukul通道
        # self.urukul0_ch2.cfg_sw(False)  # 打开urukul通道
        # self.urukul0_ch3.cfg_sw(False)  # 打开urukul通道

