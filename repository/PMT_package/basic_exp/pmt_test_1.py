from artiq.experiment import *


class PMTTest1(EnvExperiment):
    """PMT_TEST_1

    用途:
    1. 给 PMT 提供方波触发信号, 用于测试 pmt_controller.py 中的测试脚本;
    2. 输出方波的代码示例.
    """

    def build(self):
        self.setattr_device("core")
        self.setattr_device("PMT_window")
        self.setattr_argument(
            "period",
            NumberValue(default=1e-3, precision=6, unit="s", step=1e-3),
            tooltip="采样信号周期",
        )
        print("Experiment Registered: PMT Test 1")

    def prepare(self):
        print("Prepare Done")

    @kernel()
    def run(self):
        # 0. initial
        self.core.reset()
        self.PMT_window.output()
        self.PMT_window.off()

        # 1. 给方波信号
        while True:
            self.PMT_window.on()
            delay(self.period / 2)
            self.PMT_window.off()
            delay(self.period / 2)

    def analyze(self):
        print("Analysis Done")
