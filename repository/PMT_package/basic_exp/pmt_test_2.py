import numpy as np
from artiq.experiment import *
from modules.pmt import PMT
from modules.config import LOADED_PARAMETER


class PMTTest2(EnvExperiment):
    """PMT TEST 2

    用途:
    1. 测试 PMT 在某个固定增益电压和与之电压下的行为;
    2. 测试 pmt.py 中的数据集, 绘图组件, 数据处理是否正常工作;
    3. 测试 config.py 和 LOADED_PARAMETER 是否正常工作;
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time

        # 2. 仪器连接
        self.setattr_device("core")
        self.pmt = PMT(self)
        print("Experiment Registered: PMT Test 2 ")

    def prepare(self):
        self.pmt.prepare_for_idle()
        print("Prepare Done")

    @kernel()
    def run(self):
        # 0. initial
        self.core.reset()
        self.pmt.initial()

        # 1. 给方波信号
        while True:
            for repeat in range(self.repeat):
                # repeat 此读数循环, 总积分时间是 cooling_time * repeat
                self.pmt.pmt_start_count()
                delay(self.cooling_time)
                self.pmt.pmt_stop_count()
                delay(self.cooling_time)

            self.core.wait_until_mu(now_mu())
            delay(100e-6)
            self.pmt.process_for_idle()

    def analyze(self):
        print("Analysis Done")
