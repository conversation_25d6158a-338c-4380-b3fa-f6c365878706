from modules.raman_base_exp import RamanBaseExp
from artiq.experiment import *
from modules.operation_manager import Raman_Rabi

class RamanEITOnTest(RamanBaseExp, EnvExperiment):
    """ Raman and EIT On Test
    cooling 之后把 AOM 和 AOD 的光打开
    """

    def build(self):
        # 0. base build
        super().build()
        # 1. 参数设置组件
        self.setattr_argument(
            "Raman_on", BooleanValue(default=False),
            tooltip="是否开启Raman光",
            group="Experiment")
        self.setattr_argument(
            "EIT_sigma_on", BooleanValue(default=False),
            tooltip="是否开启EIT sigma光",
            group="Experiment")
        self.setattr_argument(
            "EIT_pi_on", BooleanValue(default=False),
            tooltip="是否开启EIT pi光",
            group="Experiment")
        print("Experiment Registered: Raman EIT on")

    def prepare(self):
        """prepare"""
        self.qubit_index = eval(self.qubit_index)
    def prepare_dataset(self):
        self.pmt.prepare_for_cooling_idle()
    @rpc(flags={"async"})
    def wave_compute(self, scan_parameter):
        """传入参数计算波形"""
        # 1. 定义操作
        operation = Raman_Rabi(qubit_index=self.qubit_index,rabi_time=scan_parameter)
        # 2. 将波形导入 awg
        self.l554.prepare_waveform_without_load(operation)


    @kernel()
    def run(self):
        self.prepare_dataset()
        # 0. 初始化
        self.core.reset()

        self.pmt.initial()
        if self.Raman_on:
            self.l554.initial()
            self.core.wait_until_mu(now_mu())
            self.wave_compute(scan_parameter=999.99e-6)
            self.l554.load_wave_to_awg(loops=0, replay_mode="autorestart")  # 波形导入 AWG
            self.l554.start_awg()
        self.core.break_realtime()
        self.l369.initial()
        if self.Raman_on:
            self.l554.AWG_on()
        if self.EIT_pi_on:
            self.l369.dds_for_EIT_pi.cfg_sw(True)
        if self.EIT_sigma_on:
            self.l369.dds_for_EIT_sigma.cfg_sw(True)
        while True:
            # 1. off -> cooling
            self.core.break_realtime()
            for repeat in range(self.repeat):
                delay(100e-6)
                self.pmt.pmt_start_count()
                delay(self.cooling_time)
                self.pmt.pmt_stop_count()
                delay(2e-3)  #等待PMT读数，2ms为PMT数据读取时间
            if self.scheduler.check_termination():
                break
            self.core.wait_until_mu(now_mu())
            delay(1e-3)  #
            self.pmt.data_process_for_cooling_idle()
        # request termination后关闭EIT光和AWG
        self.l554.initial()
        self.l369.dds_for_EIT_pi.cfg_sw(False)
        self.l369.dds_for_EIT_sigma.cfg_sw(False)
