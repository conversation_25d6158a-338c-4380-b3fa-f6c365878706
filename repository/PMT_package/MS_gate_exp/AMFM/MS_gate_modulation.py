"""

MS gate with modulation
"""
import numpy as np
from modules.raman_base_exp import RamanBaseExp

from modules.operation_manager import Circuit, Operation
from artiq.experiment import *
from waveform_manager import Waveform
from cloud.pulsedesign.pulse_designer import PulseDesigner

class MS_Operation(Operation):
    def __init__(self, qubit_index: tuple, operation_time: float ,exp):
        super().__init__(qubit_index)
        self.designer = PulseDesigner(np.array(self.parameters.Light_554.Motion_freq)*2*np.pi)
        self.exp = exp
        self.operation_time = operation_time
        self.compute_pulse()

        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def compute_pulse(self):

        self.dt_list = self.exp.dt_list
        self.t0_list = self.exp.t0_list
        self.detuning_list = self.exp.detuning_list
        self.amp_t = self.exp.amp_t


    def compute_AOM_wave(self, algorithm=None):

        ac_stark_shift = self.parameters.Light_554.MS_AC_Stark_shift
        idle_time = self.parameters.Light_554.AOD_time_before_AOM
        # 1. 构造波形片段
        import bisect
        index = bisect.bisect_right(self.t0_list, self.operation_time) - 1
        # if index > len(self.t0_list) - 2:
        #     index -= 1
        print(index)
        for i,amp in enumerate(self.amp_t):
            amp = amp.real
            detuning = self.detuning_list[i]
            t0 = self.t0_list[i]+idle_time
            dt = self.dt_list[i]
            if i == index:
                dt = self.operation_time+idle_time-t0
            phase = 2*np.pi*detuning*t0 +i*np.pi -np.pi/2
            # phase = 0
            # phase = i *np.pi
            if i == 0:
                X_R = Waveform(
                    duration=dt,
                    amplitude=amp,
                    phase=-phase,
                    detuning=detuning
                ) + Waveform(
                    duration=dt,
                    amplitude=amp,
                    phase= phase,
                    detuning= -detuning
                )
            elif i <= index:
                X_R = X_R* (
                        Waveform(
                            duration=dt,
                            amplitude=amp,
                            phase=-phase,
                            detuning=detuning
                        ) + Waveform(
                    duration=dt,
                    amplitude=amp,
                    phase=phase,
                    detuning=-detuning
                )
                )


        # 2. 波形赋值到属性

        self.AOM_R = X_R

        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()
        X_L = Waveform(
            duration=self.duration_time,
            amplitude=self.parameters.Light_554.AOM_AWG_amp_ms,
            phase=0,
            detuning= ac_stark_shift,
        )
        self.AOM_L = X_L


class MS(RamanBaseExp,EnvExperiment):
    """MS gate with modulation
    """

    def build(self):
        super().build()
        # 3. 参数设置组件
        self.setattr_argument("X_scan_range", Scannable(
            default=RangeScan(0, 10e-6, 20), global_min=0, global_max=5000e-6, global_step=1e-6, precision=2,
            unit="us"), group="Experiment")

        self.setattr_argument("N_basis", NumberValue(default=1, min=1, step=1, precision=0),
                              tooltip="扫描任务的重复次数", group="Experiment")
        self.setattr_argument("gate_time", NumberValue(default=300e-6, min=0, step=1e-6, precision=9,unit="us"),
                              tooltip="门时间", group="Experiment")

        print("Experiment Registered: MS gate modulation")

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)
    def prepare(self):
        """prepare"""
        # 1. 获得参数
        super().prepare()

        # 2.计算波形
        self.designer = PulseDesigner(np.array(self.parameter.Light_554.Motion_freq) * 2 * np.pi)
        self.designer.Get_Matrix_M_N(self.N_basis, self.gate_time)
        self.designer.Get_D_mn_N(self.qubit_index, self.N_basis, self.gate_time)
        self.designer.solve_ADAb()
        self.designer.demodulation(self.gate_time,self.scan_parameter[-1])

        rabi_list = abs(self.designer.demodu_result.amp_point)
        self.dt_list = self.designer.demodu_result.zero_steps
        self.t0_list = self.designer.demodu_result.zero_point
        self.detuning_list = self.designer.demodu_result.fre_point
        self.rabi_t = rabi_list / max(rabi_list) * np.sin(1.37156 * 1 / 2)
        self.amp_t = np.arcsin(self.rabi_t) / 1.37156

        print("prepare_done")

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        MS = [{"gate": "MS_Operation",
                 "qubit_index": self.qubit_index,
                 "operation_time": scan_parameter,
                 "exp": self}]
        if self.enable_SBC:
            operation_list = [SBC, MS]
        else:
            operation_list = [MS]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()