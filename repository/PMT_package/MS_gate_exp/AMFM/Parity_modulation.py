"""

Parity 测量
"""

import numpy as np
from modules.raman_base_exp import RamanBaseExp

from modules.operation_manager import Circuit
from artiq.experiment import *

try:
    from cloud.pulsedesign.pulse_designer import PulseDesigner
except:
    class PulseDesigner():
        pass


class ParityTest(RamanBaseExp,EnvExperiment):
    """Parity Modulation
    """

    def build(self):
        # 1. 参数拉取
        super().build()
        # 3. 参数设置组件
        self.setattr_argument("X_scan_range", Scannable(
            default=RangeScan(0, np.pi*2, 21), global_min=0, global_max=360, global_step=1, precision=6,
        ), group="Experiment")
        self.setattr_argument("N_basis", NumberValue(default=1, min=1, step=1, precision=0),
                              tooltip="基个数", group="Experiment")
        self.setattr_argument("total_time", NumberValue(default=300e-6, min=0, step=1e-6, precision=9, unit="us"),
                              tooltip="总时间", group="Experiment")
        self.setattr_argument("gate_time", NumberValue(default=300e-6, min=0, step=1e-6, precision=9, unit="us"),
                              tooltip="门时间", group="Experiment")
        print("Experiment Registered: Parity Modulation")

    def prepare(self):
        """prepare"""
        # 1. 获得参数
        super().prepare()
        # 4.计算波形

        self.designer = PulseDesigner(np.array(self.parameter.Light_554.Motion_freq) * 2 * np.pi)
        self.designer.Get_Matrix_M_N(self.N_basis, self.gate_time)
        self.designer.Get_D_mn_N(self.qubit_index, self.N_basis, self.gate_time)
        self.designer.solve_ADAb()
        self.designer.demodulation(self.gate_time,self.total_time)

        rabi_list = abs(self.designer.demodu_result.amp_point)
        self.dt_list = self.designer.demodu_result.zero_steps
        self.t0_list = self.designer.demodu_result.zero_point
        self.detuning_list = self.designer.demodu_result.fre_point
        self.rabi_t = rabi_list / max(rabi_list) * np.sin(1.37156 * 1 / 2)
        self.amp_t = np.arcsin(self.rabi_t) / 1.37156
        print("prepare_done")

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        pi2_time = self.parameter.Light_554.pi_2for2ions.Carrier
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        MS = [{"gate": "MS_Operation",
               "qubit_index": self.qubit_index,
               "operation_time": self.total_time,
               "exp": self}]
        Pi2 = [{
            "gate":"Raman_Rabi",
            "qubit_index":self.qubit_index,
            "rabi_time": pi2_time,
            "phase":scan_parameter
        }]
        idle = [{"gate":"Idle","qubit_index":self.qubit_index,"idle_time":1e-6}]
        if self.enable_SBC:
            operation_list = [SBC, MS,idle,Pi2]
        else:
            operation_list = [MS,idle,Pi2]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()