import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit

class MSScanAOMAmp(RamanBaseExp, EnvExperiment):
    """MS Scan AOM AMP"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 1, 21),
                global_min=0,
                global_max=1,
                global_step=0.1,
                precision=10
            ),
            group="Experiment",
        )
        print("Experiment Registered: MS Scan AOM AMP")


    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )


    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        MS = [{"gate": "MSOperation",
                 "qubit_index": self.qubit_index,
                 "aom_amp":scan_parameter}]
        if self.enable_SBC:
            operation_list = [SBC, MS]
        else:
            operation_list = [MS]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()

if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()