import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import  Circuit

class RamseyMS(RamanBaseExp, EnvExperiment):
    """Ramsey MS """
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 1000e-6, 21),
                global_min=0,
                global_max=100000e-6,
                global_step=1e-6,
                precision=10,
                unit="us",
            ),
            group="Experiment",
        )
        print("Experiment Registered: Ramsey MS ")



    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)



    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        MS = [{"gate": "MSOperation",
                 "qubit_index": self.qubit_index,
                 "operation_time": scan_parameter,
                 "detuning":-0.3e6
               }]
        pi2_time = self.parameter.Light_554.pi_2.Carrier[self.qubit_index[0]]
        Raman_Pi2 = [{"gate": "Raman_Rabi",
                      "qubit_index": self.qubit_index,
                      "rabi_time": pi2_time
                      }]
        Idle_Ramsey = [
            {
                "gate": "Idle",
                "qubit_index": self.qubit_index,
                "idle_time": 10e-6
            }
        ]
        operation_list = [Raman_Pi2,Idle_Ramsey,MS,Idle_Ramsey,Raman_Pi2]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()


if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()