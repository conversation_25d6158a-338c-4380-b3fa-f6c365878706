"""

rabi_choice选择carrier为MS扫时间 ，选择red/blue为两侧AOM同时打红/蓝边带
"""
import  numpy as np
from modules.operation_manager import Circuit, MSOperation,Operation
from modules.raman_base_exp import RamanBaseExp
from artiq.experiment import *
from waveform_manager import Waveform
try:
    from cloud.pulsedesign.AMPM import AMPMModulator
except:
    class AMPMModulator():
        pass
class MS_AMPM(Operation):
    def __init__(
        self,
        qubit_index: tuple,
        rabi_time: float,
        modulator:AMPMModulator,
        aom_amp = -1.0
    ):
        super().__init__(qubit_index)
        if aom_amp < 0:
            self.aom_amp = self.parameters.Light_554.AOM_AWG_amp_ms
        else:
            self.aom_amp = aom_amp
        self.rabi_time = rabi_time
        self.modulator = modulator
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        dt = self.modulator.t_modulation
        N_len = int(np.floor(self.rabi_time/dt))
        N = self.modulator.N_modulation
        t_remain = self.rabi_time- N_len * dt
        rabi = self.aom_amp*self.modulator.rabi* np.sin(1.37156)
        amp = np.arcsin(rabi) / 1.37156
        ratio = self.parameters.Light_554.ratio_AOM

        detuning  = self.modulator.mu / 2 / np.pi * 1e6
        # 1. 构造波形片段
        Idle = Waveform(
            duration= 0 ,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        self.AOM_L = Idle
        self.AOM_R = Idle



        for i in range(N_len):
            i_index = i % N

            carrier = Waveform(
                duration=dt,
                amplitude=amp[i_index],
                phase=0,
                detuning=0,
            )

            red = Waveform(
                duration=dt,
                amplitude=ratio/(ratio + 1),
                phase=self.modulator.phi[i_index],
                detuning=detuning
            )
            blue = Waveform(
                duration=dt,
                amplitude=1/(ratio + 1),
                phase = -self.modulator.phi[i_index],
                detuning = -detuning
            )
            self.AOM_L = self.AOM_L * carrier
            self.AOM_R = self.AOM_R * (red + blue)
        i_index = N_len % N
        carrier_end = Waveform(
            duration=t_remain,
            amplitude=amp[i_index],
            phase=0,
            detuning=0,
        )
        red = Waveform(
            duration=t_remain,
            amplitude=ratio/(ratio + 1),
            phase=self.modulator.phi[i_index],
            detuning=detuning
        )
        blue = Waveform(
            duration=t_remain,
            amplitude=1/(ratio + 1),
            phase=-self.modulator.phi[i_index],
            detuning=-detuning
        )

        self.AOM_L = self.AOM_L * carrier_end
        self.AOM_R = self.AOM_R * (red + blue)
        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()

class MSPM(RamanBaseExp,EnvExperiment):
    """MS gate phase modulation
    """

    def build(self):
        ## 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=10000e-6,
                global_step=1e-6,
                precision=10,
                unit="us",
            ),
            group="Experiment",
        )
        self.setattr_argument("N_modulation", NumberValue(default=1, min=1, step=1, precision=0),
                              tooltip="调制分段数", group="Experiment")


        print("Experiment Registered: MS gate PM")

    def prepare(self):
        """prepare"""
        super().prepare()
        gate_frequency  = self.parameter.Light_554.Motion_freq[2] + self.parameter.Light_554.freq_detuning
        gate_time = self.parameter.Light_554.MS_time
        self.modulator  = AMPMModulator(self.qubit_index,
                                         gate_time,
                                         len(self.parameter.Light_554.Motion_freq),
                                         self.N_modulation,
                                         self.parameter.Light_554.Motion_freq,
                                         gate_frequency)

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter)/1e-6 )

    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        MS = [{"gate": "MS_AMPM",
               "qubit_index": self.qubit_index,
               "rabi_time": scan_parameter,
               "modulator": self.modulator}]
        if self.enable_SBC:
            operation_list = [SBC, MS]
        else:
            operation_list = [MS]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()
