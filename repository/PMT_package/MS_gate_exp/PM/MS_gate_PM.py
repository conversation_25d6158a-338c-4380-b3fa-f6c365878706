"""

rabi_choice选择carrier为MS扫时间 ，选择red/blue为两侧AOM同时打红/蓝边带
"""
import  numpy as np
from modules.operation_manager import Circuit, MSOperation,Operation
from modules.raman_base_exp import RamanBaseExp
from artiq.experiment import *
from waveform_manager import Waveform
import pickle
import sys

try:
    from cloud.pulsedesign.PM_analytic import PulseDesigner
    # 强制让 Pickle 认为 PulseDesigner 来自旧路径
    sys.modules['__main__'] = sys.modules['cloud.pulsedesign.PM_analytic']
except:
    class PulseDesigner:
        pass


class MS_phase_modu(Operation):
    def __init__(
        self,
        qubit_index: tuple,
        rabi_time: float,
        modulator:PulseDesigner,
        aom_amp = -1.0
    ):
        super().__init__(qubit_index)
        if aom_amp < 0:
            self.aom_amp = self.parameters.Light_554.AOM_AWG_amp_ms
        else:
            self.aom_amp = aom_amp
        self.rabi_time = rabi_time
        self.modulator = modulator
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        dt = self.modulator.t_modulation
        N_len = int(np.floor(self.rabi_time/dt))
        N = self.modulator.N_modulation
        t_remain = self.rabi_time- N_len * dt
        aom_amp = self.aom_amp
        ratio = self.parameters.Light_554.ratio_AOM
        ac_stark_shift = self.parameters.Light_554.MS_AC_Stark_shift

        detuning  = self.modulator.mu / 2 / np.pi -0
        # 1. 构造波形片段
        Idle = Waveform(
            duration= 0 ,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        self.AOM_L = Idle
        self.AOM_R = Idle

        t_delay = 0.36e-6
        carrier = Waveform(
            duration=dt-t_delay,
            amplitude=aom_amp,
            phase=0,
            detuning=ac_stark_shift,
        )

        red = Waveform(
            duration=dt-t_delay,
            amplitude=ratio/(ratio + 1),
            phase=self.modulator.phi[0],
            detuning=detuning
        )
        blue = Waveform(
            duration=dt-t_delay,
            amplitude=1/(ratio + 1),
            phase = -self.modulator.phi[0],
            detuning = -detuning
        )
        self.AOM_L = self.AOM_L * carrier
        self.AOM_R = self.AOM_R * (red + blue)

        carrier = Waveform(
            duration=dt,
            amplitude=aom_amp,
            phase=0,
            detuning=ac_stark_shift,
        )
        for i in range(N_len):
            i_index = i % N
            red = Waveform(
                duration=dt,
                amplitude=ratio/(ratio + 1),
                phase=self.modulator.phi[i_index],
                detuning=detuning
            )
            blue = Waveform(
                duration=dt,
                amplitude=1/(ratio + 1),
                phase = -self.modulator.phi[i_index],
                detuning = -detuning
            )
            self.AOM_L = self.AOM_L * carrier
            self.AOM_R = self.AOM_R * (red + blue)
        i_index = N_len % N
        carrier_end = Waveform(
            duration=t_remain + t_delay,
            amplitude=aom_amp,
            phase=0,
            detuning=ac_stark_shift,
        )
        red = Waveform(
            duration=t_remain + t_delay,
            amplitude=ratio/(ratio + 1),
            phase=self.modulator.phi[i_index],
            detuning=detuning
        )
        blue = Waveform(
            duration=t_remain + t_delay,
            amplitude=1/(ratio + 1),
            phase=-self.modulator.phi[i_index],
            detuning=-detuning
        )

        self.AOM_L = self.AOM_L * carrier_end
        self.AOM_R = self.AOM_R * (red + blue)
        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()

class MSPM(RamanBaseExp,EnvExperiment):
    """MS gate phase modulation
    """

    def build(self):
        ## 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=10000e-6,
                global_step=1e-6,
                precision=10,
                unit="us",
            ),
            group="Experiment",
        )

        print("Experiment Registered: MS gate PM")

    def prepare(self):
        """prepare"""
        super().prepare()
        with open('C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\PM_modulator.pkl',
                  'rb') as f:
            self.modulator = pickle.load(f)

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter)/1e-6 )

    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        MS = [{"gate": "MS_phase_modu",
               "qubit_index": self.qubit_index,
               "rabi_time": scan_parameter,
               "modulator": self.modulator}]
        if self.enable_SBC:
            operation_list = [SBC, MS]
        else:
            operation_list = [MS]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()
