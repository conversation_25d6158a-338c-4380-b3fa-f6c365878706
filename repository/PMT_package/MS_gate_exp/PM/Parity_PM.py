import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import  Circuit
from MS_gate_PM import MS_phase_modu
import  pickle
import sys
try:
    from cloud.pulsedesign.PM_analytic import PulseDesigner
    # 强制让 Pickle 认为 PulseDesigner 来自旧路径
    sys.modules['__main__'] = sys.modules['cloud.pulsedesign.PM_analytic']  # 替换为你当前的模块
except:
    class PulseDesigner:
        pass

class ParityPM(RamanBaseExp, EnvExperiment):
    """Parity PM"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, np.pi * 2, 21),
                global_min=0,
                global_max=10000,
                global_step=0.1,
                precision=10
            ),
            group="Experiment",
        )
        print("Experiment Registered: Parity")


    def prepare(self):
        super().prepare()
        with open('C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\PM_modulator.pkl',
                  'rb') as f:
            self.modulator = pickle.load(f)

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        pi2_time = self.parameter.Light_554.pi_2for2ions.Carrier
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        idle = [{"gate":"Idle","qubit_index":self.qubit_index,"idle_time":10e-6}]
        MS = [{"gate": "MS_phase_modu",
               "qubit_index": self.qubit_index,
               "rabi_time": self.parameter.Light_554.MS_time,
               "modulator": self.modulator}]
        Pi2 = [{
            "gate":"Raman_Rabi",
            "qubit_index":self.qubit_index,
            "rabi_time": pi2_time,
            "phase":scan_parameter
        }]
        if self.enable_SBC:
            operation_list = [SBC, MS, idle,Pi2]
        else:
            operation_list = [MS,idle,Pi2]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()

if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()