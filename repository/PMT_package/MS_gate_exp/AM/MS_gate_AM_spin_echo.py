"""

rabi_choice选择carrier为MS扫时间 ，选择red/blue为两侧AOM同时打红/蓝边带
"""
import  numpy as np
from modules.operation_manager import Circuit, MSOperation,Operation
from modules.raman_base_exp import RamanBaseExp
from artiq.experiment import *
from waveform_manager import Waveform
import dill
from MS_gate_AM import MS_amp_modu,MODULATOR_PATH


class MSAM(RamanBaseExp,EnvExperiment):
    """MS gate amplitude modulation spin echo
    """

    def build(self):
        ## 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=10000e-6,
                global_step=1e-6,
                precision=10,
                unit="us",
            ),
            group="Experiment",
        )

        print("Experiment Registered: MS gate AM")

    def prepare(self):
        """prepare"""
        super().prepare()
        with open(MODULATOR_PATH,'rb') as f:
            self.modulator = dill.load(f)

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter)/1e-6 )
    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        MS = [{"gate": "MS_amp_modu",
               "qubit_index": self.qubit_index,
               "rabi_time": scan_parameter,
               "modulator": self.modulator}]
        idle =[{
            "gate":"Idle",
            "qubit_index":self.qubit_index,
            "idle_time": 0.5e-6
        }]

        Pi2 = [{
            "gate":"Raman_Rabi",
            "qubit_index":self.qubit_index,
            "rabi_time": self.parameter.Light_554.pi_2for2ions.Carrier,
            "phase": np.pi/2
        }
        ]
        Pi2_0 = [{
            "gate":"Raman_Rabi",
            "qubit_index":self.qubit_index,
            "rabi_time": self.parameter.Light_554.pi_2for2ions.Carrier,
            "phase": -np.pi/2
        }
        ]


        if self.enable_SBC:
            operation_list = [SBC, MS]
        else:
            operation_list = [MS,idle,Pi2,idle]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        try:
            self.l554.prepare_waveform(circuit)
        except Exception as e:
            print(e)
        return circuit.duration()
