import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import  Circuit
from MS_gate_AM import MS_amp_modu,MODULATOR_PATH
import  dill
from modules.circuit_to_waveform import string2circuit_list

class MSPhase(RamanBaseExp, EnvExperiment):
    """MS phase calibration"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(1, 10, 11),
                global_min=1,
                global_max=10000,
                global_step=1,
                precision=0,
            ),
            group="Experiment",
        )

        self.setattr_argument("phase",
                              NumberValue(default=0, min=0, step=1, precision=6),
                              tooltip="平移相位",
                              group="Experiment")

        print("Experiment Registered: Parity")


    def prepare(self):
        super().prepare()
        with open(MODULATOR_PATH,'rb') as f:
            self.modulator = dill.load(f)

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        circuit = []
        for i in range(int(scan_parameter)):
            circuit.append("rxx")
            circuit.append("-rx")
            circuit.append("rxx")
            circuit.append("-rx")
        operation_list = string2circuit_list(circuit,
                                           self.parameter,
                                           self.qubit_index,
                                           '00',
                                           self.modulator,
                                           self.phase
                                           )
        circuit = Circuit(operation_list)
        print(operation_list)
        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()

if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()