import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import  Circuit
from MS_gate_AM import MS_amp_modu,MODULATOR_PATH
import  dill


class MSMSShiftAM(RamanBaseExp, EnvExperiment):
    """MS-MS Shift Calibration"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(-100, 100, 21),
                global_min=-10000,
                global_max=10000,
                global_step=0.1,
                precision=10,
                unit="Hz",
            ),
            group="Experiment",
        )

        self.setattr_argument("phase",
                              NumberValue(default=0, min=0, step=1, precision=6),
                              tooltip="平移相位",
                              group="Experiment")

        print("Experiment Registered: Parity")


    def prepare(self):
        super().prepare()
        with open(MODULATOR_PATH,'rb') as f:
            self.modulator = dill.load(f)

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        idle = [{"gate":"Idle","qubit_index":self.qubit_index,"idle_time":0.5e-6}]
        MS = [{"gate": "MS_amp_modu",
               "qubit_index": self.qubit_index,
               "rabi_time": self.parameter.Light_554.MS_time,
               "modulator": self.modulator,
               "phase": self.phase,
               "shift":scan_parameter
               }]


        if self.enable_SBC:
            operation_list = [SBC, MS, idle]
        else:
            operation_list = [MS,idle,MS]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()

if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()