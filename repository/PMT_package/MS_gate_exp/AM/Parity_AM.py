import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import  Circuit
from MS_gate_AM import MS_amp_modu,MODULATOR_PATH
import  dill


class ParityAM(RamanBaseExp, EnvExperiment):
    """Parity AM"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, np.pi * 2, 21),
                global_min=0,
                global_max=10000,
                global_step=0.1,
                precision=10
            ),
            group="Experiment",
        )

        self.setattr_argument("phase",
                              NumberValue(default=0, min=0, step=1, precision=6),
                              tooltip="平移相位",
                              group="Experiment")

        print("Experiment Registered: Parity")


    def prepare(self):
        super().prepare()
        with open(MODULATOR_PATH,'rb') as f:
            self.modulator = dill.load(f)

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        pi2_time = self.parameter.Light_554.pi_2for2ions.Carrier
        shift_ms = self.parameter.Light_554.MS_AC_Stark_shift
        shift_q1 = self.parameter.Light_554.q1_AC_Stark_shift
        ms_time = self.modulator.gate_time
        aod_time = self.parameter.Light_554.AOD_time_before_AOM

        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        idle = [{"gate":"Idle","qubit_index":self.qubit_index,"idle_time":0.5e-6}]
        MS = [{"gate": "MS_amp_modu",
               "qubit_index": self.qubit_index,
               "rabi_time": self.parameter.Light_554.MS_time,
               "modulator": self.modulator,
               "phase":self.phase +2*np.pi*(0-shift_ms) * aod_time
               }]
        Pi2 = [{
            "gate":"Raman_Rabi",
            "qubit_index":self.qubit_index,
            "rabi_time": pi2_time,
            "phase":scan_parameter + 2*np.pi*(shift_ms - shift_q1) * ms_time+2*np.pi*(0 - shift_q1) * aod_time
        }]

        if self.enable_SBC:
            operation_list = [SBC, MS, idle,Pi2]
        else:
            operation_list = [MS, idle,Pi2]
        circuit = Circuit(operation_list)
        print(operation_list)
        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()

if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()