"""

rabi_choice选择carrier为MS扫时间 ，选择red/blue为两侧AOM同时打红/蓝边带
"""
import  numpy as np
from modules.operation_manager import Circuit, MSOperation,Operation
from modules.raman_base_exp import RamanBaseExp
from artiq.experiment import *
from waveform_manager import Waveform
import dill


MODULATOR_PATH = 'D:\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator.pkl'

class MS_amp_modu(Operation):
    def __init__(
        self,
        qubit_index: tuple,
        rabi_time: float,
        modulator,
        aom_amp = -1.0,
        phase = 0.0,
        shift = 0
    ):
        super().__init__(qubit_index)
        if aom_amp < 0:
            self.aom_amp = self.parameters.Light_554.AOM_AWG_amp_ms
        else:
            self.aom_amp = aom_amp
        self.rabi_time = rabi_time
        self.modulator = modulator
        self.phase = phase
        self.shift = shift
        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def amp_to_aom_amp(self,amp):
        sin_coeff = 1.6753
        aom_amp = np.arcsin(np.sin(sin_coeff)*amp)/sin_coeff
        return aom_amp
    def compute_AOM_wave(self, algorithm=None):
        dt = self.modulator.t_modulation
        N_len = int(np.floor(self.rabi_time/dt))
        N = self.modulator.N_modulation
        t_remain = self.rabi_time- N_len * dt
        aom_amp = self.aom_amp
        ratio = self.parameters.Light_554.ratio_AOM

        ac_stark_shift = self.parameters.Light_554.MS_AC_Stark_shift + self.shift

        detuning  = self.modulator.mu / 2 / np.pi + 0
        # 1. 构造波形片段
        Idle = Waveform(
            duration= 0 ,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        self.AOM_L = Idle
        self.AOM_R = Idle



        for i in range(N_len):
            i_index = i % N
            amp = self.amp_to_aom_amp(abs(aom_amp*self.modulator.rabi[i_index]))
            if self.modulator.rabi[i_index] < 0:
                phase = np.pi +self.phase
            else:
                phase = 0 +self.phase
            carrier = Waveform(
                duration=dt,
                amplitude= amp,
                phase= phase,
                detuning=ac_stark_shift,
            )
            red = Waveform(
                duration=dt,
                amplitude=ratio/(ratio + 1),
                phase=0,
                detuning=detuning
            )
            blue = Waveform(
                duration=dt,
                amplitude=1/(ratio + 1),
                phase = 0,
                detuning = -detuning
            )
            self.AOM_L = self.AOM_L * carrier
            self.AOM_R = self.AOM_R * (red + blue)
        i_index = N_len % N
        amp = self.amp_to_aom_amp(abs(aom_amp * self.modulator.rabi[i_index]))
        if self.modulator.rabi[i_index] < 0:
            phase = np.pi + self.phase
        else:
            phase = 0 + self.phase
        carrier_end = Waveform(
            duration=t_remain,
            amplitude=amp,
            phase=phase,
            detuning=ac_stark_shift,
        )
        red = Waveform(
            duration=t_remain,
            amplitude=ratio/(ratio + 1),
            phase=0,
            detuning=detuning
        )
        blue = Waveform(
            duration=t_remain,
            amplitude=1/(ratio + 1),
            phase=0,
            detuning=-detuning
        )

        self.AOM_L = self.AOM_L * carrier_end
        self.AOM_R = self.AOM_R * (red + blue)
        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()

class MSAM(RamanBaseExp,EnvExperiment):
    """MS gate amplitude modulation
    """

    def build(self):
        ## 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=10000e-6,
                global_step=1e-6,
                precision=10,
                unit="us",
            ),
            group="Experiment",
        )

        print("Experiment Registered: MS gate AM")

    def prepare(self):
        """prepare"""
        super().prepare()
        with open(MODULATOR_PATH,'rb') as f:
            self.modulator = dill.load(f)

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter)/1e-6 )
    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        MS = [{"gate": "MS_amp_modu",
               "qubit_index": self.qubit_index,
               "rabi_time": scan_parameter,
               "modulator": self.modulator}]
        if self.enable_SBC:
            operation_list = [SBC, MS]
        else:
            operation_list = [MS]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        try:
            self.l554.prepare_waveform(circuit)
        except Exception as e:
            print(e)
        return circuit.duration()
