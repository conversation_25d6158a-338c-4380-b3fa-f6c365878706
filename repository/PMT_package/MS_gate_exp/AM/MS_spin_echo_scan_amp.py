import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit
from MS_gate_AM import MS_amp_modu,MODULATOR_PATH
import  dill



class MSScanAOMAmpAM(RamanBaseExp, EnvExperiment):
    """MS Scan AOM AMP AM spin echo"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 1, 21),
                global_min=0,
                global_max=1,
                global_step=0.1,
                precision=10
            ),
            group="Experiment",
        )

        self.setattr_argument("gate_number",
                              NumberValue(default=1, min=1, step=1, precision=0),
                              tooltip="门个数",
                              group="Experiment")
        print("Experiment Registered: MS Scan AOM AMP")


    def prepare(self):
        super().prepare()
        with open(MODULATOR_PATH,'rb') as f:
            self.modulator = dill.load(f)
    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )


    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        MS = [{"gate": "MS_amp_modu",
               "qubit_index": self.qubit_index,
               "rabi_time": self.parameter.Light_554.MS_time,
               "modulator": self.modulator,
               "aom_amp":scan_parameter}]

        idle =[{
            "gate":"Idle",
            "qubit_index":self.qubit_index,
            "idle_time": 0.5e-6
        }]

        Pi2 = [{
            "gate":"Raman_Rabi",
            "qubit_index":self.qubit_index,
            "rabi_time": self.parameter.Light_554.pi_2for2ions.Carrier,
            "phase": np.pi/2
        }
        ]
        Pi2_0 = [{
            "gate":"Raman_Rabi",
            "qubit_index":self.qubit_index,
            "rabi_time": self.parameter.Light_554.pi_2for2ions.Carrier,
            "phase": -np.pi/2
        }
        ]


        if self.enable_SBC:
            operation_list = [SBC]
        else:
            operation_list = []
        for _ in range(self.gate_number):

            operation_list.append(MS)
            operation_list.append(idle)
            operation_list.append(Pi2)
            operation_list.append(idle)
            operation_list.append(Pi2)
            operation_list.append(idle)
            operation_list.append(MS)
            operation_list.append(idle)
            operation_list.append(Pi2_0)
            operation_list.append(idle)
            operation_list.append(Pi2_0)
            operation_list.append(idle)

            # operation_list.append(Pi2_0)
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()

if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()