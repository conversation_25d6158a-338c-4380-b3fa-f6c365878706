from modules.pmt import PMT
from modules.light_369 import *
from modules.light_554_awg import *
from modules.config import LOADED_PARAMETER
from artiq.experiment import *
from cloud.artiq_manager import *


class CoolingScan(EnvExperiment):
    """Cooling_Scan
    用途：
    1.扫描cooling的AOM的DDS幅度
    2.如有需要也可以以此为模板扫描频率或者其他369光的强度
    """

    def build(self):
        # 0. 初始化任务管理器,在有任务提交时退出
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        # self.parameter = Config(DEFAULT_PARAMETER)
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.dds_for_C_fre = self.parameter.Light_369.dds_for_C_fre

        # 2. 连接仪器
        self.setattr_device("core")
        self.setattr_device("scheduler")
        self.l369 = Light369(self)
        self.pmt = PMT(self)

        self.setattr_argument("cooling_scan_range", Scannable(
            default=RangeScan(0.0, 1, 21), global_min=0.0, global_max=1, global_step=1e-1, precision=6
        ), group="Experiment")
        print("Experiment Registered: Cooling Scan")


    def prepare(self):
        """实验准备阶段，将必要的参数从 config 文件中提取出来， artiq 不支持传递字典格式"""
        self.manager = ARTIQManager()

        # 1. 获得参数
        self.scan_parameter = self.cooling_scan_range.sequence

        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter))

        print("prepare_done")

    @rpc(flags={"async"})
    def tprint(self, data):
        print(data)

    @rpc(flags={})
    def check_scheduler(self) -> TBool:
        status = self.manager.scheduler.get_status()
        # if len(status) > 1:
        # keys = status.keys()
        # self.manager.scheduler.delet(min(keys))
        return len(status) > 1

    @kernel()
    def run(self):
        # 0. initial
        self.core.reset()

        # 1. 模块初始化
        self.pmt.initial()
        self.l369.initial()

        # 2. 时序
        for scan_point in range(len(self.scan_parameter)):
            # 1. off -> cooling
            self.l369.dds_for_CPD.set(frequency=self.dds_for_C_fre, phase=0.0,
                                 amplitude=self.scan_parameter[scan_point])
            for repeat in range(self.repeat):
                self.pmt.pmt_start_count()
                delay(self.cooling_time)
                self.pmt.pmt_stop_count()
                delay(self.cooling_time)

            self.core.wait_until_mu(now_mu())
            delay(1 * ms)
            # self.pmt.data_process_for_cooling_idle()
            self.pmt.data_process_for_x_scan_counts(scan_point)
            if self.scheduler.check_termination():
                break
            self.core.break_realtime()

    def analyze(self):
        pass
