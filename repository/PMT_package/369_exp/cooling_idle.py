import numpy as np
from artiq.experiment import *
from modules.pmt import PMT
from modules.config import LOADED_PARAMETER
from modules.light_369 import Light369


class CoolingIdle(EnvExperiment):
    """Cooling Idle

    用途:
    1. 控制 369 光路切换到 Cooling 状态 (同时可以作为其他 Idle 类型实验的模板);
    2. 看 cooling 计数;
    3. 将离子与 PMT 通道对准.
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time

        # 2. 模块准备
        self.setattr_device("core")
        self.setattr_device("scheduler")
        self.pmt = PMT(self)
        self.l369 = Light369(self)
        print("Experiment Registered: Cooling Idle ")

    def prepare(self):

        print("Prepare Done")
    @rpc(flags={})
    def check_scheduler(self) -> TBool:
        status = self.scheduler.get_status()
        return len(status) > 1
    @kernel()
    def run(self):
        # 0. initial
        self.pmt.prepare_for_cooling_idle()
        self.core.reset()
        self.pmt.initial()
        self.l369.initial()

        # 1. 给方波信号
        while True:
            self.core.break_realtime()
            for repeat in range(self.repeat):
                # repeat 此读数循环, 总积分时间是 cooling_time * repeat
                self.pmt.pmt_start_count()
                delay(self.cooling_time)
                self.pmt.pmt_stop_count()
                delay(self.cooling_time)

            self.core.wait_until_mu(now_mu())
            delay(100e-6)
            self.pmt.data_process_for_cooling_idle()
            if self.check_scheduler():
                break
            if self.scheduler.check_termination():
                break

    def analyze(self):
        print("Analysis Done")
