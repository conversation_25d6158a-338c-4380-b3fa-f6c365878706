import numpy as np
from artiq.experiment import *
from modules.pmt import PMT
from modules.config import LOADED_PARAMETER
from modules.light_369 import Light369


class PumpingTest(EnvExperiment):
    """Pumping Test

    用途:
    1. 扫描 Pumping 时间, 找到最优 Pumping 时间;
    2. 一维扫描类型实验的模板;
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time

        # 2. 模块准备
        self.setattr_device("core")
        self.pmt = PMT(self)
        self.l369 = Light369(self)

        # 3. 变量设置
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 20),
                global_min=0,
                global_max=1,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )
        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="扫描任务的重复次数",
            group="Experiment",
        )
        print("Experiment Registered: Pumping Test ")

    def prepare(self):
        self.scan_parameter = self.X_scan_range.sequence  # 变量 list
        self.scan_points = len(self.scan_parameter)  # 数据点数
        print("Prepare Done")

    @kernel()
    def run(self):
        # 准备 X 轴数据集
        self.pmt.prepare_for_x_scan(
            np.array(self.scan_parameter) / 1e-6
        )
        # 0. initial
        self.core.reset()
        self.pmt.initial()
        self.l369.initial()

        # 1. 给方波信号
        for task_num in range(self.task_num):
            for scan_point in range(self.scan_points):
                for repeat in range(self.repeat):
                    # 1 . 切换至 cooling 状态
                    delay(self.cooling_time)

                    # 2. 切换至 pumping 状态
                    self.l369.switch_to_pump()
                    delay(self.scan_parameter[scan_point])  # 等待 pumping

                    # 4. 切换至 detecting 状态
                    with parallel:
                        self.pmt.pmt_start_count()
                        self.l369.switch_to_detect()

                    delay(self.detecting_time)

                    # 5. 切换至 cooling 状态
                    with parallel:
                        self.pmt.pmt_stop_count()
                        self.l369.switch_to_cool()
                    delay(1 * ms)

                self.core.wait_until_mu(now_mu())
                delay(10 * ms)
                self.pmt.data_process_for_x_scan(scan_point)

    def analyze(self):
        print("Analysis Done")
