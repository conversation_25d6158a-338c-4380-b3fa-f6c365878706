import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit,Operation
from waveform_manager import Waveform
from AWG_mw import MW_drive,Idle_mw
class MWRabiAWG(RamanBaseExp, EnvExperiment):
    """MW  Ramsey AWG"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=100000e-6,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )


        self.setattr_argument("signal_frequency", NumberValue(default=200e6 , step=0.1, precision=10,unit="MHz"),
                              tooltip="信号的频率", group="Experiment")

        self.setattr_argument("AWG_full_voltage", NumberValue(default=0.0 ,max = 1, step=0.1, precision=10),
                              tooltip="AWG的电压幅度", group="Experiment")

        self.setattr_argument("pi2_time", NumberValue(default=0.0 ,max = 1, step=0.1, precision=10,unit = "us"),
                              tooltip="pi/2时间", group="Experiment")

        print("Experiment Registered: Raman Rabi")

    def prepare(self):
        super().prepare()
        self.l554.Voltage_AOM = self.AWG_full_voltage
        self.l554.Voltage_AOD = self.AWG_full_voltage

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""

        # 1. 定义操作

        print("Start_compute_Waveform")
        Rabi = [
            {
                "gate":"MW_drive",
                "qubit_index":self.qubit_index,
                "rabi_time":self.pi2_time,
                "drive_freq":self.signal_frequency
            }
        ]
        Idle_Ramsey = [
            {
                "gate": "Idle_mw",
                "qubit_index": self.qubit_index,
                "rabi_time": scan_parameter
            }
        ]
        Idle = [
            {
                "gate":"Idle_mw",
                "qubit_index":self.qubit_index,
                "rabi_time":100e-6,
            }
        ]

        operation_list = [Rabi,Idle_Ramsey,Rabi]
        print("Start_compute_circuit")

        circuit = Circuit(operation_list)
        print("End_compute_Waveform")

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        print("prepare_compute_Waveform")

        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
