from modules.light_369 import *
from modules.pmt import PMT
from modules.signal_mw import *


class PMTForHistogram(PMT):
    @rpc(flags={""})
    def read_data_double(self):
        """读数两次, 并返回其中的偶数项和奇数项矩阵
        用于一次循环中要读数两次的实验
        """
        data_temp = np.zeros([self.repeat * 2, 32])

        data_temp[: self.repeat, :] = self.read_data()
        data_temp[self.repeat:, :] = self.read_data()

        cooling_data_temp = data_temp[0::2, :]
        detecting_data_temp = data_temp[1::2, :]
        return cooling_data_temp, detecting_data_temp
    @rpc(flags={"async"})
    def prepare_for_histogram(self, threshold):
        """Histogram 数据集准备"""
        #
        self.set_dataset("histogram", [], broadcast=True)
        self.set_dataset("histogram_threshold", threshold, broadcast=True)
        # error[0] 是暗态 判断成亮态的概率, error[1] 是亮态判断成暗态的概率
        self.set_dataset("error", [0, 0], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "Histogram",
            "${artiq_applet}histogram_double histogram --x histogram_threshold --error error",
        )

    @rpc(flags={"async"})
    def process_for_histogram(self, ion_choice=(0,), threshold=1):
        """Histogram 数据处理
        流程:
        1. 读数两次, 并分割成 pump_detect 和 control_detect 计数, shape 均为 (repeat, 32);
        2. 通道求和, 分别得到 pump_detect 和 control_detect 对应于离子这一层的计数, shape 均为 (repeat, ion_num);
        3. 选择要做 histogram 的离子索引, 并对他们的通道进一步求和, 对 pump_detect 和 control_detect 分别得到 shape = (repeat, 1);
        4. 将 pump_detect 和 control_detect 的两列数拼成一个矩阵分别做 histogram, 得到 shape = (bins, 2) 的数据;
        5. 根据设定的阈值分别计算二者的错误率, 得到 shape = (2,1) 的 list;
        6. 更新数据集 error 和 histogram;
        """
        # 1. 读数两次, 分别获取 pumping data 和 control data
        pump_detect_data_temp, control_detect_data_temp = self.read_data_double()

        # 2. 将 pump -> detect 和 control -> detect 的数据分开, 分别做通道求和
        pump_detect_data_select_channels = self._sum_multi_channels(
            pump_detect_data_temp, self.parameter.PMT.Select_channels
        )  # shape = [repeat, N]

        control_detect_select_channels = self._sum_multi_channels(
            control_detect_data_temp, self.parameter.PMT.Select_channels
        )  # shape = [repeat, N]

        # 3. 根据 ion_choice 选择目标离子, 并将他的 pumping 和 control 计数粘在一起
        pumping_data = pump_detect_data_select_channels[:, ion_choice]
        # 对选中的列按行求和，得到一个 100 * 1 的矩阵
        pumping_data_single = pumping_data.sum(axis=1).reshape(-1, 1)

        control_data = control_detect_select_channels[:, ion_choice]
        # 对选中的列按行求和，得到一个 100 * 1 的矩阵
        control_data_single = control_data.sum(axis=1).reshape(-1, 1)

        data = np.column_stack((pumping_data_single, control_data_single))
        hist_data = self._compute_histogram(data, bins=25)

        indices = np.arange(hist_data.shape[0])  # 构建索引数组
        # 4. 计算第一列大于阈值的数量（暗态判断成亮态）
        count_0_above_threshold = np.sum(
            hist_data[indices > threshold, 0]
        )  # 计算第一列大于阈值的数量
        prob_0_above_threshold = count_0_above_threshold / len(data)  # 概率

        # 计算第二列小于阈值的数量（亮态判断成暗态）
        count_1_below_threshold = np.sum(
            hist_data[indices <= threshold, 1]
        )  # 计算第二列小于阈值的数量
        prob_1_below_threshold = count_1_below_threshold / len(data)  # 概率
        self.set_dataset("histogram", hist_data.tolist(), broadcast=True)
        self.set_dataset(
            "error", [prob_0_above_threshold, prob_1_below_threshold], broadcast=True
        )


class HistogramMW(EnvExperiment):
    """MW_Histogram"""

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        # self.parameter = Config(DEFAULT_PARAMETER)
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.operation_time = self.parameter.Signal_MW.mw_pi_2.zero
        self.mw_pi_2 = self.parameter.Signal_MW.mw_pi_2.zero

        # 2. 连接仪器
        self.setattr_device("core")
        self.setattr_device("scheduler")
        self.l369 = Light369(self)
        self.pmt = PMTForHistogram(self)
        self.mw = SignalMW(self)

        # 3.设置参数
        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="重复次数",
            group="Experiment",
        )
        self.setattr_argument("ion_choice", StringValue(default="(0, )"))
        self.setattr_argument(
            "histogram_threshold",
            NumberValue(
                default=self.parameter.PMT.Detect_threshold, min=0, step=1, precision=0
            ),
        )
        print("Experiment Registered: MW_Histogram")

    def prepare(self):
        """准备扫描参数和数据集"""
        self.ion_choice = eval(self.ion_choice)
        self.histogram_threshold = self.histogram_threshold

    @rpc(flags={""})
    def set_rid(self):
        # 读取当前实验rid
        rid = int(list(self.scheduler.get_status().keys())[0])
        self.set_dataset('rid',rid,broadcast=True)
    @kernel()
    def run(self):
        self.pmt.prepare_for_histogram(self.histogram_threshold)
        self.set_rid()
        # 0. initial
        self.core.reset()

        self.l369.initial()
        self.mw.initial()
        self.pmt.initial()
        self.core.break_realtime()

        for i in range(self.task_num):
            for repeat in range(self.repeat):
                # ---------------- dark state ---------------
                # 1 . 切换至 cooling 状态
                delay(self.cooling_time)

                # 2. 切换至 pumping 状态
                self.l369.switch_to_pump()

                delay(self.pumping_time)

                # ３. 切换至 detect 状态
                with parallel:
                    self.l369.switch_to_detect()
                    self.pmt.pmt_start_count()

                delay(self.detecting_time)

                with parallel:
                    self.pmt.pmt_stop_count()
                    self.l369.switch_to_cool()

                # ---------------- bright state ---------------
                # 1. 切换至 cooling 状态
                delay(self.cooling_time)

                # 2. 切换至 pumping 状态
                self.l369.switch_to_pump()
                delay(self.pumping_time)

                # 3. 切换至 control 阶段
                # with parallel:
                self.l369.switch_to_control()
                self.mw.mw_on()


                delay(self.mw_pi_2 * 2)

                # 4. 切换至 detecting 状态
                with parallel:
                    self.mw.mw_off()
                    self.pmt.pmt_start_count()
                    self.l369.switch_to_detect()

                delay(self.detecting_time)

                # 5. 切换至 off 状态
                with parallel:
                    self.pmt.pmt_stop_count()
                    self.l369.switch_to_cool()

            self.core.wait_until_mu(now_mu())
            delay(1 * ms)
            self.pmt.process_for_histogram(self.ion_choice, self.histogram_threshold)
