from modules.light_369 import *
from modules.pmt import PMT
from modules.signal_mw import *


class MWSpectrum(EnvExperiment):
    """MW_Spectrum
    用途：
    1.确定赛曼能级频率 
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        # self.parameter = Config(DEFAULT_PARAMETER)
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.mw_pi_2 = self.parameter.Signal_MW.mw_pi_2.zero

        # 2. 连接仪器
        self.setattr_device("core")
        self.l369 = Light369(self)
        self.pmt = PMT(self)
        self.mw = SignalMW(self)

        # 3. 参数设置
        self.setattr_argument("X_scan_range", Scannable(
            default=RangeScan(-1e6, 1e6, 20), global_step=1, precision=2,
            unit="MHz"), group="Experiment")

        self.setattr_argument("task_num", NumberValue(default=1, min=1, step=1, precision=0),
                              tooltip="扫描任务的重复次数", group="Experiment")
        print("Experiment Registered: MW_Spectrum")

    def prepare(self):
        """准备扫描参数和数据集"""
        self.scan_parameter = self.X_scan_range.sequence
        self.scan_points = len(self.scan_parameter)
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) / 1e6)
        print("prepare_done")

    @rpc(flags={"async"})
    def tprint(self, data):
        print(data)

    @kernel()
    def run(self):
        # 0. initial
        self.core.reset()

        self.l369.initial()
        self.mw.initial()
        self.pmt.initial()
        self.core.break_realtime()

        for task in range(self.task_num):
            for scan_point in range(self.scan_points):
                self.mw.set_mw_parameter(detuning=self.scan_parameter[scan_point])
                for repeat in range(self.repeat):
                    # 1 . 切换至 cooling 状态
                    # self.pmt.pmt_start_count()
                    delay(self.cooling_time)
                    # self.pmt.pmt_stop_count()

                    # 2. 切换至 pumping 状态
                    self.l369.switch_to_pump()

                    delay(self.pumping_time)

                    # ３. 切换至 control 状态
                    self.l369.switch_to_control()
                    self.mw.mw_on()

                    delay(self.mw_pi_2)

                    # 4. 切换至 detecting 状态
                    with parallel:
                        self.mw.mw_off()
                        self.pmt.pmt_start_count()
                        self.l369.switch_to_detect()

                    delay(self.detecting_time)

                    # 5. 切换至 off 状态
                    with parallel:
                        self.pmt.pmt_stop_count()
                        self.l369.switch_to_cool()
                    delay(10 * us)

                self.core.wait_until_mu(now_mu())
                delay(1 * ms)
                # self.pmt.data_process_for_x_scan_with_cooling_counts(scan_point)
                self.pmt.data_process_for_x_scan(scan_point)
        self.pmt.close_pmt()

    def analyze(self):
        pass
