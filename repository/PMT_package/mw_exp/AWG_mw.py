import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit,Operation
from waveform_manager import Waveform

class MW_drive(Operation):
    def __init__(self,qubit_index,rabi_time,drive_freq):
        super().__init__(qubit_index)
        self.rabi_time = rabi_time
        self.drive_freq = drive_freq
        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def compute_AOM_wave(self, algorithm=None):
        detuning_L = self.drive_freq - (self.parameters.Light_554.AOM_middle_freq +self.parameters.Light_554.AOM_freq_LR)

        drive_wave_L = Waveform(
            duration=self.rabi_time,
            amplitude=0.1,
            detuning=detuning_L,
            phase=0
        )

        drive_wave_R = Waveform(
            duration=self.rabi_time,
            amplitude=0,
            detuning=0,
            phase=0
        )
        self.AOM_L = drive_wave_L
        self.AOM_R = drive_wave_L

        self.duration_time = self.AOM_L.duration()
    def compute_AOD_wave(self):
        detuning_L = self.drive_freq - self.parameters.Light_554.AOD_middle_freq
        drive_wave_L = Waveform(
            duration=self.rabi_time,
            amplitude=1.0,
            detuning=detuning_L,
            phase=0
        )
        self.AOD_L = drive_wave_L
        self.AOD_R = drive_wave_L

class Idle_mw(Operation):
    def __init__(self,qubit_index,rabi_time):
        super().__init__(qubit_index)
        self.rabi_time = rabi_time
        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def compute_AOM_wave(self, algorithm=None):

        drive_wave_L = Waveform(
            duration=self.rabi_time,
            amplitude=0,
            detuning=0,
            phase=0
        )

        drive_wave_R = Waveform(
            duration=self.rabi_time,
            amplitude=0,
            detuning=0,
            phase=0
        )
        self.AOM_L = drive_wave_L
        self.AOM_R = drive_wave_L

        self.duration_time = self.AOM_L.duration()
    def compute_AOD_wave(self):
        drive_wave_L = Waveform(
            duration=self.rabi_time,
            amplitude=0.0,
            detuning= 0 ,
            phase=0
        )
        self.AOD_L = drive_wave_L
        self.AOD_R = drive_wave_L
class MWRabiAWG(RamanBaseExp, EnvExperiment):
    """MW  Rabi AWG"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=100000e-6,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )


        self.setattr_argument("signal_frequency", NumberValue(default=200e6 , step=0.1, precision=10,unit="MHz"),
                              tooltip="信号的频率", group="Experiment")

        self.setattr_argument("AWG_full_voltage", NumberValue(default=0.0 ,max = 1, step=0.1, precision=10),
                              tooltip="AWG的电压幅度", group="Experiment")

        print("Experiment Registered: Raman Rabi")

    def prepare(self):
        super().prepare()
        self.l554.Voltage_AOM = self.AWG_full_voltage
        self.l554.Voltage_AOD = self.AWG_full_voltage
        # repeated_array = [x for x in self.scan_parameter for _ in range(2)]
        # self.scan_parameter = repeated_array
    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""

        # 1. 定义操作

        print("Start_compute_Waveform")
        Rabi = [
            {
                "gate":"MW_drive",
                "qubit_index":self.qubit_index,
                "rabi_time":scan_parameter,
                "drive_freq":self.signal_frequency
            }
        ]
        Idle = [
            {
                "gate":"Idle_mw",
                "qubit_index":self.qubit_index,
                "rabi_time":100e-6,
            }
        ]
        operation_list = [Rabi]
        print("Start_compute_circuit")

        circuit = Circuit(operation_list)
        print("End_compute_Waveform")

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        print("prepare_compute_Waveform")

        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
