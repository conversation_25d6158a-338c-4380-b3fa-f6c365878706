from modules.light_369 import *
from modules.pmt import PMT
from modules.signal_mw import *
from modules.config import LOADED_PARAMETER


class RamseyMW(EnvExperiment):
    """MW_Ramsey

    用途:
    1. 执行 MW Ramsey 实验;
    2. 执行 <PERSON><PERSON><PERSON> 能级的 Ramsey 实验;
    3. 频率校准;
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        # self.parameter = Config(DEFAULT_PARAMETER)

        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.mw_pi_2_0 = self.parameter.Signal_MW.mw_pi_2.zero
        self.mw_pi_2_p = self.parameter.Signal_MW.mw_pi_2.positive
        self.mw_pi_2_n = self.parameter.Signal_MW.mw_pi_2.negative
        self.mw_pi_2 = self.mw_pi_2_0
        self.zeeman_p = self.parameter.Signal_MW.zeeman_p
        self.zeeman_n = self.parameter.Signal_MW.zeeman_n

        # 2. 连接仪器
        self.setattr_device("core")
        self.setattr_device("scheduler")
        self.l369 = Light369(self)
        self.pmt = PMT(self)
        self.mw = SignalMW(self)

        # 3. 参数设置
        # self.setattr_argument("mw_frequency", NumberValue(default=0, unit="Hz", precision=6), group="Experiment")

        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 100e-6, 20),
                global_min=0,
                global_max=1,
                global_step=1e-8,
                precision=8,
                unit="us",
            ),
            group="Experiment",
        )

        self.setattr_argument(
            "zeeman_choice",
            EnumerationValue(["+", "0", "-"], default="0"),
            group="Experiment",
        )

        self.setattr_argument(
            "EIT_pi",
            BooleanValue(default=False),
            group="Experiment",
        )

        self.setattr_argument(
            "EIT_sigma",
            BooleanValue(default=False),
            group="Experiment",
        )

        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="扫描任务的重复次数",
            group="Experiment",
        )
        print("Experiment Registered: MW_Ramsey")

    def prepare(self):
        """准备扫描参数和数据集"""
        self.scan_parameter = self.X_scan_range.sequence
        self.scan_points = len(self.scan_parameter)
        print("prepare_done")

    @rpc(flags={"async"})
    def tprint(self, data):
        print(data)
    @rpc(flags={""})
    def set_rid(self):
        # 读取当前实验rid
        rid = int(list(self.scheduler.get_status().keys())[0])
        self.set_dataset('rid',rid,broadcast=True)
    @kernel()
    def run(self):
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)
        self.set_rid()
        # 0. initial
        self.core.reset()
        self.pmt.initial()
        self.l369.initial()
        self.mw.initial()

        # 根据 Zeeman 能级选择 mw_pi_2 脉冲的时间
        if self.zeeman_choice == "+":
            self.mw.set_mw_parameter(detuning=self.zeeman_p)
            self.mw_pi_2 = self.mw_pi_2_p
        elif self.zeeman_choice == "-":
            self.mw.set_mw_parameter(detuning=self.zeeman_n)
            self.mw_pi_2 = self.mw_pi_2_n
        else:
            self.mw.set_mw_parameter(detuning=0.0)
            self.mw_pi_2 = self.mw_pi_2_0

        self.core.break_realtime()

        for task in range(self.task_num):
            for scan_point in range(self.scan_points):
                self.core.break_realtime()
                for repeat in range(self.repeat):
                    # 1 . 切换至 cooling 状态
                    # self.pmt.pmt_start_count()
                    delay(self.cooling_time)
                    # self.pmt.pmt_stop_count()

                    # 2. 切换至 pumping 状态
                    self.l369.switch_to_pump()
                    delay(self.pumping_time)

                    # ３.　切换至　control　状态

                    self.l369.switch_pump_to_control()
                    self.mw.mw_on()

                    delay(self.mw_pi_2)

                    self.mw.mw_off()

                    if self.EIT_pi:
                        with parallel:
                            self.l369.EOM_14_7G.off()
                            self.l369.EOM_2_1G.on()
                            self.l369.EOM_14_4G.on()
                            self.l369.EOM_41_V1.on()
                            self.l369.EOM_41_V2.on()

                            self.l369.dds_for_EIT_pi.cfg_sw(True)
                    if self.EIT_sigma:
                        with parallel:
                            self.l369.EOM_14_7G.off()
                            self.l369.EOM_2_1G.on()
                            self.l369.EOM_14_4G.on()
                            self.l369.EOM_41_V1.on()
                            self.l369.EOM_41_V2.on()

                            self.l369.dds_for_EIT_sigma.cfg_sw(True)
                    # delay ramsey 时间
                    delay(self.scan_parameter[scan_point])

                    if self.EIT_pi:
                        with parallel:
                            self.l369.dds_for_EIT_pi.cfg_sw(False)
                            self.l369.EOM_14_7G.on()
                            self.l369.EOM_2_1G.on()
                            self.l369.EOM_14_4G.on()
                            self.l369.EOM_41_V1.off()
                            self.l369.EOM_41_V2.on()

                    if self.EIT_sigma:
                        with parallel:
                            self.l369.dds_for_EIT_sigma.cfg_sw(False)
                            self.l369.EOM_14_7G.on()
                            self.l369.EOM_2_1G.on()
                            self.l369.EOM_14_4G.on()
                            self.l369.EOM_41_V1.off()
                            self.l369.EOM_41_V2.on()

                    self.mw.mw_on()
                    delay(self.mw_pi_2)

                    # 4. 切换至 detecting 状态
                    with parallel:
                        self.mw.mw_off()
                        self.pmt.pmt_start_count()
                        self.l369.switch_control_to_detect()

                    delay(self.detecting_time)

                    # 5. 切换至 off 状态
                    with parallel:
                        self.pmt.pmt_stop_count()
                        self.l369.switch_detect_to_cool()
                    # delay(6 * ms)

                self.core.wait_until_mu(now_mu())

                delay(1 * ms)
                self.pmt.data_process_for_x_scan(scan_point)
                if self.scheduler.check_termination():
                    break
            if self.scheduler.check_termination():
                break
        self.pmt.close_pmt()

    def analyze(self):
        pass
