import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Operation, Circuit


class RamanRamseyPhaseScan(RamanBaseExp, EnvExperiment):
    """Raman Ramsey Phase Scan"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, np.pi * 2, 21),
                global_min=0,
                global_step=0.01,
                precision=2,
            ),
            group="Experiment",
        )
        self.setattr_argument(
            "Ramsey_choice",
            EnumerationValue(["Carrier", "Red", "Blue"], default="Carrier"),
            tooltip="选择红/蓝边带还是载波",
            group="Experiment",
        )

        self.setattr_argument("phonon_index", NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="声子序号", group="Experiment")

        self.setattr_argument("delay_time", NumberValue(default=30e-6, min=0e-6, step=1e-6, precision=6,unit="us"),
                              tooltip="Ramsey 两个 pi_2 之间的 delay_time", group="Experiment")
        print("Experiment Registered: Raman Ramsey Phase Scan")


    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 读取pi/2时间
        pi_2_times = {
            "Carrier": self.parameter.Light_554.pi_2.Carrier[self.qubit_index[0]],
            "Red": self.parameter.Light_554.pi_2.Red,
            "Blue": self.parameter.Light_554.pi_2.Blue
        }

        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Raman_Pi2 = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "phonon_index":self.phonon_index,
                 "rabi_time": pi_2_times[self.Ramsey_choice],
                 "rabi_choice": self.Ramsey_choice}]
        Raman_Pi2_phase = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "phonon_index":self.phonon_index,
                 "rabi_time": pi_2_times[self.Ramsey_choice],
                 "phase": scan_parameter,
                 "rabi_choice": self.Ramsey_choice}]
        Idle_Ramsey = [
            {
                "gate": "Idle",
                "qubit_index": self.qubit_index,
                "idle_time": self.delay_time
            }
        ]
        if self.enable_SBC:
            operation_list = [SBC, Raman_Pi2,Idle_Ramsey,Raman_Pi2_phase]
        else:
            operation_list = [Raman_Pi2,Idle_Ramsey,Raman_Pi2_phase]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
