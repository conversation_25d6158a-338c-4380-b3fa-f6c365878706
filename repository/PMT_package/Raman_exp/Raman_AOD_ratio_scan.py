import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Operation, Circuit
from waveform_manager import *


class AodRatioOperation(Operation):
    """Rabi 554 实验, 支持支持单离子的 Carrier、Red、Blue Rabi"""

    def __init__(self, qubit_index: tuple, operation_time: float, aod_ratio=1):
        super().__init__(qubit_index)

        self.operation_time = operation_time
        self.aod_ratio = aod_ratio
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        # 1. 构造波形片段
        Idle = Waveform(
            duration=self.parameters.Light_554.AOD_time_before_AOM,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        Carrier = Waveform(
            duration=self.operation_time,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )

        # 2. 波形赋值到属性
        self.AOM_R = Idle * Carrier
        self.AOM_L = Idle * Carrier

        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()

    def compute_AOD_wave(self):
        """构造 AOD 波形"""
        if len(self.qubit_index) == 2:
            # 1. 提取该离子对对应的 AOD Ratio
            amp = [self.aod_ratio / (1 + self.aod_ratio), 1 / (1 + self.aod_ratio)]
            # 2. 构造两离子的波形
            ion_i_wave = Waveform(duration=self.duration_time,
                                  amplitude=amp[0],
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]])
            ion_j_wave = Waveform(duration=self.duration_time,
                                  amplitude=amp[1],
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[1]])
            temp_wave = ion_i_wave + ion_j_wave
        else:
            raise ValueError(
                f"qubit_index {self.qubit_index} involves more than two qubits."
            )
        self.AOD_L = temp_wave
        self.AOD_R = temp_wave


class RamanAODRatioScan(RamanBaseExp, EnvExperiment):
    """Raman AOD Ratio Scan"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument("X_scan_range", Scannable(default=RangeScan(0.5, 2, 21), 
                                                        global_min=0.1, 
                                                        global_max=10, 
                                                        global_step=1e-1, 
                                                        precision=6), group="Experiment")
        self.setattr_argument("operation_time",
                              NumberValue(default=1e-6, min=1e-6, step=1e-6, precision=6, unit="us"),
                              tooltip="操作时间",
                              group="Experiment")
        print("Experiment Registered: Raman AOD Ratio Scan")
        
    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        AOD_Ratio_scan = [{"gate": "AodRatioOperation",
                 "qubit_index": self.qubit_index,
                 "operation_time": self.operation_time,
                 "aod_ratio": scan_parameter}]
 
        operation_list = [AOD_Ratio_scan]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()


if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
