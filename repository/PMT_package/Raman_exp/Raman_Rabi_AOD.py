import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from waveform_manager import  Waveform
from modules.operation_manager import Circuit
from modules.operation_manager import Rama<PERSON>_Rabi

class RamanAOD(Raman_Rabi):

    def compute_AOD_wave(self):
        # 1. 提取该离子对对应的 AOD Ratio
        ratio = self.parameters.Light_554.AOD_AWG_ratio[self.qubit_index[0]][self.qubit_index[1]]
        crosstalk = 0.06
        amp = [ratio / (1 + ratio)*(1-crosstalk), 1 / (1 + ratio)*(1-crosstalk),crosstalk]
        # 2. 构造两离子的波形
        ion_i_wave = Waveform(duration=self.duration_time,
                              amplitude=amp[0],
                              phase=0,
                              detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]])
        ion_j_wave = Waveform(duration=self.duration_time,
                              amplitude=amp[1],
                              phase=0,
                              detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[1]])
        ion_x_wave = Waveform(duration=self.duration_time,
                              amplitude=amp[2],
                              phase=0,
                              detuning=self.parameters.Light_554.AOD_address_freqs[1])
        ion_x_wave_R = Waveform(duration=self.duration_time,
                              amplitude=amp[2],
                              phase=np.pi,
                              detuning=self.parameters.Light_554.AOD_address_freqs[1])

        temp_wave = ion_i_wave + ion_j_wave + ion_x_wave
        temp_wave_R = ion_i_wave + ion_j_wave + ion_x_wave_R

        self.AOD_L = temp_wave
        self.AOD_R = temp_wave_R



class RamanRabi(RamanBaseExp, EnvExperiment):
    """Raman Rabi AOD"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=10000e-6,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )

        self.setattr_argument("rabi_choice", EnumerationValue(['Red', 'Carrier', 'Blue'], default='Carrier'),
                              group="Experiment")

        self.setattr_argument(
            "side_choice",
            EnumerationValue(["Left", "Right", "All"], default="All"),
            tooltip="选择左侧/右侧/双边rabi",
            group="Experiment",
        )

        self.setattr_argument("phonon_index", NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="声子序号", group="Experiment")

        print("Experiment Registered: Raman Rabi")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-6)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        print("Start_compute_Waveform")
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Rabi = [{"gate": "RamanAOD",
                 "qubit_index": self.qubit_index,
                 "rabi_time": scan_parameter,
                 "phonon_index": self.phonon_index,
                 "rabi_choice": self.rabi_choice,
                 "side_choice": self.side_choice}]
        if self.enable_SBC:
            operation_list = [SBC, Rabi]
        else:
            operation_list = [Rabi]
        print("Start_compute_circuit")

        circuit = Circuit(operation_list)
        print("End_compute_Waveform")

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        print("prepare_compute_Waveform")

        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
