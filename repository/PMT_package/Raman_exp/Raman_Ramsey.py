import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import  Circuit
import sys


class <PERSON><PERSON><PERSON><PERSON><PERSON>(RamanBaseExp, EnvExperiment):
    """Raman Ramsey"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=1000000e-6,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )
        self.setattr_argument(
            "Ramsey_choice",
            EnumerationValue(["Carrier", "Red", "Blue"], default="Carrier"),
            tooltip="选择红/蓝边带还是载波",
            group="Experiment",
        )

        self.setattr_argument("phonon_index", NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="声子序号", group="Experiment")
        
        print("Experiment Registered: <PERSON><PERSON>")


    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) /1e-6 )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 读取pi/2时间
        pi_2_times = {
            "Carrier": self.parameter.Light_554.pi_2.Carrier[self.qubit_index[0]],
            "Red": self.parameter.Light_554.pi_2.Red,
            "Blue": self.parameter.Light_554.pi_2.Blue
        }

        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Raman_Pi2 = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "phonon_index":self.phonon_index,
                 "rabi_time": pi_2_times[self.Ramsey_choice],
                 "rabi_choice": self.Ramsey_choice}]
        Idle_Ramsey = [
            {
                "gate": "Idle",
                "qubit_index": self.qubit_index,
                "idle_time": scan_parameter
            }
        ]
        Idle = [
            {
                "gate": "Idle",
                "qubit_index": self.qubit_index,
                "idle_time": 20e-6
            }
        ]
        if self.enable_SBC:
            operation_list = [SBC, Raman_Pi2,Idle_Ramsey,Raman_Pi2]
        else:
            operation_list = [Raman_Pi2,Idle_Ramsey,Raman_Pi2]
        print("Start_circuit")
        circuit = Circuit(operation_list)
        print("prepare_waveform")

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)

        # waveform = circuit.AOM_L.generate_waveform(base_fre=self.parameter.Light_554.AOM_middle_freq
        #     + self.parameter.Light_554.AOM_freq_LR,
        #     sampling_rate=1e9,)
        # waveform2 = circuit.AOM_R.generate_waveform(
        #     base_fre=self.parameter.Light_554.AOM_middle_freq
        #              - self.parameter.Light_554.AOM_freq_LR,
        #     sampling_rate=1e9,
        # )
        # waveform3 = circuit.AOD_L.generate_waveform(
        #     base_fre=self.parameter.Light_554.AOD_middle_freq, sampling_rate=1e9
        # )
        # waveform4 = circuit.AOD_R.generate_waveform(
        #     base_fre=self.parameter.Light_554.AOD_middle_freq, sampling_rate=1e9
        # )

        # # 设置显示限制（默认是 1000，可以调大）
        # sys.setrecursionlimit(10000)  # 防止递归限制问题（可选）
        # np.set_printoptions(threshold=sys.maxsize)
        # if scan_parameter < 1955e-6 and scan_parameter > 1945e-6:
        #     with open('waveform.data', 'a') as f:
        #         # f.write("waveform:\n")
        #         # f.write(str(waveform[::1000]))
        #         # f.write(str(waveform2[::1000]))
        #         # f.write(str(waveform3[::1000]))
        #         # f.write(str(waveform4[::1000]))
        #         # f.write("waveform_end:\n")
        #         # f.write(str(waveform[-10000:]))
        #         # f.write(str(waveform2[-10000:]))
        #         f.write(str(waveform3[-100000:]))






        return circuit.duration()

    def add_point_to_list(self):
        if not self.Adaptive:
            return False
        probability = self.get_dataset("probability")[-1]
        return max(probability)>0.3 and max(probability)<0.7
    def get_new_step(self):
        return (self.scan_parameter[1]-self.scan_parameter[0])/10
    def update_x(self,scan_list):
        x_points = (np.array(scan_list)) *1e6
        self.set_dataset("x_points", x_points, broadcast=True)


if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
