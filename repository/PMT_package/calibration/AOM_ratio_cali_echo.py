import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit


class RamanRabi(RamanBaseExp, EnvExperiment):
    """AOM_ratio calibration with MW"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument("X_scan_range", Scannable(
            default=RangeScan(0.5, 2, 21), global_min=0.1, global_max=10, global_step=1e-1, precision=6
        ), group="Experiment")

        self.setattr_argument("operation_time",
                              NumberValue(default=1e-6, min=0, step=1e-6, precision=6, unit="us"),
                              tooltip="操作时间",
                              group="Experiment")

        print("Experiment Registered: Raman Rabi")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter))

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        print("Start_compute_Waveform")
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却


        MS = [{"gate": "MSOperation",
                   "qubit_index": self.qubit_index,
                   "operation_time": self.operation_time,
                    "aom_ratio": scan_parameter,
               }]
        if self.enable_SBC:
            operation_list = [SBC, MS]
        else:
            operation_list = [MS]
        print("Start_compute_circuit")

        circuit = Circuit(operation_list)
        print("End_compute_Waveform")

        # 2. 将波形导入 awg
        self.l554.prepare_waveform_without_load(circuit)
        print("prepare_compute_Waveform")
        self.l554.load_wave_to_awg()
        return circuit.duration()


    @kernel()
    def run_level_1(self, operation_time):
        """单次 repeat 中的时序"""
        for _ in range(self.repeat):
            self.l369.switch_to_cooling2()
            self.pmt.pmt_start_count()
            delay(self.cooling2_time)

            self.l369.switch_to_cooling1()
            delay(self.cooling_time)
            self.pmt.pmt_stop_count()

            self.l369.switch_to_pump()
            delay(self.pumping_time)

            # with parallel:
            self.l369.switch_to_control()
            delay(0 * ms)

            self.l554.AWG_on()
            self.wm.mw_on()
            delay(self.pi_2time)
            self.wm.mw_off()

            delay(operation_time/2)

            self.wm.mw_on()
            delay(self.pi_2time*2)
            self.wm.mw_off()

            delay(operation_time / 2)

            # delay(2*ms)

            self.wm.mw_on()
            delay(self.pi_2time)
            self.wm.mw_off()
            delay(1*ms)
            with parallel:
                self.l554.AWG_off()
                self.pmt.pmt_start_count()
                self.l369.switch_to_detect()

            delay(self.detecting_time)

            with parallel:
                self.pmt.pmt_stop_count()
                self.l369.switch_to_cool()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
