import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import  Circuit


class FreqCali(RamanBaseExp, EnvExperiment):
    """carrier frequency calibration"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10, 11),
                global_min=0,
                global_max=10000,
                global_step=1,
                precision=0,
            ),
            group="Experiment",
        )

        print("Experiment Registered:  carrier frequency calibration")

    def prepare(self):
        super().prepare()
        if len(self.qubit_index) == 1:
            self.pi2_time = self.parameter.Light_554.pi_2.Carrier[self.qubit_index[0]]
        elif len(self.qubit_index) == 2:
            self.pi2_time = self.parameter.Light_554.pi_2for2ions.Carrier

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Rabi = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "rabi_time": self.pi2_time
                 }]
        Rabi_m = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "rabi_time": self.pi2_time,
                "phase":np.pi
                 }]
        idle = [{"gate":"Idle","qubit_index":self.qubit_index,"idle_time":0.5e-6}]
        if self.enable_SBC:
            operation_list = [SBC]
        else:
            operation_list = [idle]
        for i in range(int(scan_parameter)):
            operation_list.append(idle)
            operation_list.append(Rabi)
            operation_list.append(idle)
            operation_list.append(Rabi_m)

        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
