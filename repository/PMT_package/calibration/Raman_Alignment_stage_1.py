import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Operation, Circuit
from modules.waveform_manager import Waveform
from modules.pmt import PMT
from auto_calitulation.fit_model import fit_gaussian, gausssion_derivative
import xmlrpc.client
import time



class RamanAlignmentStage1(RamanBaseExp, EnvExperiment):
    """Raman Alignment Stage 1"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 100, 5),
                global_min=0,
                global_max=100,
                global_step=1,
                precision=0,
            ),
            group="Experiment",
        )

        self.setattr_argument("operation_time", NumberValue(default=3e-6, min=0, step=1e-6, precision=8),
                              tooltip="Rabi 操作的时间", group="Experiment")

        self.setattr_argument("scan_axis", NumberValue(0, min=0, step=1, max=3, precision=0), tooltip="扫描位移台的轴, (0, 1, 2, 3)", group="Experiment")
        print("Experiment Registered: Raman <PERSON>")

    def prepare(self):
        super().prepare()
        # 1. 连接位移台
        self.proxy_NewPort = xmlrpc.client.ServerProxy(
            "http://***************:8001", allow_none=True
        )
        self.scan_step_init = 50 # 一阶段扫描的步长


    @rpc(flags={""})
    def move_motor(self, step):
        """移动位移台
        
        step: 移动的步数, 可正可负, 以规定左右方向
        """
        print(step)
        a = self.proxy_NewPort.call(
            "8742 103525_2", "move_relative", int(self.scan_axis), int(step)
        )
        print("move motor", step)
        # 等待位移台移动完成
        time.sleep(0.005 * abs(step) + 0.1)


    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.scan_parameter = [int(x) for x in self.scan_parameter]
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter))

    @rpc(flags={""})
    def data_process(self, scan_point):
        """数据处理的逻辑"""
        self.pmt.data_process_for_x_scan_check_ion(scan_point)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Rabi = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "rabi_time": self.operation_time}]
        if self.enable_SBC:
            operation_list = [SBC, Rabi]
        else:
            operation_list = [Rabi]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()


    @kernel()
    def run_level_3(self, scan_list):
        """N 个扫描点"""
        # 二阶段: 自适应迭代寻找峰值  
        # 1.1 跑实验
        self.scan_point = 0
        while self.scan_point < len(scan_list):
            self.move_motor(scan_list[self.scan_point])
            self.break_realtime()
            self.run_level_2(self.operation_time)
            # 添加优雅中止
            if self.scheduler.check_termination():
                break


if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
