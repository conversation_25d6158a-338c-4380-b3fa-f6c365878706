import numpy as np
from artiq.experiment import *
from modules.pmt import PMT
from modules.config import LOADED_PARAMETER
from modules.light_369 import Light369
from modules.signal_mw import SignalMW


class EIT_pi_scan(EnvExperiment):
    """EIT_pi_scan_with_pump

    用途:
    1. 扫描 Pumping 时间, 找到最优 Pumping 时间;
    2. 一维扫描类型实验的模板;
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.cooling2_time = self.parameter.Experiment.Cooling2_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.idle_time = self.parameter.Light_554.AOD_time_before_AOM
        self.eit_time = self.parameter.Experiment.EIT_Cooling_Time
        self.eit2_time = self.parameter.Experiment.EIT_Cooling2_Time
        self.dds_for_EIT_pi_amp = self.parameter.Light_369.dds_for_EIT_pi_amp
        self.pi_2time = self.parameter.Signal_MW.mw_pi_2.zero

        # 2. 模块准备
        self.setattr_device("core")
        self.pmt = PMT(self)
        self.l369 = Light369(self)
        self.wm = SignalMW(self)

        # 3. 变量设置
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(130*1e6, 170*1e6, 100),
                global_min=90*1e6,
                global_max=180*1e6,
                global_step=1e6,
                precision=3,
                unit="MHz",
            ),
            group="Experiment",
        )
        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="扫描任务的重复次数",
            group="Experiment",
        )
        print("Experiment Registered: Pumping Test ")

    def prepare(self):
        self.scan_parameter = self.X_scan_range.sequence  # 变量 list
        self.scan_points = len(self.scan_parameter)  # 数据点数
        print("Prepare Done")

    @kernel()
    def run(self):
        # 准备 X 轴数据集
        self.pmt.prepare_for_x_scan(
            np.array(self.scan_parameter) / 1e6
        )
        # 0. initial
        self.core.reset()
        self.pmt.initial()
        self.l369.initial()
        # 1. 给方波信号
        for task_num in range(self.task_num):
            for scan_point in range(self.scan_points):
                self.l369.dds_for_EIT_pi.set(
                    frequency=self.scan_parameter[scan_point],
                    phase=0.0,
                    amplitude=self.dds_for_EIT_pi_amp)
                for repeat in range(self.repeat):
                    # 1 . 切换至 cooling 状态
                    self.l369.switch_to_cooling1()
                    delay(self.cooling_time)
                    self.l369.switch_to_cooling2()
                    delay(self.cooling2_time)
                    # for i in range(5):
                    #     self.l369.switch_to_pump()
                    #     delay(self.pumping_time)
                    #     self.l369.switch_to_control()
                    #     self.wm.mw_on()
                    #     delay(self.pi_2time*2)
                    #     self.wm.mw_off()
                    #     # 2. 切换至 eit 状态
                    #     self.l369.switch_to_eit()
                    #     with parallel:
                    #         self.l369.EOM_cooling_sw.off()
                    #         self.l369.EOM_41_V1.off()
                    #         self.l369.EOM_21_CTRL.off()
                    #     delay(0.1*ms)

                    self.l369.switch_to_pump()
                    delay(self.pumping_time)
                    self.l369.switch_to_control()
                    self.wm.mw_on()
                    delay(self.pi_2time*2)
                    self.wm.mw_off()
                    # 2. 切换至 eit 状态
                    self.l369.switch_to_eit()
                    with parallel:
                        self.l369.EOM_cooling_sw.off()
                        self.l369.EOM_41_V1.off()
                        self.l369.EOM_21_CTRL.off()
                        self.pmt.pmt_start_count()
                    delay(0.2*ms)
                    # 5. 切换至 cooling 状态
                    with parallel:
                        self.pmt.pmt_stop_count()
                        self.l369.switch_to_cool()

                self.core.wait_until_mu(now_mu())
                delay(10 * ms)
                self.pmt.data_process_for_x_scan_counts(scan_point)