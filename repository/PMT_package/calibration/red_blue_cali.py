import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import  Circuit,MSOperation,Idle,Operation
from waveform_manager import Waveform


class MSOneSide(MSOperation,Operation):
    def __init__(self,**kwargs):
        super().__init__(**kwargs)
    def compute_AOD_wave(self):
        temp_wave = Waveform(
            duration=self.duration_time,
            amplitude=1.0,
            phase=0,
            detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]],
        )
        temp_wave_L = Waveform(
            duration=self.duration_time,
            amplitude=0.0,
            phase=0,
            detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]],
        )
        self.AOD_L = temp_wave_L
        self.AOD_R = temp_wave
class IdleOneSide(Idle,Operation):
    def __init__(self,**kwargs):
        super().__init__(**kwargs)
    def compute_AOD_wave(self):
        temp_wave = Waveform(
            duration=self.duration_time,
            amplitude=1.0,
            phase=0,
            detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]],
        )
        temp_wave_L = Waveform(
            duration=self.duration_time,
            amplitude=0.0,
            phase=0,
            detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]],
        )
        self.AOD_L = temp_wave_L
        self.AOD_R = temp_wave
class RamanRamsey(RamanBaseExp, EnvExperiment):
    """calibrate R/B ratio : Shift"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=1000000e-6,
                global_step=1e-6,
                precision=2,
                unit="us",
            ),
            group="Experiment",
        )
        self.setattr_argument(
            "Ramsey_choice",
            EnumerationValue(["Carrier", "Red", "Blue"], default="Carrier"),
            tooltip="选择红/蓝边带还是载波",
            group="Experiment",
        )

        self.setattr_argument("phonon_index", NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="声子序号", group="Experiment")

        print("Experiment Registered: Raman Ramsey")


    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) /1e-6 )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 读取pi/2时间
        pi_2_times = {
            "Carrier": self.parameter.Light_554.pi_2.Carrier[self.qubit_index[0]],
            "Red": self.parameter.Light_554.pi_2.Red,
            "Blue": self.parameter.Light_554.pi_2.Blue
        }

        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Raman_Pi2 = [{"gate": "Raman_Rabi",
                      "qubit_index": self.qubit_index,
                      "phonon_index":self.phonon_index,
                      "rabi_time": pi_2_times[self.Ramsey_choice],
                      "rabi_choice": self.Ramsey_choice}]
        Idle_MS_Ramsey = [
            {
                "gate": "MSOneSide",
                "qubit_index": self.qubit_index,
                "operation_time": scan_parameter,
                "aom_amp":0.0
            }

        ]
        Idle_=[{"gate": "Idle",
                                  "qubit_index": self.qubit_index,
                                  "idle_time": 0.5e-6
                                  }]
        Idle_AOD = [{"gate": "Idle",
                                  "qubit_index": self.qubit_index,
                                  }]
        Idle_MS = [{"gate": "IdleOneSide",
                     "qubit_index": self.qubit_index,
                     }]
        if self.enable_SBC:
            operation_list = [SBC, Raman_Pi2,Idle_MS_Ramsey,Raman_Pi2]
        else:
            operation_list = [Raman_Pi2,Idle_MS,Idle_MS_Ramsey,Idle_AOD,Raman_Pi2]
        print("Start_circuit")
        try:
            circuit = Circuit(operation_list)
        except Exception as e:
            print(e)
        print("prepare_waveform")

        # 2. 将波形导入 awg

        self.l554.prepare_waveform(circuit)

        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
