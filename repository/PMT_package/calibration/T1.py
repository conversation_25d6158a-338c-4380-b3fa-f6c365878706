import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit


class RamanRabi(RamanBaseExp, EnvExperiment):
    """T1 test"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10e-6, 21),
                global_min=0,
                global_max=100000e-6,
                global_step=1e-6,
                precision=2,
                unit="ms",
            ),
            group="Experiment",
        )


        print("Experiment Registered: T1")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) / 1e-3)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:

        return scan_parameter

    @kernel()
    def run_level_1(self, operation_time):
        """单次 repeat 中的时序"""
        for _ in range(self.repeat):
            self.l369.switch_to_cooling2()
            self.pmt.pmt_start_count()
            delay(self.cooling2_time)

            self.l369.switch_to_cooling1()
            delay(self.cooling_time)
            self.pmt.pmt_stop_count()


            # for i in range(5):
            #     self.l369.switch_to_pump()
            #     delay(self.pumping_time)
            #     self.l369.switch_to_control()

            #     # 2. 切换至 eit 状态
            #     self.l369.switch_to_eit()
            #     with parallel:
            #         self.l369.EOM_cooling_sw.off()
            #         self.l369.EOM_41_V1.off()
            #         self.l369.EOM_21_CTRL.off()
            #     delay(0.1*ms)
            # delay(0.2*ms)
            # delay(0.2*ms)
            self.l369.switch_cool_to_eit()
            delay(self.eit_time)
            self.l369.switch_eit_to_eit2()
            delay(self.eit2_time)

            self.l369.switch_to_pump()
            delay(self.pumping_time)

            # with parallel:
            self.l369.switch_to_control()
            delay(0 * ms)
            self.wm.mw_on()
            delay(self.pi_2time*2)
            self.wm.mw_off()

            with parallel:
                self.l369.sbc_cooling(self.enable_SBC)
                delay(operation_time)

            with parallel:
                self.pmt.pmt_start_count()
                self.l369.switch_to_detect()

            delay(self.detecting_time)

            with parallel:
                self.pmt.pmt_stop_count()
                self.l369.switch_to_cool()

if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
