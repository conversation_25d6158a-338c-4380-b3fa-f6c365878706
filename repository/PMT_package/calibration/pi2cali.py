import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import  Circuit


class Pi2Cali(RamanBaseExp, EnvExperiment):
    """pi_2 time calibration"""
    def build(self):
        # 0. base build
        super().build()

        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 10, 11),
                global_min=0,
                global_max=10000,
                global_step=1,
                precision=0,
            ),
            group="Experiment",
        )

        print("Experiment Registered: Rabi Pi/2 calibration")

    def prepare(self):
        super().prepare()
        if len(self.qubit_index) == 1:
            self.pi2_time = self.parameter.Light_554.pi_2.Carrier[self.qubit_index[0]]
        elif len(self.qubit_index) == 2:
            self.pi2_time = self.parameter.Light_554.pi_2for2ions.Carrier

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan(np.array(self.scan_parameter) )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Rabi = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "rabi_time": self.pi2_time
                 }]
        idle = [{"gate":"Idle","qubit_index":self.qubit_index,"idle_time":0.5e-6}]
        if self.enable_SBC:
            operation_list = [SBC,Rabi]
        else:
            operation_list = [idle,Rabi]
        for i in range(int(scan_parameter)):
            operation_list.append(idle)
            operation_list.append(Rabi)

        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()



if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
