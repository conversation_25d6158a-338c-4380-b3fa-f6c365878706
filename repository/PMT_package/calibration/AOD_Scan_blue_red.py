import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Operation, Circuit
from modules.waveform_manager import Waveform
from repository.MS_gate_exp.AOM_ratio import AOM_ratio_PMT
from modules.operation_manager import MSOperation

class AOD_Scan(MSOperation):
    """
    AOD Scan 波形构造
    """

    def __init__(
        self,
        qubit_index: tuple,
        aod_fre: float,
        rabi_choice="Blue",
        operation_time: float = 2e-6,
    ):
        """

        Parameters
        ----------
        qubit_index: 离子索引
        aod_fre: aod 扫描频率, 主要变量
        rabi_choice: 边带选择
        operation_time: 操作时间
        """
        super().__init__(qubit_index,rabi_choice=rabi_choice,operation_time=operation_time)
        self.aod_fre = aod_fre


    def compute_AOM_wave(self, algorithm=None):
        """构造 AOD scan 的 AOM 波形"""
        # 1. 创建 AOM 波形
        super().compute_AOM_wave()
        Idle = Waveform(
            duration=self.parameters.Light_554.AOD_time_before_AOM,
            amplitude=0,
            phase=0,
            detuning=0,
        )


        # 2. 波形赋值到属性
        self.AOM_L = Idle * self.AOM_L
        self.AOM_R = Idle * self.AOM_R

        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()

    def compute_AOD_wave(self):
        """构造 AOD scan 的, AOD 波形
        注意失谐为 aod_fre, 在初始化阶段传进来的扫描参数
        """
        # 1. 构造波形
        AOD_wave = Waveform(
            duration=self.duration_time, amplitude=1.0, detuning=self.aod_fre
        )

        # 2. 波形到属性
        self.AOD_L = AOD_wave
        self.AOD_R = AOD_wave


class RamanAODScan(RamanBaseExp, EnvExperiment):
    """Raman AOD Scan"""
    def build(self):
        # 0. base build
        super().build()
        self.pmt = AOM_ratio_PMT(self)
        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(-1e6, 1e6, 30),
                global_min=-25e6,
                global_max=25e6,
                global_step=0.1e6,
                precision=2,
                unit="kHz",
            ),
            group="Experiment",
        )
        self.setattr_argument("ion_index",
                              NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="校准离子编号",
                              group="Experiment")
        self.setattr_argument(
            "operation_time",
            NumberValue(default=3e-6, min=1e-6, step=1e-6,unit="us", precision=8),
            tooltip="操作的时间",
            group="Experiment",
        )
        print("Experiment Registered: Raman AOD Scan blue/red")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def data_process(self, scan_poit):
        self.pmt.data_process_for_x_scan_aomratio(scan_poit, self.ion_index)
    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan((np.array(self.scan_parameter) + self.parameter.Light_554.AOD_middle_freq) / 3)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter,rabi_choice = "Blue") -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        AOD_Scan_Operation = AOD_Scan(qubit_index=(0,),
                                      aod_fre=scan_parameter,
                                      operation_time=self.operation_time,
                                      rabi_choice=rabi_choice)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(AOD_Scan_Operation)
        return AOD_Scan_Operation.duration()


    @kernel()
    def run_level_1(self, operation_time):
        """单次 repeat 中的时序"""
        for _ in range(self.repeat):
            delay(self.cooling_time)

            self.l369.switch_cool_to_eit()
            delay(self.eit_time)

            self.l369.switch_eit_to_pump()
            delay(self.pumping_time)

            with parallel:
                self.l369.switch_pump_to_control()
                self.l554.AWG_on()

            with parallel:
                self.l369.sbc_cooling(self.enable_SBC)
                delay(operation_time)

            with parallel:
                self.l554.AWG_off()
                self.pmt.pmt_start_count()
                self.l369.switch_control_to_detect()

            delay(self.detecting_time)

            with parallel:
                self.pmt.pmt_stop_count()
                self.l369.switch_detect_to_cool()

    @kernel()
    def run_level_2(self, scan_parameter):
        """加入一个扫描点的波形计算和数据处理"""
        while True:
            # 红边带
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter,rabi_choice="Red")

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1(operation_time)

            self.core.wait_until_mu(now_mu())

            # 蓝边带
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter, rabi_choice="Blue")

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1(operation_time)

            self.core.wait_until_mu(now_mu())

            # 3. 数据处理
            self.data_process(self.scan_point)

            # 4. 离子状态检查

            if self.check_lost():
                self.save_ions()  # 封装救离子的逻辑
            else:
                self.scan_point += 1
                break
            # 添加优雅中止
            if self.scheduler.check_termination():
                break

    def analyze(self):
        pass
