import numpy as np
from artiq.experiment import *
from sympy.stats.rv import probability

from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Operation, Circuit
from waveform_manager import *
from modules.pmt import PMT

class AOD_ratio_PMT(PMT):
    def prepare_for_x_scan_aodratio(self, x_points):
        """one dimension scan"""
        self.prepare_for_x_scan(x_points)
        self.set_dataset("probability_all", [], broadcast=True)

    @rpc(flags={"async"})
    def data_process_for_x_scan_aodratio(self, scan_point, data_before_len):
        # 1. 读取数据
        data_temp_all = self.read_data_double()

        # 2. 处理Cooling数据，判断离子是否丢失
        data_temp_cooling = data_temp_all[0::2, :]
        # print(data_temp_cooling)
        self.cooling_count_data_process(data_temp_cooling)

        # 4. 处理Detect数据
        data_temp = data_temp_all[1::2, :]

        # 5.PMT32通道数据
        pmt_data_32 = np.sum(data_temp, 0)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)

        # 7. 各离子激发概率计算
        probability = self.each_ion_data_process(data_temp, scan_point)
        data_len = len(self.get_dataset("probability_all"))
        if data_len <= scan_point+data_before_len:
            self.append_to_dataset("probability_all", probability)
        else:
            self.mutate_dataset("probability_all", scan_point+data_before_len, probability)


class AodRatioOperation(Operation):
    """Aod ratio operation"""

    def __init__(self, qubit_index: tuple, operation_time: float, aod_ratio=1):
        super().__init__(qubit_index)

        self.operation_time = operation_time
        self.aod_ratio = aod_ratio
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        # 1. 构造波形片段
        Idle = Waveform(
            duration=self.parameters.Light_554.AOD_time_before_AOM,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        Carrier = Waveform(
            duration=self.operation_time,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )

        # 2. 波形赋值到属性
        self.AOM_R = Idle * Carrier
        self.AOM_L = Idle * Carrier

        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()

    def compute_AOD_wave(self):
        """构造 AOD 波形"""
        if len(self.qubit_index) == 2:
            # 1. 提取该离子对对应的 AOD Ratio
            amp = [self.aod_ratio / (1 + self.aod_ratio), 1 / (1 + self.aod_ratio)]
            # 2. 构造两离子的波形
            ion_i_wave = Waveform(duration=self.duration_time,
                                  amplitude=amp[0],
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]])
            ion_j_wave = Waveform(duration=self.duration_time,
                                  amplitude=amp[1],
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[1]])
            temp_wave = ion_i_wave + ion_j_wave
        else:
            raise ValueError(
                f"qubit_index {self.qubit_index} involves more than two qubits."
            )
        self.AOD_L = temp_wave
        self.AOD_R = temp_wave


class RamanAODRatioScan(RamanBaseExp, EnvExperiment):
    """Raman AOD Ratio Scan"""
    def build(self):
        # 0. base build
        super().build()
        self.pmt = AOD_ratio_PMT(self)
        # 1. 参数设置组件
        self.setattr_argument("X_scan_range", Scannable(default=RangeScan(0.5, 2, 21),
                                                        global_min=0.1,
                                                        global_max=10,
                                                        global_step=1e-1,
                                                        precision=6), group="Experiment")
        self.setattr_argument("operation_time",
                              NumberValue(default=1e-6, min=1e-6, step=1e-6, precision=6, unit="us"),
                              tooltip="操作时间",
                              group="Experiment")
        print("Experiment Registered: Raman AOD Ratio Scan")

    def prepare(self):
        super().prepare()

    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_x_scan_aodratio(np.array(self.scan_parameter) )

    @rpc(flags={""})
    def wave_compute(self, scan_parameter,ion_1,ion_2) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        AOD_Ratio_scan = [{"gate": "AodRatioOperation",
                           "qubit_index": (ion_1,ion_2),
                           "operation_time": self.operation_time,
                           "aod_ratio": scan_parameter}]

        operation_list = [AOD_Ratio_scan]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()
    def data_process(self, scan_point,data_before_len):
        self.pmt.data_process_for_x_scan_aodratio(scan_point,data_before_len)
    @kernel()
    def run_level_3(self, scan_list):
        """N 个扫描点"""
        N_before = 0
        for i in range(len(self.qubit_index)):
            for j in range(i+1,len(self.qubit_index)):
                i_ion = self.qubit_index[i]
                j_ion = self.qubit_index[j]
                self.scan_point = 0
                while self.scan_point < len(scan_list):
                    self.run_level_2(scan_list[self.scan_point],qubit_1 = i_ion,qubit_2 = j_ion,data_before_len = len(scan_list)*N_before)

                    # 添加优雅中止
                    if self.scheduler.check_termination():
                        break
                N_before += 1
                if self.scheduler.check_termination():
                    break
            if self.scheduler.check_termination():
                break
    @kernel()
    def run_level_2(self, scan_parameter,qubit_1,qubit_2,data_before_len ):
        """加入一个扫描点的波形计算和数据处理"""
        while True:
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter,qubit_1,qubit_2)
            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1(operation_time)

            self.core.wait_until_mu(now_mu())

            # 3. 数据处理
            self.data_process(self.scan_point,data_before_len)

            # 4. 离子状态检查

            if self.check_lost():
                self.save_ions()  # 封装救离子的逻辑
            else:
                self.scan_point += 1
                break
            # 添加优雅中止
            if self.scheduler.check_termination():
                break
if __name__ == "__main__":
    from artiq.frontend import artiq_run
    artiq_run.run()
