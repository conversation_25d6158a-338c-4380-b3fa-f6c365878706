import numpy as np
from artiq.experiment import *
from modules.raman_base_exp import RamanBaseExp
from modules.operation_manager import Circuit
from modules.pmt import PMT

class HetingRatePMT(PMT):

    def data_process_for_heating_rate(self,scan_point):
        background_counts = np.array(self.get_dataset("background_counts"))
        data_temp_all = self.read_data_double()
        data_temp = data_temp_all[0::2,:]

        # #
        # pmt_data_32 = np.sum(data_temp, 0)
        # self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)
        #
        # sum_all_red = np.sum(pmt_data_32)
        #
        # #
        # print("data_temp",data_temp)
        select_channel_counts_red = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels
                                                          )
        # print("select_channel_counts_red",select_channel_counts_red)
        # counts_all_red = np.sum(np.sum(select_channel_counts_red, axis=0))
        print("select_channel_counts_red:",select_channel_counts_red)
        print("select_channel_counts_red:",np.sum(select_channel_counts_red, axis=0))
        counts_all_red = np.sum(np.sum(select_channel_counts_red, axis=0))
        # print("counts_all_red",counts_all_red)

        counts_non_background_counts_red = counts_all_red - background_counts

        data_temp_all = self.read_data_double()
        data_temp = data_temp_all[0::2,:]

        select_channel_counts_blue = self._sum_multi_channels(data_temp,
                                                             self.parameter.PMT.Select_channels
                                                             )

        counts_all_blue = np.sum(np.sum(select_channel_counts_blue, axis=0))

        counts_non_background_counts_blue = counts_all_blue - background_counts

        n_bar = counts_non_background_counts_red/(counts_non_background_counts_blue-counts_non_background_counts_red)
        data_len = len(self.get_dataset("probability"))
        if data_len <= scan_point:
            self.append_to_dataset("probability", [n_bar])
            self.append_to_dataset("count_red", counts_all_red)
            self.append_to_dataset("count_blue", counts_all_blue)
        else:
            self.mutate_dataset("probability", scan_point, [n_bar])
            self.mutate_dataset("count_red", scan_point, counts_non_background_counts_red)
            self.mutate_dataset("count_blue", scan_point, counts_non_background_counts_blue)

        pmt_data_32 = np.sum(data_temp, 0)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)
    def prepare_for_heating_rate(self, x_points):
        self.prepare_for_x_scan(x_points)

        self.set_dataset("count_red", [], broadcast=True)
        self.set_dataset("count_blue", [], broadcast=True)

        self.set_dataset("background_counts",0,broadcast=True)

    def pre_detect_processing(self):

        data = self.read_data()
        select_channels_counts = self._sum_multi_channels(data,
                                                          self.parameter.PMT.Select_channels)
        counts = np.sum(np.sum(select_channels_counts, axis=0))
        self.set_dataset("background_counts",counts,broadcast=True)


class HeatingRate(RamanBaseExp, EnvExperiment):
    """ Phonon number """

    def build(self):
        super().build()
        self.pmt = HetingRatePMT(self)
        # 1. 参数设置组件
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(0, 6, 7),
                global_min=0,
                global_max=1000,
                global_step=1,
                precision=0
            ),
            group="Experiment",
        )

        self.setattr_argument(
            "side_choice",
            EnumerationValue(["Left", "Right", "All"], default="All"),
            tooltip="选择左侧/右侧/双边rabi",
            group="Experiment",
        )

        self.setattr_argument("operation_time", NumberValue(default=0, min=0, step=1e-6, precision=2,unit="us"),
                              tooltip="操作时间", group="Experiment")



    @kernel()
    def run_level_3(self, scan_list):
        self.scan_point = 0
        while self.scan_point < len(scan_list):

            self.core.wait_until_mu(now_mu())
            self.run_background_count()
            self.pmt.pre_detect_processing()

            self.run_level_2(scan_list[self.scan_point])

            if self.scheduler.check_termination():
                break

    @kernel()
    def run_level_2(self, scan_parameter):
        """加入一个扫描点的波形计算和数据处理"""
        while True:
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter,"Red")

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1(operation_time)

            self.core.wait_until_mu(now_mu())

            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter,"Blue")

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1(operation_time)

            self.core.wait_until_mu(now_mu())

            # 3. 数据处理

            self.data_process(self.scan_point)

            # 4. 离子状态检查

            if self.check_lost():
                self.save_ions()  # 封装救离子的逻辑
            else:
                self.scan_point += 1
                break
            # 添加优雅中止
            if self.scheduler.check_termination():
                break
    @kernel()
    def run_background_count(self):
        self.core.break_realtime()
        for i in range(self.repeat):

            delay(self.cooling_time)

            self.l369.switch_cool_to_eit()
            delay(self.eit_time)

            self.l369.switch_eit_to_pump()
            delay(self.pumping_time)

            # with parallel:
            self.l369.switch_pump_to_control()

            with parallel:
                self.pmt.pmt_start_count()
                self.l369.switch_control_to_detect()

            delay(self.detecting_time)

            with parallel:
                self.pmt.pmt_stop_count()
                self.l369.switch_detect_to_cool()
    @rpc(flags={""})
    def prepare_dataset(self):
        # 2. 准备实验数据集
        self.pmt.prepare_for_heating_rate(np.array(self.scan_parameter))
    @rpc(flags={""})
    def data_process(self, scan_point):
        self.pmt.data_process_for_heating_rate(scan_point)
        # self.pmt.data_process_for_x_scan(scan_point)
    @rpc(flags={""})
    def wave_compute(self, scan_parameter,rabi_choice) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}] # 边带冷却
        Rabi = [{"gate": "Raman_Rabi",
                 "qubit_index": self.qubit_index,
                 "rabi_time": self.operation_time,
                 "phonon_index": int(scan_parameter),
                 "rabi_choice": rabi_choice,
                 "side_choice": self.side_choice}]
        if self.enable_SBC:
            operation_list = [SBC, Rabi]
        else:
            operation_list = [Rabi]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()