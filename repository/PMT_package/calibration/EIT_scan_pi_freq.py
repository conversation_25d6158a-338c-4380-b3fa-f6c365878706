import numpy as np
from artiq.experiment import *
from modules.pmt import PMT
from modules.config import LOADED_PARAMETER
from modules.light_369 import Light369


class EIT_pi_scan(EnvExperiment):
    """EIT_pi_scan

    用途:
    1. 扫描 Pumping 时间, 找到最优 Pumping 时间;
    2. 一维扫描类型实验的模板;
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.idle_time = self.parameter.Light_554.AOD_time_before_AOM
        self.eit_time = self.parameter.Experiment.EIT_Cooling_Time
        self.eit2_time = self.parameter.Experiment.EIT_Cooling2_Time

        self.dds_for_EIT_pi_amp = self.parameter.Light_369.dds_for_EIT_pi_amp

        # 2. 模块准备
        self.setattr_device("core")
        self.setattr_device("scheduler")
        self.pmt = PMT(self)
        self.l369 = Light369(self)

        # 3. 变量设置
        self.setattr_argument(
            "X_scan_range",
            Scannable(
                default=RangeScan(130*1e6, 170*1e6, 100),
                global_min=90*1e6,
                global_max=180*1e6,
                global_step=1e6,
                precision=3,
                unit="MHz",
            ),
            group="Experiment",
        )
        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="扫描任务的重复次数",
            group="Experiment",
        )
        print("Experiment Registered: Pumping Test ")

    def prepare(self):
        self.scan_parameter = self.X_scan_range.sequence  # 变量 list
        self.scan_points = len(self.scan_parameter)  # 数据点数
        print("Prepare Done")
    @rpc(flags={""})
    def set_rid(self):
        # 读取当前实验rid
        rid = int(list(self.scheduler.get_status().keys())[0])
        self.set_dataset('rid',rid,broadcast=True)
    @kernel()
    def run(self):

        # 准备 X 轴数据集
        self.pmt.prepare_for_x_scan(
            np.array(self.scan_parameter) / 1e6
        )
        self.set_rid()
        # 0. initial
        self.core.reset()
        self.pmt.initial()
        self.l369.initial()
        # 1. 给方波信号
        for task_num in range(self.task_num):
            for scan_point in range(self.scan_points):
                for repeat in range(self.repeat):
                    # 1 . 切换至 cooling 状态
                    delay(self.cooling_time)


                    self.l369.dds_for_EIT_pi.set(
                        frequency=self.scan_parameter[scan_point],
                        phase=0.0,
                        amplitude=self.dds_for_EIT_pi_amp)

                    # 2. 切换至 eit 状态
                    with parallel:
                        self.pmt.pmt_start_count()
                        self.l369.switch_cool_to_eit()
                    delay(0.5*ms)
                    # 5. 切换至 cooling 状态
                    with parallel:
                        self.pmt.pmt_stop_count()
                        self.l369.switch_eit_to_pump()
                    self.l369.switch_pump_to_detect()
                    self.l369.switch_detect_to_cool()

                self.core.wait_until_mu(now_mu())
                delay(10 * ms)
                self.pmt.data_process_for_x_scan_counts(scan_point)