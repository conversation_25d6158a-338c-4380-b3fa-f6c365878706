"""

rabi_choice选择carrier为MS扫时间 ，选择red/blue为两侧AOM同时打红/蓝边带
"""

from modules.operation_manager import Circuit
from modules.raman_base_exp import RamanBaseExp
from modules.pmt import PMT
from artiq.experiment import *
import numpy as np


class AOM_ratio_PMT(PMT):
    def prepare_for_x_scan_aomratio(self, x_points):
        """one dimension scan"""
        self.prepare_for_x_scan(x_points)
        self.set_dataset("probability_all", [], broadcast=True)

    @rpc(flags={"async"})
    def data_process_for_x_scan_aomratio(self, scan_point,ion_index = 0,data_before_len=0):
        data_temp_2 = self.read_data_double()

        half_length = int(len(data_temp_2)/2)
        print(data_temp_2)
        data_temp = data_temp_2[0:half_length]

        pmt_data_32 = np.sum(data_temp, 0)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)
        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num
        threshold_data = self._threshold_sum(select_channels_counts, self.parameter.PMT.Detect_threshold)  # 1 * ion_num
        probability1 = (threshold_data / self.parameter.Experiment.Repeat).tolist()[0]  #
        data_temp2 = data_temp_2[half_length+1:]

        pmt_data_32 = np.sum(data_temp2, 0)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)
        select_channels_counts = self._sum_multi_channels(data_temp2,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num
        threshold_data = self._threshold_sum(select_channels_counts, self.parameter.PMT.Detect_threshold)  # 1 * ion_num
        probability2 = (threshold_data / self.parameter.Experiment.Repeat).tolist()[0]  #

        probability = [probability1[ion_index],probability2[ion_index]]
        data_len = len(self.get_dataset("probability"))
        if data_len <= scan_point :
            self.append_to_dataset("probability", probability)

        else:
            self.mutate_dataset("probability", scan_point, probability)

        data_len = len(self.get_dataset("probability_all"))
        if data_len <= scan_point+data_before_len:
            self.append_to_dataset("probability_all", probability)
        else:
            self.mutate_dataset("probability_all", scan_point+data_before_len, probability)

        cooling_count_sum = np.sum(select_channels_counts)
        self.set_dataset("Cooling_Count", cooling_count_sum, broadcast=True)


class AomRatio(RamanBaseExp, EnvExperiment):
    """AOM red-blue-ratio calibration
    """

    def build(self):
        # 0. base build
        super().build()

        # 1. 通道建立

        self.pmt = AOM_ratio_PMT(self)

        # 2. 参数设置组件
        self.setattr_argument("X_scan_range", Scannable(
            default=RangeScan(0.5, 2, 21), global_min=0.1, global_max=10, global_step=1e-1, precision=6
        ), group="Experiment")

        self.setattr_argument("operation_time",
                              NumberValue(default=1e-6, min=0, step=1e-6, precision=6, unit="us"),
                              tooltip="操作时间",
                              group="Experiment")

        self.setattr_argument("ion_index",
                              NumberValue(default=0, min=0, step=1, precision=0),
                              tooltip="操作时间",
                              group="Experiment")

        print("Experiment Registered: AOM red/blue ratio scan")

    def prepare(self):
        """prepare"""
        super().prepare()
        self.ion = 0

    @rpc(flags={""})
    def prepare_dataset(self):
        self.pmt.prepare_for_x_scan_aomratio(self.scan_parameter)

    @rpc(flags={""})
    def data_process(self, scan_poit,ion_index,data_before_len):
        self.pmt.data_process_for_x_scan_aomratio(scan_poit,ion_index,data_before_len=data_before_len)

    @rpc(flags={""})
    def wave_compute(self, scan_parameter,rabi_choice,ion_index) -> TFloat:
        """传入参数计算波形"""
        # 1. 定义操作
        SBC = [{"gate": "SBC", "qubit_index": (-1,)}]  # 边带冷却
        MS = [{"gate": "MSOperation",
               "qubit_index": (ion_index,ion_index),
               "operation_time":self.operation_time,
               "aom_ratio":scan_parameter,
               "rabi_choice": rabi_choice}]
        if self.enable_SBC:
            operation_list = [SBC, MS]
        else:
            operation_list = [MS]
        circuit = Circuit(operation_list)

        # 2. 将波形导入 awg
        self.l554.prepare_waveform(circuit)
        return circuit.duration()

    @kernel()
    def run_level_1(self, operation_time):
        """单次 repeat 中的时序"""
        for _ in range(self.repeat):
            delay(self.cooling_time)

            self.l369.switch_cool_to_eit()
            delay(self.eit_time)

            self.l369.switch_eit_to_pump()
            delay(self.pumping_time)

            with parallel:
                self.l369.switch_pump_to_control()
                self.l554.AWG_on()

            with parallel:
                self.l369.sbc_cooling(self.enable_SBC)
                delay(operation_time)

            with parallel:
                self.l554.AWG_off()
                self.pmt.pmt_start_count()
                self.l369.switch_control_to_detect()

            delay(self.detecting_time)

            with parallel:
                self.pmt.pmt_stop_count()
                self.l369.switch_detect_to_cool()

    @kernel()
    def run_level_2(self, scan_parameter,ion_index,data_before_len):
        """加入一个扫描点的波形计算和数据处理"""
        while True:
            # 红边带
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter,rabi_choice="Red",ion_index=ion_index)

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1(operation_time)

            self.core.wait_until_mu(now_mu())

            # 蓝边带
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter, rabi_choice="Blue",ion_index=ion_index)

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1(operation_time)

            self.core.wait_until_mu(now_mu())

            # 3. 数据处理
            self.data_process(self.scan_point,ion_index,data_before_len)
            # 4. 离子状态检查

            if self.check_lost():
                self.save_ions()  # 封装救离子的逻辑
            else:
                self.scan_point += 1
                break
            # 添加优雅中止
            if self.scheduler.check_termination():
                break
    @kernel()
    def run_level_3(self, scan_list):
        """N 个扫描点"""
        N_before = 0
        for ion in self.qubit_index:
            self.scan_point = 0
            data_before_len = N_before * len(scan_list)
            while self.scan_point < len(scan_list):
                self.run_level_2(scan_list[self.scan_point],ion,data_before_len)
                # 添加优雅中止
                if self.scheduler.check_termination():
                    break
            N_before+=1
            if self.scheduler.check_termination():
                break
    def analyze(self):
        pass
