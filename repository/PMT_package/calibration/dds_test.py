
from artiq.experiment import *
from modules.signal_mw import *

class DDSTest(EnvExperiment):
    """DDSTest """
    def build(self):
        self.parameter = LOADED_PARAMETER()
        self.setattr_device("core")
        self.setattr_device("core_dma")
        self.mw = SignalMW(self)

    @kernel
    def record_wave(self):
        with self.core_dma.record("1"):
            self.mw.dds_for_mw.set(0.0,0.0,0.0,phase_mode = 2)
    @kernel
    def run(self):
        self.core.reset()
        self.mw.initial()
        self.core.break_realtime()

        # dds.set*100
        start_time = self.core.get_rtio_counter_mu()
        self.mw.dds_for_mw.set(0.0,0.0,0.0,phase_mode = 2)

        stop_time = self.core.get_rtio_counter_mu()
        print("1:",self.core.mu_to_seconds(stop_time-start_time))
        self.core.break_realtime()

        # dds.set_phase +set_frequency * 100
        start_time = self.core.get_rtio_counter_mu()

        self.mw.dds_for_mw.set_phase(0.0)
        self.mw.dds_for_mw.set_frequency(0.0)

        stop_time = self.core.get_rtio_counter_mu()
        print("2:",self.core.mu_to_seconds(stop_time-start_time))
        self.core.break_realtime()

        #dma
        self.record_wave()
        self.core.break_realtime()
        start_time = self.core.get_rtio_counter_mu()
        self.core_dma.playback("1")
        stop_time = self.core.get_rtio_counter_mu()
        print("3:",self.core.mu_to_seconds(stop_time-start_time))
        self.core.break_realtime()

        #profiles
        self.mw.dds_for_mw.set(0.0,0.0,0.0,profile=0)
        self.mw.dds_for_mw.set(0.0,0.0,0.0,profile=1)
        self.core.break_realtime()
        start_time = self.core.get_rtio_counter_mu()

        self.mw.dds_for_mw.cpld.set_profile(1)

        stop_time = self.core.get_rtio_counter_mu()
        print("4:",self.core.mu_to_seconds(stop_time-start_time))
        self.core.break_realtime()