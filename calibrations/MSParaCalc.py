from artiq.experiment import *
from modules.check_information import get_period,get_last_time,update_last_time
from ms_package.ms_parameter import recommend_mode,ms_parameters
import time
import numpy as np
import dill
from cloud.pulsedesign.AM_analytic import AMPulseDesigner,update_rabi
from cloud.pulsedesign.pulse_design_function import pst_area_signed
MODULATOR_PATH = 'C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator_list.pkl'
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)

@rpc(flags={""})
def back_check_list(index)->TList(TStr):
    return []

@rpc(flags={""})
def check0_pass(index)->TBool:
    logger.info(f"------ * Start MS Para Calc {index} Check * -------")

    last_time = get_last_time("MSParaCalc")[index[0]][index[1]]
    period = get_period("MSParaCalc")
    time_now = time.time()

    time_diff = time_now - last_time
    if time_diff > period:
        update_last_time("MSAOMAmp",index,0.0)
        logger.info("not pass, start calibration")
        return False
    else:
        logger.info("check pass")
        return True
@rpc(flags={""})
def check1_pass(index)->TBool:
    with open(MODULATOR_PATH,'rb') as f:
        modulator_list = dill.load(f)
    tester= modulator_list[index[0]][index[1]]
    signed_area = pst_area_signed(tester.rabi_half, update_rabi, tester.paras)
    logger.info(f"Signed Area: {signed_area}" )
    if abs(signed_area) > 3:
        return True
    else:
        logger.info(f"Area not pass")
        return False

@kernel
def update(exp,index):
    update_ms_paramiters(exp,index)

@rpc(flags={""})
def update_ms_paramiters(exp,index):
    with open(MODULATOR_PATH,'rb') as f:
        modulator_list = dill.load(f)

    n_ions  = int(exp.parameter.Experiment.ion_num)
    mode_1,mode_2 = recommend_mode(n_ions,index[0],index[1],4)

    # f1 = exp.parameter.Light_554.Motion_freq[mode_1]
    # f2 = exp.parameter.Light_554.Motion_freq[mode_2]
    # detuning,t_gate = ms_parameters(f1,f2,-20,1)
    # detuning = abs(detuning)
    exp.parameter.Light_554.MS_phonon_index[index[0]][index[1]] = mode_1*1.0
    exp.parameter.Light_554.MS_phonon_index[index[1]][index[0]] = mode_1*1.0
    # exp.parameter.Light_554.freq_detuning[index[0]][index[1]] = detuning
    # exp.parameter.Light_554.freq_detuning[index[1]][index[0]] = detuning
    # exp.parameter.Light_554.MS_time[index[0]][index[1]] = t_gate
    # exp.parameter.Light_554.MS_time[index[1]][index[0]] = t_gate
    # exp.parameter.update_config_json()
    i = index[0]
    j = index[1]

    parameter = exp.parameter

    gate_time = 700e-6
    detuning = -4000

    phonon_index, _ = recommend_mode(n_ions, i, j, N_cut = 10)
    phonon_index = int(phonon_index)
    logger.info(f"phonon_index ({i}, {j}): {phonon_index}")
    # phonon_index = int(parameter.Light_554.MS_phonon_index[i][j])
    # gate_frequency = parameter.Light_554.Motion_freq[phonon_index] + detuning

    logger.info(parameter.Light_554.Motion_freq)
    logger.info(f"Main Frequency: {parameter.Light_554.Motion_freq[phonon_index]}")
    # parameter.Light_554.Motion_freq += np.random.uniform(-1e3, 1e3, N_ions)
    tester = AMPulseDesigner((i, j),
                             phonon_index,
                             gate_time,
                             n_ions,
                             parameter.Light_554.Motion_freq,
                             detuning)
    logger.info("tester is done")

    #N_range = [40]
    N_range = range(64, 100, 4)
    goal = 40
    tester.run(N_range, goal)

    detuning = tester.mu/2/np.pi-parameter.Light_554.Motion_freq[phonon_index]

    # Saving Result
    modulator_list[i][j] = tester
    modulator_list[j][i] = tester

    parameter.Light_554.MS_phonon_index[i][j] = phonon_index
    parameter.Light_554.MS_phonon_index[j][i] = phonon_index
    parameter.Light_554.MS_time[i][j] = gate_time
    parameter.Light_554.MS_time[j][i] = gate_time
    parameter.Light_554.freq_detuning[i][j] = detuning
    parameter.Light_554.freq_detuning[j][i] = detuning

    parameter.Light_554.MS_AC_Stark_shift[i][j] = 0.0
    parameter.Light_554.MS_AC_Stark_shift[j][i] = 0.0

    signed_area = pst_area_signed(tester.rabi_half, update_rabi, tester.paras)
    if signed_area > 0:
        parameter.Light_554.freq_detuning[i][j] *= -1
        parameter.Light_554.freq_detuning[j][i] *= -1
    logger.info(tester.rabi)
    logger.info(f"Detuning: {detuning}",)
    logger.info(f"Qubit Pair: {(i, j)} Finished")


    with open(MODULATOR_PATH,'wb') as f:
        dill.dump(modulator_list, f)

    parameter.update_config_json()


    update_last_time("MSParaCalc",index)