from artiq.experiment import *
from structure.points_structure import decode_route
import numpy as np
from structure.route import AOD_scan_f
from modules.check_information import get_period,get_last_time,update_last_time
from cloud.fit_func import AOD_scan_fit
import time
from calibrations.cali_func import dataset_prepare
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)
@rpc(flags={""})
def back_check_list(index)->TList(TStr):
    return []

@rpc(flags={""})
def check0_pass(index)->TBool:
    logger.info(f"------ * Start AOD Address Check {index}* -------")
    last_time = get_last_time("AODAddrFreq")
    period = get_period("AODAddrFreq")
    time_now = time.time()
    time_diff = time_now - last_time
    # print("time_diff:",time_diff)
    if time_diff > period:
        logger.info("not pass, start calibration")
        return False
    else:
        logger.info("check pass")
        return True

@rpc(flags={""})
def check1_pass(index)->TBool:
    return True

@kernel()
def update(exp,qubit_index_list):
    exp.point_now_temp = exp.point_now
    init_aod_scan(exp)
    update_adaptive(exp,"max(probability)>0.2",[i*0.1e6+79e6 for i in range(61)],1e6)
    dataset_prepare(exp,[i*0.1e6+79e6 for i in range(61)])
    create_aod_scan_route(exp)
    exp.core.break_realtime()
    exp.run_task_num(1, [i*0.1e6+79e6 for i in range(61)])
    exp.recover_condition()
    aod_addr_freq_update(exp,0)
    update_adaptive(exp,exp.adaptive_condition,exp.points_param_list,exp.default_unit)
    recover_route(exp)
    exp.point_now = exp.point_now_temp

@rpc(flags={""})
def init_aod_scan(exp):
    exp.is_circuit = False


@rpc(flags={""})
def update_adaptive(exp,condition,points_list,unit):
    exp.adaptive_condition_now = condition
    exp.points_param_list_now = points_list
    exp.x_unit = unit

@rpc(flags={""})
def create_aod_scan_route(exp):
    exp.route_now = AOD_scan_f(start = 79e6,stop = 85e6,n_points=61).route
    exp.points_list = decode_route(exp.route_now)

@rpc(flags = {""})
def recover_route(exp):
    exp.route_new = exp.route

@rpc(flags={""})
def aod_addr_freq_update(exp, qubit_index):
    x = np.array(exp.get_dataset('x_points'))
    y = np.array(exp.get_dataset('y_probability'))[:]
    peaks = AOD_scan_fit(x,y)
    peaks_Hz = np.array(peaks) * 1e6
    exp.parameter.Light_554.AOD_middle_freq = peaks_Hz[0]
    exp.parameter.Light_554.AOD_address_freqs = peaks_Hz-peaks_Hz[0]
    exp.parameter.update_config_json()
    update_last_time("AODAddrFreq",[])