from artiq.experiment import *
from scipy.stats import FitError

from structure.points_structure import decode_route
import numpy as np
from structure.route import Ra<PERSON>_scan_t
from modules.check_information import get_period,get_reference,get_last_time,update_last_time
from cloud.fit_func import <PERSON><PERSON>_fit
from modules.config import LOADED_PARAMETER
import time
from calibrations.cali_func import dataset_prepare
import matplotlib.pyplot as plt
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)

@rpc(flags={""})
def back_check_list(index)->TList(TStr):
    # return ["AODAddrFreq"]
    return []

@rpc(flags={""})
def check0_pass(index)->TBool:
    logger.info(f"------ * Start Rabi Time {index} Check * -------")
    last_time = get_last_time("RabiTime")[index[0]]
    period = get_period("RabiTime")
    time_now = time.time()
    time_diff = time_now - last_time
    if time_diff > period:
        logger.info("not pass, start calibration")
        return False
    else:
        logger.info("check pass")
        return True

@rpc(flags={""})
def check1_pass(index)->TBool:
    parameter = LOADED_PARAMETER()

    rabi_pi2  = parameter.Light_554.pi_2.Carrier[index[0]]
    ref = get_reference("RabiTime")
    if rabi_pi2 > ref:
        update_last_time("RabiTime",index)
        logger.info("check 1 not pass, start calibration")
        return False
    else:
        logger.info("check 1 pass")
        return True

@kernel()
def update(exp,qubit_index_list):
    qubit_index = qubit_index_list[0]
    init_rabi_update(exp)
    exp.point_now_temp = exp.point_now
    dataset_prepare(exp,[i*2.0 for i in range(21)])
    exp.core.break_realtime()
    create_pi2_cali_route(exp,qubit_index)
    exp.run_task_num(1, [i*2.0 for i in range(21)])
    pi2_time_update(exp,qubit_index)
    exp.point_now = exp.point_now_temp

@rpc(flags={""})
def create_pi2_cali_route(exp,qubit_index):
    exp.points_list = decode_route(Rabi_scan_t(qubit_index,start = 0e-6,stop = 40e-6,n_points=21).route)

@rpc(flags={""})
def init_rabi_update(exp):
    exp.is_circuit = False

@rpc(flags={""})
def pi2_time_update(exp, qubit_index):
    # 1. 获取数据
    x = np.array(exp.get_dataset('x_points'))
    y = np.array(exp.get_dataset('y_probability'))[:,qubit_index]

    # 2. 拟合
    try:
        rabi_time,t_fit,y_fit,popt = Rabi_fit(x,y)
    except Exception as e:
        logger.error(f"Rabi time fit error {qubit_index}")
        raise FitError("Rabi time fit error")

    # 3. 计算 Rabi 时间
    # aom_delay=-(np.pi/2+popt[3])/np.pi/2/popt[1]/1e6
    # aom_delay = aom_delay% rabi_time
    aom_delay = 0
    # print("aom_delay:",aom_delay)
    pi2_time = aom_delay + rabi_time/4

    if max(y) < 0.5:
        pi2_time = 10e-6
    # print("pi2_time:",pi2_time)
    #画图
    # fig,ax = plt.subplots()
    # ax.plot(t_fit,y_fit,"--")
    # ax.plot(x, y,'-o')
    # ax.set_xlabel('$t$ (us)')
    # ax.set_ylabel('Probability')
    # ax.set_title('Raman Rabi')
    # plt.show()

    # 4. 数据更新
    exp.parameter.Light_554.pi_2.Carrier[int(qubit_index)] = pi2_time
    exp.parameter.Light_554.AOM_phase_delay = aom_delay
    exp.parameter.update_config_json()
    try:
        update_last_time("RabiTime",[qubit_index])
        logger.info(f"update rabi time success: qubit_index {qubit_index}")
    except Exception as e:
        logger.error(f"update rabi time failed: qubit index {qubit_index}")