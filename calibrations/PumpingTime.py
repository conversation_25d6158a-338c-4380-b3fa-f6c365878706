from artiq.experiment import *
from structure.points_structure import decode_route
import numpy as np
from cloud.fit_func import decay_fit
from modules.check_information import get_period,get_reference,get_last_time
import time

@rpc(flags={""})
def back_check_list()->TList(TStr):
    return []

@rpc(flags={""})
def check0_pass()->TBool:
    last_time = get_last_time("PumpingTime")
    period = get_period("PumpingTime")
    time_now = time.time()

    time_diff = time_now - last_time

    print("time_diff:",time_diff)
    if time_diff > period:
        return False
    else:
        return True

@rpc(flags={""})
def check1_pass(exp)->TBool:
    if exp.parameter.Experiment.Pumping_Decay_Time > 10e-6:
        return False
    else:
        return True

@kernel()
def update(exp,qubit_index_list):
    """
    更新实验参数

    Parameters
    ----------
    exp: 实验脚本类? EnvExperiment
    qubit_index_list: why list?

    Returns
    -------

    """
    exp.point_now_temp = exp.point_now # 标记一个当前数据点

    exp.core.break_realtime()

    @kernel()
    def run_pumping_test(exp):
        exp.qcmos.exp.qcmos.prepare_for_x_scan([5*i for i in range(21)])
        exp.scan_parameter = exp.X_scan_range.sequence
        exp.scan_points = len(exp.scan_parameter)
        # 0. initial
        while True:
            exp.core.break_realtime()
            for scan_point in range(exp.scan_points):

                for repeat in range(exp.repeat):
                    # 1 . 切换至 cooling 状态

                    delay(exp.cooling_time)

                    # 2. 切换至 pumping 状态
                    exp.l369.switch_cool_to_pump()

                    delay(exp.scan_parameter[scan_point])  # 等待 pumping

                    # ３. 切换至 control 状态
                    exp.l369.switch_pump_to_control()

                    delay(1e-6)  # delay 一个 control 阶段的时间
                    # delay( 2 * ms)
                    # 4. 切换至 detecting 状态
                    with parallel:
                        exp.l369.switch_control_to_detect()
                        exp.qcmos.qcmos_start_count()

                    delay(exp.detecting_time)
                    # 5. 切换至 cooling 状态
                    with parallel:
                        exp.qcmos.qcmos_stop_count()

                    delay(1 * ms)
                    exp.l369.switch_detect_to_cool()


                delay(3 * ms)
                exp.core.wait_until_mu(now_mu())
                exp.qcmos.process_for_x_scan(scan_point)
                exp.core.break_realtime()


# 更新 pi/2 参数
    pumping_time_update(exp)

    exp.point_now = exp.point_now_temp


@rpc(flags={""})
def pumping_time_update(exp):
    """获取数据, 拟合, 更新"""
    x = np.array(exp.get_dataset('x_points'))
    y = np.array(exp.get_dataset('probability'))[:,0]
    decay_time = decay_fit(x,y)[0]/1e6
    exp.parameter.Experiment.Pumping_Decay_Time = decay_time
    exp.parameter.update_config_json()

@kernel()
def repair():
    alert()

@rpc(flags={""})
def alert():
    print("Pumping Time:Warning!")