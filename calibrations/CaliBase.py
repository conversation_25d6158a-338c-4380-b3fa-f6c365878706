from artiq.experiment import *
import importlib
import calibrations
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)
PARAMETERLIST = [
    "RabiTime",
    "AODAddrFreq",
    "MSFidelity",
    "MSAOMAmp",
    "MSAOMAmp2",
    "MSShift",
    "MSPhase",
    "MotionFreq",
    "MSParaCalc"
]

modules_dict = {}

# 注册所有的校准脚本
for param in PARAMETERLIST:
    modules_dict[param] = importlib.import_module(f"calibrations.{param}")

@rpc(flags={""})
def back_check_list(name, index)->TList(TStr):
    """

    Parameters
    ----------
    name
    index

    Returns
    -------

    """
    logger.info(f"get back check list for {name}, {index}")
    return modules_dict[name].back_check_list(index)

@rpc(flags={""})
def check0_pass(name, index)->TBool:
    # logger.info(name)
    return modules_dict[name].check0_pass(index)

@rpc(flags={""})
def check1_pass(name,index)->TBool:
    return modules_dict[name].check1_pass(index)

@kernel()
def update(exp,name,index):
    if name == "RabiTime":
        calibrations.RabiTime.update(exp,index)
    elif name == "AODAddrFreq":
        calibrations.AODAddrFreq.update(exp,index)
    elif name == "MSFidelity":
        calibrations.MSFidelity.update(exp,index)
    elif name == "MSAOMAmp":
        calibrations.MSAOMAmp.update(exp,index)
    elif name == "MotionFreq":
        calibrations.MotionFreq.update(exp,index)
    elif name == "MSParaCalc":
        calibrations.MSParaCalc.update(exp,index)
    elif name == "MSShift":
        # exp.tprint("MSShift>>>>>>>>>>>>>>>>>>>>>>>>>")
        calibrations.MSShift.update(exp,index)
    elif name == "MSPhase":
        calibrations.MSPhase.update(exp,index)

@rpc(flags={""})
def dataset_prepare(exp, dataset):
    if exp.parameter.Experiment.system_name == "M2":
        exp.qcmos.process_for_x_scan_re_repeat(dataset)
    else:
        exp.pmt.prepare_for_x_scan(dataset)

