from artiq.experiment import *
import numpy as np
from modules.check_information import get_period,get_reference,get_last_time
import time

@rpc(flags={""})
def back_check_list()->TList(TStr):
    return []

@rpc(flags={""})
def check0_pass()->TBool:
    last_time = get_last_time("DetectThreshold")
    period = get_period("DetectThreshold")
    time_now = time.time()

    time_diff = time_now - last_time

    print("time_diff:",time_diff)
    if time_diff > period:
        return False
    else:
        return True

# @rpc(flags={""})
# def check1_pass(exp)->TBool:
#     if exp.parameter.Experiment.ion_num != exp.parameter.ref.Experiment.ion_num :
#         return False
#     else:
#         return True

@kernel()
def update(exp):
    exp.point_now_temp = exp.point_now


    @kernel()
    def run_detect_threshold(self):
        self.qcmos.prepare_for_histogram()

        for re_repeat in range(self.re_repeat):
            for repeat in range(self.repeat):
                # ---------------- dark state ---------------
                # 1 . 切换至 cooling 状态
                delay(self.pre_cooling_time)
                self.l369.switch_pre_cooling_to_cooling()
                delay(self.cooling_time)
                # 2. 切换至 pumping 状态
                self.l369.switch_cool_to_pump()
                delay(self.pumping_time)
                # ３. 切换至 detect 状态
                with parallel:
                    self.l369.switch_pump_to_detect()
                    self.qcmos.qcmos_start_count()
                delay(self.detecting_time)
                self.qcmos.qcmos_stop_count()
                delay(2 * ms)
                self.l369.switch_detect_to_pre_cooling()

                # ---------------- bright state ---------------
                # 1. 切换至 cooling 状态
                delay(0.5 * ms)
                delay(self.pre_cooling_time)
                self.l369.switch_pre_cooling_to_cooling()
                delay(self.cooling_time)
                # 2. 切换至 pumping 状态
                self.l369.switch_cool_to_pump()
                delay(self.pumping_time)
                # 3. 切换至 control 阶段
                with parallel:
                    self.mw.mw_on()
                    self.l369.switch_pump_to_control()
                delay(self.mw_pi_2 * 2)
                # 4. 切换至 detecting 状态
                with parallel:
                    self.mw.mw_off()
                    self.qcmos.qcmos_start_count()
                    self.l369.switch_control_to_detect()
                delay(self.detecting_time)
                # 5. 切换至 off 状态
                self.qcmos.qcmos_stop_count()
                delay(2 * ms)
                self.l369.switch_detect_to_pre_cooling()
            delay(10 * ms)
            self.core.wait_until_mu(now_mu())
            self.qcmos.process_for_single_bundle_double(re_repeat)
            self.core.break_realtime()

            delay(10 * ms)
            self.core.wait_until_mu(now_mu())
            self.qcmos.process_for_histogram(self.ion_choice, re_repeat=self.re_repeat)

    exp.run_detect_threshold()

    threshold_update(exp)

    exp.point_now = exp.point_now_temp

@rpc(flags={""})
def create_roi_cali_route(exp,phonon_index,qubit_index):
    pass

@rpc(flags={""})
def threshold_update(exp):
    """更新 config 中 SPAM \ Detect_threshold\Dark_to_Bright\ Bright_to_Dark 四个参数"""
    SPAM = exp.get_dataset("SPAM")
    Detect_threshold = exp.get_dataset("Detect_threshold")
    Errors_Dark_to_Bright = exp.get_dataset("Errors_Dark_to_Bright")
    Errors_Bright_to_Dark = exp.get_dataset("Errors_Bright_to_Dark")

    exp.parameter.QCMOS.Errors.SPAM = SPAM
    exp.parameter.QCMOS.Errors.Dark_to_Bright =  Errors_Dark_to_Bright
    exp.parameter.QCMOS.Errors.Bright_to_Dark =  Errors_Bright_to_Dark

    exp.parameter.QCMOS.Detect_threshold = Detect_threshold

    exp.parameter.update_config_json()

