import random

from artiq.experiment import *
from structure.points_structure import decode_route
from modules.config import LOADED_PARAMETER
import numpy as np
from cloud.fit_func import Cross_fit
from modules.check_information import get_period,get_reference,get_last_time,update_last_time
import time
from structure.route import MS_scan_amp
from calibrations.cali_func import dataset_prepare
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)

@rpc(flags={""})
def back_check_list(index)->TList(TStr):
    return [
        f"RabiTime_{index[0]}",
        f"RabiTime_{index[1]}"
    ]

@rpc(flags={""})

def check0_pass(index)->TBool:
    logger.info(f"------ * Start MS AOM Amp {index} Check * -------")
    last_time = get_last_time("MSAOMAmp")[index[0]][index[1]]
    period = get_period("MSAOMAmp")
    time_now = time.time()
    time_diff = time_now - last_time
    if time_diff > period:
        logger.info("not pass, start calibration")
        return False
    else:
        logger.info("check pass")
        return True

@rpc(flags={""})
def check1_pass(index)->TBool:
    parameter = LOADED_PARAMETER()
    ms_aom_amp  = parameter.Light_554.AOM_AWG_amp_ms[index[0]][index[1]]
    ref = get_reference("MSAOMAmp")
    if ms_aom_amp < ref:
        return True
    else:
        update_last_time("MSAOMAmp",index,0.0)
        return False


@kernel()
def update(exp,index):
    exp.point_now_temp = exp.point_now
    init_ms_aom_amp_update(exp,index)
    dataset_prepare(exp,[i*0.08+0.1 for i in range(11)])
    create_ms_aom_amp_route(exp,index,0.1,0.9,11,n_gate=1)
    exp.core.break_realtime()
    exp.run_task_num(1,[i*0.08+0.1 for i in range(11)])
    ms_amp = ms_aom_amp_update(exp,index)

    # create_ms_aom_amp_route(exp,index,ms_amp-0.05,ms_amp+0.05,21,n_gate=3)
    # dataset_prepare(exp,[(i-10)*0.005+ms_amp for i in range(21)])
    # exp.core.break_realtime()
    # exp.run_task_num(1,[(i-10)*0.005+ms_amp for i in range(21)])
    # ms_aom_amp_update(exp,index)
    # recover_route(exp)

    exp.point_now = exp.point_now_temp
@rpc(flags={""})
def init_ms_aom_amp_update(exp,index):
    exp.is_circuit = False
    exp.ion_choice = (index[0],index[1])

@rpc(flags={""})
def create_ms_aom_amp_route(exp,index,start,stop,n_points,n_gate=1,):
    qubit_index = (index[0],index[1])
    exp.points_list = decode_route(
        MS_scan_amp(qubit_index, start=start, stop=stop, n_points=n_points,n_gate=n_gate).route
    )

@rpc(flags={""})
def ms_aom_amp_update(exp,index)->TFloat:
    x = np.array(exp.get_dataset("x_points"))
    y = np.array(exp.get_dataset("computational_basis_probability"))
    P = y[:,0:4:3]
    crosses = Cross_fit(x,P)
    if len(crosses)>0:
        logger.info(f" P11 = P00  at aom amplitude = {crosses[0]} " )
        exp.parameter.Light_554.AOM_AWG_amp_ms[index[0]][index[1]] = crosses[0]
        exp.parameter.Light_554.AOM_AWG_amp_ms[index[1]][index[0]] = crosses[0]
        exp.parameter.update_config_json()
        update_last_time("MSAOMAmp",index)
        return crosses[0]
    else:
        exp.parameter.Light_554.AOM_AWG_amp_ms[index[0]][index[1]] = 0.9 #设置为不通过check1的值
        exp.parameter.Light_554.AOM_AWG_amp_ms[index[1]][index[0]] = 0.9 #设置为不通过check1的值
        exp.parameter.update_config_json()

    update_last_time("MSAOMAmp",index)
    return 0.4
@rpc(flags={""})
def recover_route(exp):
    exp.route_now = exp.route