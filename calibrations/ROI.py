from artiq.experiment import *
import numpy as np
from modules.check_information import get_period,get_reference,get_last_time
import time

@rpc(flags={""})
def back_check_list()->TList(TStr):
    return []

@rpc(flags={""})
def check0_pass()->TBool:
    last_time = get_last_time("ROI")
    period = get_period("ROI")
    time_now = time.time()

    time_diff = time_now - last_time

    print("time_diff:",time_diff)
    if time_diff > period:
        return False
    else:
        return True

@rpc(flags={""})
def check1_pass(exp)->TBool:
    if exp.parameter.Experiment.ion_num != exp.parameter.ref.Experiment.ion_num :
        return False
    else:
        return True

@kernel()
def update(exp):
    exp.point_now_temp = exp.point_now

    @kernel()
    def run_roi_cali(exp):
        exp.qcmos.prepare_for_histogram()
        # 0. initial
        while True:
            exp.core.break_realtime()
            for repeat in range(exp.repeat):
                # ---------------- bright state ---------------
                # 1 . 切换至 cooling 状态
                delay(exp.pre_cooling_time)
                exp.l369.switch_pre_cooling_to_cooling()
                exp.qcmos.qcmos_start_count()
                delay(3 * ms)
                exp.qcmos.qcmos_stop_count()

                # ---------------- dark state ---------------
                delay(3 * ms)
                # 2. 切换至 pumping 状态
                exp.l369.switch_cool_to_pump()
                delay(exp.pumping_time * 5)

                exp.qcmos.qcmos_start_count()
                delay(3 * ms)
                exp.qcmos.qcmos_stop_count()
                delay(2 * ms)
                exp.l369.switch_detect_to_pre_cooling()

            delay(10 * ms)
            exp.core.wait_until_mu(now_mu())
            # 使用像素标记格式进行校准
            exp.qcmos.process_for_roi_calibration()
            exp.core.break_realtime()

            if exp.check_lost():
                exp.save_ions()  # 封装救离子的逻辑
            else:
                break

            # 添加优雅中止
            if exp.scheduler.check_termination():
                break

    exp.run_roi_cali()

    roi_update(exp)

    exp.point_now = exp.point_now_temp

@rpc(flags={""})
def create_roi_cali_route(exp,phonon_index,qubit_index):
    pass

@rpc(flags={""})
def roi_update(exp):
    roi = remove_padding(exp.get_dataset("new_roi"))
    back_ground_noise = exp.get_dataset("background_noise")
    ion_num = len(roi)

    exp.parameter.QCMOS.Experiment.roi_for_ions = roi
    exp.parameter.QCMOS.bit16.background_noise =  back_ground_noise
    exp.parameter.Experiment.ion_num = ion_num

    exp.parameter.update_config_json()


def remove_padding(padded_array: np.ndarray):
    """
    从填充了 [-1, -1] 的均匀数组中移除占位符，还原原始的不规则列表结构。

    Parameters
    ----------
    padded_array : np.ndarray
        形状为 (n_ions, max_pixels, 2) 的数组，用 [-1, -1] 填充过。

    Returns
    -------
    List[List[List[int]]]
        原始的像素标记列表，格式为 [[[x1, y1], [x2, y2], ...], ...]。
    """
    original_list = []
    for ion in padded_array:
        # 过滤掉 [-1, -1] 的占位符
        valid_pixels = [coord.tolist() for coord in ion if not np.all(coord == -1)]
        original_list.append(valid_pixels)
    return original_list