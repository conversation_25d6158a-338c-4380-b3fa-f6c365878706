from artiq.experiment import *
from pythonparser.ast import Raise
from scipy.stats import FitError

from structure.points_structure import decode_route
import numpy as np
from structure.route import <PERSON><PERSON>_<PERSON>_scan_t
from modules.check_information import get_period,get_reference,get_last_time,update_last_time
from ms_package.ms_parameter import lamb_dicke_parameter
from cloud.fit_func import Ramsey_fit
import time
from modules.config import LOADED_PARAMETER
from calibrations.cali_func import *
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)

@rpc(flags={""})
def back_check_list(index)->TList(TStr):
    qubit_index = get_qubit_index(index[0])
    str = f"RabiTime_{qubit_index}"
    return [str]

@rpc(flags={""})
def check0_pass(index)->TBool:
    logger.info(f"------ * Start Motion Freq {index}Check * -------")
    last_time = get_last_time("MotionFreq")[index[0]]
    period = get_period("MotionFreq")
    time_now = time.time()
    time_diff = time_now - last_time
    if time_diff > period:
        logger.info("not pass, start calibration")
        return False
    else:
        logger.info("check pass")
        return True

@rpc(flags={""})
def check1_pass(index)->TBool:
    parameter = LOADED_PARAMETER()
    freq_diff = parameter.ref.Light_554.Motion_freq[index[0]] - parameter.Light_554.Motion_freq[index[0]]

    if abs(freq_diff) > 100:
        update_last_time("MotionFreq",index,0.0)
        logger.info(f"check 1 not pass, start calibration")
        return False
    else:
        logger.info("check 1 pass")
        return True

@kernel()
def update(exp,index):
    exp.point_now_temp = exp.point_now
    # 打印 声子频率校准信息
    init_phonon_freq_cali(exp,index)
    # 更新校准参考值
    update_ref(exp, index[0])
    # 获取比特索引
    qubit_index = get_qubit_index(index[0])
    dataset_prepare(exp,[50.0*i for i in range(21)])
    exp.core.break_realtime()
    create_route(exp,index[0],qubit_index)
    exp.run_task_num(1,[50.0*i for i in range(21)])
    update_new_freq(exp, index[0],qubit_index)
    exp.point_now = exp.point_now_temp

@rpc(flags={""})
def init_phonon_freq_cali(exp,index):
    exp.is_circuit = False

@rpc(flags={""})
def update_ref(exp,phonon_index):
    exp.parameter.ref.Light_554.Motion_freq[phonon_index] = exp.parameter.Light_554.Motion_freq[phonon_index]
    exp.parameter.update_config_json()

@rpc(flags={""})
def get_qubit_index(phonon_index)->TInt64:
    parameter = LOADED_PARAMETER()
    freq_old = parameter.ref.Light_554.Motion_freq

    eta_b = lamb_dicke_parameter(parameter.Experiment.ion_num, freq_old)
    qubit_index = np.argmax(abs(eta_b[:,phonon_index]))
    return qubit_index

@rpc(flags={""})
def create_route(exp,phonon_index,qubit_index):
    parameter = LOADED_PARAMETER()
    freq_old = parameter.ref.Light_554.Motion_freq
    eta_b = lamb_dicke_parameter(parameter.Experiment.ion_num, freq_old)
    detuned_frequency = exp.parameter.Light_554.Motion_freq[phonon_index] - 2000
    route = Raman_Ramsey_scan_t(qubit_index,start = 0e-6,
                                stop = 1000e-6,n_points=21,ramsey_choice="Blue",
                                phonon_frequency=detuned_frequency,
                                eta = abs(eta_b[qubit_index,phonon_index])
                                ).route
    exp.points_list = decode_route(route)

@rpc(flags={""})
def update_new_freq(exp, phonon_index, qubit_index):
    # 1. 获取数据
    x = np.array(exp.get_dataset('x_points'))
    y = np.array(exp.get_dataset('y_probability'))[:,qubit_index]

    # 2. 拟合
    try:
        detuning,_,_,_ = Ramsey_fit(x,y)
        logger.info(f"detuning: {detuning - 2000}")
    except Exception as e:
        logger.error(f"motion fre fit failed: phonon_index {phonon_index}, qubit_index {qubit_index}")
        raise FitError(f"motion fre fit failed: {e}")

    # 3. 更新数据
    exp.parameter.Light_554.Motion_freq[phonon_index] += detuning - 2000
    exp.parameter.update_config_json()
    update_last_time("MotionFreq",[phonon_index])
    print(exp.ion_num)
    for i in range(exp.ion_num):
        for j in range(exp.ion_num):
            update_last_time("MSParaCalc",[i,j],0.0)