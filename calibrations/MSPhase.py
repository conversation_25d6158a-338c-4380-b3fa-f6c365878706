import random

from artiq.experiment import *
from structure.points_structure import decode_route
from modules.config import LOADED_PARAMETER
import numpy as np
from cloud.fit_func import Parity_fit
from modules.check_information import get_period,get_reference,get_last_time,update_last_time
import time
from structure.route import Parity_scan_phi,Pi2MS_scan_phase
from calibrations.cali_func import dataset_prepare

from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)

@rpc(flags={""})
def back_check_list(index)->TList(TStr):
    return []

@rpc(flags={""})

def check0_pass(index)->TBool:
    logger.info(f"------ * Start MS Phase {index} Check * -------")

    last_time = get_last_time("MSPhase")[index[0]][index[1]]
    period = get_period("MSPhase")
    time_now = time.time()

    time_diff = time_now - last_time
    if time_diff > period:
        logger.info("not pass, start calibration")
        return False
    else:
        logger.info("check pass")
        return True

@rpc(flags={""})
def check1_pass(index)->TBool:
    return True

@kernel()
def update(exp,index):
    exp.point_now_temp = exp.point_now
    init_ms_phi_update(exp,index)

    dataset_prepare(exp,[i*np.pi*2.0/30.0+0.0 for i in range(31)])
    create_ms_phase_route(exp,index)
    exp.core.break_realtime()
    exp.run_task_num(1,[i*np.pi*2.0/30.0+0.0 for i in range(31)])

    ms_phase_update(exp,index)

    recover_route(exp)
    exp.point_now = exp.point_now_temp

@rpc(flags={""})
def init_ms_phi_update(exp,index):
    exp.parameter.Light_554.MS_phase[index[0]][index[1]] = 0.0
    exp.parameter.update_config_json()
    exp.is_circuit = False
    exp.ion_choice = (index[0],index[1])

@rpc(flags={""})
def create_ms_phase_route(exp,index):
    qubit_index = (index[0],index[1])
    # exp.route_now = Parity_scan_phi(qubit_index,start=0.0, stop=2*np.pi, n_points=31, n_gate=1).route
    exp.route_now =  Pi2MS_scan_phase(qubit_index,start=0.0, stop=2*np.pi, n_points=31).route
    exp.points_list = decode_route(exp.route_now)

@rpc(flags={""})
def ms_phase_update(exp,index):
    x = np.array(exp.get_dataset("x_points"))
    y = np.array(exp.get_dataset("computational_basis_probability"))

    # parity = y[:,0]+y[:,3] -y[:,1]-y[:,2]
    # contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])
    # amp = _[0]
    # phase=_[3]
    # if amp < 0:
    #     phase += np.pi
    # exp.parameter.Light_554.MS_phase[index[0]][index[1]] -= phase/2
    # exp.parameter.Light_554.MS_phase[index[1]][index[0]] -= phase/2

    parity = y[:,0]-y[:,3]
    contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])

    phase_ = _[3]
    amp_ = _[0]
    # logger.info("amp:",amp_,",phase:",phase_)
    exp.parameter.Light_554.MS_phase[index[0]][index[1]] = -phase_/2 + np.pi/2
    exp.parameter.Light_554.MS_phase[index[1]][index[0]] = -phase_/2 + np.pi/2

    exp.parameter.update_config_json()
    update_last_time("MSPhase",index)

@rpc(flags={""})
def recover_route(exp):
    exp.route_now = exp.route