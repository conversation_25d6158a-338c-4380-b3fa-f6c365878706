import random

from artiq.experiment import *
from structure.points_structure import decode_route
from structure.route import Parity_scan_phi, MS_scan_n
import numpy as np
from cloud.fit_func import Parity_fit
from modules.check_information import get_period,get_reference,get_last_time,update_last_time
from modules.config import LOADED_PARAMETER
from scipy.optimize import curve_fit
import time
from calibrations.cali_func import dataset_prepare
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)
@rpc(flags={""})
def back_check_list(index)->TList(TStr):
    parameter = LOADED_PARAMETER()
    main_mode = parameter.Light_554.MS_phonon_index[index[0]][index[1]]
    return [
        f"MotionFreq_{int(main_mode)}",
        # f"MotionFreq_{int(main_mode)-1}",
        f"MotionFreq_{int(main_mode)+1}",
        # f"MotionFreq_{int(main_mode)-2}",
        f"MotionFreq_{int(main_mode)+2}",
        f"MSParaCalc__{index[0]}_{index[1]}",
        f"MSAOMAmp_{index[0]}_{index[1]}",
        f"MSShift_{index[0]}_{index[1]}",
        f"RabiTime_{index[0]}",
        f"RabiTime_{index[1]}",
        f"MSPhase_{index[0]}_{index[1]}",
        f"MSAOMAmp2_{index[0]}_{index[1]}",
    ]

@rpc(flags={""})
def check0_pass(index)->TBool:
    logger.info(f"------ * Start MS Fidelity {index} Check * -------")
    last_time = get_last_time("MSFidelity")[index[0]][index[1]]
    period = get_period("MSFidelity")
    time_now = time.time()
    time_diff = time_now - last_time
    if time_diff > period:
        logger.info("not pass, start calibration")
        return False
    else:
        logger.info("check pass")
        return True

@rpc(flags={""})
def check1_pass(index)->TBool:
    parameter = LOADED_PARAMETER()
    fidelity  = parameter.Light_554.MS_fidelity[index[0]][index[1]]
    ref = get_reference("MSFidelity")
    if fidelity > ref:
        logger.info(f"check 1 pass, fidelity {fidelity}")
        return True
    else:
        update_last_time("MSFidelity",index,0.0)
        logger.info("check 1 not pass, start calibration")
        return False

@kernel()
def update(exp,index):
    exp.point_now_temp = exp.point_now

    init_parity_update(exp,index)
    for i_gate in [1]:

        dataset_prepare(exp,[np.pi*2/30*i for i in range(31)])
        create_parity_route(exp,index,i_gate)
        exp.core.break_realtime()
        exp.run_task_num(1,[np.pi*2/30*i for i in range(31)])
        parity_update(exp,i_gate)

    # exp.pmt.prepare_for_x_scan([1.0,3.0,5.0])
    # create_ms_n_route(exp,index)
    # exp.core.break_realtime()
    # exp.run_task_num(1,[1.0,3.0,5.0])
    fidelity_update(exp,index)

    recover_route(exp)
    exp.point_now = exp.point_now_temp
@rpc(flags={""})
def init_parity_update(exp,index):
    logger.info(f"------ * start MS {index} fidelity measure * ------")
    exp.is_circuit = False
    exp.ion_choice = (index[0],index[1])
    exp.ms_parity = [0,0,0]

@rpc(flags={""})
def create_parity_route(exp,index,n_gate):
    exp.route_now = Parity_scan_phi(index,start=0.0, stop=2*np.pi, n_points=31, n_gate=n_gate).route
    exp.points_list = decode_route(exp.route_now)
@rpc(flags={""})
def parity_update(exp,gate_number):

    x = np.array(exp.get_dataset("x_points"))
    y = np.array(exp.get_dataset("computational_basis_probability"))
    parity = y[:,0]+y[:,3]-y[:,1]-y[:,2]
    try:
        contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])
    except Exception as e:
        logger.info("fit parity failed:")
        logger.error(e)
        contrast = 0

    exp.ms_parity[int((gate_number-1)/2)] = contrast

@rpc(flags={""})
def create_ms_n_route(exp,index):
    exp.route_now = MS_scan_n(index, start=1, stop=5, n_points=3).route
    exp.points_list = decode_route(exp.route_now)

@rpc(flags={""})
def fidelity_update(exp,index):

    x = np.array(exp.get_dataset("x_points"))
    y = np.array(exp.get_dataset("computational_basis_probability"))
    p0011 = y[:,0]+y[:,3]
    # parity = np.array(exp.ms_parity)
    # f = p0011/2+parity/4
    # def Fun(x,a,b):
    #     return a+b*x
    # try:
    #     popt,pcov = curve_fit(Fun, x, f , p0 = [1,0])
    # except Exception as e:
    #     logger.info("Fidelity fit failed:")
    #     logger.info(e)
    #     popt = [0,-1]
    # fidelity = min(1.0 + popt[1],f[1]+0.05) #拟合得到单个门保真度，与贝尔态保真度+SPAM 5%取较小值
    # fidelity = 0.9
    # fidelity = p0011[0]/2+exp.ms_parity[0]/4
    fidelity = exp.ms_parity[0]/2

    exp.parameter.Light_554.MS_fidelity[index[0]][index[1]] = fidelity
    exp.parameter.Light_554.MS_fidelity[index[1]][index[0]] = fidelity
    exp.parameter.update_config_json()
    update_last_time("MSFidelity",index)



@rpc(flags={""})
def recover_route(exp):
    exp.route_now = exp.route
