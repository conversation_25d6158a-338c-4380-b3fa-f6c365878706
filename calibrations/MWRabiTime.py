from artiq.experiment import *
from structure.points_structure import decode_route
import numpy as np
from structure.route import <PERSON><PERSON>_scan_t
from cloud.fit_func import <PERSON><PERSON>_fit
from modules.check_information import get_period,get_reference,get_last_time
import time

@rpc(flags={""})
def back_check_list()->TList(TStr):
    return []

@rpc(flags={""})
def check0_pass()->TBool:
    last_time = get_last_time("MWRabiTime")
    period = get_period("MWRabiTime")
    time_now = time.time()

    time_diff = time_now - last_time

    print("time_diff:",time_diff)
    if time_diff > period:
        return False
    else:
        return True

@rpc(flags={""})
def check1_pass(exp,zeeman_choice)->TBool:
    diff = 0
    if zeeman_choice == "0":
        diff = exp.parameter.Signal_MW.mw_pi_2.zero - exp.parameter.ref.Signal_MW.mw_pi_2.zero
    elif zeeman_choice == "+":
        diff = exp.parameter.Signal_MW.mw_pi_2.positive - exp.parameter.ref.Signal_MW.mw_pi_2.positive
    elif zeeman_choice == "-":
        diff = exp.parameter.Signal_MW.mw_pi_2.negative - exp.parameter.ref.Signal_MW.mw_pi_2.negative
    if abs(diff) > 10e-6:
        return False
    else:
        return True

@kernel()
def update(exp,qubit_index_list,zeeman_choice):
    """
    更新实验参数

    Parameters
    ----------
    exp: 实验脚本类? EnvExperiment
    qubit_index_list: why list?

    Returns
    -------

    """
    qubit_index = qubit_index_list[0]
    exp.point_now_temp = exp.point_now # 标记一个当前数据点

    exp.core.break_realtime()

    @kernel()
    def run_mw_rabi(exp):
        exp.qcmos.exp.qcmos.prepare_for_x_scan([i for i in range(21)])
        exp.scan_parameter = exp.X_scan_range.sequence
        exp.scan_points = len(exp.scan_parameter)
        # 0. initial
        while True:
            exp.core.break_realtime()
            if exp.zeeman_choice == "+":
                exp.mw.set_mw_parameter(detuning=exp.zeeman_p)
            elif exp.zeeman_choice == "-":
                exp.mw.set_mw_parameter(detuning=exp.zeeman_n)
            else:
                exp.mw.set_mw_parameter(detuning=0.0)
            exp.core.break_realtime()

            for scan_point in range(exp.scan_points):
                for repeat in range(exp.repeat):
                    # 1 . 切换至 cooling 状态
                    # self.pmt.pmt_start_count()
                    delay(exp.pre_cooling_time)
                    exp.l369.switch_pre_cooling_to_cooling()
                    delay(exp.cooling_time)
                    # self.pmt.pmt_stop_count()

                    # 2. 切换至 pumping 状态
                    exp.l369.switch_cool_to_pump()

                    delay(exp.pumping_time)

                    # ３. 切换至 control 状态
                    with parallel:
                        exp.l369.switch_pump_to_control()
                        exp.mw.mw_on()

                    delay(exp.scan_parameter[scan_point])

                    # 4. 切换至 detecting 状态
                    with parallel:
                        exp.mw.mw_off()
                        exp.qcmos.qcmos_start_count()
                        exp.l369.switch_control_to_detect()

                    delay(exp.detecting_time)

                    # 5. 切换至 off 状态
                    with parallel:
                        exp.qcmos.qcmos_stop_count()
                    delay(1.5 * ms)
                    exp.l369.switch_detect_to_pre_cooling()

                delay(1 * ms)
                exp.core.wait_until_mu(now_mu())
                exp.qcmos.process_for_x_scan(scan_point)

                if exp.scheduler.check_termination():
                    break
                exp.core.break_realtime()

    run_mw_rabi(exp)

# 更新 pi/2 参数
    pi2_time_update(exp,qubit_index,zeeman_choice)

    exp.point_now = exp.point_now_temp


@rpc(flags={""})
def pi2_time_update(exp, qubit_index,zeeman_choice):
    """获取数据, 拟合, 更新"""
    x = np.array(exp.get_dataset('x_points'))
    y = np.array(exp.get_dataset('probability'))[:,qubit_index]
    pi_2_time = Rabi_fit(x,y)[0]/4
    if zeeman_choice == "0":
        exp.parameter.Signal_MW.mw_pi_2.zero = pi_2_time
    elif zeeman_choice == "+":
        exp.parameter.Signal_MW.mw_pi_2.positive = pi_2_time
    elif zeeman_choice == "-":
        exp.parameter.Signal_MW.mw_pi_2.negative = pi_2_time
    exp.parameter.update_config_json()

@kernel()
def repair():
    alert()

@rpc(flags={""})
def alert():
    print("MW Rabi Time:Warning!")