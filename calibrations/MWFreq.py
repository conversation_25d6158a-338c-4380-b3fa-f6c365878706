from artiq.experiment import *
from structure.points_structure import decode_route
import numpy as np
from structure.route import <PERSON><PERSON>_scan_t
from cloud.fit_func import Ramsey_fit
from modules.check_information import get_period,get_reference,get_last_time
import time

@rpc(flags={""})
def back_check_list()->TList(TStr):
    return ['MWRabiTime']

@rpc(flags={""})
def check0_pass()->TBool:
    last_time = get_last_time("MWFreq")
    period = get_period("MWFreq")
    time_now = time.time()

    time_diff = time_now - last_time

    print("time_diff:",time_diff)
    if time_diff > period:
        return False
    else:
        return True

@rpc(flags={""})
def check1_pass(exp,zeeman_choice)->TBool:
    diff = 0
    if zeeman_choice == "0":
        diff = exp.parameter.Signal_MW.dds_for_mw_fre - exp.parameter.ref.Signal_MW.dds_for_mw_fre
    elif zeeman_choice == "+":
        diff = exp.parameter.Signal_MW.zeeman_p - exp.parameter.ref.Signal_MW.zeeman_p
    elif zeeman_choice == "-":
        diff = exp.parameter.Signal_MW.zeeman_n - exp.parameter.ref.Signal_MW.zeeman_n
    if abs(diff) > 2000:
        return False
    else:
        return True

@kernel()
def update(exp,qubit_index_list,zeeman_choice):
    """
    更新实验参数

    Parameters
    ----------
    exp: 实验脚本类? EnvExperiment
    qubit_index_list: why list?

    Returns
    -------

    """
    qubit_index = qubit_index_list[0]
    exp.point_now_temp = exp.point_now # 标记一个当前数据点

    update_ref(exp,zeeman_choice)

    change_freq(exp,zeeman_choice)

    @kernel()
    def run_mw_rabi(exp):
        exp.qcmos.exp.qcmos.prepare_for_x_scan([25*i for i in range(41)])
        exp.scan_parameter = exp.X_scan_range.sequence
        exp.scan_points = len(exp.scan_parameter)
        # 0. initial
        while True:
            exp.core.break_realtime()
            if exp.zeeman_choice == "+":
                exp.mw.set_mw_parameter(detuning=exp.zeeman_p)
                exp.mw_pi_2 = exp.mw_pi_2_p
            elif exp.zeeman_choice == "-":
                exp.mw.set_mw_parameter(detuning=exp.zeeman_n)
                exp.mw_pi_2 = exp.mw_pi_2_n
            else:
                exp.mw.set_mw_parameter(detuning=0.0)
                exp.mw_pi_2 = exp.mw_pi_2_0
            exp.core.break_realtime()

            for scan_point in range(exp.scan_points):
                exp.core.break_realtime()
                for repeat in range(exp.repeat):
                    # 1 . 切换至 cooling 状态
                    # self.qcmos.qcmos_start_count()
                    delay(exp.cooling_time)
                    # self.qcmos.qcmos_stop_count()

                    # 2. 切换至 pumping 状态
                    exp.l369.switch_cool_to_pump()
                    delay(exp.pumping_time)

                    # ３.　切换至　control　状态
                    with parallel:
                        exp.l369.switch_pump_to_control()
                        exp.mw.mw_on()

                    delay(exp.mw_pi_2)

                    exp.mw.mw_off()
                    delay(2 * us)
                    if exp.EIT_pi:
                        with parallel:
                            # self.l369.EOM_cooling_sw.on()
                            # self.l369.EOM_21_CTRL.on()
                            # self.l369.EOM_41_V1.on()
                            exp.l369.dds_for_EIT_pi.cfg_sw(True)
                    if exp.EIT_sigma:
                        with parallel:
                            # self.l369.EOM_cooling_sw.on()
                            # self.l369.EOM_21_CTRL.on()
                            # self.l369.EOM_41_V1.on()
                            exp.l369.dds_for_EIT_sigma.cfg_sw(True)
                    # delay ramsey 时间
                    delay(exp.scan_parameter[scan_point])

                    if exp.EIT_pi:
                        with parallel:
                            exp.l369.dds_for_EIT_pi.cfg_sw(False)
                            # self.l369.EOM_cooling_sw.off()
                            # self.l369.EOM_21_CTRL.off()
                            # self.l369.EOM_41_V1.off()
                    if exp.EIT_sigma:
                        with parallel:
                            exp.l369.dds_for_EIT_sigma.cfg_sw(False)
                            # self.l369.EOM_cooling_sw.off()
                            # self.l369.EOM_21_CTRL.off()
                            # self.l369.EOM_41_V1.off()
                    delay(2 * us)
                    exp.mw.mw_on()
                    delay(exp.mw_pi_2)

                    # 4. 切换至 detecting 状态
                    with parallel:
                        exp.mw.mw_off()
                        exp.qcmos.qcmos_start_count()
                        exp.l369.switch_control_to_detect()

                    delay(exp.detecting_time)

                    # 5. 切换至 off 状态
                    with parallel:
                        exp.qcmos.qcmos_stop_count()
                    delay(1.0 * ms)
                    exp.l369.switch_detect_to_pre_cooling()
                delay(3 * ms)
                exp.core.wait_until_mu(now_mu())

                delay(1 * ms)
                exp.qcmos.process_for_x_scan(scan_point)
                if exp.scheduler.check_termination():
                    break

    run_mw_rabi(exp)

# 更新 pi/2 参数
    freq_update(exp,qubit_index,zeeman_choice)

    exp.point_now = exp.point_now_temp

@rpc(flags={""})
def update_ref(exp,zeeman_choice):
    if zeeman_choice == "0":
        exp.parameter.ref.Signal_MW.dds_for_mw_fre = exp.parameter.Signal_MW.dds_for_mw_fre
    elif zeeman_choice == "+":
        exp.parameter.ref.Signal_MW.zeeman_p = exp.parameter.Signal_MW.zeeman_p
    elif zeeman_choice == "-":
        exp.parameter.ref.Signal_MW.zeeman_n = exp.parameter.Signal_MW.zeeman_n
    exp.parameter.update_config_json()

@rpc(flags={""})
def change_freq(exp,zeeman_choice):
    if zeeman_choice == "0":
        exp.parameter.Signal_MW.dds_for_mw_fre -= 2000
    elif zeeman_choice == "+":
        exp.parameter.Signal_MW.zeeman_p -= 2000
    elif zeeman_choice == "-":
        exp.parameter.Signal_MW.zeeman_n -= 2000
    exp.parameter.update_config_json()

@rpc(flags={""})
def freq_update(exp, qubit_index,zeeman_choice):
    """获取数据, 拟合, 更新"""
    x = np.array(exp.get_dataset('x_points'))
    y = np.array(exp.get_dataset('probability'))[:,qubit_index]
    detuning = Ramsey_fit(x,y)[0]
    if zeeman_choice == "0":
        exp.parameter.Signal_MW.dds_for_mw_fre += detuning
    elif zeeman_choice == "+":
        exp.parameter.Signal_MW.zeeman_p += detuning
    elif zeeman_choice == "-":
        exp.parameter.Signal_MW.zeeman_n += detuning
    exp.parameter.update_config_json()

@kernel()
def repair():
    alert()
    # 可以用mw spectrum，但后面再加

@rpc(flags={""})
def alert():
    print("MW Rabi Time:Warning!")