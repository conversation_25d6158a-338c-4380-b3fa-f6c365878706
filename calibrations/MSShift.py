import random

from artiq.experiment import *
from structure.points_structure import decode_route
from modules.config import LOADED_PARAMETER
import numpy as np
from cloud.fit_func import <PERSON><PERSON>_fit,Parity_fit
from modules.check_information import get_period,get_reference,get_last_time,update_last_time
import time
from structure.route import Ramsey_MS_scan_phase,MS_scan_phase
from calibrations.cali_func import dataset_prepare
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)

@rpc(flags={""})
def back_check_list(index)->TList(TStr):
    return []

@rpc(flags={""})

def check0_pass(index)->TBool:
    logger.info(f"------ * Start MS Shift {index} Check * -------")

    last_time = get_last_time("MSShift")[index[0]][index[1]]
    period = get_period("MSShift")
    time_now = time.time()
    time_diff = time_now - last_time
    if time_diff > period:
        logger.info("not pass, start calibration")
        return False
    else:
        logger.info("check pass")
        return True

@rpc(flags={""})
def check1_pass(index)->TBool:
    return True


@kernel()
def update(exp,index):
    exp.point_now_temp = exp.point_now
    init_ms_shift_update(exp,index)
    dataset_prepare(exp,[i*np.pi*2.0/30.0+0.0 for i in range(31)])
    create_ms_shift_route(exp,index)
    exp.core.break_realtime()
    exp.run_task_num(1,[i*np.pi*2.0/30.0+0.0 for i in range(31)])
    ms_shift_update(exp,index)

    recover_route(exp)
    exp.point_now = exp.point_now_temp

@rpc(flags={""})
def init_ms_shift_update(exp,index):
    logger.info(f"-------------------------ms {index} shift measure-------------------------")
    # exp.parameter.Light_554.MS_AC_Stark_shift[index[0]][index[1]] = 0.0
    # exp.parameter.Light_554.MS_AC_Stark_shift[index[1]][index[0]] = 0.0
    exp.parameter.update_config_json()
    exp.is_circuit = False
    exp.ion_choice = (index[0],index[1])

@rpc(flags={""})
def create_ms_shift_route(exp,index):
    qubit_index = (index[0],index[1])
    try:
        exp.points_list = decode_route(
            MS_scan_phase(qubit_index, start=0.0, stop=np.pi*2, n_points=31).route
            # Ramsey_MS_scan_phase(qubit_index, start=0.0, stop=np.pi*2, n_points=31).route
        )
    except Exception as e:
        logger.info(e)

@rpc(flags={""})
def ms_shift_update(exp,index):
    x = np.array(exp.get_dataset("x_points"))

    y = np.array(exp.get_dataset("computational_basis_probability"))
    parity = y[:,0]-y[:,3]
    contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])
    a = _[0]
    b = _[3]+np.pi/2
    if a < 0:
        b = b - np.pi
    logger.info(f"shift fit: {a, b}")
    gate_time = exp.parameter.Light_554.MS_time[index[0]][index[1]]
    shift_ms = (-b)/2/2/np.pi/gate_time
    exp.parameter.Light_554.MS_AC_Stark_shift[index[0]][index[1]] += shift_ms
    exp.parameter.Light_554.MS_AC_Stark_shift[index[1]][index[0]] += shift_ms

    # y = np.array(exp.get_dataset("y_probability"))
    # rabi_554,t_fit,y_fit,popt0 = Rabi_fit(x,y[:,index[0]])
    # rabi_554,t_fit,y_fit,popt1 = Rabi_fit(x,y[:,index[1]])
    # logger.info("POPT:",popt0)
    # phi_shift = popt0[3]-np.pi/2
    # if popt0[0]<0:
    #     phi_shift -= np.pi
    #
    # phi_shift = (phi_shift+np.pi)%(2*np.pi)-np.pi
    # parameter = LOADED_PARAMETER()
    # ms_time = parameter.Light_554.MS_time[index[0]][index[1]]
    # shift_ms = phi_shift/2/np.pi/ms_time+parameter.Light_554.q1_AC_Stark_shift
    # logger.info("MS Shift:",shift_ms)
    # exp.parameter.Light_554.MS_AC_Stark_shift[index[0]][index[1]] = shift_ms
    # exp.parameter.Light_554.MS_AC_Stark_shift[index[1]][index[0]] = shift_ms

    exp.parameter.update_config_json()
    update_last_time("MSShift",index)

@rpc(flags={""})
def recover_route(exp):
    exp.route_now = exp.route