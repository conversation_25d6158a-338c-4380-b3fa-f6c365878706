import random

from artiq.experiment import *
from structure.points_structure import decode_route
from modules.config import LOADED_PARAMETER
import numpy as np
from cloud.fit_func import Cross_fit
from modules.check_information import get_period,get_reference,get_last_time,update_last_time
import time
from structure.route import MS_scan_amp
from calibrations.cali_func import dataset_prepare
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)

"""
AOM 幅度 精细校准
"""
@rpc(flags={""})
def back_check_list(index)->TList(TStr):
    return [
        f"RabiTime_{index[0]}",
        f"RabiTime_{index[1]}"
    ]

@rpc(flags={""})

def check0_pass(index)->TBool:
    last_time = get_last_time("MSAOMAmp")[index[0]][index[1]]
    period = get_period("MSAOMAmp")
    time_now = time.time()

    time_diff = time_now - last_time

    logger.info("time_diff:",time_diff)
    if time_diff > period:
        return False
    else:
        return True

@rpc(flags={""})
def check1_pass(index)->TBool:
    parameter = LOADED_PARAMETER()
    ms_aom_amp  = parameter.Light_554.AOM_AWG_amp_ms[index[0]][index[1]]
    ref = get_reference("MSAOMAmp")
    if ms_aom_amp < ref:
        return True
    else:
        update_last_time("MSAOMAmp",index,0.0)
        return False


@kernel()
def update(exp,index):
    exp.point_now_temp = exp.point_now
    init_ms_aom_amp_update(exp,index)

    ms_amp = get_ms_aom_amp(exp,index)

    create_ms_aom_amp_route(exp,index,ms_amp-0.05,ms_amp+0.05,21,n_gate=5)
    dataset_prepare(exp,[(i-10)*0.005+ms_amp for i in range(21)])
    exp.core.break_realtime()
    exp.run_task_num(1,[(i-10)*0.005+ms_amp for i in range(21)])
    ms_aom_amp_update(exp,index)
    recover_route(exp)

    exp.point_now = exp.point_now_temp
@rpc(flags={""})
def init_ms_aom_amp_update(exp,index):
    exp.is_circuit = False
    exp.ion_choice = (index[0],index[1])

@rpc(flags={""})
def create_ms_aom_amp_route(exp,index,start,stop,n_points,n_gate=1,):
    qubit_index = (index[0],index[1])
    exp.points_list = decode_route(
        MS_scan_amp(qubit_index, start=start, stop=stop, n_points=n_points,n_gate=n_gate).route
    )
@rpc(flags={""})
def get_ms_aom_amp(exp,index)->TFloat:
    aom_amp = exp.parameter.Light_554.AOM_AWG_amp_ms[index[0]][index[1]]
    return aom_amp
@rpc(flags={""})
def ms_aom_amp_update(exp,index)->TFloat:
    x = np.array(exp.get_dataset("x_points"))
    y = np.array(exp.get_dataset("computational_basis_probability"))
    P = y[:,0:4:3]
    crosses = Cross_fit(x,P)
    if len(crosses)>0:
        logger.info(f" P11 = P00  at aom amplitude = {crosses[0]} " )
        exp.parameter.Light_554.AOM_AWG_amp_ms[index[0]][index[1]] = crosses[0]
        exp.parameter.Light_554.AOM_AWG_amp_ms[index[1]][index[0]] = crosses[0]
        exp.parameter.update_config_json()
        update_last_time("MSAOMAmp",index)
        return crosses[0]
    else:
        return get_ms_aom_amp(exp,index)
@rpc(flags={""})
def recover_route(exp):
    exp.route_now = exp.route