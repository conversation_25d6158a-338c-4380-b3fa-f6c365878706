"""
用于绘制多个数据集的 XY 图
"""

import numpy as np
import PyQt5  # make sure pyqtgraph imports Qt5
from PyQt5.QtCore import QTimer
from PyQt5.QtWidgets import QPushButton

import pyqtgraph

from artiq.applets.simple import TitleApplet


def generate_distinct_colors(n):
    """生成n种不同颜色的函数"""
    colors = []
    for i in range(n):
        color = pyqtgraph.intColor(i, hues=n)
        colors.append(color)
    return colors


class XYPlot(pyqtgraph.PlotWidget):
    def __init__(self, args, req):
        pyqtgraph.PlotWidget.__init__(self)
        self.setBackground("w")
        # self.setBackground("#1d2d61")

        self.args = args
        self.timer = QTimer()
        self.timer.setSingleShot(True)
        # self.colors = generate_distinct_colors(32)
        self.timer.timeout.connect(self.length_warning)
        self.pause = False

        # # 添加用于恢复 pause 的按钮
        # self.pauseButton = QPushButton("Resume", self)
        # self.pauseButton.clicked.connect(self.toggle_pause)

    def data_changed(self, value, metadata, persist, mods, title):
        data = value
        try:
            y_orig = np.array(data[self.args.y])  # list
            if y_orig.shape == (0, ):
                return
        except KeyError:
            return

        if y_orig.shape[0] !=0:
            # 获取 x 轴的数据
            x = data.get(self.args.x, (False, None))

            # 如果数据的第一行是 NaN，则删除它
            if np.isnan(y_orig[0]).all():
                y_orig = y_orig[1:]

            if x is None:
                x = np.arange(y_orig.shape[0])
            else:
                # 截断或扩展 x 轴数据以匹配 y 轴的长度
                x = x[:y_orig.shape[0]]

            # 清除当前图像并重新绘制
            self.clear()
            row = y_orig.shape[1]
            self.colors = generate_distinct_colors(row)

            # 预定义颜色和标记符号列表
            # colors = ['g', 'r', 'b', 'c', 'm', 'y', 'k']

            # 为 y_orig 的每一列绘制图像
            for i in range(y_orig.shape[1]):
                y = y_orig[:, i]
                color = self.colors[i % len(self.colors)]
                if len(x) == len(y[:]):
                    self.plot(x, y[:], pen=color, name="ion_{}".format(i), symbol="o")

            # 添加图例和标题
            self.addLegend()
            self.setTitle(title)

            # 设置 x 轴范围
            if not self.pause:  # 仅在未暂停时更新
                if len(x) != 0:
                    self.setXRange(0, x[-1])  # 更新为显示从 0 到 x 轴最后一个元素的范围
        else:
            if not self.timer.isActive():
                self.timer.start()

    def toggle_pause(self):
        self.pause = False  # 将 pause 设置为 False，恢复更新

    def wheelEvent(self, event):
        self.pause = True  # 在鼠标滚轮滚动时设置 pause 为 True
        super().wheelEvent(event)  # 调用父类的 wheelEvent 方法以保留默认行为

    def mousePressEvent(self, event):
        self.pause = True  # 当鼠标按下时设置 pause 为 True
        super().mousePressEvent(event)

    def length_warning(self):
        self.clear()
        text = "⚠️ dataset lengths mismatch:\n" \
               "There should be one more bin boundaries than there are Y values"
        self.addItem(pyqtgraph.TextItem(text))


def main():
    applet = TitleApplet(XYPlot)
    applet.add_dataset("y", "Y values")
    applet.add_dataset("x", "X values", required=False)
    applet.add_dataset("threshold", "Threshold", required=False)
    # applet.add_dataset("error", "Error bars for each X value", required=False)
    # applet.add_dataset("fit", "Fit values for each X value", required=False)
    applet.run()


if __name__ == "__main__":
    main()
