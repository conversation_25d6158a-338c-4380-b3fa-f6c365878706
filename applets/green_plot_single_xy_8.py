#!/usr/bin/env python3

import numpy as np
import PyQt5  # make sure pyqtgraph imports Qt5
from PyQt5.QtCore import QTimer
import pyqtgraph
from PyQt5.QtGui import QFont
from artiq.applets.simple import TitleApplet


class XYPlot(pyqtgraph.PlotWidget):
    def __init__(self, args, req):
        pyqtgraph.PlotWidget.__init__(self)
        self.args = args
        self.timer = QTimer()
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.length_warning)

        # 绘图前记录是否含有不匹配的数据长度
        self.mismatch = {'X values': False,
                         'Error bars': False,
                         'Fit values': False}
        self.init_ui()  # 调用 initUI 方法初始化 UI

    def init_ui(self):
        """
        初始化 UI 的方法。使用配置字典来管理各种 UI 组件的设置。
        """

        # 设置白色背景（如果需要）
        # self.setBackground('w')

        # # 定义字体
        # label_font = QFont("Times New Roman", 18)
        # tick_font = QFont("Times New Roman", 12)
        #
        # # 设置左轴（Y 轴）标签
        # left_axis = self.getPlotItem().getAxis('left')
        # left_axis.setLabel('Probability', color='k', font=label_font)
        # left_axis.setTickFont(tick_font)
        # left_axis.setTextPen('k', width=2)
        #
        # # 设置底轴（X 轴）标签
        # bottom_axis = self.getPlotItem().getAxis('bottom')
        # bottom_axis.setLabel('Scan Parameter', color='k', font=label_font)
        # bottom_axis.setTickFont(tick_font)
        # bottom_axis.setTextPen('k', width=2)
        #
        # # 边框设置
        # self.getPlotItem().getViewBox().setBorder(color='k', width=2)
        pass

    # 用于处理默认值和长度检查的函数
    def process_data(self, data, key):
        result = data.get(key, None)
        if result is None or not len(result):
            return None
        return result

    def data_validation(self, x, y, error, fit):
        """当数据更新时，检查数据长度是否匹配"""

        # 检查 y 和 x 的长度是否匹配
        if not len(y) or len(y) != len(x):
            self.mismatch['X values'] = True

        # 检查 error 的长度是否匹配 y
        if error is not None and hasattr(error, "__len__"):
            if not len(error) or len(error) != len(y):
                self.mismatch['Error bars'] = True

        # 检查 fit 的长度是否匹配 y
        if fit is not None:
            if not len(fit) or len(fit) != len(y):
                self.mismatch['Fit values'] = True

    def plot_data(self, x, y, error, fit, title):
        """绘图逻辑"""
        self.clear()
        self.setTitle(title)
        self.addLegend()

        if error is not None:
            errorPen = {'color': (0, 255, 0), 'width': 3}

            errbars = pyqtgraph.ErrorBarItem(x=np.array(x), y=np.array(y), height=error, pen=errorPen)
            self.addItem(errbars)

        if fit is not None:
            xi = np.argsort(x)
            fit_pen = pyqtgraph.mkPen(color=(255, 0, 0), width=1)
            self.plot(x[xi], fit[xi], pen=fit_pen)

        self.plot(x, y, pen="g", symbol="x", symbolSize=10, symbolBrush=(0, 0, 255))

    def data_changed(self, value, metadata, persist, mods, title):
        """数据更新时自动调用此函数，更新数据"""
        # 1. 数据提取
        y = self.process_data(value, self.args.y)
        if y is None:
            """如果没有 y 数据，不绘图"""
            return
        x = self.process_data(value, self.args.x)
        error = self.process_data(value, self.args.error)
        fit = self.process_data(value, self.args.fit)
        if x is None:
            x = np.arange(len(y))

        # 2. 数据检查
        if any(self.mismatch.values()):
            if not self.timer.isActive():
                self.timer.start(1000)
            return

        # 3. 绘图
        self.plot_data(x, y, error, fit, title)  # 调用绘图函数


    def length_warning(self):
        self.clear()
        text = "⚠️ dataset lengths mismatch:\n"
        errors = ', '.join([k for k, v in self.mismatch.items() if v])
        text = ' '.join([errors, "should have the same length as Y values"])
        self.addItem(pyqtgraph.TextItem(text))


def main():
    applet = TitleApplet(XYPlot)
    applet.add_dataset("y", "Y values")
    applet.add_dataset("x", "X values", required=False)
    applet.add_dataset("error", "Error bars for each X value", required=False)
    applet.add_dataset("fit", "Fit values for each X value", required=False)
    applet.run()


if __name__ == "__main__":
    main()
