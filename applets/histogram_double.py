#!/usr/bin/env python3

import PyQt5  # make sure pyqtgraph imports Qt5
import numpy as np
from PyQt5.QtCore import QTimer, Qt
import pyqtgraph
from PyQt5 import QtWidgets
from artiq.applets.simple import TitleApplet
from PyQt5.QtGui import QColor
import PyQt5.QtCore as QtCore
import logging

logger = logging.getLogger(__name__)


# QtWidgets.QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
# QtWidgets.QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)
# QtCore.QCoreApplication.setAttribute(QtCore.Qt.AA_EnableHighDpiScaling, True)
# QtCore.QCoreApplication.setAttribute(QtCore.Qt.AA_UseHighDpiPixmaps, True)
class HistogramPlot(pyqtgraph.PlotWidget):
    def __init__(self, args, req):
        pyqtgraph.PlotWidget.__init__(self)
        self.args = args
        self.timer = QTimer()
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.length_warning)

    def data_changed(self, value, metadata, persist, mods, title):
        """数据更新, 接收 N * 2 的 histogram 数据, 第一列为亮态数据, 第二列为暗态数据
        x, 标记数据, 非必须
        """
        # 1. 获取数据
        # data = value
        try:
            y_orig = np.array(value[self.args.y])  # 得到 y 数据的 list 表示
            if self.args.x is None:
                x = 0
            else:
                x = value[self.args.x]

            if self.args.error is None:
                error = [0, 0]
            else:
                error = value[self.args.error]
        except KeyError:
            return

        if len(y_orig) != 0:
            # 2. 清空绘图组件
            self.timer.stop()
            self.clear()
            # 3. 提取数据
            state_0 = y_orig[:, 0]
            state_1 = y_orig[:, 1]
            x_pos = np.arange(len(state_0))
            bar_width = 0.3  # 每个直方图的宽度
            self.setBackground("#ffffff")
            self.getAxis('left').setPen("k")
            self.getAxis('left').setTextPen("k")
            self.getAxis('bottom').setPen("k")
            self.getAxis('bottom').setTextPen("k")
            # 4. 绘图
            bar0 = pyqtgraph.BarGraphItem(
                x=x_pos - bar_width / 2,
                height=state_0,
                width=bar_width,
                brush=QColor("#f76c2c"),
            )
            self.addItem(bar0)
            bar1 = pyqtgraph.BarGraphItem(
                x=x_pos + bar_width / 2,
                height=state_1,
                width=bar_width,
                brush=QColor("#f9d342"),
            )
            self.addItem(bar1)
            if x is not None:
                line = pyqtgraph.InfiniteLine(
                    pos=x, angle=90, pen=pyqtgraph.mkPen(color="#000000", width=2)
                )  # 红色竖线#e74c3c #f7f4e9
                self.addItem(line)

            e1 = error[0]
            e2 = error[1]
            total_error_rate = e1 + e2
            overlap_text = (
                f"暗态判断成亮态: {e1}\n"
                f"亮态判断成暗态: {e2}\n"
                f"总错误率: {total_error_rate/2}"
            )

            text_item = pyqtgraph.TextItem(
                overlap_text, color="#000000", anchor=(0.5, 0)
            )  # 文本位置可调
            # text_item.setPos(0.5, 1.05)  # 将文本位置设置在图形上方  # 设置文本位置在右上角
            # text_item.setPos(self.width() - 150, 10)
            text_item.setFont(pyqtgraph.QtGui.QFont("Arial", 12))
            text_item.setPos(
                self.plotItem.viewRange()[0][1] - 4,
                self.plotItem.viewRange()[1][1] - 10,
            )
            self.addItem(text_item)

            self.autoRange()  # 自动调整显示范围
        else:
            if not self.timer.isActive():
                self.timer.start()

    def length_warning(self):
        self.clear()
        text = (
            "⚠️ dataset lengths mismatch:\n"
            "There should be one more bin boundaries than there are Y values"
        )
        self.addItem(pyqtgraph.TextItem(text))


def main():
    applet = TitleApplet(HistogramPlot)
    applet.add_dataset("y", "histogram")
    applet.add_dataset("x", "threshold", required=False)
    applet.add_dataset("error", "error_rate", required=False)
    applet.run()


if __name__ == "__main__":
    main()
