import numpy as np
from PyQt5.QtCore import QTimer
from PyQt5.QtCore import Qt
import pyqtgraph
from artiq.applets.simple import TitleApplet


class SingleShotHistogram(pyqtgraph.PlotWidget):
    """
    本绘图组件由于绘制经过 single shot histogram
    """
    def __init__(self, args, req):
        pyqtgraph.PlotWidget.__init__(self)
        self.args = args
        self.timer = QTimer()
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.length_warning)

    def data_changed(self, value, metadata, persist, mods, title):
        try:
            y_orig = np.array(value[self.args.y])  # 得到 y 数据的 list 表示
        except KeyError:
            return
        
        if y_orig.shape[0] != 0:  # 数据不为 0 才进一步搞
            # 2. 清空绘图组件
            self.timer.stop()
            self.clear()
            # self.setBackground("#1d2d61")
            self.setBackground("w")
            # 3. 提取数据和形状检查
            if y_orig.ndim != 2 or y_orig.shape[1] != 2:
                return
            
            # 提取 X 轴和 Y 轴
            x_original = y_orig[:, 0].astype(int)  # 整数
            y = y_orig[:, 1].astype(float)  # 

            # 确定最高位数，转换为 Python int 以使用 bit_length()
            max_x = max(x_original)
            max_bits = int(max_x).bit_length()
            # 将 X 转换为固定长度的二进制字符串，保留前导零
            x_binary = [format(int(val), f"0{max_bits}b") for val in x_original]

            # 对 X 进行排序
            sorted_indices = np.argsort(x_original)
            x_sorted = x_original[sorted_indices]
            y_sorted = y[sorted_indices]
            x_binary_sorted = [x_binary[i] for i in sorted_indices]

            # 为避免空位，将 X 映射到连续的位置
            x_mapped = np.arange(len(x_sorted))

            bg = pyqtgraph.BarGraphItem(x=x_mapped, height=y_sorted, width=0.618, brush="#f9d342")
            self.addItem(bg)
            
            # 设置 X 轴范围
            self.setXRange(-0.5, len(x_sorted) - 0.5)
            # 设置 Y 轴范围自动
            self.enableAutoRange(axis=pyqtgraph.ViewBox.YAxis)

            # 设置 X 轴标签为固定长度的二进制
            ax = self.getAxis("bottom")
            ticks = [(pos, label) for pos, label in zip(x_mapped, x_binary_sorted)]
            ax.setTicks([ticks])

            # 设置 X 轴标签竖直显示
            # ax.setStyle(tickTextAngle=90)  # 将标签旋转 90 度

            # 设置标题和标签
            self.setTitle("Computational Basis Histogram", color="k")  # 标题使用白色
            self.setLabel("left", "Counts", color="k")  # 设置Y轴标签颜色为白色
            self.setLabel("bottom", f"Basis", color="k")

            # 设置轴线和字体颜色为白色，增强对比度
            ax = self.getAxis("bottom")
            ax.setPen(pyqtgraph.mkPen(color="k", width=1))  # 设置底部轴线为白色
            ax.setTextPen(pyqtgraph.mkPen(color="k"))  # 设置文字颜色为白色

            left_axis = self.getAxis("left")
            left_axis.setPen(pyqtgraph.mkPen(color="k", width=1))  # 设置左侧轴线为白色
            left_axis.setTextPen(pyqtgraph.mkPen(color="k"))  # 设置文字颜色为白色

            # 设置网格线
            # grid_pen = pyqtgraph.mkPen(color="#ffffff", width=0.5, style=Qt.DotLine)
            grid_pen = pyqtgraph.mkPen(color="k", width=0.5, style=Qt.DotLine)
            self.showGrid(x=True, y=True, alpha=0.3)

            # 获取 ViewBox 并设置网格线样式
            view_box = self.getPlotItem().getViewBox()
            view_box.setBackgroundColor(None)
            
        else:
            if not self.timer.isActive():
                self.timer.start()

    def length_warning(self):
        self.clear()
        text = "⚠️ dataset lengths mismatch:\n" \
               "There should be one more bin boundaries than there are Y values"
        self.addItem(pyqtgraph.TextItem(text))


def main():
    applet = TitleApplet(SingleShotHistogram)
    applet.add_dataset("y", "single_shot_histogram")
    applet.run()


if __name__ == "__main__":
    main()
