"""
pmt 32 通道的绘图组件
"""
# 导入所需模块
from PyQt5.QtCore import QTimer
import pyqtgraph as pg
from artiq.applets.simple import TitleApplet
from PyQt5.QtGui import QFont
from pyqtgraph import BarGraphItem


class PMTPlot(pg.PlotWidget):
    def __init__(self, args, req):
        super().__init__()  # 使用 super() 继承父类的初始化，更 Pythonic
        self.setup_ui(args)  # UI 初始化
        self.setup_timer()  # 定时器初始化

    def setup_ui(self, args):
        """UI 初始化。

        设置背景色、x 轴和 y 轴的标签。
        """
        self.args = args
        self.setBackground('w')  # 设置白色背景
        self.setXRange(0, 32)  # 设置 x 轴的显示范围为 -1 到 33
        # self.setYRange(0, 100)  # 设置 y 轴的显示范围为 -1 到 100

        # 禁用自动范围调整
        # self.setMouseEnabled(x=False)
        self.disableAutoRange('x')

        # 轴 UI 设置
        # 两个轴的特性设置
        self.getPlotItem().getAxis('left').setLabel(
            '<span style="font-size: 18pt; font-family: Times New Roman; color: black;">Number of Photon</span>')
        self.getPlotItem().getAxis('left').setTickFont(QFont("Times New Roman", 12))
        self.getPlotItem().getAxis('left').setTextPen('k', width=2)

        self.getPlotItem().getAxis('bottom').setLabel(
            '<span style="font-size: 18pt; font-family: Times New Roman; color: black;">PMT Channel Index</span>')
        self.getPlotItem().getAxis('bottom').setTickFont(QFont("Times New Roman", 12))
        self.getPlotItem().getAxis('bottom').setTextPen('k', width=2)

        # 边框设置
        self.getPlotItem().getViewBox().setBorder(color='k', width=2)

    def setup_timer(self):
        """定时器设置。

        创建一个单次触发的定时器，用于显示数据长度不匹配的警告。
        """
        self.timer = QTimer(self)
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.length_warning)  # 超时则调用 length_warning 方法

    def get_data(self, data):
        """获取数据。

        从 data 字典中提取 x 和 y 轴的数据。
        """
        try:
            y = data[self.args.y]
            x = data[self.args.x] if self.args.x else list(range(len(y))) + [len(y)]
        except KeyError:
            return None, None  # 如果数据缺失，返回 None
        return x, y

    def data_changed(self, value, metadata, persist, mods, title):
        """数据更新处理。

        当数据发生变化时，调用该方法。根据新数据更新图表。
        """
        x, y = self.get_data(value)  # 获取数据


        if x is not None and len(x) == len(y):
            self.update_plot(x, y, title)  # 更新图表
        else:
            self.timer.start(1000)  # 启动定时器

    def update_plot(self, x, y, title):
        """更新绘图。

        清除当前图表并根据新的 x 和 y 数据绘制新图。
        """
        self.timer.stop()  # 停止定时器
        self.clear()  # 清除图表
        # self.plot(x, y, stepMode=True, fillLevel=0, brush=(0, 0, 255, 150))  # 绘制新图

        curve_x = []
        curve_y = []
        width=0.9
        curve_x.append(x[0] - width / 2)
        curve_y.append(0)
        y_data=y
        for j, y in enumerate(y_data):
            # 柱子左边缘
            curve_x.append(x[j] - width / 2)
            curve_y.append(0)
            curve_x.append(x[j] - width / 2)
            curve_y.append(y)

            # 柱子右边缘
            curve_x.append(x[j] + width / 2)
            curve_y.append(y)
            curve_x.append(x[j] + width / 2)
            curve_y.append(0)

            # 添加最后一个点的底部
        curve_x.append(x[-1] + width / 2)
        curve_y.append(0)

        # 绘制曲线并设置填充颜色
        self.plot(curve_x, curve_y, stepMode=False, pen=pg.mkPen((255,255,50), width=0.6), fillLevel=0, brush=(150, 150, 10, 200))

        self.setTitle(title)  # 设置图表标题

    def length_warning(self):
        """长度警告。

        当数据长度不匹配时，显示一个警告信息。
        """
        self.clear()
        text = "⚠️ dataset lengths mismatch: There should be one more bin boundaries than there are Y values"
        self.addItem(pg.TextItem(text))  # 添加警告文本到图表


def main():
    applet = TitleApplet(PMTPlot)  # 创建 PMTPlot 类的实例
    applet.add_dataset("y", "Y values")  # 添加 y 数据集
    applet.add_dataset("x", "Bin boundaries", required=False)  # 添加 x 数据集（可选）
    applet.run()  # 运行 applet


if __name__ == "__main__":
    main()  # 程序入口
