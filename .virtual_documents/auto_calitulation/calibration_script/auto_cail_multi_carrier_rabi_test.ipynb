


# 项目路径调整
import os

current_path =  os.path.abspath("")
print(current_path)

project_root = os.path.abspath(os.path.join(current_path, "../.."))
os.chdir(project_root)
print(os.path.abspath(""))



import numpy as np

from cloud.artiq_manager import get_result
from modules.config import LOADED_PARAMETER
from auto_calitulation.fit_model import aod_ratio_fit, rabi_sin_fit, plot_sin_fitted_data, sin_for_fit
from auto_calitulation.tools_for_calibration import get_result_x_y, change_dir
from pprint import pprint



x, y = get_result_x_y(15415, qubit_index=(0, 2))
print(x)
print(y)
x = x * 1e-6



param = rabi_sin_fit(x, y)
param


plot_sin_fitted_data(x, y, param, sin_for_fit)


import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import differential_evolution

# 定义拟合的目标函数
def sin_for_fit(x, amp, fre, phase, shift):
    return amp * np.sin(2 * np.pi * fre * x + phase) + shift

def rabi_fit_single(x, y):
    """
    x: 形状为  shape = (N, ) 的一维数组
    y: 形状为 shape = (N, m) 的二维数组
    """
    fit_params = []
    
    # 对每一列（每一条曲线）进行拟合
    for i in range(y.shape[1]):
        init_guess = sin_init_guess(x, y[:, i])
        print("Initial Guess:", init_guess)
        
        # 使用差分进化进行全局优化
        opt_params = differential_evolution_fitting(x, y[:, i])
        fit_params.append(opt_params)

    return np.array(fit_params)

def differential_evolution_fitting(x, y):
    """
    使用差分进化算法进行参数拟合
    """
    # 差分进化算法的适应度函数
    def fitness_function(params):
        amp, fre, phase, shift = params
        fitted_y = sin_for_fit(x, amp, fre, phase, shift)
        error = np.sum((y - fitted_y)**2)  # 使用最小二乘误差作为适应度
        return error
    
    # 设置差分进化的参数
    bounds = [(0, 5), (0, 5), (0, 2 * np.pi), (-5, 5)]  # 参数的边界：振幅、频率、相位、偏移量
    
    # 使用 scipy 的 differential_evolution 进行全局优化
    result = differential_evolution(fitness_function, bounds, maxiter=1000, popsize=50, mutation=(0.5, 1), recombination=0.7)
    
    return result.x  # 返回最优参数

def sin_init_guess(x, y):
    """
    功能: 对一组 x, y 数据做初始猜测, 以优化拟合精度
    return: shape = (4, ) 
    """
    # 进行傅里叶变换
    Y = abs(np.fft.fft(y))
    freqs = np.fft.fftfreq(len(x), d=x[1] - x[0])[1:len(x)//2]  # 计算正频率部分
    peak_freq_index = np.argmax(np.abs(Y[1:])) + 1  # 跳过零频率
    peak_freq = np.abs(freqs[peak_freq_index])  # 根据主频索引确认主频
    peak_freq_hz = peak_freq / (2 * np.pi)  # 转换为 Hz

    # 使用傅里叶变换结果估计初始猜测的参数
    amp = np.max(y) - np.min(y)  # 振幅估计为最大值与最小值之差
    fre = peak_freq_hz  # 主频估计为傅里叶变换中的主频，单位为 Hz
    phase = 0  # 初始相位猜测为0
    shift = np.mean(y)  # 偏移量（D）为信号的平均值
    return [amp, fre, phase, shift]

# 绘图函数
def plot_fitted_data(x, y, fit_params, model_func):
    # x 是 (1, N)，y 是 (N, m)，fit_params 是 (m, 4)
    x = np.squeeze(x)
    plt.figure(figsize=(10, 6))

    # 绘制原始数据和拟合结果
    for i in range(y.shape[1]):
        # 绘制原始数据点
        plt.plot(x, y[:, i], 'o', label=f'Original Data {i+1}')
        
        # 使用拟合参数生成拟合曲线
        fitted_y = model_func(x, *fit_params[i])
        
        # 绘制拟合曲线
        plt.plot(x, fitted_y, '-', label=f'Fitted Curve {i+1}')
    
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.legend()
    plt.title('Data Fitting to Sine Function')
    plt.grid(True)
    plt.show()

# 测试：带噪声的单个频率的正弦信号
x = np.linspace(0, 10, 100)  # x 变为一维数组
y = 2 * np.sin(1.5 * x + 0.5) + 1  # 正弦信号
y += 0.5 * np.random.randn(x.shape[0])  # 加噪声

y = y[:, np.newaxis]  # 转换为二维数组

# 拟合数据
fit_params = rabi_fit_single(x, y)
print("Fitting Parameters:\n", fit_params)

# 绘制结果
plot_fitted_data(x, y, fit_params, sin_for_fit)



import numpy as np
from scipy.optimize import curve_fit, differential_evolution

def sin_for_fit(x, amp, fre, phase, shift):
    """正弦函数模型，用于拟合"""
    return amp * np.sin(2 * np.pi * fre * x + phase) + shift

def sin_init_guess(x, y):
    """根据傅里叶变换估计初始参数"""
    Y = abs(np.fft.fft(y))
    freqs = np.fft.fftfreq(len(x), d=x[1] - x[0])[1:len(x)//2]
    peak_freq_index = np.argmax(np.abs(Y[1:])) + 1
    peak_freq = np.abs(freqs[peak_freq_index])
    peak_freq_hz = peak_freq / (2 * np.pi)

    amp = np.max(y) - np.min(y)
    fre = peak_freq_hz
    phase = 0
    shift = np.mean(y)
    return [amp, fre, phase, shift]

def rabi_sin_fit(x, y):
    """对单个或多个 Rabi 振荡曲线进行正弦函数拟合。
    
    参数:
    x: ndarray
        时间点数据, shape=(N,) 的一维数组
    y: ndarray
        测量数据, shape=(N, m) 的二维数组, 其中 N 是时间点数, m 是曲线数量

    返回:
    ndarray
        拟合参数数组, shape=(m, 4), 每行包含一条曲线的 4 个拟合参数
        参数顺序为: [振幅, 频率, 相位, 偏置]
    
    说明:
    使用 sin_for_fit 函数作为拟合模型, 通过 sin_init_guess 获取初始参数估计。
    """
    fit_params = []

    # 对每一列（每一条曲线）进行拟合
    for i in range(y.shape[1]):
        init_guess = sin_init_guess(x, y[:, i])
        
        # 设置合理的边界
        bounds = (
            (0.0, init_guess[1]/2, -np.pi, -np.inf),  # lower
            (1.0, init_guess[1]*2, np.pi, np.inf)     # upper
        )
        
        try:
            # 尝试使用 curve_fit 进行拟合
            popt, _ = curve_fit(sin_for_fit, x, y[:, i], p0=init_guess, bounds=bounds)
            fit_params.append(popt)
        except Exception as e:
            print(f"Curve fitting failed for curve {i+1}, using DE for global optimization: {e}")
            
            # 如果拟合失败，使用差分进化进行全局优化
            fit_params.append(differential_evolution_fitting(x, y[:, i], bounds))
    
    return np.array(fit_params)

def differential_evolution_fitting(x, y, bounds):
    """使用差分进化进行参数拟合"""
    # 差分进化算法的适应度函数
    def fitness_function(params):
        amp, fre, phase, shift = params
        fitted_y = sin_for_fit(x, amp, fre, phase, shift)
        error = np.sum((y - fitted_y)**2)  # 使用最小二乘误差作为适应度
        return error
    
    # 使用 scipy 的 differential_evolution 进行全局优化
    result = differential_evolution(fitness_function, bounds, maxiter=1000, popsize=50, mutation=(0.5, 1), recombination=0.7)
    
    return result.x  # 返回最优参数

# 示例测试
if __name__ == "__main__":
    # 测试数据：带噪声的单个频率的正弦信号
    x = np.linspace(0, 10, 100)  # x 变为一维数组
    y = 2 * np.sin(1.5 * x + 0.5) + 1  # 正弦信号
    y += 0.5 * np.random.randn(x.shape[0])  # 加噪声
    y = y[:, np.newaxis]  # 转换为二维数组

    # 执行拟合
    fit_params = rabi_sin_fit(x, y)
    print("Fitting Parameters:\n", fit_params)




