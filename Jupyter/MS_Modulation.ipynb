#%% md
# 初始化
#%% md
## 连接
#%%
%matplotlib inline
from jupyter_function import *
import dill
import json
#%%
from cloud.pulsedesign.pulse_design_function import djmt,Parameters

import matplotlib.pyplot as plt
from matplotlib.collections import LineCollection
def draw_pst(paras:Parameters):
    nu = paras.nu
    tau = paras.tau
    M=len(nu)
    N_t=2001
    djm_values=np.zeros((M,2,N_t),dtype=float)#存储djm的实部和虚部
    
    t_plot=np.linspace(0.001,tau,N_t)
    for m in range(M):
        for i in range(N_t):
            d_re,d_im=djmt(m,t_plot[i],paras)
            djm_values[m,0,i]=d_re
            djm_values[m,1,i]=d_im

        x=djm_values[m,0,:]
        y=djm_values[m,1,:]
        points=np.array([x,y]).T.reshape(-1,1,2)
        segments=np.concatenate([points[:-1],points[1:]],axis=1)

        #创建颜色映射
        cmap=plt.get_cmap('viridis')#选择颜色映射
        norm=plt.Normalize(0,len(x))#归一化到点的编号范围

        #创建LineCollection
        lc=LineCollection(segments,cmap=cmap,norm=norm)
        lc.set_array(np.arange(len(x)))#设置颜色随点的编号变化

        #绘图
        fig,ax=plt.subplots(figsize=(4,4))
        plt.axhline(0,color='black',linewidth=0.5,linestyle='--')#实轴
        plt.axvline(0,color='black',linewidth=0.5,linestyle='--')#虚轴
        line=ax.add_collection(lc)
        #fig.colorbar(line,ax=ax,label='PointIndex')
        lim=np.max(abs(djm_values[m,:,:]))
        #lim=15
        ax.set_title('$d_{jm}$intheComplexPlane')
        #设置坐标轴范围
        ax.set_xlim(-lim,lim)
        ax.set_ylim(-lim,lim)
        plt.show()
#%%
manager = ARTIQManager()
#%%
parameter = read_parameter()
#%% md
# PM
#%% md
## 门参数设置
#%%
parameter = read_parameter()
qubit_index = (2,3)
phonon_index = 0
gate_time = 200e-6
detuning = 10000
N_modulation = 52
parameter.Light_554.MS_phonon_index = phonon_index
parameter.Light_554.MS_time = gate_time
parameter.Light_554.freq_detuning = detuning
# parameter.update_config_json()

gate_frequency = parameter.Light_554.Motion_freq[phonon_index] + parameter.Light_554.freq_detuning

from cloud.pulsedesign.PM_analytic import PulseDesigner


modulator = PulseDesigner(qubit_index,
                          gate_time,
                          len(parameter.Light_554.Motion_freq),
                          N_modulation,
                          parameter.Light_554.Motion_freq,
                          gate_frequency)

parameter.Light_554.freq_detuning = modulator.mu/2/np.pi-parameter.Light_554.Motion_freq[phonon_index]
detuning = modulator.mu/2/np.pi-parameter.Light_554.Motion_freq[phonon_index]
print("detuning:",parameter.Light_554.freq_detuning)
# parameter.update_config_json()
#%% md
## 画图
#%%
draw_pst(modulator.paras)
#%% md
## 导入
#%%
parameter = read_parameter()
parameter.Light_554.MS_phonon_index = phonon_index
parameter.Light_554.MS_time = gate_time
parameter.Light_554.freq_detuning = detuning
parameter.update_config_json()
#%%
# 保存计算波形到文件
with open('C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\cloud\\pulsedesign\\PM_modulator.pkl', 'wb') as f:
    dill.dump(modulator, f)
#%%
#读取旧的波形
with open('C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\\ionctrl_develop\\cloud\\pulsedesign\\PM_modulator.pkl',
            'rb') as f:
    modulator = dill.load(f)
#%%
modulator.omega/2/np.pi
#%% md
## 扫AOM幅度
#%%
parameter = read_parameter()
# parameter.Light_554.MS_phonon_index = phonon_index
parameter.Light_554.MS_time = gate_time*17
# parameter.Light_554.freq_detuning = detuning
parameter.update_config_json()
#%%
# 定义扫描参数
scan_start = 0.135
scan_stop = 0.14
n_scan_point = 11
# qubit_index = (0,1)

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_aom = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan',
                               start=scan_start,
                               stop=scan_stop,
                               npoints= n_scan_point),
                      'qubit_index':str(qubit_index),
                      'ion_choice':str(qubit_index)
                          },
                   'class_name': 'MSScanAOMAmpPM',
                   'file': 'repository\\MS_gate_exp\\PM\\MS_scan_aom_amp_PM.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MS_aom,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])
x = x[0:len(y)]
# 寻找 00和11 交叉点
P = y[:,0:4:3]
crosses = Cross_fit(x,P)

fig,ax = plt.subplots()
for cross in crosses:
    ax.axvline(x = cross,color = "r",linestyle ="--")
if len(crosses)>0:
    print(f" P11 = P00  at aom amplitude = {crosses[0]} " )

ax.plot(x, y,'-o')
ax.set_xlabel('AOM amplitude')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan AOM amplitude')
# save_plot(fig,base_filename=f"MS_AOM_amp_RID{rid}")
#%%
parameter = read_parameter()
parameter.Light_554.AOM_AWG_amp_ms = crosses[0]
# parameter.Light_554.AOM_AWG_amp_ms = 0.134365

parameter.update_config_json()
#%% md
## MS扫时间
#%%
#定时扫描参数
scan_start = 00e-6
scan_stop = 1000e-6
n_scan_point = 51

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MSPM_t = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'MSPM',
                   'file': 'repository\\MS_gate_exp\\PM\\MS_gate_PM.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MSPM_t,manager)
#%% md
## Parity
#%%
parameter = read_parameter()
# parameter.Light_554.MS_phonon_index = phonon_index
parameter.Light_554.MS_time = gate_time*21
# parameter.Light_554.freq_detuning = detuning
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0
scan_stop = 2 * np.pi
n_scan_point = 21

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
Parity = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan',
                            start=scan_start,
                            stop=scan_stop,
                            npoints= n_scan_point
                            ),

                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'ParityPM',
                   'file': 'repository\\MS_gate_exp\\PM\\Parity_PM.py',
                   'log_level': 30}
rid= submit_exp(Parity,manager)
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])[:16]
y = np.array(result["datasets"]["computational_basis_probability"])[:16]
parity = y[:,0]+y[:,3]-y[:,1]-y[:,2]

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])
fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/100)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan Parity')
# save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%% md
# AM
#%% md
## AOM 幅度-Rabi对应关系扫描
#%%
from scipy.optimize import curve_fit,minimize
def Rabi_fit(t,y):
    amp = y.max()-y.min()
    a0 = y.mean()
    data_fft = abs(np.fft.fft(y))
    pos = max(enumerate(data_fft[1:int(len(t) / 2)]), key=lambda x: x[1])[0]
    xmin = t[0]
    xmax = t[-1]
    xscale = xmax - xmin
    fft = np.fft.fft(y)
    freq = np.fft.fftfreq(len(t), t[1]-t[0])
    freq = abs(freq[np.argmax(np.abs(fft[1:])) + 1]) 
    print(freq)
    p0 = (1/2, freq, 1/2, 1.5*np.pi, 1000) # amp, freq, a0, phi0,tau
    popt, pcov = curve_fit(Sin,t,y,p0 =p0,bounds=([0.48,0.0,0.4,0,1000],[0.5,2,0.6,np.inf,np.inf]),maxfev = 10000)

    t_fit = np.linspace(t[0],t[-1],1001)
    y_fit = Sin(t_fit,popt[0],popt[1],popt[2],popt[3],popt[4])
    rabi_time = abs(round(1/popt[1]/1e6,9))

    return rabi_time,t_fit, y_fit,popt
def Sin(x, amp, freq, a0, phi0,gamma):
    return amp*np.exp(-x/gamma)*np.sin(2*np.pi*freq*x + phi0) + a0
#%%
from scipy.optimize import least_squares
def Rabi_fit(t,y):
    def residual(params, x, y):
        amp, freq, a0, phi0,gamma = params
        return y - (amp*np.exp(-x/gamma)*np.sin(2*np.pi*freq*x + phi0) + a0)

    # 使用多个随机初值尝试
    best_params = None
    best_error = float('inf')

    for _ in range(20):
        initial_guess = [
            np.random.uniform(0, 0.5),      # A
            np.random.uniform(0.1, 1),      # freq
            np.random.uniform(0, 1),        # offset
            np.random.uniform(0, 2*np.pi),  # phase
            np.random.uniform(1000, 1001)        # offset
        ]
        bounds = (
            [0, 0,0, -2*np.pi, -np.inf],  # 参数下限
            [1, 2, 1,2*np.pi, np.inf]      # 参数上限
                )
        result = least_squares(residual, initial_guess, args=(t, y),bounds=bounds)
        if result.cost < best_error:
            best_error = result.cost
            best_params = result.x
    popt = best_params
    t_fit = np.linspace(t[0],t[-1],1001)
    y_fit = Sin(t_fit,popt[0],popt[1],popt[2],popt[3],popt[4])
    rabi_time = abs(round(1/popt[1]/1e6,9))

    return rabi_time,t_fit, y_fit,popt
#%%
ion_choice = 3
def submit_rabi(aom_amp):
    #定义扫描参数
    scan_start = 0e-6
    scan_stop = 2e-6/aom_amp
    n_scan_point = 51
    
    #更新外部修改的config参数
    parameter = read_parameter()

    #定义expid
    RabiRaman = {'arguments': {'X_scan_range':
                            dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                            'rabi_choice':'Carrier',
                            'side_choice':'All',
                            'qubit_index':f'({ion_choice},)',
                            'aom_amp': aom_amp
                            },
                    'class_name': 'RamanRabi',
                    'file': 'repository\\QCMOS_package\\Raman_Rabi_amp.py',
                    'log_level': 30}

    #提交实验
    rid= submit_exp(RabiRaman,manager)
    return rid

def fit_rabi(rid):
    # 实验结果提取
    result = get_result(f"{rid}",root_dir=result_dir)
    x = np.array(result["datasets"]["x_points"])[8:]
    y = np.array(result["datasets"]["probability"])[8:]
    x = x[:len(y)]
    # 拟合拉比
    rabi_554,t_fit,y_fit,popt = Rabi_fit(x,y[:,ion_choice])
    print(popt)
    plt.figure()
    plt.plot(x,y[:,ion_choice],".")
    plt.plot(t_fit,y_fit)
    aom_delay=-(np.pi/2+popt[3])/np.pi/2/popt[1]/1e6
    aom_delay = aom_delay% rabi_554
    
    return rabi_554
#%%
amp_list = np.linspace(0.1,0.9,21)
rabi_list = []
rid_list = []
for amp in amp_list:
    rid = submit_rabi(amp)
    rid_list.append(rid)
    rabi_freq = 2*np.pi/fit_rabi(rid)
    rabi_list.append(rabi_freq)
#%%
rid
#%% md
### 拟合Rabi
#%%
# 重新拟合拉比
rid_list = np.array(range(15))+114634
rabi_list = []
amp_list = np.linspace(0.1,0.9,21)
for rid in rid_list:
    rabi_freq = 2*np.pi/fit_rabi(rid)
    rabi_list.append(rabi_freq)
#%%
data_length=15
plt.plot(amp_list[:data_length],rabi_list[:data_length],".")
def sin_fun(x,a,b):
    return a*np.sin(x*b)
from scipy.optimize import curve_fit
p0 = [5e6,2.0]
popt,_ = curve_fit(sin_fun,amp_list[:data_length],rabi_list[:data_length],p0=p0,maxfev = 10000)
x_fit = np.linspace(amp_list[0],amp_list[data_length],101)
y_fit = sin_fun(x_fit,popt[0],popt[1])
plt.plot(x_fit,y_fit,"-")
plt.title(f'Rabi =2$\pi$×{popt[0]/2/np.pi:.1f} sin({popt[1]:.4f} AOMamp)')
#%% md
## 门参数设置
#%%

#%%
# qubit_index = (1,2)
# phonon_index = ms_parameter["main_mode"][qubit_index[0]][qubit_index[1]]
#%%
# from cloud.ms_parameter import ms_parameters

# phonon_mode_1 = phonon_index
# phonon_mode_2 =phonon_index+1

# f_strench = parameter.Light_554.Motion_freq[phonon_mode_1]
# f_COM = parameter.Light_554.Motion_freq[phonon_mode_2]
# rate = 16

# delta,tau,Rabi_theory = ms_parameters(f_strench=f_strench,f_COM=f_COM,rate=rate,K=2)

# print("delta: 2pi*" ,delta/2/np.pi, "Hz")
# print("gate time:", tau*1e6 ,"μs")
# print("Rabi: ",1/(Rabi_theory/2/np.pi)*1e6,"μs")
# 1/(delta/2/np.pi)*10**6*3
#%%
qubit_index = (1,2)
#%%
parameter = read_parameter()
qubit_index = (2,3)
phonon_index = ms_parameter["main_mode"][qubit_index[0]][qubit_index[1]]
print("main mode:",phonon_index)


# gate_time = ms_parameter["gate_time"][qubit_index[0]][qubit_index[1]]

# gate_time = 1/(delta/2/np.pi)*10**6*3*1e-6
gate_time = 400e-6

print("gate time:",gate_time)

# detuning = delta/2/np.pi
detuning= 8000

# N_modulation = int(gate_time/20e-6)*2

N_modulation = 32

parameter.Light_554.MS_phonon_index[qubit_index[0]][qubit_index[1]] = phonon_index
parameter.Light_554.MS_time[qubit_index[0]][qubit_index[1]] = gate_time
parameter.Light_554.freq_detuning[qubit_index[0]][qubit_index[1]] = detuning
#%% md
## 画图
#%%
draw_pst(modulator.paras)
#%% md
## 保存
#%%
# 保存计算波形到文件
with open('D:\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator.pkl', 'wb') as f:
    dill.dump(modulator, f)
#%%
# 保存计算波形到多离子线路配置

# modulator_list = [ [None for _ in range(7)] for i in range(7) ]
with open('D:\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator_list.pkl','rb') as f:
    modulator_list = dill.load( f)
modulator_list[qubit_index[0]][qubit_index[1]] = modulator
modulator_list[qubit_index[1]][qubit_index[0]] = modulator
with open('D:\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator_list.pkl','wb') as f:
    dill.dump(modulator_list, f)
#%%
parameter = read_parameter()
parameter.Light_554.MS_phonon_index[qubit_index[0]][qubit_index[1]] = phonon_index
parameter.Light_554.MS_time[qubit_index[0]][qubit_index[1]] = gate_time
parameter.Light_554.freq_detuning[qubit_index[0]][qubit_index[1]] = detuning
parameter.update_config_json()
#%%
ms_parameter["gate_time"][qubit_index[0]][qubit_index[1]] = gate_time
ms_parameter["gate_time"][qubit_index[1]][qubit_index[0]] = gate_time

ms_parameter["main_mode"][qubit_index[0]][qubit_index[1]] = phonon_index
ms_parameter["main_mode"][qubit_index[1]][qubit_index[0]] = phonon_index

ms_parameter["detuning"][qubit_index[0]][qubit_index[1]] = detuning
ms_parameter["detuning"][qubit_index[1]][qubit_index[0]] = detuning

with open(ms_parameter_file_path,'w') as json_file:
    json.dump(ms_parameter,json_file, indent=4)
#%% md
## 读取
#%%
parameter = read_parameter()
qubit_index = (0,4)
#%%
with open('D:\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator.pkl',
            'rb') as f:
    modulator = dill.load(f)
#%% md
### 从modulator_list 和 gate_parameter读取参数
#%%
# modulator
with open('D:\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator_list.pkl','rb') as f:
    modulator_list = dill.load( f)
modulator = modulator_list[qubit_index[0]][qubit_index[1]]
with open('D:\\PycharmProjects\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator.pkl', 'wb') as f:
    dill.dump(modulator, f)

with open(ms_parameter_file_path,'r') as json_file:
    ms_parameter = json.load(json_file)

# parameter = read_parameter()
# parameter.Light_554.MS_time[qubit_index[0]][qubit_index[1]]  = ms_parameter["gate_time"][qubit_index[0]][qubit_index[1]] 
# parameter.Light_554.MS_phonon_index[qubit_index[0]][qubit_index[1]]  = ms_parameter["main_mode"][qubit_index[0]][qubit_index[1]]
# parameter.Light_554.freq_detuning[qubit_index[0]][qubit_index[1]]   = ms_parameter["detuning"][qubit_index[0]][qubit_index[1]]
# parameter.Light_554.MS_AC_Stark_shift[qubit_index[0]][qubit_index[1]]  = ms_parameter["AC_Stark_shift"][qubit_index[0]][qubit_index[1]]
# parameter.Light_554.MS_phase[qubit_index[0]][qubit_index[1]]  = ms_parameter["phase"][qubit_index[0]][qubit_index[1]]

# parameter.update_config_json()
#%%
from ms_package.ms_parameter import recommend_mode

N_ions = len(parameter.Light_554.Motion_freq)
for i in range(N_ions):
    for j in range(i + 1, N_ions):
        phonon_index, _ = recommend_mode(N_ions, i, j, N_cut = 4)
        phonon_index = int(phonon_index)
        print(f"phonon_index ({i}, {j}): {phonon_index}")
        parameter.Light_554.MS_phonon_index[i][j] = phonon_index
        parameter.Light_554.MS_phonon_index[j][i] = phonon_index
parameter.update_config_json()
#%% md
## all-to-all 波形生成 & 保存
#%%
from cloud.pulsedesign.AM_analytic import AMPulseDesigner, update_rabi
from cloud.pulsedesign.pulse_design_function import pst_area_signed

# parameter.update_config_json()
parameter = read_parameter()
modulator_list = [ [None for _ in range(7)] for i in range(7) ]
with open('C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator_list.pkl','rb') as f:
    modulator_list = dill.load(f)

SingletonMeta._instances = {}
# parameter = LOADED_PARAMETER()
N_ions = len(parameter.Light_554.Motion_freq)
area_list = np.zeros((N_ions, N_ions))
displacement_list = np.zeros((N_ions, N_ions))

for i in range(N_ions):
    for j in range(i + 1, N_ions):
        SingletonMeta._instances = {}
        parameter = LOADED_PARAMETER()

        gate_time = 350e-6
        detuning = -8000
        
        phonon_index, _ = recommend_mode(N_ions, i, j, N_cut = 4)
        phonon_index = int(phonon_index)
        print(f"phonon_index ({i}, {j}): {phonon_index}")
        # phonon_index = int(parameter.Light_554.MS_phonon_index[i][j])
        # gate_frequency = parameter.Light_554.Motion_freq[phonon_index] + detuning

        print(parameter.Light_554.Motion_freq)
        print("Main Frequency:", parameter.Light_554.Motion_freq[phonon_index])
        # print("Gate Frequency:", gate_frequency)
        # parameter.Light_554.Motion_freq += np.random.uniform(-1e3, 1e3, N_ions)
        tester = AMPulseDesigner((i, j),
                                 phonon_index,
                                 gate_time,
                                 N_ions,
                                 parameter.Light_554.Motion_freq,
                                 detuning)

        #N_range = [40]
        N_range = range(20, 68, 8)
        goal = 5.0
        tester.run(N_range, goal)
        area_list[i, j] = tester.area
        displacement_list[i, j] = tester.total1
        detuning = tester.mu/2/np.pi-parameter.Light_554.Motion_freq[phonon_index]

        # Saving Result
        modulator_list[i][j] = tester
        modulator_list[j][i] = tester

        parameter.Light_554.MS_phonon_index[i][j] = phonon_index
        parameter.Light_554.MS_phonon_index[j][i] = phonon_index
        parameter.Light_554.MS_time[i][j] = gate_time
        parameter.Light_554.MS_time[j][i] = gate_time
        parameter.Light_554.freq_detuning[i][j] = detuning
        parameter.Light_554.freq_detuning[j][i] = detuning

        print(tester.rabi)
        print("Signed Area: ", pst_area_signed(tester.rabi_half, update_rabi, tester.paras))
        print("Detuning:",detuning)
        print("Qubit Pair (%d, %d) Finished\n" % (i, j))


with open('C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator_list.pkl','wb') as f:
    dill.dump(modulator_list, f)

parameter.update_config_json()
#%%
i = 2
j = 4

SingletonMeta._instances = {}
parameter = LOADED_PARAMETER()

gate_time = 350e-6
detuning = -8000

phonon_index, _ = recommend_mode(N_ions, i, j, N_cut = 4)
phonon_index = int(phonon_index)
print(f"phonon_index ({i}, {j}): {phonon_index}")
# phonon_index = int(parameter.Light_554.MS_phonon_index[i][j])
# gate_frequency = parameter.Light_554.Motion_freq[phonon_index] + detuning

print(parameter.Light_554.Motion_freq)
print("Main Frequency:", parameter.Light_554.Motion_freq[phonon_index])
# print("Gate Frequency:", gate_frequency)
# parameter.Light_554.Motion_freq += np.random.uniform(-1e3, 1e3, N_ions)
tester = AMPulseDesigner((i, j),
                            phonon_index,
                            gate_time,
                            N_ions,
                            parameter.Light_554.Motion_freq,
                            detuning)

#N_range = [40]
N_range = range(20, 68, 8)
goal = 5.0
tester.run(N_range, goal)
area_list[i, j] = tester.area
displacement_list[i, j] = tester.total1
detuning = tester.mu/2/np.pi-parameter.Light_554.Motion_freq[phonon_index]

# Saving Result
modulator_list[i][j] = tester
modulator_list[j][i] = tester

parameter.Light_554.MS_phonon_index[i][j] = phonon_index
parameter.Light_554.MS_phonon_index[j][i] = phonon_index
parameter.Light_554.MS_time[i][j] = gate_time
parameter.Light_554.MS_time[j][i] = gate_time
parameter.Light_554.freq_detuning[i][j] = detuning
parameter.Light_554.freq_detuning[j][i] = detuning

print(tester.rabi)
print("Signed Area: ", pst_area_signed(tester.rabi_half, update_rabi, tester.paras))
print("Detuning:",detuning)
print("Qubit Pair (%d, %d) Finished\n" % (i, j))
#%%
print(area_list)
#%% md
## 扫AOM幅度
#%%
gate_time = 350e-6
#%%
parameter = read_parameter()
# parameter.Light_554.MS_phonon_index = phonon_index
parameter.Light_554.MS_time[qubit_index[0]][qubit_index[1]] = gate_time*1
parameter.Experiment.Repeat = 100
# parameter.Light_554.freq_detuning = detuning
parameter.update_config_json()
#%%
# 定义扫描参数
scan_start = 0.2
scan_stop = 0.3
n_scan_point = 11
# qubit_index = (0,1)
n_gate = 1
#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_aom = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan',
                               start=scan_start,
                               stop=scan_stop,
                               npoints= n_scan_point),
                      'qubit_index':str(qubit_index),
                      'ion_choice':str(qubit_index),
                    #   'ion_choice':str((2,3,4)),
                      'gate_number':n_gate
                          },
                   'class_name': 'MSScanAOMAmpAM',
                   'file': 'repository\\MS_gate_exp\\AM\\MS_scan_aom_amp_AM.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MS_aom,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])
x = x[0:len(y)]
# 寻找 00和11 交叉点
P = y[:,0:4:3]
crosses = Cross_fit(x,P)

fig,ax = plt.subplots()
for cross in crosses:
    ax.axvline(x = cross,color = "r",linestyle ="--")
if len(crosses)>0:
    print(f" P11 = P00  at aom amplitude = {crosses[0]} " )

ax.plot(x, y,'-o')
ax.set_xlabel('AOM amplitude')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan AOM amplitude')
# save_plot(fig,base_filename=f"MS_AOM_amp_RID{rid}")
#%%
parameter = read_parameter()
parameter.Light_554.AOM_AWG_amp_ms[qubit_index[0]][qubit_index[1]] = crosses[0]
parameter.Light_554.AOM_AWG_amp_ms[qubit_index[1]][qubit_index[0]] = crosses[0]
parameter.Experiment.Repeat =100
# parameter.Light_554.AOM_AWG_amp_ms = 0.303
parameter.Light_554.MS_time[qubit_index[0]][qubit_index[1]] = gate_time*1
parameter.Light_554.MS_time[qubit_index[1]][qubit_index[0]] = gate_time*1
parameter.update_config_json()
#%%
ms_parameter["AOM_amp"][qubit_index[0]][qubit_index[1]] = crosses[0]
ms_parameter["AOM_amp"][qubit_index[1]][qubit_index[0]] = crosses[0]
with open(ms_parameter_file_path,'w') as json_file:
    json.dump(ms_parameter,json_file, indent=4)
#%% md
## MS 扫时间
#%%
#定时扫描参数
scan_start = 0000e-6
scan_stop = 501e-6
n_scan_point = 51

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MSAM_t = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'MSAM',
                   'file': 'repository\\MS_gate_exp\\AM\\MS_gate_AM.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MSAM_t,manager)
#%%
-0.44972049231142586-0.005393724342287977+0.024819072179670977-0.017667325131575488+0.06693391986338813+0.4695554194198398
#%% md
## pi/2-MS
#%%
#定义扫描参数
scan_start = 0
scan_stop = 2 * np.pi
n_scan_point = 31
#更新外部修改的config参数
parameter = read_parameter()

Phase_cali = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan',
                            start=scan_start,
                            stop=scan_stop,
                            npoints= n_scan_point
                            ),

                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index),
                          'Adaptive':True
                          },
                   'class_name': 'Pi2MSAM',
                   'file': 'repository\\MS_gate_exp\\AM\\Pi2_MS_AM.py',
                   'log_level': 30}
rid= submit_exp(Phase_cali,manager)
#%%
rid = 117826
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])[:]
y = np.array(result["datasets"]["computational_basis_probability"])[:]
parity = y[:,0]-y[:,3]

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])

fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/100)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS-MS gate scan phase')
# save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%%
phase_ = _[3]
amp_ = _[0]
print(amp_,phase_)
# phase_=0.48
#%%
parameter = read_parameter()
parameter.Light_554.MS_phase = -phase_/2+np.pi/2
parameter.update_config_json()
#%%
ms_parameter["phase"][qubit_index[0]][qubit_index[1]] = parameter.Light_554.MS_phase
ms_parameter["phase"][qubit_index[1]][qubit_index[0]] = parameter.Light_554.MS_phase
with open(ms_parameter_file_path,'w') as json_file:
    json.dump(ms_parameter,json_file, indent=4)
#%% md
## Parity
#%%
parameter = read_parameter()
# parameter.Light_554.MS_phonon_index = phonon_index

parameter.Light_554.MS_time = gate_time*1
# parameter.Light_554.freq_detuning = detuning
parameter.update_config_json()
#%%
-0.021896017003270315-0.8031244713749937
#%%
#更新外部修改的config参数
parameter = read_parameter()
#定义扫描参数
scan_start = 0
scan_stop = 2 * np.pi
n_scan_point = 31
phase =  parameter.Light_554.MS_phase 


#定义expid
Parity = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan',
                            start=scan_start,
                            stop=scan_stop,
                            npoints= n_scan_point
                            ),

                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index),
                          "phase":phase
                          },
                   'class_name': 'ParityAM',
                   'file': 'repository\\MS_gate_exp\\AM\\Parity_AM.py',
                   'log_level': 30}
rid= submit_exp(Parity,manager)
#%%
print(phase)
#%%
rid = 122544
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])[:]
y = np.array(result["datasets"]["computational_basis_probability"])[:]
parity = y[:,0]+y[:,3]-y[:,1]-y[:,2]

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])

fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/400)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan Parity')
# save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%%
a=_[3]
b = _[0]
print(a,b)
#%% md
## MS-MS(phi)
#%%

#定义扫描参数
scan_start = 0
scan_stop = 2 * np.pi
n_scan_point = 21
#更新外部修改的config参数
parameter = read_parameter()
phase = -0.4350537703606339/2

Shift_cali = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan',
                            start=scan_start,
                            stop=scan_stop,
                            npoints= n_scan_point
                            ),
                          'phase':phase,
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index),
                          'Adaptive':True
                          },
                   'class_name': 'ShiftAM',
                   'file': 'repository\\MS_gate_exp\\AM\\Shift_cali_AM.py',
                   'log_level': 30}
rid= submit_exp(Shift_cali,manager)
#%%

#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])[:]
y = np.array(result["datasets"]["computational_basis_probability"])[:]
parity = y[:,0]-y[:,3]

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])

fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/100)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS-MS gate scan phase')
# save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%%
a = _[0]
b = _[3]+np.pi/2
print(a,b)
# b-a
# b=-np.pi
#%%

#%%
shift_ms = (-b)/2/2/np.pi/gate_time
print(shift_ms)
#%%
parameter = read_parameter()
parameter.Light_554.MS_AC_Stark_shift += shift_ms
parameter.update_config_json()
#%%
ms_parameter["AC_Stark_shift"][qubit_index[0]][qubit_index[1]] = parameter.Light_554.MS_AC_Stark_shift
ms_parameter["AC_Stark_shift"][qubit_index[1]][qubit_index[0]] = parameter.Light_554.MS_AC_Stark_shift
with open(ms_parameter_file_path,'w') as json_file:
    json.dump(ms_parameter,json_file, indent=4)
#%%
print(modulator.Rabi)
#%%
rid = 123366
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])[:]
y = np.array(result["datasets"]["y_probability"])[:]
rabi_554,t_fit,y_fit,popt0 = Rabi_fit(x,y[:,2])
print("POPT:",popt0)
phi_shift = popt0[3]-np.pi/2
if popt0[0]<0:
    phi_shift -= np.pi

phi_shift = (phi_shift+np.pi)%(2*np.pi)-np.pi
print(phi_shift)
parameter = read_parameter()
ms_time = parameter.Light_554.MS_time[2][4]
print(ms_time)
shift_ms = phi_shift/ms_time/2/np.pi
print("MS Shift:",shift_ms)

#%% md
## 自动化校准
#%%
from MS_AM_cali import cali_ms_gate
#%%
cali_ms_gate(qubit_index)

#%% md
# AM + Spin echo
#%% md
## AOM 扫幅度
#%%
parameter = read_parameter()
# parameter.Light_554.MS_phonon_index = phonon_index
parameter.Light_554.MS_time = gate_time*1
parameter.Experiment.Repeat = 100
# parameter.Light_554.freq_detuning = detuning
parameter.update_config_json()
#%%
# 定义扫描参数
scan_start = 0.27
scan_stop = 0.29
n_scan_point = 11
# qubit_index = (0,1)
n_gate = 9
#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_aom = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan',
                               start=scan_start,
                               stop=scan_stop,
                               npoints= n_scan_point),
                      'qubit_index':str(qubit_index),
                      'ion_choice':str(qubit_index),
                      'gate_number':n_gate
                          },
                   'class_name': 'MSScanAOMAmpAM',
                   'file': 'repository\\MS_gate_exp\\AM\\MS_spin_echo_scan_amp.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MS_aom,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])
x = x[0:len(y)]
# 寻找 00和11 交叉点
P = y[:,0:4:3]
crosses = Cross_fit(x,P)

fig,ax = plt.subplots()
for cross in crosses:
    ax.axvline(x = cross,color = "r",linestyle ="--")
if len(crosses)>0:
    print(f" P11 = P00  at aom amplitude = {crosses[0]} " )

ax.plot(x, y,'-o')
ax.set_xlabel('AOM amplitude')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan AOM amplitude')
# save_plot(fig,base_filename=f"MS_AOM_amp_RID{rid}")
#%%
parameter = read_parameter()
parameter.Light_554.AOM_AWG_amp_ms = crosses[0]
parameter.Experiment.Repeat =100
# parameter.Light_554.AOM_AWG_amp_ms = 0.303
parameter.Light_554.MS_time = gate_time*1
parameter.update_config_json()
#%% md
## 扫Shift
#%%
# 定义扫描参数
scan_start = 0.15
scan_stop = 0.17
n_scan_point = 11
# qubit_index = (0,1)
n_gate = 5
#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_aom = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan',
                               start=scan_start,
                               stop=scan_stop,
                               npoints= n_scan_point),
                      'qubit_index':str(qubit_index),
                      'ion_choice':str(qubit_index),
                      'gate_number':n_gate
                          },
                   'class_name': 'MSScanAOMAmpAM',
                   'file': 'repository\\MS_gate_exp\\AM\\MS_spin_echo_scan_amp.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MS_aom,manager)
#%% md
## MS-MS phi
#%%

#定义扫描参数
scan_start = 0
scan_stop = 2 * np.pi
n_scan_point = 31
#更新外部修改的config参数
parameter = read_parameter()
phase = -0.4350537703606339/2

Shift_cali = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan',
                            start=scan_start,
                            stop=scan_stop,
                            npoints= n_scan_point
                            ),
                          'phase':phase,
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index),
                          },
                   'class_name': 'ShiftAM',
                   'file': 'repository\\MS_gate_exp\\AM\\Shift_cali_AM_spin_echo.py',
                   'log_level': 30}
rid= submit_exp(Shift_cali,manager)
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])[:]
y = np.array(result["datasets"]["computational_basis_probability"])[:]
parity = y[:,0]-y[:,3]

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])

fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/100)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS-MS gate scan phase')
# save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%%
a = _[0]
b = _[3]+np.pi/2
print(a,b)
# b=-np.pi
shift_ms = (-b)/2/2/np.pi/gate_time/2
print(shift_ms)

#%%
parameter = read_parameter()
parameter.Light_554.MS_AC_Stark_shift += shift_ms
parameter.update_config_json()
#%%

#%% md
## Parity
#%%

#%% md
# Qutip模拟调制演化

#%%
from cloud.pulsedesign.pulse_design_function import Parameters
from qutip import *

Rabi = modulator.Rabi
paras = modulator.paras
Obj_addressing = qubit_index
eta = modulator.eta
tau = gate_time
pi = np.pi
P = modulator.N_ions


#%%
# 设置要模拟的优化结果，包括振幅和基列表

def ms_qutip_simulation(tlist,drift = 0,d_center = 0,phase_delay = 0):
    def phase_laser_t(paras:Parameters,t):
        # return(paras.phi[0])
        t_us=(t-phase_delay)*1e6
        Ns= paras.findt(t_us)%len(paras.phi)
        # return 0
        if Ns > len(paras.phi)-1:
            return paras.phi[-1]
        elif Ns <0:
            return paras.phi[0]
        return paras.phi[Ns]
        # return 0
    def Hr_coeff(w,mu,j):
        def H1(t,args):
            return  np.exp(-1.0j*(w-mu)*t+1j*phase_laser_t(paras,t))

        return H1
    def Hr_dag_coeff(w,mu,j):
        def H1_dag(t,args):
            return np.exp(1.0j*(w-mu)*t-1j*phase_laser_t(paras,t))
        return H1_dag
    def Hb_coeff(w,mu,j):
        def H1(t,args):
            return  np.exp(1.0j*(w-mu)*t-1j*phase_laser_t(paras,t))
        return H1
    def Hb_dag_coeff(w,mu,j):
        def H1_dag(t,args):
            return np.exp(-1.0j*(w-mu)*t+1j*phase_laser_t(paras,t))
        return H1_dag
    # 定义涉及到的矩阵

    N_cut = [10,4,2,2,2,2,2] # motional space size
    A = []
    for i_a in range(P):
        a = tensor(qeye(2),qeye(2))
        for i in range(P):
            if i == i_a:
                a = tensor(a,destroy(N_cut[i]))
            else:
                a = tensor(a,qeye(N_cut[i]))
        A.append(a)

    # 定义spin算符
    s1 = tensor(sigmap(),qeye(2))
    s2 = tensor(qeye(2), sigmap())

    for i in range(P):
        s1 = tensor(s1,qeye(N_cut[i]))
        s2 = tensor(s2,qeye(N_cut[i]))
    S = [s1,s2]

    # 定义哈密顿量系数随时间变化

    # 计算哈密顿量
    H = []
    for j,s in enumerate(S):
        for k,a in enumerate(A):
            try:
                j_ion = Obj_addressing[j]
            except:
                j_ion = 2
            mu = paras.mu*1e6
            nu_k = paras.nu[k]*1e6-drift
            H_kj_red = [eta[j_ion][k]*Rabi * a*s *0.5j, Hr_coeff(nu_k,mu-d_center,j)]
            H_kj_red_dag = [eta[j_ion][k]*Rabi * a.dag()*s.dag() *-0.5j, Hr_dag_coeff(nu_k,mu-d_center,j)]
            H_kj_blue = [eta[j_ion][k]*Rabi * a.dag()*s *0.5j, Hb_coeff(nu_k,mu+d_center,j)]
            H_kj_blue_dag = [eta[j_ion][k]*Rabi * a*s.dag()*-0.5j, Hb_dag_coeff(nu_k,mu+d_center,j)]

            H.append(H_kj_red)
            H.append(H_kj_red_dag)
            H.append(H_kj_blue)
            H.append(H_kj_blue_dag)


    
    psi0 = tensor(basis(2,0),basis(2,0))
    for i in range(P):
        psi0 = tensor(psi0,basis(N_cut[i],0))
    result = mesolve(H, psi0, tlist,[], [])
    return result
#%%
tlist = np.linspace(0.0, tau*7, 1001)
# qutip 演化模拟
result = ms_qutip_simulation(tlist,d_center= 2000)
#%%
# 画P00，P01+P10，P11 随时间的演化

result_states = [ptrace(s,[0,1]) for s in result.states]

target = tensor(basis(2,0),basis(2,0)) # target state
p_00 = np.array([fidelity(s,target) for s in result_states])**2
plt.plot(tlist*1e6, p_00,label=r'$\left| 00\right> $')

target = tensor(basis(2,1),basis(2,1)) # target state
p_11 = np.array([fidelity(s,target) for s in result_states])**2
plt.plot(tlist*1e6, p_11, label=r'$ \left| 11\right> $')



target = (tensor(basis(2,0),basis(2,1))) # target state
p_01 = np.array([fidelity(s,target) for s in result_states])**2
target = (tensor(basis(2,1),basis(2,0))) # target state
p_10 = np.array([fidelity(s,target) for s in result_states])**2
plt.plot(tlist*1e6, p_01, label=r'${\left| 01\right>}$')
plt.plot(tlist*1e6, p_10, label=r'${\left| 10\right>}$')


plt.xlabel('$t$ ($\mu$s)')
plt.ylabel('P')
plt.legend()
#%%
# 模拟声子频率发生漂移后的门保真度
def phonon_detuned_simulation(drift=0, d_center = 0):
    

    tlist = np.linspace(0.0, tau, 1001)
    result = ms_qutip_simulation(tlist,drift=drift,d_center=d_center)
    
    result_states = ptrace(result.states[-1],[0,1])

    def parity_fidelity(rho0):
        phis = np.linspace(0.0, np.pi,1001)
        Us = [Qobj([[1/np.sqrt(2),1/np.sqrt(2)*np.exp(1j*phi)],[-1/np.sqrt(2)*np.exp(-1j*phi),1/np.sqrt(2)]]) for phi in phis]
        rhos_parity = [tensor(U,U)*rho0 *tensor(U.dag(),U.dag())for U in Us]
        parity = [ rho[0][0].real+rho[3][3].real-rho[1][1].real-rho[2][2].real for rho in rhos_parity]

        fidelity_parity = ((abs(max(parity))+abs(min(parity)))/2+rho0[0][0].real+rho0[3][3].real)/2
        return fidelity_parity

    return parity_fidelity(result_states)
#%%
phonon_detuned_simulation(drift=0*2*pi)
#%% md
## 失谐漂移的影响
#%%
drift_range = 900
drift_list = np.linspace(-drift_range*2*pi,drift_range*2*pi , 7)
fidelity_list = [phonon_detuned_simulation(drift=d) for d in drift_list]
#%%
plt.plot(drift_list/2/pi,fidelity_list,label = "robust")
# plt.plot(drift_list/2/pi,fidelity_list_0,label = "no robust")
# plt.plot(drift_list/2/pi,np.array(fidelity_list)-np.array(fidelity_list_0))
plt.legend()
plt.ylabel("Fidelity Difference")
plt.xlabel("mis-set detuning (Hz)")
#%% md
## 中心频率漂移影响
#%%
d_center_range = 2000
d_center_list = np.linspace(-d_center_range*2*pi,d_center_range*2*pi , 11)
fidelity_list = [phonon_detuned_simulation(d_center=d) for d in d_center_list]
#%%
plt.plot(d_center_list/2/pi,fidelity_list,label = "d_center")
# plt.plot(drift_list/2/pi,fidelity_list_0,label = "no robust")
# plt.plot(drift_list/2/pi,np.array(fidelity_list)-np.array(fidelity_list_0))
plt.legend()
plt.ylabel("Fidelity Difference")
plt.xlabel("mis-set center frequncy (Hz)")
#%%
