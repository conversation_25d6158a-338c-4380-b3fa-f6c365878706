import sys
import os
import time
from datetime import datetime
sys.path.append(os.path.abspath('../'))

from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER,SingletonMeta
from cloud.fit_func import (
    Parity_fit,
    Parity_fit_MLE,
    Ra<PERSON>_fit,Peak_fit,
    Ramsey_fit,
    Rabi_fit_thermal,
    Cross_fit,
    Cross_fit_Y,
    MS_01_fit,
    AOD_scan_fit,
    Spectrum_fit
    )
from cloud.figure import save_plot

import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
import scienceplots
plt.style.use('science')
plt.style.use(['no-latex'])  

## 调整模版
params = {
  'figure.dpi' : 200, ## jupyter中显示图片的分辨率
}
mpl.rcParams.update(params)

parameter = LOADED_PARAMETER()
result_dir = 'C://Users//Administrator//PycharmProjects//ionctrl_develop_new//ionctrl_develop//results'

def read_parameter():
    '''
    外部修改后读取实验参数
    '''
    SingletonMeta._instances ={}
    parameter = LOADED_PARAMETER()
    return parameter

def submit_exp(expid:dict,manager:ARTIQManager):
    '''
    提交实验并监控
    '''
    rid= manager.submit(expid)
    if isinstance(rid,str):
        print(rid)
        return 0
    else:
        status = manager.monitor_experiment(rid)
        print(status["status"])
        rid = status["rid"]
    return rid

def write_to_file(file_name,result):
    '''
    将实验结果写入文件
    格式为 日期 -
    Parameters
    ----------
    file_name
    result

    Returns
    -------

    '''
    # 获取当前时间
    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    # 打开文件并写入时间和结果，每次在新行写入
    with open(file_name, "a") as file:
        file.write(f"{current_time} - {result}\n")

def write_to_file_with_rid(file_name,result,rid):
    '''
    将实验结果写入文件
    格式为 日期 -
    Parameters
    ----------
    file_name
    result

    Returns
    -------

    '''
    # 获取当前时间
    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    # 打开文件并写入时间和结果，每次在新行写入
    with open(file_name, "a") as file:
        file.write(f"{current_time} - {result} - {rid}\n")

def data_read(file_name):
    timestamps = []
    data = []
    with open(file_name, "r") as file: # 
        for line in file:
            # 去掉空白字符（包括换行符）
            line = line.strip()
            # 分离时间和数据部分
            time_data = line.split(" -")
            if len(time_data) == 2:
                time_str, data_str = time_data
            if len(time_data) == 3:
                time_str, data_str, rid_str = time_data 

            # 解析时间为 datetime 对象
            timestamp = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            timestamps.append(timestamp)
            
            # 解析数据部分
            data_values = eval(data_str)
            data.append(data_values)
        return timestamps,data