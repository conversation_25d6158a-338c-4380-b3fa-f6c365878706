#%% md
# 导入
#%%
%matplotlib inline
from jupyter_function import *

import qiskit
from qiskit import QuantumCircuit
from qiskit.compiler import transpile
import numpy as np
#%%
manager = ARTIQManager()

#%%
qc = QuantumCircuit(7)
# qc.ry(np.pi,3)
# qc.ry(np.pi/2,2)
# qc.ry(np.pi/2,4)
# qc.ry(np.pi,4)
# qc.h(2)
qc.cx(2,4)
# qc.cx(3,2)
# qc.cx(2,3)
# for i in range(5):
#     qc.rzz(np.pi/2,2,4)

# qc.rxx(np.pi/2,2,4)
# qc.ry(-np.pi/2,2)
# qc.ry(-np.pi/2,4)
# qc.rx(np.pi,3)
# qc.cx(3,4)
# qc.cx(2,4)
# qc.rx(np.pi,4)
# qc.rzz(np.pi/2,2,3)
# qc.rzz(np.pi/2,2,4)
# qc.ry(np.pi/2,4)
# qc.ry(np.pi/2,2)

print(qc)
#%% md
## QV
#%%
from qiskit import QuantumCircuit, transpile
from qiskit.circuit.library import QuantumVolume
import numpy as np
from qiskit import QuantumCircuit
from qiskit_aer import Aer
from qiskit.circuit.library import QuantumVolume
from qiskit.quantum_info import Statevector
from qiskit.visualization import plot_histogram
# 创建7量子比特的量子体积测试电路(QV8)
num_qubits = 7
qv_qubits = [2, 3,4]  # 选择在比特2,3,4上进行QV8测试
depth = 3  # QV8的深度

# 创建量子体积电路
qv_circuit = QuantumVolume(len(qv_qubits), depth, seed=0)


# 将3比特的QV电路映射到7比特系统的指定比特上
full_circuit = QuantumCircuit(num_qubits, num_qubits)

# 添加QV电路到指定比特
full_circuit.append(qv_circuit, qv_qubits)
qc_trans = transpile(full_circuit, basis_gates=["rx","ry","rzz"],optimization_level=3)
print(qc_trans)

#%%
from qiskit import QuantumCircuit
from qiskit.circuit.library import QuantumVolume
#fromqiskit.compilerimporttranspile
from qiskit_aer import AerSimulator
from qiskit_aer.noise import NoiseModel,errors
from qiskit import transpile

def get_QV_circuit(n_qubit,seed):
    '''
    generateaQVcircuitwithnqubit
    parameters:
    n_qubit:int,numberofqubit
    '''
    if seed is not None:
        qc=QuantumVolume(n_qubit,n_qubit,seed=seed)
    else:
        qc=QuantumVolume(n_qubit,n_qubit)
    qc=transpile(qc,basis_gates=['rx','ry','rzz'],optimization_level=3)

    # qc_2 = QuantumCircuit(2)
    # n_zz = 0
    # for instruction in qc:
    #     if instruction.name == "rzz":
    #         n_zz +=1
    #         # continue
    #     qc_2.append(instruction)
    #     if n_zz >6:
    #         break
        

    return qc

def get_simulator(fidelity,readout_matrix):
    noise_model=get_noise_model(fidelity,readout_matrix)
    simulator=AerSimulator(noise_model=noise_model)
    return simulator


def get_noise_model(fidelity,readout_matrix):
    N=len(fidelity)

    #创建噪声模型
    noise_model=NoiseModel()
    #添加单比特门噪声
    for i in range(N):
        f=fidelity[i,i]
        p_depol=(1-f)*2/1
        depol_error=errors.depolarizing_error(p_depol,1)
        noise_model.add_quantum_error(depol_error,['r'],[i])
    #添加MS门噪声
    for i in range(N):
        for j in range(i+1,N):
            f=fidelity[i,j]
            p_depol=(1-f)*4/3
            depol_error=errors.depolarizing_error(p_depol,2)
            noise_model.add_quantum_error(depol_error,['rxx'],[i,j])
    #添加SPAM噪声
    noise_model.add_all_qubit_readout_error(readout_matrix)
    return noise_model
def calculate_heavy_outputs(qc):
    """
    严格计算量子线路的HeavyOutput计算基

    参数:
    qc:量子线路(不应包含测量操作)

    返回:
    heavy_outputs:HeavyOutput的列表(二进制字符串)
    median_prob:概率中位数
    probabilities:所有计算基态的概率字典
    """
    #获取线路的量子比特数
    num_qubits=qc.num_qubits

    #计算状态向量
    statevector=Statevector.from_instruction(qc)

    #获取所有可能计算基及其概率
    probabilities=statevector.probabilities_dict()

    #计算概率中位数
    prob_values=np.array(list(probabilities.values()))
    median_prob=np.median(prob_values)

    #找出heavyoutputs(概率大于中位数的输出)
    heavy_outputs=[output for output,prob in probabilities.items() if prob>median_prob]

    return heavy_outputs,median_prob,probabilities

import copy
def simulate_qv_counts(n_qubit,simulator,shots,initial_layout=None,seed = None):
    qc=get_QV_circuit(n_qubit,seed)
    qc0 = copy.deepcopy(qc)
    #计算heavyoutput
    heavy_outputs,_,_=calculate_heavy_outputs(qc)

    qc.measure_all()
    #定义量子比特映射：将逻辑比特[0,1,2,3,4]映射到物理比特[2,3,4,5,6]
    if initial_layout==None:
        initial_layout=range(n_qubit)#关键步骤：手动映射

    #在transpile时指定initial_layout


    qc_optimized=transpile(
    qc,
    simulator,
    basis_gates=['r','rxx'],
    initial_layout=initial_layout,
    #optimization_level=0,#禁用高级优化
    #routing_method='none'
    )


    result=simulator.run(qc_optimized,shots=shots).result()
    counts=result.get_counts(qc)
    return counts,heavy_outputs,qc0

#%%

n_qubit = 4
f = np.array([
    [0.9986,0.9888,0.9905,0.9817,0.9883,0.9903,0.9909],
    [0.0000,0.9992,0.9969,0.9939,0.9920,0.9950,0.9909],
    [0.0000,0.0000,0.9988,0.9909,0.9966,0.9939,0.9891],
    [0.0000,0.0000,0.0000,0.9987,0.9957,0.9938,0.9927],
    [0.0000,0.0000,0.0000,0.0000,0.9980,0.9969,0.9902],
    [0.0000,0.0000,0.0000,0.0000,0.0000,0.9973,0.9881],
    [0.0000,0.0000,0.0000,0.0000,0.0000,0.0000,0.9971]
])
f = np.array([
    [0.9993,0.9888,0.9905,0.9817,0.9883,0.9903,0.9909],
    [0.0000,0.9995,0.9969,0.9939,0.9920,0.9950,0.9909],
    [0.0000,0.0000,0.9995,0.9909,0.9966,0.9939,0.9891],
    [0.0000,0.0000,0.0000,0.9996,0.9957,0.9938,0.9927],
    [0.0000,0.0000,0.0000,0.0000,0.9995,0.9969,0.9902],
    [0.0000,0.0000,0.0000,0.0000,0.0000,0.9995,0.9881],
    [0.0000,0.0000,0.0000,0.0000,0.0000,0.0000,0.9994]
])
for i in range(n_qubit):
    f[i,i] = 1
    for j in range(i+1,n_qubit):
        f[i,j] = 1
print(f)
readout_matrix = [[0.985,0.015],[0.015,0.985]]
readout_matrix = [[1,0],[0,1]]
shots  =10000

simulator = get_simulator(f,readout_matrix)
counts,heavy_outputs,qc = simulate_qv_counts(n_qubit,simulator,shots,seed = 100)
print(len(heavy_outputs))
print(qc)

plot_histogram(counts,title="QV simulation")


#%%
full_circuit = QuantumCircuit(num_qubits,num_qubits)
full_circuit.append(qc, [5,4,2,1])
qc_trans = transpile(full_circuit, basis_gates=["rx","ry","rzz"],optimization_level=0)

#%%
# qc_trans = QuantumCircuit(7)
# for instruction in qc:
#     qubit_new = qc.qubits.index(instruction.qubits[0])*-2+4
#     if instruction.name =="rx":
#         qc_trans.rx(instruction.params[0],qubit_new)
#     if instruction.name =="ry":
#         qc_trans.ry(instruction.params[0],qubit_new)
#%% md
## 线路编译
#%%
qc = QuantumCircuit(7)
# qc.ry(np.pi,3)

# qc.ry(np.pi/2,2)

# qc.rx(np.pi,2)
# qc.rx(np.pi,4)
n_gate = 2
for i in range(n_gate):
    qc.rx(np.pi/2/n_gate,2)
# qc.rx(np.pi/2,2)
# qc.ry(np.pi/2,4)
# qc.ry(np.pi,4)
# qc.h(4)
# qc.h(2)

# for i in range(5):
#     qc.cx(2,4)
#     qc.cx(4,2)
# qc.cx(2,4)
# qc.cx(4,2)
# qc.cx(2,4)
# qc.cx(4,2)

# qc.rx(np.pi,2)
# for i in range(3):
#     qc.rx(np.pi/2,2)
#     qc.rzz(np.pi/2,2,4)

# qc.rxx(np.pi/2,2,4)
# qc.ry(-np.pi/2,2)
# qc.ry(-np.pi/2,4)
# qc.rx(np.pi,3)
# qc.cx(3,4)
# qc.cx(2,4)
# qc.rx(np.pi,4)
# qc.rzz(np.pi/2,2,3)
# qc.rzz(np.pi/2,2,4)
# qc.ry(np.pi/2,4)
# qc.ry(np.pi/2,2)

print(qc)
#%%
qc_trans = transpile(qc, basis_gates=["rx","ry","rzz"],optimization_level=1)
#%%
qc_trans = qc
#%% md
## 线路提交
#%%
print(qc_trans)
#%%
def compile_to_artiq(qc:QuantumCircuit):
    circuit_list = []

    for instruction in qc:
        if instruction.name in ["rx","ry"]:
            qubit_index = qc.qubits.index(instruction.qubits[0])
            theta  = instruction.params[0]
            phi = 0 if instruction.name == "rx" else -np.pi/2
            if theta < 0:
                theta = -theta
                phi += np.pi
            opration = [{"gate":"Rphi",
                         "qubit_index":(qubit_index,),
                         "phi":phi,
                         "theta":theta,
                         }]
            circuit_list.append(opration)
        if instruction.name in ["rzz"]:
            qubit_index0 = qc.qubits.index(instruction.qubits[0])
            qubit_index1 = qc.qubits.index(instruction.qubits[1])
            opration = [{"gate":"Rzz",
                         "qubit_index":(qubit_index0,qubit_index1),
                         }]
            circuit_list.append(opration)
    return circuit_list
#%%
circuit_list = compile_to_artiq(qc_trans)
print(circuit_list)
#%%
#定义expid
expid_circuit = {'arguments': {
                      'ion_choice':"(1,2,4,5)",
                      'circuit_list':circuit_list
                          },
                   'class_name': 'CircuitRun',
                   'file': 'repository\\circuit\\circuit.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(expid_circuit,manager)
#%% md
# QV测试
#%%

#%%
def testQV(seed):
    #模拟QV
    counts,heavy_outputs,qc = simulate_qv_counts(n_qubit,simulator,shots,seed = seed)
    plot_histogram(counts,title="QV simulation")


    # 按值降序排序，并获取前两个键
    heavy_ = sorted(counts.items(), key=lambda x: x[1], reverse=True)[:8]
    keys = [item[0] for item in heavy_]
    #编译线路
    full_circuit = QuantumCircuit(num_qubits,num_qubits)
    full_circuit.append(qc, [5,4,2,1])
    qc_trans = transpile(full_circuit, basis_gates=["rx","ry","rzz"],optimization_level=0)
    circuit_list = compile_to_artiq(qc_trans)

    #定义expid
    expid_circuit = {'arguments': {
                        'ion_choice':"(1,2,4,5)",
                        'circuit_list':circuit_list
                            },
                    'class_name': 'CircuitRun',
                    'file': 'repository\\circuit\\circuit.py',
                    'log_level': 30}
    #提交实验
    rid= submit_exp(expid_circuit,manager)

    p = 0
    result = get_result(f"{rid}",root_dir=result_dir)
    y = np.array(result["datasets"]["computational_basis_probability"])

    for key in keys:
        key_number = int(key, 2) 
        p += y[-1,key_number]
    
    return(p)
#%%
plist = []
for i in range(50):
    p = testQV(i)
    print(p)
    plist.append(p)
#%%
plt.hist(plist, bins=10, edgecolor='black') 
#%%
from scipy.stats import bootstrap
data = [p for p in plist if p < 1.0]

n = len(data)  # 样本大小
B = 1000      # Bootstrap 重采样次数（通常 1000~10000）
# 定义计算均值的函数
def mean_statistic(data):
    return np.mean(data)

# 执行 Bootstrap
res = bootstrap(
    (data,),           # 输入数据（必须是序列形式）
    mean_statistic,    # 目标统计量（这里是均值）
    n_resamples=B,     # 重采样次数
    method='basic'     # 方法：'basic', 'percentile', 'BCa' 等
)
mean = mean_statistic(data)
print(f"样本均值:{mean:.4f}")
# 计算方差（标准误差的平方）
bootstrap_se = res.standard_error  # 标准误差
bootstrap_variance = bootstrap_se 
print(f"Bootstrap 估计的样本均值标准差: {bootstrap_se:.4f}")
#%% md
## 数据处理
#%%
def simulate_qv_ho(n_qubit,seed = None):
    qc=get_QV_circuit(n_qubit,seed)
    #计算heavyoutput
    heavy_outputs,_,prob=calculate_heavy_outputs(qc)


    return prob,heavy_outputs,qc
#%%
def QV_data_prosess(rids,seeds,n_qubit):
    plist = []
    for i,rid in enumerate(rids):
        seed = seeds[i]
        counts,heavy_outputs,qc = simulate_qv_ho(n_qubit,seed = seed)
        # 按值降序排序，并获取前一半的键
        heavy_ = sorted(counts.items(), key=lambda x: x[1], reverse=True)[:int(2**n_qubit/2)]
        keys = [item[0] for item in heavy_]
        

        p = 0
        result = get_result(f"{rid}",root_dir=result_dir)
        y = np.array(result["datasets"]["computational_basis_probability"])

        for key in keys:
            key_number = int(key, 2) 
            p += y[-1,key_number]
        plist.append(p)
    return plist
#%%
rids = np.array(range(1))+118300
seeds = np.array(range(1))+100
plist = QV_data_prosess(rids,seeds,4)
#%%
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import bootstrap


data = [p for p in plist if p < 1.0]

# 计算原始均值
mean = np.mean(data)

# 用Bootstrap计算均值的标准差（标准误差）
def mean_statistic(sample):
    return np.mean(sample)

# 执行Bootstrap（重采样1000次）
bootstrap_result = bootstrap(
    (data,), 
    mean_statistic, 
    n_resamples=1000,
    method='basic'
)
std_error = bootstrap_result.standard_error  # Bootstrap标准误差

# 绘制直方图
plt.hist(data, bins=15, edgecolor='black', alpha=0.7, color='skyblue', label='')

# 标记均值线（红色实线）
plt.axvline(mean, color='red', linestyle='-', linewidth=2, label=f'Mean = {mean:.3f}')
plt.axvline(2/3, color='red', linestyle='--', linewidth=1, label='')
# 标记均值 ± Bootstrap标准差（绿色虚线）
plt.axvline(mean + std_error, color='green', linestyle='--', linewidth=1.5, label=f'Mean ± SE = {std_error:.3f}')
plt.axvline(mean - std_error, color='green', linestyle='--', linewidth=1.5)

# 添加图例和标题
plt.legend()
plt.title('HOP Histogram with Mean and Bootstrap Standard Error')
plt.xlabel('Heavey Output Probability')
plt.ylabel('')

plt.grid(axis='y', alpha=0.3)
plt.show()
#%%
plist2 = [p for p in plist if p < 1.0]
# plist2 = plist2[::-1]
mean_list = []
se_list = []
for i in range(len(plist2)-1):
    data = plist2[:i+2]
    mean = np.mean(data)

    # 用Bootstrap计算均值的标准差（标准误差）
    def mean_statistic(sample):
        return np.mean(sample)

    # 执行Bootstrap（重采样1000次）
    bootstrap_result = bootstrap(
        (data,), 
        mean_statistic, 
        n_resamples=1000,
        method='basic'
    )
    std_error = bootstrap_result.standard_error  # Bootstrap标准误差
    mean_list.append(mean)
    se_list.append(std_error)
#%%
# 定义生成参数化样本的函数
def parametric_resample(data):
    mu = np.mean(data)
    if len(data)<2:
        sigma = mu/10
    else:
        sigma = np.std(data, ddof=1)
    return np.random.normal(loc=mu, scale=sigma, size=len(data))

# 自定义 Bootstrap 函数
def parametric_bootstrap(data, statistic, n_resamples=1000):
    resampled_stats = []
    for _ in range(n_resamples):
        new_sample = parametric_resample(data)
        resampled_stats.append(statistic(new_sample))
    return resampled_stats

#%%
mean_list = []
ci_lower_list = []
ci_upper_list = []
for i in range(len(plist2)):
    data = plist2[:i+1]
    mean = np.mean(data)

    # 计算统计量
    bootstrap_means = parametric_bootstrap(data, np.mean)
    ci_lower, ci_upper = np.percentile(bootstrap_means, [1, 99])
    mean_list.append(mean)
    ci_lower_list.append(ci_lower)
    ci_upper_list.append(ci_upper)
#%%
plt.plot(plist2,".")

x = np.array(range(len(mean_list)))
y = mean_list
# y_lower = np.array(mean_list)-np.array(se_list)
y_lower = ci_lower_list
# y_upper = np.array(mean_list)+np.array(se_list)
y_upper = ci_upper_list
plt.plot(x, y, color='blue', label='Mean Line')  # 画线
plt.fill_between(
    x, y_lower, y_upper,
    color='blue', alpha=0.2, label='Error Band'  # 半透明色块
)
plt.axhline(2/3, color='red', linestyle='--', linewidth=1, label='')
plt.xlabel("Circuit index")
plt.ylabel("Heavey output probability")
#%%
