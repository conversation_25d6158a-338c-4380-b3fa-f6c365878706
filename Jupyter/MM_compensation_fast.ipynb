#%% md
# Readme
#%% md
补偿微运动前，请注意检查DC和RF是否正常, 确认RF scan开启
#%% md
# 导入包
#%%

%pylab inline
import os
import logging
import time
import asyncio
import datetime
import glob
from pprint import pprint
import pandas as pd
import numpy as np
import winsound
np.set_printoptions(precision=3)
import serial
import matplotlib as mpl
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
from scipy.signal import find_peaks
from scipy.optimize import minimize_scalar,minimize, rosen, rosen_der
from scipy import interpolate
import warnings
warnings.filterwarnings("ignore")

import h5py
import ast
#%% md
# 导入hdf5读取模块
#%%
def getRID(file_dir,rid):
    '''
    Give a rid, find its absolute file path
    '''
    for root, dirs, files in os.walk(file_dir):
#         print('root_dir:', root)  # 当前目录路径
#         print('sub_dirs:', dirs)  # 当前路径下所有子目录
#         print('files:', files)  # 当前路径下所有非目录子文件
        filepath = root
        for file in files:
            if (str(rid) in file)&(str('.h5') in file):
                filepath = str(root+'/'+file)
                return filepath

            
class read_hdf5():
    """
    read h5 file and save it as a txt and a csv files in the original filepath
    filename = filepath + filename
    You can either give a full file path or give its rid number, e.g.
    # exp_data = read_hdf5('E:/jmcui/bladetrap_control/results/2021-07-08/20/000023963-Rabi_flopping.h5')
    # exp_data = read_hdf5(23963)
    
    use self.info to list the experiment information
    use self.arguments to access arguments
    use self.datasets to access the data
    
    """
    def __init__(self,filename,callprint=True):
        self.callprint=callprint
        self.info=dict()
        #self.filepath_root = 'E:/jmcui/bladetrap_control/results/'
        self.filepath_root = 'C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\results' 
        self.Task = ''
        self.findfile(filename)
        self.readfile()
        self.savefile()
        
    def findfile(self,filename):
        #判断是否为rid，全为数字
        if str(filename).isdigit():
            self.filename = getRID(self.filepath_root,filename)
        else:
            self.filename = filename
        
        if self.callprint:
            print ('Find it:', self.filename)
        
        print ('Find it:', self.filename)
        
        
    def str2dict(self,str0):
        '''
        turn the string to a dict
        '''
        
        str0 = str0.decode("utf-8")
        str1 = str0.replace('null', 'None')
        print(str1)
        str1 = str1.replace('N/A', 'None')
        str1 = str1.replace('false', 'None')
        return ast.literal_eval(str1)
        
    def readfile(self):
        '''
        Read HDF5 file and save the data in a dict: self.data
        '''
        #print (self.filename)
        with h5py.File(glob.glob(self.filename)[0]) as file:
            if self.callprint:
                print('Keys:',list(file))
                print('Datasets:',list(file["datasets"]))
            data=dict()
            for item in list(file):
                data[item]=np.array(file[item])
                #print (item,np.array(file[item])
                
            self.datasets=dict()
            for item in list(file["datasets"]):
                self.datasets[item]=np.array(file['datasets/%s'%item])
        self.arguments=dict()    
        Expinfo = data['expid'].tolist()
        self.rid = data['rid']
        self.arguments = self.str2dict(Expinfo)['arguments']
        self.info=data
        self.info['expid'] = self.str2dict(Expinfo)

        try:
            self.Task = self.arguments['Task']
        except:
            self.Task = self.info['expid']['class_name']
    def savefile(self):
        '''
        save the HDF5data to a txt file
        # 如果想要数组全部输出，不含省略号，则使用np.set_printoptions(threshold=10000)
        '''
        ftxt = open(self.filename[:-3]+'.txt',"w")
        ftxt.write('%s'%self.info['rid'])
        ftxt.write('%s'%self.arguments)
        ftxt.write('%s'%self.datasets)
        ftxt.close()
        
    def plot(self,fontsize=12,xaxis='x_axis',yaxis='Rabicounts_plot',figsize=(10,6),alpha=0,beta=0):
        '''
        call this function to plot different datasets
        figures are saved in the same folder with the h5 file
        '''
        
        plt.figure(figsize=figsize)
        labels = ['CH:ion0','CH:ion1']
        
            
        for fig_i, data0 in  enumerate(self.datasets[yaxis].T):
            t = np.nan_to_num(self.datasets[xaxis], copy=True)
            
            plt.plot(t,data0[:],label=labels[fig_i])
        plt.legend(fontsize= 10,loc='best')
        yaxis = 'CoolingCounts'
        xaxis = 'F'
#         plt.plot(self.data[xaxis],self.data[yaxis])
        plt.title('%s'%self.Task+'_%s'%yaxis+'_PID: %s'%self.rid +'_alpha= %.2f'%alpha+'_beta=%.2f'%beta,fontsize=fontsize)
        plt.xlabel(xaxis,fontsize=fontsize)
        plt.ylabel(yaxis,fontsize=fontsize)
        plt.savefig(self.filename[:-3]+'_%s'%yaxis+'.pdf')
        plt.show()
        
    
#%% md
# 导入artiq
#%%
from sipyco.pc_rpc import (Client, AsyncioClient,BestEffortClient, AutoTarget)
from artiq.master.databases import DeviceDB
from artiq.master.worker_db import DeviceManager
from artiq.experiment import *
#%%
# let's move to a location where we have our artiq setup 
os.chdir(os.path.expanduser("C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop")) 
#%%

from cloud.artiq_manager import ARTIQManager
from modules.config import LOADED_PARAMETER

manager = ARTIQManager()
parameter = LOADED_PARAMETER()
schedule = manager.scheduler
exps = manager.experiment_db
datasets = manager.dataset_db

print("current schedule")
print(schedule.get_status())
print("experiments:")
print(exps.list_directory("repository"))
#%% md
# 连接DC
#%%
import socket               # 导入 socket 模块
import time

class Mysocket:
    '''
    定义与DC树莓派的socket连接
    '''
    def __init__(self, ip, port):
        self.ip = ip
        self.port = port
        self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.s.connect((ip, port))

    def send(self, data):
        # self.cleancache()
        self.s.send(data.encode())

    def recv(self):
        temp = self.s.recv(1024).decode()
        # return re.sub("Keep-Alive Packet", "", temp)
        return temp

    def close(self):
        self.s.close()

    def shutdown(self):
        self.s.shutdown(socket.SHUT_WR)
        
    def cleancache(self):
        data=self.s.recv(1024)
        return data

#指定连接DC树莓派的地址与端口
socketA = Mysocket("*************", 6666) 
socketB = Mysocket("*************", 6666)

Channel_electrodes = {
            "A1": 10,
            "A2": 9,
            "A3": 8,
            "A4": 7,
            "A5": 6,
            "RF1": 14,
            "B1": 5,
            "B2": 4,
            "B3": 3,
            "B4": 2,
            "B5": 1,
            "RF2": 14,
        }
Axial_channels = ['A3','B3','RF1','RF2'] #channels for VA3,VB3,VRF11,VRF12 #看文章再说

channel_list_A = [0,1,2,3,4,5] #树莓派A中包含的通道编号，用于判断向哪台树莓派发送信息
#%%

def set_DC(channels,voltages):
    '''
    设置电极电压
    channels: 通道编号的 list
    voltages: 对应通道待设置的电压值
    '''
    channels = np.array([channels]).flatten()
    voltages = np.array([voltages]).flatten()
    for i,ch in enumerate(channels):
        if ch not in Channel_electrodes.values(): 
            print ('CH%2d out of range  0-5,8-13'%ch)
            continue
        if (voltages[i]>10)|(voltages[i]<-10):
            print ('Dangerous, Voltage on CH%2d out of range ±11'%ch)
            continue
        try:
            
            tempvalue = voltages[i]
            if tempvalue > 10:
                tempvalue = 10
            elif tempvalue < -10:
                tempvalue = -10
            if int(channels[i]) in channel_list_A:  
                socketA = Mysocket("*************", 6666)   
                socketA.send(f"SetDC_{int(channels[i])}_{tempvalue}")
                print(f"Channel_{channels[i]}:{socketA.recv()}")
            else:
                socketB = Mysocket("*************", 6666) 
                socketB.send(f"SetDC_{int(channels[i])}_{tempvalue}")
                print(f"Channel_{channels[i]}:{socketB.recv()}")
        except Exception as e:
            print("TCP_error on CH%2d："%ch,e)
            continue  
   
def save_DC(New_Volts,Old_voltages,New_CHlist = ['A3','B3','RF1','RF2'],index=0):
    '''
    save the DC voltage file
    New_Volts: 待保存的电压值
    Old_voltages: 旧的电压值，为 通道名：电压值 的dict
    New_CHlist: 待保存电压对应的通道名
    index:  存的时候文件名有用到,用于区分在同一天存的不同组电压值
    '''
    Voltage_to_save = ['0.000000' for i in range(16)]
    New_BASE_voltages = Old_voltages
    
    for i,ch_i in enumerate(New_CHlist):
        New_BASE_voltages[ch_i] = New_Volts[i]
    
    for ch_i in Channel_electrodes:
        Voltage_to_save[Channel_electrodes[ch_i]] = '%.6f'%New_BASE_voltages[ch_i]

    Voltage_to_save_str = ''
    for vi in Voltage_to_save[:-1]:
        Voltage_to_save_str+=vi
        Voltage_to_save_str+=' '
    Voltage_to_save_str+=Voltage_to_save[-1] 

    voltage_path = 'D:\\Voltages\\' 
    filename = voltage_path+'tickle_%s_%s'%(datetime.datetime.now().strftime("%Y-%m-%d"),index)
    with open(filename, 'w') as output:
        output.write(Voltage_to_save_str)
    print('Save new voltage in %s'%filename)
    return filename

def read_DC(DC_filepath):
    '''
    Load the DC voltage file to a dict
    DC_filepath: 待加载文件的路径

    return: 通道名：电压值 的字典
    '''
    file1 = open(DC_filepath,"r+") 
    voltages_read = file1.read()
    file1.close()
    voltages_1to16 = np.array([float(vi) for vi in voltages_read.split(' ')]).flatten()

    voltages = dict()
    for electrode in Channel_electrodes.keys():
        voltages[electrode] = voltages_1to16[Channel_electrodes[electrode]]
    
    return voltages


def Volt_Compensation(alpha=0,beta=0,scale=0.04):
    '''
    根据alpha和beta确定四个电压的改变量
    '''
    #根据之前扫出的斜率，旋转alpha，beta的坐标

    alpha0 =  alpha
    beta0 =  beta
    dVA3 = scale*(alpha0+beta0)
    dVB3 = -dVA3
    dVRF1 = scale*(alpha0-beta0)
    dVRF2 = -dVRF1

    return [dVA3,dVB3,dVRF1,dVRF2]
#%% md
# 连接RF
#%%
import os

from modules.RigolDZ1000_package.Rigol_DZ1000 import RigolDZ1000
#%%
# CH1 = RigolDZ1000(CH=1)
# CH1.setFreq(14.445*MHz)
CH2 = RigolDZ1000(CH=2)
CH2.stopScan()
#%%
CH2 = RigolDZ1000(CH=2)
CH2.setAmp(0.005)
CH2.setFreq(100)

#%% md
# 定义实验提交函数
#%%
def timestamp_to_filepath(timeStamp=1623100830):
    '''
    Give a timestamp, convert it to date and hour, for example ,2021-07-20, 21
    '''
    dateArray = datetime.datetime.utcfromtimestamp(timeStamp)
    otherStyleTime = dateArray.strftime("%Y-%m-%d %H:%M:%S")
    RIDdate = otherStyleTime[:10]
    RIDhour = otherStyleTime[11:13]
    return RIDdate,RIDhour
    
def Tickle_frequency_scan(alpha=0,beta=0,scan_freq=np.arange(934000,941000,500),
                          CH2=None,scale_factor=0.04,Tickle_volt=0.2,SamplingTime=1,
                          setPMT=str([[12,13,14]]),setDC=True):
    '''
    用于tickle scan
    Params:
        alpha :  float scalar
        beta  :  float scalar for 1D scan or a 1d-array for 2D scan
        scan_freq : 1d-array in unit of Hz
        CH2   :  a CH2 = RigolDZ1000(CH=2) instance
        scale_factor : float scalar
        Tickle_volt  : float scalar 0.2V
    Returns:
         RID
         All data saved in HDF5 files with the RID
    '''
    
    if CH2:
        CH2.DZ1000.write(':SOUR%s:SWE:STAT OFF'%(CH2.CH))
        CH2.DZ1000.write('OUTP%s OFF'%(CH2.CH))
    
    RID_recorder = []
    beta_list = np.array([beta]).flatten()
    for num_vertical,beta0 in enumerate(beta_list):  
        if setDC:
            New_Volts = np.array([VA3_init,VB3_init,VRF1_init,VRF2_init])+np.array(Volt_Compensation(alpha=alpha,beta=beta0,scale=scale_factor))
            # New_Volts = [New_Volts[0],New_Volts[0],New_Volts[1],New_Volts[1],New_Volts[2],New_Volts[3]] #由于电极短路，轴向DC电极有两个
            print ('New_Volts',New_Volts)
            for setturns in [1]:
                for i,channel in enumerate(Axial_channels):
                    socketA = Mysocket("*************", 6666) 
                    socketB = Mysocket("*************", 6666) 
                    set_DC(Channel_electrodes[channel],New_Volts[i])
                time.sleep(0.5)

        scan=dict(ty ='RangeScan',start = float(scan_freq[0]), stop= float(scan_freq[-1]),npoints=len(scan_freq))

        expid = dict( 
            file="repository/QCMOS_package/MMcompensation.py",
            class_name="MMcompensation",
            log_level=logging.WARNING,
            arguments=dict(
                X_scan_range = scan,
                SamplingTime_ms = SamplingTime,  # detecting time  in seconds
                Tickle_volt = Tickle_volt,
                alpha = float(alpha),
                beta = float(beta0),
                # PMT_Select_Channels=setPMT,
            ),
        )
        if not schedule.get_status():
            rid = schedule.submit(pipeline_name="main", expid=expid,
                priority=0, due_date=None, flush=False)
            RID_recorder.append(rid)
            print (rid)
        print("current schedule")
        print(schedule.get_status())

        # wait for experiment to finish
        # this can be written nicer by subscribing and reacting to scheduler changes
        while rid in schedule.get_status():
            time.sleep(1.0)
            
        time.sleep(1.0)
            
        exp_data = read_hdf5(rid)
        exp_data.plot(xaxis='x_points',yaxis='probability',figsize=(10,1),alpha=alpha,beta=beta0)
    
    if CH2: 
        CH2.stopScan()
    if setDC:
        for key in ['A3','B3','RF1','RF2']:
            set_DC(Channel_electrodes[key],BASE_voltages[key])
    
    return RID_recorder

def Tickle_frequency_scan_fast(base_voltages,alpha=0,beta=0,scan_freq=np.arange(934000,941000,500),
                          scale_factor=0.04,Tickle_volt=0.2,SamplingTime=1,
                          setPMT=str([[12,13,14]]),setDC=True):
    '''
    用于tickle scan
    Params:
        alpha :  float scalar
        beta  :  float scalar for 1D scan or a 1d-array for 2D scan
        scan_freq : 1d-array in unit of Hz
        CH2   :  a CH2 = RigolDZ1000(CH=2) instance
        scale_factor : float scalar
        Tickle_volt  : float scalar 0.2V
    Returns:
         RID
         All data saved in HDF5 files with the RID
    ''' 
    
    RID_recorder = []
    plot_list = np.array([beta]).flatten()
    alpha_list = np.array([alpha]).flatten()
    scan=dict(ty ='RangeScan',start = float(scan_freq[0]), stop= float(scan_freq[-1]),npoints=len(scan_freq))

    expid = dict( 
        file="repository/QCMOS_package/MMcompensation_fast.py",
        class_name="MMcompensation_Fast",
        log_level=logging.WARNING,
        arguments=dict(
            X_scan_range = scan,
            SamplingTime_ms = SamplingTime,  # detecting time  in seconds
            Tickle_volt = Tickle_volt,
            alpha_start = float(alpha_list[0]),
            alpha_end = float(alpha_list[-1]),
            beta_start = float(plot_list[0]),
            beta_end = float(plot_list[-1]),
            DC_scan_number=max([len(alpha_list),len(plot_list)]),
            V_A=base_voltages['A3'],
            V_B=base_voltages['B3'],
            V_RF1=base_voltages['RF1'],
            V_RF2=base_voltages['RF2'],
            scale=scale_factor,
            # PMT_Select_Channels=setPMT,
        ),
    )
    if not schedule.get_status():
        rid = schedule.submit(pipeline_name="main", expid=expid,
            priority=0, due_date=None, flush=False)
        RID_recorder.append(rid)
        print (rid)
    print("current schedule")
    print(schedule.get_status())

    # wait for experiment to finish
    # this can be written nicer by subscribing and reacting to scheduler changes
    while rid in schedule.get_status():
        time.sleep(1.0)
        
    time.sleep(1.0)
    return rid

def plot_tickle_fast(rid):
    exp_data = read_hdf5(rid)
    f_scan_num = len(exp_data.datasets['x_points'])
    scan_result_recorder = {'x_points':0,'Cooling_Counts':[],'beta':[],'alpha':[]}
    
    for i_scan in range(len(exp_data.datasets['alpha'])):
        scan_result_recorder['x_points'] = exp_data.datasets['x_points']
        scan_result_recorder['Cooling_Counts'].append(exp_data.datasets['y_counts_all'][f_scan_num*i_scan+1:f_scan_num*(i_scan+1)])
        scan_result_recorder['beta'].append(exp_data.datasets['beta'][i_scan])
        scan_result_recorder['alpha'].append(exp_data.datasets['alpha'][i_scan])
    
    timeStamp = int(exp_data.info['run_time'])
    RIDdate,RIDhour = timestamp_to_filepath(timeStamp=timeStamp)
    Path_to_savefile=exp_data.filepath_root+RIDdate+'/'

    cooling_data = np.array(scan_result_recorder['Cooling_Counts']).T[0].T
    frequency_data = np.array([scan_result_recorder['x_points']]).flatten()
    if scan_result_recorder['beta'][0]!=scan_result_recorder['beta'][-1]:
        plot_list = np.array([scan_result_recorder['beta']]).flatten()
        alpha_plot=scan_result_recorder['alpha']
        beta_plot = scan_result_recorder['beta']
        optimal_plot=(plot_list[0]+plot_list[-1])/2.0

        fig,ax = plt.subplots(nrows=1, ncols=2,figsize=(10,4))
        cset1 = ax[0].imshow(cooling_data,cmap=plt.cm.viridis,aspect='auto',
                extent = [frequency_data[0],frequency_data[-1],plot_list[-1],plot_list[0]+1e-6])  #extent = [x_min , x_max, y_min , y_max]
        ax[0].set_xlabel('Freq')
        ax[0].set_ylabel(r'$\beta$')
        ax[0].set_title(f'$\\alpha$={alpha_plot[0]}~{alpha_plot[-1]}',fontsize=14)
        fig.colorbar(cset1, ax=ax[0])
        
        if (len(cooling_data)>=4):
            cooling_std = np.std(np.array(cooling_data),axis=1)
            cooling_std_fit = interpolate.interp1d(plot_list,cooling_std,3,fill_value="extrapolate")
            # plt.figure(figsize=(4,3))
            plot_beta_range=np.arange(plot_list[0],plot_list[-1],0.001)
            ax[1].plot(plot_beta_range,cooling_std_fit(plot_beta_range),label='Polyfit')
            ax[1].plot(plot_list,np.std(np.array(cooling_data),axis=1),label='std',marker='o',linewidth =0)
            

            beta_min_guess = plot_list[np.where(cooling_std==np.min(cooling_std))[0][0]]-0.01
            optimal_plot = minimize(cooling_std_fit,beta_min_guess,method='Nelder-Mead',tol=1e-7)['x']
            beta_opt = optimal_plot
            alpha_opt = (optimal_plot-beta_plot[0])/(beta_plot[-1]-beta_plot[0])*(alpha_plot[-1]-alpha_plot[0])+alpha_plot[0]
            ax[1].vlines(x=optimal_plot,ymin=np.min(cooling_std)*0.9,ymax=np.max(cooling_std)*1.1,color='crimson')
            plt.title('$\\alpha_{opt}$='+'%.6f'%alpha_opt+',$\\beta_{opt}$=%.6f'%beta_opt,fontsize=12)

            plt.xlabel(r'$\beta$')
            plt.ylabel(r'$P_{s.t.d.}$')
            plt.legend(loc='best')
    elif scan_result_recorder['alpha'][0]!=scan_result_recorder['alpha'][-1]:
        plot_list = np.array([scan_result_recorder['alpha']]).flatten()

        beta_plot = scan_result_recorder['beta']
        alpha_plot = scan_result_recorder['alpha']
        
        optimal_plot=(plot_list[0]+plot_list[-1])/2.0

        fig,ax = plt.subplots(nrows=1, ncols=2,figsize=(10,4))
        cset1 = ax[0].imshow(cooling_data,cmap=plt.cm.viridis,aspect='auto',
                extent = [frequency_data[0],frequency_data[-1],plot_list[-1],plot_list[0]+1e-6])  #extent = [x_min , x_max, y_min , y_max]
        ax[0].set_xlabel('Freq')
        ax[0].set_ylabel('$\\alpha$')
        ax[0].set_title(f'$\\beta$={beta_plot[0]}~{beta_plot[-1]}',fontsize=14)
        fig.colorbar(cset1, ax=ax[0])
        
        if (len(cooling_data)>=4):
            cooling_std = np.std(np.array(cooling_data),axis=1)
            cooling_std_fit = interpolate.interp1d(plot_list,cooling_std,3,fill_value="extrapolate")
            # plt.figure(figsize=(4,3))
            plot_beta_range=np.arange(plot_list[0],plot_list[-1],0.001)
            ax[1].plot(plot_beta_range,cooling_std_fit(plot_beta_range),label='Polyfit')
            ax[1].plot(plot_list,np.std(np.array(cooling_data),axis=1),label='std',marker='o',linewidth =0)
            

            beta_min_guess = plot_list[np.where(cooling_std==np.min(cooling_std))[0][0]]-0.01
            optimal_plot = minimize(cooling_std_fit,beta_min_guess,method='Nelder-Mead',tol=1e-7)['x']
            alpha_opt = optimal_plot
            beta_opt = (optimal_plot-alpha_plot[0])/(alpha_plot[-1]-alpha_plot[0])*(beta_plot[-1]-beta_plot[0])+beta_plot[0]
            ax[1].vlines(x=optimal_plot,ymin=np.min(cooling_std)*0.9,ymax=np.max(cooling_std)*1.1,color='crimson')
            plt.title('$\\alpha_{opt}$='+'%.6f'%alpha_opt+',$\\beta_{opt}$=%.6f'%beta_opt,fontsize=12)

            plt.xlabel('$\\alpha$')
            plt.ylabel(r'$P_{s.t.d.}$')
            plt.legend(loc='best')
    else:
        pass
    plt.tight_layout()
    plt.show()
    
    return float(alpha_opt),float(beta_opt)
    
#%% md
# 初始化电压
#%% md
<font color=red>运行下面这段代码时注意检查日期！！！</font>
#%%
'''
读取Json格式的电压，用于光学补微运动之后

--------------------------
|    注意修改配置文件名！  |
--------------------------

'''
import json
DC_filepath0 = 'D:\\uqctrl_CODE\\code\\more ion1.json'  #设置初始电压作为基准，可以是昨天的电压，或者一直以来都用的比较好的电压
BASE_voltages = {}
with open(DC_filepath0,"r") as f:
    data = json.load(f)
    for channel in data["channels"]:
        BASE_voltages[channel["channel_id"]]=float(channel["value"])

print ('Base voltage:\n',BASE_voltages)

#%%
'''
set DC Voltages
'''

for key in list(BASE_voltages):
    set_DC(Channel_electrodes[key],BASE_voltages[key])

# VA3_init,VB3_init,VRF1_init,VRF2_init作为迭代参数，参与后续代码
VA3_init,VB3_init,VRF1_init,VRF2_init = BASE_voltages['A3'],BASE_voltages['B3'],BASE_voltages['RF1'],BASE_voltages['RF2']
VA3_init,VB3_init,VRF1_init,VRF2_init

print("当前电压：",BASE_voltages)
#%% md
# 补偿微运动
#%% md
## 扫描声子频率
#%%
'''
set scan parameters
'''

wx_freq_init =1500*kHz  #扫描完成后请将此处频率改为测得的声子频率

half_span=200*kHz
step=1*kHz
#half_span=10*kHz
#step=0.2*kHz
scan_freq_wx_init = np.arange(wx_freq_init-half_span,wx_freq_init+half_span,step)

# 稍微改变电压让谱更好扫
alpha_temp =0

beta_temp = 0

Tickle_volt_x = 0.1
Tickle_volt_temp = Tickle_volt_x*7
center_channel = 18
setPMT=f'[[{center_channel-1},{center_channel},{center_channel+1}]]'
# setPMT=f'[[{center_channel}]]'
#%%
'''
scan on freq of x (higher)
'''

Tickle_frequency_scan(alpha=alpha_temp,beta=beta_temp,CH2=CH2,scan_freq=scan_freq_wx_init,
                          scale_factor=0.04,Tickle_volt=Tickle_volt_temp ,SamplingTime=0.2,setPMT=setPMT)
                          
    
#%% md
## 光电结合补微运动
#%%

Tickle_volt_x = 0.1
Tickle_volt=Tickle_volt_x*2


if CH2:
        CH2.DZ1000.write(':SOUR%s:SWE:STAT OFF'%(CH2.CH))
        CH2.DZ1000.write('OUTP%s OFF'%(CH2.CH))

CH2 = RigolDZ1000(CH=2)
CH2.setAmp(Tickle_volt)

wx_freq_init =1854*kHz  #扫描完成后请将此处频率改为测得的声子频率
half_span=10*kHz
step=1*kHz
scan_freq = np.arange(wx_freq_init-half_span,wx_freq_init+half_span,step)
for i in range(50):
    for freq in scan_freq:
        CH2.setFreq(freq)
        time.sleep(0.1)

#%%
#停止tickle
if CH2: 
    CH2.stopScan()
#%% md
## 扫描Alpha固定时的Beta值
#%%
'''
set scan parameters
'''

wx_freq = 1764*kHz
half_span=20*kHz
scan_freq_wx = np.arange(wx_freq-half_span,wx_freq+half_span+2*kHz,1*kHz)  #因为冷却需要时间，所以扫描范围不对称

alpha_temp = 0
beta_temp=np.arange(-1,1,0.1)
# beta_temp=np.zeros(2)
Tickle_volt_x=0.1
Tickle_volt_temp=Tickle_volt_x*7
#%%
'''
submit tickle task
'''
rid=Tickle_frequency_scan_fast(base_voltages=BASE_voltages,alpha=alpha_temp,beta=beta_temp,scan_freq=scan_freq_wx,
                          scale_factor=0.04,Tickle_volt=Tickle_volt_temp,SamplingTime=0.1,setPMT=setPMT)
alpha1,beta1 = plot_tickle_fast(rid)


#%%
alpha1,beta1 = plot_tickle_fast(rid)
#%% md
# 固定beta 扫alpha(此cell在”固定alpha，扫beta“无效时使用)
#%%
wx_freq =1823*kHz
span=10*kHz
scan_freq_wx = np.arange(wx_freq-span,wx_freq+span+2*kHz,1*kHz)  #因为冷却需要时间，所以扫描范围不对称
alpha_temp = np.arange(-1,1,0.1)
# alpha_temp = 0
beta_temp=0
Tickle_volt_x=0.1
Tickle_volt_temp=Tickle_volt_x*6

#%%
'''
submit tickle task
'''
rid=Tickle_frequency_scan_fast(base_voltages=BASE_voltages,alpha=alpha_temp,beta=beta_temp,scan_freq=scan_freq_wx,
                          scale_factor=0.04,Tickle_volt=Tickle_volt_temp,SamplingTime=0.1,setPMT=setPMT)
alpha1,beta1 = plot_tickle_fast(rid)
#%%
alpha1,beta1 = plot_tickle_fast(rid)
#%% md
# alpha，beta 同时线性增长，用于沿某一方向扫
#%%
wx_freq =1571 * kHz
span=5 * kHz
scan_freq_wx = np.arange(wx_freq-span,wx_freq+span+1 * kHz,0.5*kHz)  #因为冷却需要时间，所以扫描范围不对称

# alpha_temp = np.arange(-1,1.1,1)
beta_temp= np.arange(-5,5.1,1)
alpha_temp = (beta_temp*****)/-1.02
Tickle_volt_temp=Tickle_volt_x * 0.1
#%%
'''
submit tickle task
'''
rid=Tickle_frequency_scan_fast(base_voltages=BASE_voltages,alpha=alpha_temp,beta=beta_temp,scan_freq=scan_freq_wx,
                          scale_factor=0.04,Tickle_volt=Tickle_volt_temp,SamplingTime=0.1,setPMT=setPMT)

#%%
alpha1,beta1 = plot_tickle_fast(rid)
#%%
#调试同一个点跑多次时作图使用，一般不会用到
def tickle_plot_same(RID_list=[0],callprint=True,callfit=True,initial_guess=0):
    '''
    imshow for data of Tickle_frequency_scan()
    plot the imshow of a list of RIDs
    '''
    RID_list = np.array([RID_list]).flatten()
    scan_result_recorder = {'x_points':0,'Cooling_Counts':[],'beta':[],'alpha':[]}
    for rid_i in RID_list:
        exp_data = read_hdf5(rid_i,callprint=callprint)
        scan_result_recorder['x_points'] = exp_data.datasets['x_points']
        scan_result_recorder['Cooling_Counts'].append(exp_data.datasets['probability'][1:])
        scan_result_recorder['beta'].append(exp_data.datasets['beta'])
        scan_result_recorder['alpha'].append(exp_data.datasets['alpha'])
        
    timeStamp = int(exp_data.info['run_time'])
    RIDdate,RIDhour = timestamp_to_filepath(timeStamp=timeStamp)
    Path_to_savefile=exp_data.filepath_root+RIDdate+'/'

    cooling_data = np.array(scan_result_recorder['Cooling_Counts']).T[0].T
    frequency_data = np.array([scan_result_recorder['x_points']]).flatten()
    alpha_list = RID_list-RID_list[0]
    optimal_alpha=(alpha_list[0]+alpha_list[-1])/2.0
    

    beta_plot=scan_result_recorder['beta'][0]

    fig,ax = plt.subplots(nrows=1, ncols=3,figsize=(10,4))
    cset1 = ax[0].imshow(cooling_data,cmap=plt.cm.viridis,aspect='auto',
               extent = [frequency_data[0],frequency_data[-1],alpha_list[-1],alpha_list[0]+1e-6])  #extent = [x_min , x_max, y_min , y_max]
    ax[0].set_xlabel('Freq')
    ax[0].set_ylabel('experiment number')
    ax[0].set_title(r'$\beta$=%s'%scan_result_recorder['beta'][0],fontsize=14)
    fig.colorbar(cset1, ax=ax[0])
    
    if (len(cooling_data)>=4):
        cooling_std = np.std(np.array(cooling_data),axis=1)
        cooling_std_fit = interpolate.interp1d(alpha_list,cooling_std,3,fill_value="extrapolate")
        # plt.figure(figsize=(4,3))
        plot_beta_range=np.arange(alpha_list[0],alpha_list[-1],0.001)
        ax[2].plot(plot_beta_range,cooling_std_fit(plot_beta_range),label='Polyfit')
        ax[2].plot(alpha_list,np.std(np.array(cooling_data),axis=1),label='std',marker='o',linewidth =0)
        
        if callfit:
            if initial_guess:
                alpha_min_guess = initial_guess
            else:
                alpha_min_guess = alpha_list[np.where(cooling_std==np.min(cooling_std))[0][0]]-0.01
            optimal_alpha = minimize(cooling_std_fit,alpha_min_guess,method='Nelder-Mead',tol=1e-7)['x']
            ax[1].vlines(x=optimal_alpha,ymin=np.min(cooling_std)*0.9,ymax=np.max(cooling_std)*1.1,color='crimson')
        plt.title('alpha=%.6f'%scan_result_recorder['alpha'][0],fontsize=12)

        plt.xlabel('experiment number')
        plt.ylabel(r'$P_{s.t.d.}$') 
        plt.legend(loc='best')
#     plt.xlim(frequency_data[0]/1e3,frequency_data[-1]/1e3)
    ax[1].hist(cooling_std)
    plt.tight_layout()
    # plt.savefig(Path_to_savefile+'RID%s_to_RID%s_MMcompensation_scan_alpha%.2f.pdf'%(RID_list[0],RID_list[-1],alpha_plot))
    plt.show()
    
    return float(beta_plot),float(optimal_alpha)

beta1,alpha1 = tickle_plot_same(RID_list_beta1,callprint=False,callfit=False)
#%%

#%%
def Fun(x,a1,a2):                   
    return a1*x+a2
#%%
Alpha_x=[0,0.5,-0.5]
Beta_x=  [-0.055,0.199,-0.25] 
#%%
import numpy as np
import matplotlib.pyplot as plt
def Fun(x,a1,a2):                   
    return a1*x+a2

para_x,pcov_x=curve_fit(Fun,Alpha_x,Beta_x)
Alpha_x=np.array(Alpha_x)
print(para_x[0],para_x[1])
plt.plot(Alpha_x,Beta_x,'rx')
plt.plot(Alpha_x,Fun(Alpha_x,para_x[0],para_x[1]))
plt.xlabel(r'$\alpha$')
plt.ylabel(r'$\beta$')
plt.title(f'fitting beta={para_x[0]:10.3f} alpha+{para_x[1]:10.3f}',fontsize=12)
#%%
para_x=[0,0]
para_x[0]=2.55
para_x[1]=-0.068
print(para_x[0],para_x[1])
#%%
Alpha_y=[0.099, 0.147,-0.077]
Beta_y=  [0,-0.5,0.5]
#%%
import numpy as np
import matplotlib.pyplot as plt
def Fun(x,a1,a2):                   
    return a1*x+a2

para_y,pcov_y=curve_fit(Fun,Alpha_y,Beta_y)
Alpha_y=np.array(Alpha_y)
plt.plot(Alpha_y,Beta_y,'rx')
plt.plot(Alpha_y,Fun(Alpha_y,para_y[0],para_y[1]))

print(para_y[0],para_y[1])
plt.xlabel(r'$\alpha$')
plt.ylabel(r'$\beta$')
plt.title(f'fitting beta={para_y[0]:10.3f} alpha+{para_y[1]:10.3f}',fontsize=12)
#%%
para_y=[-0.001,-0.02]  
print(para_y[0],para_y[1])
#%% md
### 得到交点并设置电压
#%%
alpha_crossing=(para_x[1]-para_y[1])/(para_y[0]-para_x[0])
beta_crossing=para_x[0]*alpha_crossing+para_x[1]
print(alpha_crossing,beta_crossing)
#%%
alpha_crossing=(para_x[1]-para_y[1])/(para_y[0]-para_x[0])
beta_crossing=para_x[0]*alpha_crossing+para_x[1]
plt.plot(Alpha_x,Fun(Alpha_x,para_x[0],para_x[1]))
plt.plot(Alpha_y,Fun(Alpha_y,para_y[0],para_y[1]))
plt.plot(alpha_crossing,beta_crossing,'r*')
plt.plot(Alpha_x,Beta_x,'bx')
plt.plot(Alpha_y,Beta_y,'rx')
print(alpha_crossing,beta_crossing)
plt.xlabel(r'$\alpha$')
plt.ylabel(r'$\beta$')
plt.title(f'crossing at alpha={alpha_crossing:10.3f}, beta={beta_crossing:10.3f}',fontsize=12)
ax = plt.gca()
ax.set_aspect(1)
#%%
'''
使用alpha beta进行光学补微运动之后，通过本单元格将电压值保存
'''

alpha_crossing=0.058576
beta_crossing=-0.009032
#%%
# alpha_crossing=-1.550709
# beta_crossing=beta1 
#%%
print(alpha_crossing)
print(beta_crossing)
#%%
alpha_optimal = alpha_crossing
beta_optimal = beta_crossing
scale_factor = 0.04
New_Volts = np.array([VA3_init,VB3_init,VRF1_init,VRF2_init])+np.array(Volt_Compensation(alpha=alpha_optimal,beta=beta_optimal,scale=scale_factor))
# New_Volts = [New_Volts[0],New_Volts[0],New_Volts[1],New_Volts[1],New_Volts[2],New_Volts[3]]
print ('New_Volts',New_Volts)
for i,channel in enumerate(Axial_channels):
    set_DC(Channel_electrodes[channel],New_Volts[i])

#%%
new_voltage_filepath = save_DC(New_Volts=New_Volts,Old_voltages=BASE_voltages,New_CHlist = ['A3','B3','RF1','RF2'],index=1)
print(new_voltage_filepath)
#%%
'''
将tickle电压文件转换为 Json
注意修改读取的tickle电压文件！
'''
import json
def save_json(Volts,filename ,path ='D:\uqctrl_CODE\code\\' ):
    print("Save voltage in "+path+filename)
    with open(path+filename,'w') as f:
        
        data =  {"ipA":"***************","ipB":"**************","channels":[]}
        for v_channel in Volts:
            data["channels"].append(
                {
                    "channel_id": v_channel,
                    "value":str(Volts[v_channel]),
                    "step":"0.1"
                }
            )
        json.dump(data,f,indent=4)

DC_filepath0 = new_voltage_filepath  #修改为要转换的文件
index = "1"
filename = 'tickle_%s_%s'%(datetime.datetime.now().strftime("%Y-%m-%d"),index)+".json"
save_json(read_DC(DC_filepath0),filename)
#%%
for i in read_DC(DC_filepath0):
    print(i)

#%%
import socket
s = socket.socket()
s.connect(('***************',8889))

# 最后一位为电压值
s.send(b"set_00_1.5015")
print(s.recv(1024))
#%%
import socket


#改RF参考1
s = socket.socket()
s.connect(('***************',6666))
# 最后一位为电压值
s.send(b"SetDC_1_0")##1.532
print(s.recv(1024))

#改RF参考2
s = socket.socket()
s.connect(('***************',6666))
# 最后一位为电压值
s.send(b"SetDC_2_4.23")
print(s.recv(1024))

#改相位参考
s = socket.socket()
s.connect(('***************',6666))
# 最后一位为电压值
s.send(b"SetDC_3_3.19")
print(s.recv(1024))
#%%

x=np.linspace(0,2,1000)

delta1=0.1
mu1=0.9
y1=delta1/(np.sqrt((x**2-mu1)**2+delta1**2)+x**2-mu1)
plt.plot(x,y1,label="$\mu/E_F=0.9$")
delta2=1
mu2=0
y2=delta2/(np.sqrt((x**2-mu2)**2+delta2**2)+x**2-mu2)
plt.plot(x,y2,label="$\mu/E_F=0$")
delta3=1.3
mu3=-1
y3=delta3/(np.sqrt((x**2-mu3)**2+delta3**2)+x**2-mu3)
plt.plot(x,y3,label="$\mu/E_F=-1$")
plt.legend()
plt.xlabel("$k/k_F$")
plt.ylabel("$v_k/u_k$")

#%%

#%%
x1=[1.328,1.565,1.798,1.997,2.21,2.427]
y1=[359.6,524.8,316.9,201.9,176.5,206.4]
x2=[1.575,1.788,1.98,2.167,2.365, 2.572]
y2=[47.9,500.3,102.8,96.4,108.0,39.0]
plt.plot(x1,y1,label="$mode1$",marker='.')
plt.plot(x2,y2,label="$mode2$",marker='.')
plt.legend()
plt.xlabel("$Phonon Frequency$ (MHz)")
plt.ylabel("$Heating Rate$ (quanta/s)" )

#%%
a=np.array([0,0,0,0])
a[0:2]