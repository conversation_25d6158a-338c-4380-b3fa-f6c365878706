#%%
from jupyter_function import *

import numpy as np
from ms_package.ms_parameter import lamb_dicke_parameter, mode_x, mode_z
from modules.config import LOADED_PARAMETER
#%%
parameter = LOADED_PARAMETER()
freq = parameter.Light_554.Motion_freq
eta = lamb_dicke_parameter(parameter.Experiment.ion_num, freq)
print(eta)
#%%
import seaborn as sns
import matplotlib.pyplot as plt

plt.imshow(np.abs(eta), cmap='hot', interpolation='nearest')
plt.colorbar()  # 添加颜色条
plt.title("LD coefficient")
plt.show()
#%%
radial_com = 2.18e6
axial_com = 2e5

axial_mode_egv,axial_mode = mode_z(12)
axial_mode_fre = np.sqrt(axial_mode_egv) * axial_com
print(axial_mode_fre)
plt.imshow(np.abs(axial_mode), cmap='hot', interpolation='nearest')
plt.colorbar()  # 添加颜色条
plt.title("Axial mode")
plt.show()
#%%
beta = radial_com/axial_com
radial_mode_egv,radial_mode = mode_x(12,beta)
radial_mode_fre = np.sqrt(radial_mode_egv) * axial_com
print(radial_mode_fre)