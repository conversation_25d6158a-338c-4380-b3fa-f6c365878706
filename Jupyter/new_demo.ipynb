#%% md
## 连接

#%%
%matplotlib inline
from jupyter_function import *
#%%
manager = ARTIQManager()
#%%
parameter = read_parameter()
#%%
def submit_demo_exp(route,points_info,ion_choice = (0,),adaptive_condition = "False",is_circuit = False):
    exp = {'arguments': {'route': route,
                            'points_info': points_info,
                            'ion_choice':str(ion_choice),
                            "adaptive_condition":adaptive_condition,
                            "is_circuit": is_circuit
                            },
                    'class_name': 'RouteDemo',
                    'file': 'repository\\demo\\demo.py',
                    'log_level': 30}

    #提交实验
    rid = submit_exp(exp,manager)
    return rid
#%% md
# 实验
#%%
# 选取离子
qubit_index = 2
phonon_index = 1
qubit_ms = (5,6)
#%% md
## AOD Scan
#%%
"""
AODScan: 该操作会用指定的AOD频率打拉曼光
args:
    "time": 操作时间,若不指定则会读取离子0的pi/2时间
    "frequency":AOD频率,若不指定则会读取AOD_middle_freq
"""

points_info = [{
    'points': np.linspace(98.1e6, 100.4e6, 51),  # 原始扫描点
}]

route = []
for param in points_info[0]['points']:
     shot = [
            {
                "name": "AODScan",
                "args": {
                    "samplingdense":"frequency",
                    "time":5e-6,
                    "frequency": param
                }
            },
            
        ]
     route.append(shot)

"""
对于含有"samplingdense"参数的操作,会在特定条件下增加扫描的点
扫描点的增加条件通过 adaptive_condition 以字符串形式传入
"""
adaptive_condition = "max(probability)>0.2"

#%%
rid = submit_demo_exp(route,points_info,adaptive_condition=adaptive_condition)
#%% md
## Rabi
#%%
"""
Rabi: Raman_Rabi 操作
args:
    "qubit_index": 操作的离子编号,元组,
    "time": 操作时间,
    "theta": 操作角度,
    若同时指定“time”及“theta”,以time优先,若均不指定,则会执行pi/2脉冲 
    "phase": 操作相位,默认为0
    "aom_amp": aom幅度,默认为 config中的aom_amp
    "rabi_choice": Carrier/Red/Blue,默认为Carrier
    "detuning": Carrier操作的失谐,默认为0 
    "phonon_index": Red/Blue边带的声子编号,默认为0
    "phonon_frequency": 指定Red/Blue边带的声子频率
    若同时指定"phonon_frequency"与"phonon_index",以"phonon_frequency"优先
    "side_choice": All/Right/Left,默认为All
"""

# 例1 rabi扫t

points_info = [{
    'points': np.linspace(0.0e-6, 60e-6, 21),  # 原始扫描点
}]

route = []
for param in points_info[0]['points']:
     shot = [
            {
                "name": "Rabi",
                "args": {
                    "qubit_index": (qubit_index,),
                    "time":param
                }
            },
            
        ]
     route.append(shot)
rid = submit_demo_exp(route,points_info)

#%%
points_info = [{
    'points': np.linspace(0.0e-6, 60e-6, 151),  # 原始扫描点
}]

route = []
for param in points_info[0]['points']:
     shot = [
            {
                "name": "H",
                "args": {
                    "qubit_index": (0,),
                }
            },
            {
                "name": "H",
                "args": {
                    "qubit_index": (1,),
                }
            },
            {
                "name": "MS",
                "args": {
                    "qubit_index": (0,1),
                }
            },
        ]
     route.append(shot)
rid = submit_demo_exp(route,points_info)

#%%
# 例2：Spectrum

points_info = [{
    'points': np.linspace(-2.6e6, -3.3e6, 151),  # 原始扫描点
}]

route = []
for param in points_info[0]['points']:
     shot = [
            {
                "name": "Rabi",
                "args": {
                    "samplingdense": "detuning",
                    "qubit_index": (1,),
                    "theta":40*np.pi,
                    "detuning":param
                }
            },
            
        ]
     route.append(shot)

rid = submit_demo_exp(route,points_info)
#%% md
## Ramsey 
#%%
"""
=========================================================
Idle: 闲置,AOM幅度为0
args: 
    "qubit_index":操作的离子编号,元组,用于AOD频率衔接后续操作
    "time":闲置时间
=========================================================
Rphi:转轴在XY平面上的单比特旋转门
args:
    "qubit_index":操作的离子编号，元组
    "theta":旋转角度,默认pi/2
    "phi":旋转轴与X轴夹角,默认为0
=========================================================

"""


points_info = [{
    'points': np.linspace(0.5e-6, 5000e-6, 11),  # 原始扫描点
}]


route = []
for param in points_info[0]['points']:
     shot = [
            {
                "name": "Rphi",
                "args": {
                    "qubit_index": (qubit_index,),
                    "theta":np.pi/2,
                    "phi":0.0
                }
            },
            {
                 "name":"Idle",
                 "args":{
                      "qubit_index":(qubit_index,),
                      "time": param
                 }
            },
            {
                "name": "Rphi",
                "args": {
                    "qubit_index": (qubit_index,),
                    "theta":np.pi/2,
                    "phi":0.0
                }
            },
        ]
     route.append(shot)
#%%
rid = submit_demo_exp(route,points_info)
#%% md
## 蓝边带Ramsey
#%%
points_info = [{
    'points': np.linspace(1.0e-6, 5001e-6, 21), 
}]


route = []
for param in points_info[0]['points']:
     shot = [
            {
                "name": "Rabi",
                "args": {
                    "qubit_index": (qubit_index,),
                    "theta":np.pi/2,
                    "rabi_choice":"Blue",
                    "phonon_index":phonon_index
                }
            },
            {
                 "name":"Idle",
                 "args":{
                      "qubit_index":(qubit_index,),
                      "time": param
                 }
            },
            {
                "name": "Rabi",
                "args": {
                    "qubit_index": (qubit_index,),
                    "theta":np.pi/2,
                    "rabi_choice":"Blue",
                    "phonon_index":phonon_index
                }
            },
        ]
     route.append(shot)
#%%
rid = submit_demo_exp(route,points_info)
#%% md
## MS
#%%
"""
MS: MS操作
args:
    "qubit_index":操作离子编号,元组,
    "time":操作时间,读取config
    "aom_amp":aom幅度,默认读取config,
    "aom_ratio":aom红蓝成分比例,默认读取config,
    "phonon_index":主要声子模式,默认读取config,
    "detuning":MS操作与声子的失谐,默认读取config,
    "ac_stark_shift":AC Stark shift,默认读取config,
    "phase":操作相位,默认读取config,
    "rabi_choice":Carrier/Red/Blue,用于探测红蓝成分的拉比频率,默认Carrier
"""
#%%
#例1：MS扫光强
points_info = [{
    'points': np.linspace(0.1, 0.4, 11),  # 原始扫描点
}]

n_gate = 1

route = []
for param in points_info[0]['points']:
    shot  = []
    ms = {
            "name":"MS",
            "args":{
                "qubit_index":(qubit_ms[0],qubit_ms[1]),
                "aom_amp": param
            }
        }
    idle = {
            "name":"Idle",
            "args":{
                "qubit_index":(qubit_ms[0],qubit_ms[1]),
                "time":0.5e-6
            }
    }
    for _ in range(int(n_gate)):
        shot.append(ms)
        shot.append(idle)
    route.append(shot)
#%%
rid = submit_demo_exp(route,points_info,ion_choice=qubit_ms)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])
x = x[0:len(y)]
# 寻找 00和11 交叉点
P = y[:,0:4:3]
crosses = Cross_fit(x,P)

fig,ax = plt.subplots()
for cross in crosses:
    ax.axvline(x = cross,color = "r",linestyle ="--")
if len(crosses)>0:
    print(f" P11 = P00  at aom amplitude = {crosses[0]} " )

ax.plot(x, y,'-o')
ax.set_xlabel('AOM amplitude')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan AOM amplitude')
# save_plot(fig,base_filename=f"MS_AOM_amp_RID{rid}")
#%%
parameter = read_parameter()
parameter.Light_554.AOM_AWG_amp_ms[qubit_ms[0]][qubit_ms[1]] = crosses[0]
parameter.Light_554.AOM_AWG_amp_ms[qubit_ms[1]][qubit_ms[0]] = crosses[0]
# parameter.Light_554.AOM_AWG_amp_ms = 0.303
parameter.update_config_json()
#%%
# 例2 MS扫时间

points_info = [{
    'points': np.linspace(0.0, 800e-6, 81), 
}]

route = []
for param in points_info[0]['points']:
     shot = [
            {
                "name": "MS",
                "args": {
                    "qubit_index": qubit_ms,
                    "time": param
                }
            },
            
        ]
     route.append(shot)
#%%
rid = submit_demo_exp(route,points_info,ion_choice=qubit_ms)
#%%
# 例3 MS-MS(phi)
points_info =[
    {
        "points":np.linspace(0.0,np.pi*2,31)
    }
]

route = []
for param in points_info[0]["points"]:
    shot = [
        {
            "name":'MS',
            "args":{
                "qubit_index": qubit_ms,
                "phase":0
            }
        },
        {
            "name":'MS',
            "args":{
                "qubit_index": qubit_ms,
                "phase":param
            }
        },
    ]
    route.append(shot)
rid = submit_demo_exp(route,points_info,ion_choice=qubit_ms)
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])[:]
y = np.array(result["datasets"]["computational_basis_probability"])[:]
parity = y[:,0]-y[:,3]

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])

fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/100)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS-MS gate scan phase')
# save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%%
a = _[0]
b = _[3]+np.pi/2
print(a,b)
# b-a
# b=-np.pi
#%%
shift_ms = (-b)/2/2/np.pi/gate_time
print(shift_ms)
#%%
parameter = read_parameter()
parameter.Light_554.MS_AC_Stark_shift[qubit_index[0]][qubit_index[1]] += shift_ms
parameter.Light_554.MS_AC_Stark_shift[qubit_index[1]][qubit_index[0]] += shift_ms
parameter.update_config_json()
#%%
# 例4 Pi2-MS
points_info =[
    {
        "points":np.linspace(0.0,np.pi*2,31)
    }
]

route = []
for param in points_info[0]["points"]:
    shot = [
        
        {
            "name":"Rphi",
            "args":{
                "qubit_index":(qubit_ms[0],),
            }
        },
        {
            "name":"Rphi",
            "args":{
                "qubit_index":(qubit_ms[1],),
            }
        },
        {
            "name":'MS',
            "args":{
                "qubit_index": qubit_ms,
                "phase":param
            }
        },
    ]
    route.append(shot)
rid = submit_demo_exp(route,points_info,ion_choice=qubit_ms)
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])[:]
y = np.array(result["datasets"]["computational_basis_probability"])[:]
parity = y[:,0]-y[:,3]

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])

fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/100)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS-MS gate scan phase')
# save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%%
phase_ = _[3]
amp_ = _[0]
print(amp_,phase_)
# phase_=0.48
#%%
parameter = read_parameter()
parameter.Light_554.MS_phase[qubit_index[0]][qubit_index[1]]= -phase_/2+np.pi/2
parameter.Light_554.MS_phase[qubit_index[1]][qubit_index[0]]= -phase_/2+np.pi/2
parameter.update_config_json()
#%%
# 例5 Parity
points_info =[
    {
        "points":np.linspace(0.0,np.pi*2,31)
    }
]

route = []
for param in points_info[0]["points"]:
    shot = [
        {
            "name":'MS',
            "args":{
                "qubit_index": qubit_ms
            }
        },
        {
            "name":"Idle",
                    "args":{
                        "qubit_index":(qubit_ms[0],qubit_ms[1]),
                        "time":0.5e-6
                    }
        },
        {
            "name":'MS',
            "args":{
                "qubit_index": qubit_ms
            }
        },
        {
            "name":"Idle",
                    "args":{
                        "qubit_index":(qubit_ms[0],qubit_ms[1]),
                        "time":0.5e-6
                    }
        },
        {
            "name":'MS',
            "args":{
                "qubit_index": qubit_ms
            }
        },
        {
            "name":"Idle",
                    "args":{
                        "qubit_index":(qubit_ms[0],qubit_ms[1]),
                        "time":0.5e-6
                    }
        },
        {
            "name":"Rphi",
            "args":{
                "qubit_index":(qubit_ms[0],),
                "phi":param 
            }
        },
        {
            "name":"Rphi",
            "args":{
                "qubit_index":(qubit_ms[1],),
                "phi":param 
            }
        }
    ]
    route.append(shot)
rid = submit_demo_exp(route,points_info,ion_choice=qubit_ms)
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])[:]
y = np.array(result["datasets"]["computational_basis_probability"])[:]
parity = y[:,0]+y[:,3]-y[:,1]-y[:,2]

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])

fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/400)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan Parity')
# save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%%
a=_[3]
b = _[0]
print(a,b)
#%% md
## 线路
#%%
"""
Rzz: Rzz操作
args:
    qubit_index:操作的离子,元组
"""
#%% md
# 校准
#%% md
## Rabi_fidelity
#%%
def cali_rabi_pi2(qubit_index):
    print("calibrat rabi pi2")

def cali_rabi_fidelity(qubit_index):
    fidelity = measure_rabi_fidelity(qubit_index)
    ref = 0.95
    if fidelity < ref :
        cali_rabi_pi2(qubit_index)
        fidelity = measure_rabi_fidelity(qubit_index)
    if fidelity < ref :
        raise ValueError(f"qubit {qubit_index} rabi fidelity {fidelity} after calibration, lower than {ref}")

def measure_rabi_fidelity(qubit_index):
    import random
    n_gate = 1
    lower = 0.5 * np.pi
    upper = 1 * np.pi
    random_theta = random.uniform(lower, upper)
    print(random_theta*n_gate/np.pi)
    p_exact = np.sin(random_theta*n_gate/2)**2 
    print("p_exact:",p_exact)

    point = []
    for i in range(n_gate):
        R_theta = {
            "name": "Rphi",
            "args": {
                "qubit_index":(qubit_index,),
                "theta":random_theta,
                "phi":0
            }
        }
        idel_gate = {
            "name":"Idle",
            "args":{
                "qubit_index":(qubit_index,),
                "time":0.5e-6
            }
        }
        point.append(R_theta)
        point.append(idel_gate)
    route = [point]
    
    points_info = [{
    'points': np.array([float(n_gate)]),  # 原始扫描点
    }]
    rid = submit_demo_exp(route,points_info)

    result = get_result(f"{rid}",root_dir=result_dir)

    p = result["datasets"]["y_probability"][0][qubit_index]
    
    fidelity10 = np.sqrt(p*p_exact)+np.sqrt((1-p)*(1-p_exact))

    fidelity  = 1-(1-fidelity10)/n_gate
    print("fidelity:",fidelity)
    return fidelity

#%%
cali_rabi_fidelity(3)
#%%
points_info =[
    {
        "points":np.linspace(0.0,np.pi*2,31)
    }
]

route = []
for param in points_info[0]["points"]:
    shot = [
        {
            "name":'MS',
            "args":{
                "qubit_index": (0,1)
            }
        },
        {
            "name":"Rphi",
            "args":{
                "qubit_index":(0,),
                "phi":param 
            }
        }
    ]
    route.append(shot)
#%% md
## 线路
#%%
from qiskit import QuantumCircuit
from modules.circuit_to_shot import circuit2shot

#%%
qc = QuantumCircuit(7,7)
qc.h(2)
print(qc)
#%%
qc = QuantumCircuit(12,12)

qc.h(5)
# qc.cx(0,1)
# qc.cx(1,2)
qc.cx(5,6)
qc.cx(5,4)
# qc.cx(3,4)
# qc.cx(4,5)
# qc.cx(5,6)
# qc.cx(6,7)
# qc.cx(7,8)
# qc.cx(8,9)
# qc.cx(9,10)
# qc.cx(5,6)

# qc.cx(10,11)

print(qc)
#%%
qc = QuantumCircuit(7,7)


qc.h(2)
qc.cx(2,3)
qc.h(2)
qc.z(2)
qc.z(3)
qc.h(3)
qc.cx(2,3)
qc.h(2)
qc.h(4)
qc.cx(4,5)
qc.h(4)
qc.z(4)
qc.z(5)
qc.h(5)
qc.cx(4,5)
qc.h(4)


print(qc)
#%%
points_info =[
    {
        "points":np.linspace(0.0,np.pi*2,1)
    }
]

route = [circuit2shot(qc)]
# route = [circuit2shot(full_circuit)]
print(route)

#%%
rid = submit_demo_exp(route,points_info,ion_choice=(4,5,6), is_circuit=True)
#%% md
## RB

#%%
from cloud.circuit_generater import generate_rb1,get_rb2_from_rb1

print(generate_rb1(10))
#%%
points_info =[
    {
        "points":np.linspace(10,50,10)
    }
]
rb_qubits = (1,)
num_qubits = 12
route = []
for param in points_info[0]["points"]:
    full_circuit = QuantumCircuit(num_qubits,1)
    qc = generate_rb1(int(param))
    print(qc)
    #添加RB电路到指定比特
    full_circuit.x(rb_qubits)
    full_circuit.append(qc, rb_qubits,(0,))
    print(full_circuit)
    shot = circuit2shot(full_circuit)
    print(shot)
    route.append(shot)

#%%
rid = submit_demo_exp(route,points_info,ion_choice=rb_qubits, is_circuit=True)
#%%
print(rid)
#%%
from scipy.optimize import curve_fit
import numpy as np
import matplotlib.pyplot as plt
def Fun(x,a1,a2,a3):
    return a1*a2**x+0.5
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["y_probability"])[:,rb_qubits]

x = x[:len(y)]
y =y.flatten()
print(x,y)

popt, pcov = curve_fit(Fun, x, y)
a1, a2, a3 = popt
print(f"拟合参数: a1 = {a1:.4f}, a2 = {a2:.4f}, a3 = {a3:.4f}")

# 绘制拟合结果
x_fit = np.linspace(min(x), max(x), 100)
y_fit = Fun(x_fit, *popt)

plt.scatter(x, y, label='data')
plt.plot(x_fit, y_fit, 'r-', label='fit func')
fit_eqn = f"$y = {a1:.4f} \\cdot {a2:.4f}^x + 0.5$"
plt.text(0.5, 0.9, fit_eqn, transform=plt.gca().transAxes, 
         fontsize=12, bbox=dict(facecolor='white', alpha=0.8))
plt.legend()
plt.xlabel('x')
plt.ylabel('y')
plt.title('result')
plt.show()

#%% md
# 校准测试
#%%
from structure.route import MS_scan_phase
#%%
points_info =[
    {
        "points":np.linspace(0.0,np.pi*2,31)
    }
]

route = MS_scan_phase((2,4), start=0.0, stop=np.pi*2, n_points=31).route
#%%
rid = submit_demo_exp(route,points_info,ion_choice=(2,4))
#%%
from calibrations.MSParaCalc import update_ms_paramiters

class EXP:
    def __init__(self):
        self.parameter = parameter
exp = EXP()
#%%
update_ms_paramiters(exp,[3,0])
#%%
for i in range(7):
    for j in range(i+1,7):
        update_ms_paramiters(exp,[i,j])
#%%
