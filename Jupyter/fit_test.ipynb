#%%
import numpy as np
from scipy.optimize import curve_fit
from scipy.signal import savgol_filter  # 用于基线校正

def calculate_fit_quality(t, y, params):
    """
    计算拟合质量指标
    
    Parameters
    ----------
    t : array_like
        时间数据点
    y : array_like
        原始信号值
    params : tuple
        拟合参数 (amp, freq, offset, phase, tau)
        
    Returns
    -------
    dict
        包含以下指标:
        - r_squared : R平方值
        - rmse : 均方根误差
    """
    # 定义拟合函数（需与拟合时使用的函数一致）
    def sin_func(t, amp, freq, offset, phase, tau):
        return offset + amp * np.exp(-t/tau) * np.sin(2*np.pi*freq*t + phase)
    
    # 计算预测值
    y_pred = sin_func(t, *params)
    
    # R-squared计算
    ss_res = np.sum((y - y_pred)**2)
    ss_tot = np.sum((y - np.mean(y))**2)
    r_squared = 1 - (ss_res / (ss_tot + 1e-10))  # 避免除零
    
    # 均方根误差
    rmse = np.sqrt(np.mean((y - y_pred)**2))
    
    return {
        'r_squared': r_squared,
        'rmse': rmse,
        'residuals': y - y_pred
    }
    
def robust_rabi_fit(time_points, signal_values, baseline_correction=True):
    """
    鲁棒的拉比振荡拟合函数
    
    Parameters
    ----------
    time_points : array_like
        时间点数组（单位：秒）
    signal_values : array_like
        信号值数组（需为电压/电流等线性响应）
    baseline_correction : bool
        是否进行基线校正（默认True）

    Returns
    -------
    tuple (rabi_time, fit_time, fit_signal, params, r_squared)
        返回拟合结果和评估指标
    """
    # 数据预处理
    signal_values = np.asarray(signal_values)
    valid_mask = np.isfinite(signal_values)
    y_clean = signal_values[valid_mask]
    t_clean = time_points[valid_mask]

    if baseline_correction:
        baseline = savgol_filter(y_clean, window_length=51, polyorder=3) 
        y_clean = y_clean - baseline

    # 鲁棒的初始参数估计
    amp = np.percentile(y_clean, 95) - np.percentile(y_clean, 5)
    offset = np.median(y_clean)
    
    # 多方法频率估计
    try:
        freqs = np.fft.rfftfreq(len(y_clean), d=np.diff(t_clean).mean())
        fft_vals = np.abs(np.fft.rfft(y_clean * np.hanning(len(y_clean))))
        main_freq = freqs[np.argmax(fft_vals[1:]) + 1]  # 跳过DC分量
    except:
        main_freq = 1e6  # 默认1MHz

    # 带边界的拟合
    bounds = (
        [0.1*amp, 0.5*main_freq, -2*abs(offset), 0, 0],
        [2*amp, 2*main_freq, 2*abs(offset), 2*np.pi, np.inf]
    )
    
    params, _ = curve_fit(
        lambda t, a, f, o, p, tau: o + a * np.exp(-t/tau) * np.sin(2*np.pi*f*t + p),
        t_clean, y_clean,
        p0=[amp, main_freq, offset, np.pi/2, 1e3],
        bounds=bounds,
        maxfev=10000
    )

    # 结果验证
    fit_quality = calculate_fit_quality(t_clean, y_clean, params)
    rabi_time = abs(1/params[1])  # 秒
    
    return {
        'rabi_time_ns': rabi_time * 1e9,  # 纳秒
        'fitted_params': params,
        'fit_quality': fit_quality,
        'freq_estimate_hz': params[1]
    }
#%%
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
import matplotlib as mpl

# 设置全局字体（支持Windows/macOS/Linux）
try:
    mpl.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']  # 常见中文字体
    mpl.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
except:
    print("中文设置失败，请手动安装中文字体")
    
def generate_test_cases():
    """生成不同测试场景的拉比振荡数据"""
    t = np.linspace(0, 1e-6, 500)  # 1微秒时间范围，500个点
    
    # 真实参数
    true_params = {
        'amp': 0.5,    # 振幅 (V)
        'freq': 5e6,    # 频率 (Hz)
        'offset': 1.0,  # 直流偏移 (V)
        'phase': np.pi/4, # 相位 (rad)
        'tau': 2e-6     # 衰减时间 (s)
    }
    
    # 理想信号
    ideal_signal = true_params['offset'] + true_params['amp'] * np.exp(-t/true_params['tau']) * \
                   np.sin(2*np.pi*true_params['freq']*t + true_params['phase'])
    
    # 测试场景
    test_cases = {
        "理想情况": {
            "t": t,
            "y": ideal_signal,
            "desc": "无噪声无干扰的理想数据"
        },
        "高斯噪声": {
            "t": t,
            "y": ideal_signal + np.random.normal(0, 0.05, len(t)),
            "desc": "添加5%高斯噪声"
        },
        "基线漂移": {
            "t": t,
            "y": ideal_signal + 0.3*np.sin(2*np.pi*1e5*t),
            "desc": "叠加100kHz低频漂移"
        },
        "数据截断": {
            "t": t[:300],  # 只保留前60%数据
            "y": ideal_signal[:300],
            "desc": "不完整时间序列"
        },
        "脉冲干扰": {
            "t": t,
            "y": ideal_signal + 0.8*(np.random.rand(len(t)) < 0.02),  # 2%的脉冲噪声
            "desc": "存在随机脉冲干扰"
        }
    }
    
    return true_params, test_cases

def run_tests(fit_function):
    """运行所有测试案例并可视化结果"""
    true_params, test_cases = generate_test_cases()
    
    plt.figure(figsize=(15, 10))
    for i, (name, case) in enumerate(test_cases.items(), 1):
        # 执行拟合
        try:
            rabi_time, t_fit, y_fit, params = fit_function(case["t"], case["y"])
            fit_error = {
                'amp': abs(params[0] - true_params['amp'])/true_params['amp'],
                'freq': abs(params[1] - true_params['freq'])/true_params['freq'],
                'rabi_time': abs(rabi_time*1e6 - (1/true_params['freq']))/true_params['freq']
            }
            status = "✓"
        except Exception as e:
            status = f"✗ ({str(e)})"
            fit_error = None
        
        # 绘制结果
        plt.subplot(3, 2, i)
        plt.scatter(case["t"], case["y"], s=5, label="原始数据", alpha=0.6)
        if status == "✓":
            plt.plot(t_fit, y_fit, 'r-', label=f"拟合结果\nRabi={rabi_time*1e6:.2f}μs")
        plt.title(f"{name} {status}\n{case['desc']}")
        plt.xlabel("Time (s)")
        plt.ylabel("Signal (V)")
        plt.legend()
        
        # 打印误差分析
        print(f"案例 '{name}':")
        if fit_error:
            print(f"  频率误差: {fit_error['freq']*100:.2f}%")
            print(f"  拉比时间误差: {fit_error['rabi_time']*100:.2f}%")
        print("-"*50)
    
    plt.tight_layout()
    plt.show()

# 使用改进后的拟合函数进行测试
run_tests(robust_rabi_fit)  # 替换为你的实际拟合函数
#%%

def Rabi_fit(t, y):
    """
    改进版的拉比振荡拟合函数（保持原始输入输出接口）

    参数:
        t : array_like - 时间点数组
        y : array_like - 信号值数组

    返回:
        rabi_time : float - 拉比时间(μs)
        t_fit : ndarray - 拟合时间点
        y_fit : ndarray - 拟合曲线
        popt : ndarray - 拟合参数 [amp, freq, a0, phi0, tau]
    """
    # 1. 数据预处理（去NaN+基线校正）
    valid_mask = np.isfinite(y)
    t_clean, y_clean = t[valid_mask], y[valid_mask]

    try:
        baseline = savgol_filter(y_clean, window_length=min(51, len(y_clean)//2*2+1), polyorder=3)
        y_corrected = y_clean - baseline
    except:
        y_corrected = y_clean.copy()

    # 2. 初始参数估计（鲁棒方法）
    amp_est = np.percentile(y_corrected, 95) - np.percentile(y_corrected, 5)
    a0_est = np.median(y_corrected)

    # 频率估计（FFT+峰值检测）
    fft_vals = np.abs(np.fft.fft(y_corrected * np.hanning(len(y_corrected))))
    freqs = np.fft.fftfreq(len(y_corrected), d=np.diff(t_clean).mean())
    pos = np.argmax(fft_vals[(freqs > 0) & (freqs < 1e8)])  # 限制合理频率范围
    freq_est = freqs[freqs > 0][pos]

    # 3. 定义拟合函数（与原始保持一致）
    def Sin(t, amp, freq, a0, phi0, tau):
        return a0 + amp * np.exp(-t/tau) * np.sin(2*np.pi*freq*t + phi0)

    # 4. 带约束的拟合
    p0 = (
        amp_est/2,
        max(freq_est, 1e3),  # 防止频率为0
        a0_est,
        1.5*np.pi,
        (t_clean[-1]-t_clean[0])*2
    )

    bounds = (
        [0, 1e3, -np.inf, 0, 0],           # 下限
        [amp_est*2, np.inf, np.inf, 2*np.pi, np.inf]  # 上限
    )

    try:
        popt, pcov = curve_fit(
            Sin, t_clean, y_corrected,
            p0=p0, bounds=bounds, maxfev=10000
        )
    except:
        popt = p0  # 失败时回退到初始估计

    # 5. 生成与原始函数完全一致的输出
    t_fit = np.linspace(t[0], t[-1], 1001)
    y_fit = Sin(t_fit, *popt)
    rabi_time = abs(round(1/popt[1]/1e6, 9))  # 保留9位小数

    return rabi_time, t_fit, y_fit, popt

#%%
import numpy as np
import matplotlib.pyplot as plt

# 生成理想拉比振荡数据
t = np.linspace(0, 1e-6, 1000)  # 1μs 时间范围
freq_true = 5e6  # 5 MHz 真实频率
amp_true = 1.0
phi0_true = 0.5
a0_true = 0.1
tau_true = 2e-6  # 2μs 衰减时间

y_true = a0_true + amp_true * np.exp(-t/tau_true) * np.sin(2 * np.pi * freq_true * t + phi0_true)

# 调用 Rabi_fit 拟合
rabi_time, t_fit, y_fit, popt = Rabi_fit(t, y_true)
print(y_fit)
print(popt)
# 绘制结果
plt.figure(figsize=(10, 5))
plt.scatter(t, y_true, label="原始数据", s=5)
plt.plot(t_fit, y_fit, 'r-', label="拟合曲线")
plt.xlabel("时间 (s)")
plt.ylabel("信号强度")
plt.title(f"理想拉比振荡拟合 (真实频率: {freq_true/1e6:.2f} MHz, 拟合频率: {popt[1]/1e6:.2f} MHz)")
plt.legend()
plt.show()
#%%
import numpy as np
import matplotlib.pyplot as plt

# 生成理想拉比振荡数据
t = np.linspace(0, 1e-6, 1000)  # 1μs 时间范围
freq_true = 5e6  # 5 MHz 真实频率
amp_true = 1.0
phi0_true = 0.5
a0_true = 0.1
tau_true = 2e-6  # 2μs 衰减时间

y_true = a0_true + amp_true * np.exp(-t/tau_true) * np.sin(2 * np.pi * freq_true * t + phi0_true)

# 调用 Rabi_fit 拟合
rabi_time, t_fit, y_fit, popt = Rabi_fit(t, y_true)

# 绘制结果
plt.figure(figsize=(10, 5))
plt.scatter(t, y_true, label="原始数据", s=5)
plt.plot(t_fit, y_fit, 'r-', label="拟合曲线")
plt.xlabel("时间 (s)")
plt.ylabel("信号强度")
plt.title(f"理想拉比振荡拟合 (真实频率: {freq_true/1e6:.2f} MHz, 拟合频率: {popt[1]/1e6:.2f} MHz)")
plt.legend()
plt.show()
#%%
import numpy as np

def kronecker_product(matrix_list, submatrix_indices):
    """
    计算指定子矩阵的Kronecker积（直积）
    
    参数:
        matrix_list: 包含多个子矩阵的列表，每个子矩阵是2D numpy数组
        submatrix_indices: 要选择的子矩阵索引列表，如 [0, 2, 3]
    
    返回:
        所选子矩阵的Kronecker积结果
    """
    if not submatrix_indices:
        raise ValueError("子矩阵索引列表不能为空")
    
    # 检查索引是否有效
    for idx in submatrix_indices:
        if idx < 0 or idx >= len(matrix_list):
            raise ValueError(f"索引 {idx} 超出范围 (0-{len(matrix_list)-1})")
    
    # 将列表中的子矩阵转换为numpy数组
    submatrices = [np.array(matrix_list[idx]) for idx in submatrix_indices]
    
    # 计算Kronecker积
    result = submatrices[0]
    for mat in submatrices[1:]:
        result = np.kron(result, mat)
    
    return result

# 示例数据
SPAM_matrix_inverted = [
    [[1.01531151, -0.0406547], [-0.01531151, 1.0406547]],
    [[1.00999474, -0.04208311], [-0.00999474, 1.04208311]],
    [[1.00776398, -0.02743271], [-0.00776398, 1.02743271]],
    [[1.01351351, -0.02598753], [-0.01351351, 1.02598753]],
    [[1.01206083, -0.03670687], [-0.01206083, 1.03670687]],
    [[1.02228117, -0.03872679], [-0.02228117, 1.03872679]],
    [[1.01465201, -0.03192046], [-0.01465201, 1.03192046]]
]

# 使用示例：选择第0、2、3号子矩阵计算直积
selected_indices = [0, 1, 2, 3]
result = kronecker_product(SPAM_matrix_inverted, selected_indices)

print("选择的子矩阵索引:", selected_indices)
print("Kronecker积结果:")
print(result)
print("结果矩阵形状:", result.shape)
#%%
