#%% md
# artiq_manager_MSgate
#%% md
# 初始化

全部实验默认在两离子情况下进行
#%% md
## 连接
#%%

%matplotlib inline 
from jupyter_function import *

#%%
manager = ARTIQManager()
#%%
parameter = read_parameter()
#%% md
## 常用参数更新
#%%
# parameter.Light_554.SBC_num = 0
# parameter.Light_554.SBC_time = 35e-6
# # parameter.PMT.Select_channels = [[14,15,16]]#单离子
# parameter.PMT.Select_channels = [[11,12,13],[15,16,17]]#2离子
# parameter.Experiment.Repeat = 100
# parameter.Experiment.Cooling_Time = 1000e-6
# parameter.Experiment.Pumping_Time = 100e-6
# parameter.Experiment.Detecting_Time = 200e-6
# parameter.Experiment.EIT_Cooling_Time = 2e-3
# parameter.update_config_json()
#%% md
# SPAM优化
#%% md
## MW Rabi
#%%
#定义扫描参数
scan_start = 0
scan_stop = 100e-6
n_scan_point = 26

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RabiMW = {'arguments': {'X_scan_range': dict(ty ='RangeScan',
                                             start=scan_start, stop=scan_stop, npoints=n_scan_point),
                        'zeeman_choice':"0",
                        'task_num':'1'
                        },
                   'class_name': 'RabiMW',
                   'file': 'repository\\mw_exp\\mw_rabi.py',
                   'log_level': 30}

#提交实验 
rid = submit_exp(RabiMW,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

# 拟合拉比
print(y[:,0])
mw_rabi,t_fit,y_fit,_ = Rabi_fit(x,y[:,0])
# mw_rabi,t_fit,y_fit,_ = Rabi_fit(x,y)
print(f"rabi_time = {mw_rabi*1e6} us")
#画图
fig,ax = plt.subplots()
ax.plot(x, y,'-o')
ax.plot(t_fit,y_fit,"--")
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title(f'MW Rabi RID{rid}')
# save_plot(fig,base_filename=f"MW_Rabi_RID{rid}")

#%%
#更新参数
pi2_time = mw_rabi/4
# pi2_time = 4e-6

parameter.Signal_MW.mw_pi_2.zero = pi2_time
parameter.update_config_json()
#%% md
## MW Ramsey
#%%
#定义扫描参数
scan_start = 0
scan_stop = 10000e-6
n_scan_point = 12

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RamseyMW = {'arguments': {'X_scan_range': 
                            dict(ty ='RangeScan',start=scan_start, stop=scan_stop, npoints=n_scan_point),
                            'zeeman_choice':"+"},
                   'class_name': 'RamseyMW',
                   'file': 'repository\\mw_exp\\mw_ramsey.py',
                   'log_level': 30}
                   
#提交实验
rid= submit_exp(RamseyMW,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]
x = x[:len(y)]

#拟合Ramsey
detuning,_,t_fit,y_fit = Ramsey_fit(x,y[:,0])
print(f"detuning = {detuning} Hz")
#画图
fig,ax = plt.subplots()
ax.plot(x, y,'-o')
ax.plot(t_fit,y_fit,"--")
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('MW Ramsey')
save_plot(fig,base_filename=f"MW_Ramsey_RID{rid}")
#%%
# 更新微波频率
print(detuning) 
# parameter.Signal_MW.dds_for_mw_fre += detuning
parameter.Signal_MW.dds_for_mw_fre += detuning

parameter.update_config_json()
#%% md
## 塞曼能级
#%%
#定义扫描参数
scan_start = 0
scan_stop = 100e-6
n_scan_point = 21

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RabiMW = {'arguments': {'X_scan_range': dict(ty ='RangeScan',
                                             start=scan_start, stop=scan_stop, npoints=n_scan_point),
                        'zeeman_choice':"-",
                        'task_num':'1'
                        },
                   'class_name': 'RabiMW',
                   'file': 'repository\\mw_exp\\mw_rabi.py',
                   'log_level': 30}
#提交实验
rid = submit_exp(RabiMW,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

# 拟合拉比
mw_rabi,t_fit,y_fit,_ = Rabi_fit(x,y[:,0])
print(f"rabi_time = {mw_rabi*1e6} us")
#画图
fig,ax = plt.subplots()
ax.plot(x, y,'-o')
ax.plot(t_fit,y_fit,"--")
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('MW Rabi')
# save_plot(fig,base_filename=f"MW_Rabi_RID{rid}")

#%%
#更新参数
pi2_time = mw_rabi/4
# pi2_time = 7e-6

parameter.Signal_MW.mw_pi_2.negative = pi2_time
parameter.update_config_json()
#%%


#定义扫描参数
scan_start = 0
scan_stop = 20000e-6
n_scan_point = 11

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RamseyMW = {'arguments': {'X_scan_range': 
                            dict(ty ='RangeScan',start=scan_start, stop=scan_stop, npoints=n_scan_point),
                            'zeeman_choice':"-"},
                   'class_name': 'RamseyMW',
                   'file': 'repository\\mw_exp\\mw_ramsey.py',
                   'log_level': 30}
#提交实验
rid = submit_exp(RamseyMW,manager)
#%%
rid0 = 112963
rid = 112962
#%%
#测试三次样条插值拟合
# import numpy as np
import scipy.interpolate as interp

# 1. 输入已知的离散数据点（x, y）
result0 = get_result(f"{rid0}",root_dir=result_dir)
x0 = np.array(result0["datasets"]["x_points"])
y0 = np.array(result0["datasets"]["probability"])[:,0]
# x = (x-104.43)*2

result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:,0]

y = np.array([float(i) for i in y])
y0 = np.array([float(i) for i in y0])
print(y)
print(y0)
# print(y-y0)
y = y-y0
# x = x0
# y = y0
# 2. 创建三次样条插值对象
spline = interp.CubicSpline(x, y, bc_type='natural')  # bc_type='natural' 保证二阶导数在两端为0

# 3. 用插值对象预测任意一个x的值
x_new = 145  # 假设我们想要插值x=2.5对应的y值
y_new = spline(x_new)  # 得到插值结果
print(f"插值点 x = {x_new} 对应的 y = {y_new}")

# 4. 绘制原始数据点与插值曲线
# plt.style.use('science') 
# params = {
#   'figure.dpi' : 200, ## jupyter中显示图片的分辨率
# }
# mpl.rcParams.update(params)
x_fine = np.linspace(min(x), max(x), len(x)*100)  # 创建一个细化的x数组用于绘图
y_fine = spline(x_fine)  # 计算细化x数组对应的y值

# plt.figure(figsize=(8, 6))
plt.plot(x, y, 'o',markersize='2')  # 原始数据点
plt.plot(x_fine, y_fine, '-',)  # 三次样条插值曲线

plt.xlabel('detuning of pi/MHz')
plt.ylabel('scatterring rate (0.2ms)')
# plt.legend()
# # plt.title('Cubic Spline Interpolation')
# plt.show()



#%%
cooling_rate = []
cooling_limit = []
delta = []
Omega_phonon = 3.18666/2
# Omega_phonon = 0.625/2
Lamb_Dicke = 1 #归一化
Gamma = 1 #归一化

color_1 = 'darkblue'
color_2 = 'darkred'

x_min = 142
x_max = 158
step = 0.02
for temp_delta_pi in np.linspace(x_min,x_max,int((x_max-x_min)/step)):
    delta.append(temp_delta_pi)
    A_minus = Lamb_Dicke**2*Gamma*(spline(temp_delta_pi)+spline(temp_delta_pi+Omega_phonon))
    A_plus = Lamb_Dicke**2*Gamma*(spline(temp_delta_pi)+spline(temp_delta_pi-Omega_phonon))
    cooling_rate.append(A_minus-A_plus)
    cooling_limit.append(A_plus/(A_minus-A_plus))

delta = np.array(delta)
cooling_rate = np.array(cooling_rate)
cooling_limit = np.array(cooling_limit)
# 图形绘制

# 创建图形和左侧纵坐标轴
fig, ax1 = plt.subplots(figsize=(10, 6))

# 绘制第一条曲线（左侧）
ax1.set_xlabel('$Δ_π$(MHz)')
ax1.set_ylabel('phonon number', color=color_1)
ax1.plot(delta,
         cooling_limit,
         color=color_1,
         label='cooling limit')
ax1.tick_params(axis='y', labelcolor=color_1)
ax1.set_ylim(0,10)
# 创建右侧纵坐标轴
ax2 = ax1.twinx()

# 绘制第二条曲线（右侧）
ax2.set_ylabel('cooling rate /s', color=color_2)
ax2.plot(delta,
         cooling_rate,
         color=color_2,
         label='cooling rate')
ax2.tick_params(axis='y', labelcolor=color_2)
# ax2.set_ylim(0,1000)

# 添加图例（合并两个图例）
lines1, labels1 = ax1.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()
ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

# 添加标题
plt.title('EIT Cooling')


from scipy.signal import argrelextrema
def find_extrema(arr):
    # 找到极大值（比较相邻元素）
    maxima_indices = argrelextrema(np.array(arr), np.greater, order=50)[0]
    maxima = [(i, arr[i]) for i in maxima_indices]

    # 找到极小值（比较相邻元素）
    minima_indices = argrelextrema(np.array(arr), np.less, order=50)[0]
    minima = [(i, arr[i]) for i in minima_indices]

    return maxima, minima

maxima, minima = find_extrema(cooling_rate)
print("population_pure")
# print("局部极小值：",
#       [f"值 {val}（失谐 {delta[idx]/(2*np.pi)/1e6}）" for idx, val in minima])
# for idx, val in maxima:
print("局部极大值：",
      [f"值 {val}（失谐 {delta[idx]}）" for idx, val in maxima])

# 显示图形
plt.savefig("EIT cooling")
plt.show()

#%%
# 插值绘图函数

import scipy.interpolate as interp

def plot_spline(x,y):
    spline = interp.CubicSpline(x, y, bc_type='natural')  # bc_type='natural' 保证二阶导数在两端为0
    x_fine = np.linspace(min(x), max(x), len(x)*5)  # 创建一个细化的x数组用于绘图
    y_fine = spline(x_fine)  # 计算细化x数组对应的y值
    
    #画图
    fig,ax = plt.subplots()
    ax.plot(x, y,'-o',markersize='2')
    ax.plot(x_fine, y_fine,'--',markersize='0.1')

    ax.set_xlabel('scan parameter')
    ax.set_ylabel('result')
    ax.set_title(f' Raw data show ')
    return spline

def plot_result0(rid):
    # 实验结果提取
    result = get_result(f"{rid}",root_dir=result_dir)
    x = np.array(result["datasets"]["x_points"])
    y = np.array(result["datasets"]["probability"])[:,0]
    # 变换为可计算数据格式
    x = np.array([float(i) for i in x])
    y = np.array([float(i) for i in y])

    #平滑拟合绘图
    spline = plot_spline(x,y)

    return [x,y],spline
#%% md
## EIT 封装
#%%
# test EIT probe scan封装代码

from scipy.signal import argrelextrema
import numpy as np
color_1 = 'darkblue'
color_2 = 'darkred'
us = 1e-6
ms = 1e-3
MHz = 1e6

# 提交扫EIT probe频率扫描的任务
def EIT_probe_scan(scan_freq =np.arange(140*MHz,160*MHz,0.2*MHz),
                    RID_list = None
                    ):
    
    if RID_list == None:
        RID_list = [None,None]

    RID_background = RID_list[0]
    RID_scattering = RID_list[1]
    
    scan = dict(ty="RangeScan",start = float(scan_freq[0]), stop= float(scan_freq[-1]),npoints=len(scan_freq))

    if RID_background is None:    
        EIT_probe_scan_background = {'arguments': {'X_scan_range':scan
                        },
                'class_name': 'EIT_probe_scan_background',
                'file': 'repository\\calibration\\EIT_probe_scan_background.py',
                'log_level': 30}
        RID_background= submit_exp(EIT_probe_scan_background,manager)
        print(RID_background,"RID_background")
        try:
            raw_data_background, spline_background = plot_result0(RID_background)
        except Exception as e:
            print(e)
            return [RID_background,RID_scattering]
    else:
        raw_data_background, spline_background = plot_result0(RID_background)

    if RID_scattering is None:
        EIT_probe_scan = {'arguments': {'X_scan_range':scan
                        },
                'class_name': 'EIT_probe_scan',
                'file': 'repository\\calibration\\EIT_probe_scan.py',
                'log_level': 30}
        RID_scattering= submit_exp(EIT_probe_scan,manager)

        try:
            raw_data_scattering, spline_scattering = plot_result0(RID_scattering)
        except Exception as e:
            print(e)
            return [RID_background,RID_scattering]
    else:
        raw_data_scattering, spline_background = plot_result0(RID_scattering)

    x = raw_data_scattering[0]
    y = raw_data_scattering[1]-raw_data_background[1]
    spline = interp.CubicSpline(x, y, bc_type='natural')  # bc_type='natural' 保证二阶导数在两端为0
    plot_spline (x,y)
    return [RID_background,RID_scattering],spline

# 提交EIT参数获取的任务
def EIT_parameter_draw (scan_freq =np.arange(140*MHz,160*MHz,0.2*MHz),
                        RID_list = None):
    
    # 参数初始化
    delta = []
    cooling_rate = []
    cooling_limit = []

    # 开启实验扫描
    RID, spline = EIT_probe_scan(scan_freq = scan_freq,
                                RID_list = RID_list
                                )

    # 去掉左右2*2MHz，以2*0.02MHz为step，进行参数扫描
    x_min = scan_freq[0]/1e6+4/2
    x_max = scan_freq[-1]/1e6-4/2
    step = 0.02
    Lamb_Dicke = 1 #归一化
    Gamma = 1 #归一化

    parameter = read_parameter()

    try:
        Omega_phonon = parameter.Light_554.Motion_freq[-2]/1e6/2 # stretch模式声子频率
    except:
        Omega_phonon = parameter.Light_554.Motion_freq[-1]/1e6/2 # com模式声子频率
    

    # print(x_max,"x_max")
    # print(x_min,"x_min")

    for temp_delta_pi in np.linspace(x_min,x_max,int((x_max-x_min)/step)):
        delta.append(temp_delta_pi)
        A_minus = Lamb_Dicke**2*Gamma*(spline(temp_delta_pi)+spline(temp_delta_pi+Omega_phonon))
        A_plus = Lamb_Dicke**2*Gamma*(spline(temp_delta_pi)+spline(temp_delta_pi-Omega_phonon))
        cooling_rate.append(A_minus-A_plus)
        cooling_limit.append(A_plus/(A_minus-A_plus))
    delta = np.array(delta)
    cooling_rate = np.array(cooling_rate)
    cooling_limit = np.array(cooling_limit)

    # 图形绘制

    # 创建图形和左侧纵坐标轴
    fig, ax1 = plt.subplots(figsize=(10, 6))

    # 绘制第一条曲线（左侧）
    ax1.set_xlabel('$Δ_π$(MHz)')
    ax1.set_ylabel('phonon number', color=color_1)
    ax1.plot(delta,
            cooling_limit,
            color=color_1,
            label='cooling limit')
    ax1.tick_params(axis='y', labelcolor=color_1)
    ax1.set_ylim(0,10)
    # 创建右侧纵坐标轴
    ax2 = ax1.twinx()

    # 绘制第二条曲线（右侧）
    ax2.set_ylabel('cooling rate /s', color=color_2)
    ax2.plot(delta,
            cooling_rate,
            color=color_2,
            label='cooling rate')
    ax2.tick_params(axis='y', labelcolor=color_2)
    # ax2.set_ylim(0,1000)

    # 添加图例（合并两个图例）
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

    # 添加标题
    plt.title('EIT Cooling')
    plt.savefig("EIT cooling")
    plt.show()

    return delta,cooling_rate,cooling_limit

def find_extrema(arr,order=3):
    # 找到极大值（比较相邻元素）
    maxima_indices = argrelextrema(np.array(arr), np.greater, order=order)[0]
    maxima = [(i, arr[i]) for i in maxima_indices]

    # 找到极小值（比较相邻元素）
    minima_indices = argrelextrema(np.array(arr), np.less, order=order)[0]
    minima = [(i, arr[i]) for i in minima_indices]

    return maxima, minima
#%%
delta,cooling_rate,cooling_limit = EIT_parameter_draw(scan_freq =np.arange(145*MHz,155*MHz,0.05*MHz),
                        RID_list = [117169,117173])

maxima, minima = find_extrema(cooling_rate,order=20)
print("population_pure")
# print("局部极小值：",
#       [f"值 {val}（失谐 {delta[idx]/(2*np.pi)/1e6}）" for idx, val in minima])
# for idx, val in maxima:
print("局部极大值：",
      [f"值 {val}（失谐 {delta[idx]}）" for idx, val in maxima])

#%%
from fit_lib.test import  print1
print1()
#%%

# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

#拟合Ramsey
detuning,_,t_fit,y_fit = Ramsey_fit(x[:300],y[:300,3])
print(f"detuning = {detuning} Hz")
#画图
fig,ax = plt.subplots()
ax.plot(x[:300], y[:300],'-o')
ax.plot(t_fit,y_fit,"--")
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('MW Ramsey')
save_plot(fig,base_filename=f"MW_Ramsey_RID{rid}")
#%%
# 更新微波频率
print(detuning) 
# parameter.Signal_MW.dds_for_mw_fre += detuning
# parameter.Signal_MW.zeeman_p -= detuning
parameter.Signal_MW.zeeman_n -= detuning

parameter.update_config_json()
#%%
parameter.Light_369.dds_for_EIT_sigma_fre = (parameter.Light_369.dds_for_EIT_pi_fre 
                                             + parameter.Signal_MW.zeeman_p/2)

parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0
scan_stop = 500e-6
n_scan_point = 21

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RabiMW = {'arguments': {'X_scan_range': dict(ty ='RangeScan',
                                             start=scan_start, stop=scan_stop, npoints=n_scan_point),
                        'zeeman_choice':"+",
                        'task_num':'1'
                        },
                   'class_name': 'RabiMW',
                   'file': 'repository\\mw_exp\\mw_rabi.py',
                   'log_level': 30}
#提交实验
rid = submit_exp(RabiMW,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

# 拟合拉比
mw_rabi,t_fit,y_fit,_ = Rabi_fit(x,y[:,0])
print(f"rabi_time = {mw_rabi*1e6} us")
#画图
fig,ax = plt.subplots()
ax.plot(x, y,'-o')
ax.plot(t_fit,y_fit,"--")
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('MW Rabi')
# save_plot(fig,base_filename=f"MW_Rabi_RID{rid}")

#%%
#更新参数
pi2_time = mw_rabi/4
# pi2_time = 7e-6

parameter.Signal_MW.mw_pi_2.positive = pi2_time
parameter.update_config_json()
#%%

#%%


#定义扫描参数
scan_start = 0
scan_stop = 2000e-6
n_scan_point = 11

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RamseyMW = {'arguments': {'X_scan_range': 
                            dict(ty ='RangeScan',start=scan_start, stop=scan_stop, npoints=n_scan_point),
                            'zeeman_choice':"+"},
                   'class_name': 'RamseyMW',
                   'file': 'repository\\mw_exp\\mw_ramsey.py',
                   'log_level': 30}
#提交实验
rid = submit_exp(RamseyMW,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

#拟合Ramsey
detuning,_,t_fit,y_fit = Ramsey_fit(x[:300],y[:300,3])
print(f"detuning = {detuning} Hz")
#画图
fig,ax = plt.subplots()
ax.plot(x[:300], y[:300],'-o')
ax.plot(t_fit,y_fit,"--")
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('MW Ramsey')
save_plot(fig,base_filename=f"MW_Ramsey_RID{rid}")
#%%
# 更新微波频率
print(detuning) 
# parameter.Signal_MW.dds_for_mw_fre += detuning
parameter.Signal_MW.zeeman_p += detuning
# parameter.Signal_MW.zeeman_n -= detuning

parameter.update_config_json()
#%% md
## Histogram
#%%
# 设置Detection参数
parameter.Light_369.dds_for_D_amp = 0.75  #不得超过1
parameter.Experiment.Repeat = 400
pi2_time = 4.5e-6
parameter.Signal_MW.mw_pi_2.zero = pi2_time


if parameter.Light_369.dds_for_D_amp > 1.0:
    print("不得超过0.3，设置为0.3")
    parameter.Light_369.dds_for_D_amp = 1.0

parameter.update_config_json()
#%%
#定义实验次数
task_num = 1
#更新外部修改的config参数
parameter = read_parameter()

#定义expid
Histogram = {'arguments': {'task_num':int(task_num)},
                   'class_name': 'HistogramMW',
                   'file': 'repository\\mw_exp\\mw_histogram.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(Histogram,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = range(25)
y = np.array(result["datasets"]["histogram"])[:]

# 读取0，1态错误率
error = np.array(result["datasets"]["error"])

error_0 = error[0]
error_1 = error[1]
print(f"|0⟩态错误率： {error_0}，|1⟩态错误率： {error_1}")

#画图
fig,ax = plt.subplots()
ax.plot(x, y,'-o')
ax.set_xlabel('Photon Counts')
ax.set_ylabel('Event Number')
ax.set_title(f'Histogram RID{rid}')
# save_plot(fig,base_filename=f"Histogram_RID{rid}")
#%% md
## Pumping test
#%%
# 设置Pumping参数
parameter.Light_369.dds_for_P_amp = 1.0  #不得超过1.0

if parameter.Light_369.dds_for_P_amp > 1.0:
    print("不得超过1.0,设置为1.0")
    parameter.Light_369.dds_for_P_amp = 1.0

parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0
scan_stop = 20e-6
n_scan_point = 11
#更新外部修改的config参数
parameter = read_parameter()

#定义expid
PumpingTest = {'arguments': {'X_scan_range': dict(ty ='RangeScan',
                                                  start=scan_start, stop=scan_stop, npoints=n_scan_point)},
                   'class_name': 'PumpingTest',
                   'file': 'repository\\369_exp\\pumping_test.py',
                   'log_level': 30}

#提交实验
rid= submit_exp(PumpingTest,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]


#画图
fig,ax = plt.subplots()
ax.plot(x, y,'-o')
ax.set_yscale('log')
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title(f'Pumping Test RID{rid}')
# save_plot(fig,base_filename=f"Pumping_test_RID{rid}")
#%%
# 更新Pumping 时间
parameter.Experiment.Pumping_Time = 35e-6

parameter.update_config_json()
#%% md
# 拉曼光校准
#%% md
## AOD scan
#%%
# 减弱拉曼光，
parameter = read_parameter()
parameter.Light_554.AOM_AWG_amp = 0.3
parameter.Experiment.Repeat = 100
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = -1.0e6
scan_stop =11e6
n_scan_point = 161

# 设置操作时间
operation_time  =7e-6

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
AODScan = {'arguments': {'X_scan_range': dict(ty ='RangeScan',
                                              start=scan_start, stop=scan_stop, npoints= n_scan_point),
                        'side_choice':'All',
                        'operation_time':operation_time
                                              },
                   'class_name': 'RamanAODScan',
                   'file': 'repository\\QCMOS_package\\Raman_AOD_scan.py',
                   'log_level': 30}

#提交实验
rid= submit_exp(AODScan,manager)
#%%
rid= 2782
#%%
np.array([100.33352897447591, 99.99246897186684, 99.69985564154109, 99.42127433392325, 99.1466151707685, 98.85239863948745, 98.51398355058383])-np.array([100.33380231824904, 99.99459614048509, 99.7020263825547, 99.42222774029183, 99.14820850718516, 98.85317634734018, 98.51527513649698])
#%%
# 实验结果提取
parameter = read_parameter()
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["y_probability"])[:]
x = x[0:len(y)]
#拟合峰值
try:
    peaks = AOD_scan_fit(x,y)
except Exception as e:
    print(e)
    peaks = [x[0],x[-1]]
#画图

fig,ax = plt.subplots()
for peak in peaks:
    if peak is not None:
        ax.axvline(x = peak,color = "r",linestyle ="--")
        pass
    # print(peak)
print(f"peaks at {peaks} MHz")
ax.plot(x, y,'-o')
ax.set_xlabel('AOD frequency (MHz)')
ax.set_ylabel('Probability')
ax.set_title('AOD Scan')
# save_plot(fig,base_filename=f"AOD_Scan_RID{rid}")
#%%
# 更新参数
# peaks = [98.71,99.24]
peaks_Hz = np.array(peaks)*1e6
parameter.Light_554.AOD_middle_freq = peaks_Hz[0]
parameter.Light_554.AOD_address_freqs = peaks_Hz-peaks_Hz[0]
parameter.update_config_json()
#%%
from scipy.optimize import curve_fit,minimize
from scipy.signal import find_peaks
def Gaussian(x, mu, sigma, a, a0):
    return a * (1 / (sigma * np.sqrt(2 * np.pi))) * np.exp(-((x - mu)**2) / (2 * sigma**2)) + a0
def AOD_scan_fit_Gaussian(x,y,fit_range = 10):
    y = y.T
    fit_params = []
    peak_list = []
    x_fits = []
    y_fits = []
    for y_i in y:    
        peaks, properties = find_peaks(y_i,height = 0.2)
        highest_peak_index = peaks[np.argmax(properties['peak_heights'])]
        highest_peak_height = y_i[highest_peak_index]

        peak_region = np.arange(highest_peak_index-fit_range,highest_peak_index+fit_range+1)
        peak_region = peak_region[(peak_region >=0 )&(peak_region < len(y_i))]

        y_peak = y_i[peak_region]
        x_peak = x[peak_region]

        p0 = [x[highest_peak_index],0.1,highest_peak_height,0]
        try:
            popt,_ = curve_fit(Gaussian,x_peak,y_peak, p0 = p0,
                               bounds=([0,0,0,0],[np.inf,np.inf,np.inf,np.inf]))
            fit_params.append(popt)
            peak_list.append(popt[0])
            
            x_fit = np.linspace(x_peak[0],x_peak[-1],101)
            y_fit = Gaussian(x_fit,*popt)
            x_fits.append(x_fit)
            y_fits.append(y_fit)

        except:
            print("拟合失败")
            fit_params.append(None)
            peak_list.append(x[highest_peak_index])
    print(fit_params)

    
    return peak_list,x_fits,y_fits
#%%
[100.20373978788389, 99.86326910722454, 99.5700677567628, 99.29294793185952, 99.0160393881777, 98.72021628345878, 98.38051334477449]
[100.20231277169637, 99.86230090183057, 99.56876747494363, 99.29157572304082, 99.0147716291865, 98.71884445388453, 98.37939805338331]
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]
x = x[0:len(y)]
y=2*np.arcsin(np.sqrt(y))/(1e-6-0.36e-6)/(2*np.pi)
#拟合峰值
try:
    peaks,x_fits,y_fits = AOD_scan_fit_Gaussian(x,y,fit_range=10)
except Exception as e:
    print(e)
    peaks = [x[0],x[-1]]
#画图
fig,ax = plt.subplots()
for i,x_fit in enumerate(x_fits):
    plt.plot(x_fit,y_fits[i],"--")
# for peak in peaks:
#     if peak is not None:
#         ax.axvline(x = peak,color = "r",linestyle ="--")
#         pass
    # print(peak)
print(f"peaks at {peaks} MHz")
ax.plot(x, y,'o',markersize=0.5)
ax.set_xlabel('AOD frequency (MHz)')
ax.set_ylabel('Probability')
ax.set_title('AOD Scan')
# save_plot(fig,base_filename=f"AOD_Scan_RID{rid}")
#%% md
## 选取离子
#%%
parameter = read_parameter()
qubit_index = (2,3) # 做门的比特
ion_choice = 5 # BSB 作用离子
#%% md
## Raman Rabi
#%%

parameter = read_parameter()
parameter.Light_554.AOM_AWG_amp = 0.12
parameter.Experiment.Repeat = 100
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0
scan_stop = 50e-6
n_scan_point = 51

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RabiRaman = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'rabi_choice':'Carrier',
                          'side_choice':'All',
                          'qubit_index':f'({ion_choice},)'
                          },
                   'class_name': 'RamanRabi',
                   'file': 'repository\\Raman_exp\\Raman_Rabi.py',
                   'log_level': 30}

#提交实验
rid= submit_exp(RabiRaman,manager)
#%%
rid = 113619
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]
x = x[:len(y)]
# 拟合拉比
rabi_554,t_fit,y_fit,popt = Rabi_fit(x,y[:,ion_choice])
print(popt)

aom_delay=-(np.pi/2+popt[3])/np.pi/2/popt[1]/1e6
aom_delay = aom_delay% rabi_554
print(aom_delay)
#画图
fig,ax = plt.subplots()
ax.plot(t_fit,y_fit,"--")
ax.plot(x, y,'-o')
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('Raman Rabi')
print(f"rabi time: {rabi_554}")
# save_plot(fig,base_filename=f"Raman_Rabi_RID{rid}")


#%%
(29.5-0.359)/(5+np.arcsin(np.sqrt(0.44))/np.pi)/4+0.359
#%%
#更新参数
pi2_time = rabi_554/4 + aom_delay
# pi2_time = 2e-6
print(pi2_time)
parameter.Light_554.pi_2.Carrier[int(ion_choice)] = pi2_time
parameter.update_config_json()
#%% md
## Raman Ramsey
#%%
#定义扫描参数
scan_start = 00e-6
scan_stop = 5000e-6
n_scan_point = 11

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RamanRamsey = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'Ramsey_choice':'Carrier',
                          'qubit_index':f'({ion_choice},)'
                          },
                   'class_name': 'RamanRamsey',
                   'file': 'repository\\Raman_exp\\Raman_Ramsey.py',
                   'log_level': 30}

#提交实验
rid= submit_exp(RamanRamsey,manager)
#%%
rid=115973
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]
x = x[:len(y)]
#拟合Ramsey
detuning,coherence_time,t_fit,y_fit = Ramsey_fit(x,y[:,ion_choice])

#画图
fig,ax = plt.subplots()
ax.plot(t_fit,y_fit,"--")
ax.plot(x, y,'-o')
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('554 Ramsey')
# save_plot(fig,base_filename=f"554_Ramsey_RID{rid}")

print(f"失谐：{detuning} Hz,调 {detuning/2}Hz")
#%% md
## 校准单比特
#%% md
### 粗略校准单比特pi/2
#%%

parameter = read_parameter()
parameter.Light_554.AOM_AWG_amp = 0.12
parameter.Experiment.Repeat = 400
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 45e-6
scan_stop = 50e-6
n_scan_point = 6

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RabiRaman = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'rabi_choice':'Carrier',
                          'side_choice':'All',
                          'qubit_index':f'({ion_choice},)'
                          },
                   'class_name': 'RamanRabi',
                   'file': 'repository\\Raman_exp\\Raman_Rabi.py',
                   'log_level': 30}

#提交实验
rid= submit_exp(RabiRaman,manager)
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:,ion_choice]
index05 = np.argmin(abs(y-0.5))
print(index05)
x05 = x[index05]
y05 = y[index05]
pi2_time=((x05-0.359)/(5+np.arcsin(np.sqrt(y05))/np.pi)/4+0.359)*1e-6
print(pi2_time)
#%%

#%%
pi2_time=((48.4-0.359)/(5+np.arcsin(np.sqrt(0.588))/np.pi)/4+0.359)*1e-6

#%%
parameter = read_parameter()
#更新参数
print(pi2_time)
parameter.Light_554.pi_2.Carrier[int(ion_choice)] = pi2_time
parameter.update_config_json()
#%% md
### 精确校准单比特 pi/2
#%%
parameter = read_parameter()
parameter.Experiment.Repeat = 100
parameter.Light_554.pi_2.Carrier[int(ion_choice)] =(2.85e-06-0.358*1e-6)/1+0.358*1e-6
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0
scan_stop = 20
n_scan_point = 21

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
cali_pi2 = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan', 
                            start=scan_start, 
                            stop=scan_stop, 
                            npoints= n_scan_point
                            ),
                          'qubit_index':str((ion_choice,)),
                          'ion_choice':str((ion_choice,))
                          },
                   'class_name': 'Pi2Cali',
                   'file': 'repository\\calibration\\pi2cali.py',
                   'log_level': 30}
rid= submit_exp(cali_pi2,manager)
#%% md
### 校准单比特shift
#%%
#定义扫描参数
scan_start = 1
scan_stop = 1001
n_scan_point = 11

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
cali_freq = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan', 
                            start=scan_start, 
                            stop=scan_stop, 
                            npoints= n_scan_point
                            ),
                          'qubit_index':str((ion_choice,)),
                          'ion_choice':str((ion_choice,))
                          },
                   'class_name': 'FreqCali',
                   'file': 'repository\\calibration\\carrier_freq_cali.py',
                   'log_level': 30}
rid= submit_exp(cali_freq,manager)
#%%
parameter = read_parameter()
parameter.Light_554.q1_AC_Stark_shift = 200
parameter.update_config_json()
#%% md
## 两离子 Rabi
#%%
#定义扫描参数
scan_start = 0e-6
scan_stop = 50e-6
n_scan_point = 51

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RamanRabi = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'rabi_choice':'Carrier',
                          'side_choice':'All',
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'RamanRabi',
                   'file': 'repository\\Raman_exp\\Raman_Rabi.py',
                   'log_level': 30}

#提交实验
rid= submit_exp(RamanRabi,manager)
#%%
#104318 2.5T 20250319 12:40-15:00 [100.06518911343646, 99.69433259984402, 99.3525561092693, 99.01905362521602, 98.63910972740716]

#104324 2.5T 20250319 15:13-16:02 [100.09094256195107, 99.70898308086338, 99.37529443080616, 99.02912424655412, 98.65617156878321]

#104528 2.5T 20250319 16:06-17.03 [100.0897949444078, 99.71376583448996, 99.38009340785358, 99.03840086734732, 98.6599659011223]
#%%
rid_list = np.array(range(1))+115950
y_all = []
for rid in rid_list:
    result = get_result(f"{rid}",root_dir=result_dir)
    y = np.array(result["datasets"]["probability"])[2:]
    if rid == 115950:
        y_all = y
    else:
        y_all = np.vstack((y_all,y))
x = np.arange(len(y_all))
#%%
pip install plotly
#%%
import plotly.graph_objects as go
fig = go.Figure()
fig.add_trace(go.Scatter(x=x,y=y_all[:,2],mode = "lines"))
#%%
fig,ax = plt.subplots()
print(len(y_all))
x = np.arange(len(y_all))*540/len(y_all)
ax.plot(x,y_all)
ax.set_xlabel('time(min)')
ax.set_ylabel('Probability')
ax.set_ylim([0,1])
ax.set_title('554 Rabi Fluctuaction ')
#%%
rid = 103931
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

# 拟合拉比
rabi_554,t_fit,y_fit,_ = Rabi_fit(x,y[:,qubit_index[0]])
#画图
fig,ax = plt.subplots()
ax.plot(x, y,'-o')
ax.plot(t_fit,y_fit,"--")
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('554 Rabi 2ions')
print(f"rabi time: {rabi_554}")
# save_plot(fig,base_filename=f"554_Rabi_2ions_RID{rid}")
#%%
pi2_time = rabi_554/4
print(pi2_time)
#%%
#更新参数
pi2_time = rabi_554/4
# pi2_time = 2e-6

parameter.Light_554.pi_2for2ions.Carrier = pi2_time
parameter.update_config_json()
#%% md
## 两离子AOD ratio
#%%
parameter = read_parameter()
parameter.Experiment.Repeat = 400
parameter.Light_554.AOM_AWG_amp = 0.3
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0.97
scan_stop = 1.03
n_scan_point = 25

#定义操作时间和离子编号
operation_time = 20e-6
#更新外部修改的config参数
parameter = read_parameter()

#定义expid
AODratio = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'operation_time': operation_time,
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'RamanAODRatioScan',
                   'file': 'repository\\Raman_exp\\Raman_AOD_ratio_scan.py',
                   'log_level': 30}

#提交实验
rid= submit_exp(AODratio,manager)
#%%
# 实验结果读取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

# 寻找交叉点
crosses = Cross_fit(x,y,qubit_index)

fig,ax = plt.subplots()
for cross in crosses:
    ax.axvline(x = cross,color = "r",linestyle ="--")
    print(f"交叉点: ratio = {cross}")
ax.plot(x, y,'-o')
ax.set_xlabel('AOD ratio')
ax.set_ylabel('Probability')
ax.set_title('AOD Ratio scan')

# save_plot(fig,base_filename=f"AOD_Ratio_scan_RID{rid}")
#%%
#更新参数
aod_ratio = crosses[0]
# aod_ratio = 1.034
parameter.Light_554.AOD_AWG_ratio[qubit_index[0]][qubit_index[1]] = aod_ratio
parameter.Light_554.AOD_AWG_ratio[qubit_index[1]][qubit_index[0]] = 1/aod_ratio

parameter.Experiment.Repeat = 100
parameter.update_config_json()
#%% md
# 声子频率校准
#%% md
## Raman Spectrum
#%%
# 减弱拉曼光，
parameter = read_parameter()
parameter.Light_554.AOM_AWG_amp = 0.15

#%%
#定义扫描参数
scan_start = -1.86e6
scan_stop = -2.18e6
n_scan_point = 191
ion_choice = 5
#更新外部修改的config参数
parameter = read_parameter()

# 设置操作时间
t_pi2=parameter.Light_554.pi_2.Carrier[int(ion_choice)]
n_phonon_scan = 101
operation_time  = 200e-6
print(f"Operation time: {operation_time}")

#定义expid
Spectrum = {'arguments':{'X_scan_range':
                         dict(ty = 'RangeScan',start=scan_start,stop = scan_stop,npoints = n_scan_point),
                         'operation_time':operation_time,
                         'qubit_index':f'({ion_choice},)'
                         },
                         'class_name':'RamanSpectrum',
                         'file':'repository\\QCMOS_package\\Raman_Spectrum.py',
                         'log_level':30}

#提交实验
rid = submit_exp(Spectrum,manager)
#%%

#%%
rid=115950
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["y_probability"])[:,ion_choice]
x = x[0:len(y)]
#拟合峰值
try:
    peaks = Spectrum_fit(x,y)
except:
    peaks = [x[0],x[-1]]
#画图
fig,ax = plt.subplots()
for peak in peaks:
    if peak is not None:
        ax.axvline(x = peak,color = "r",linestyle ="--")
    # print(peak)
ax.plot(x, y,'-o')
ax.set_xlabel('Frequency (MHz)')
ax.set_ylabel('Probability')
ax.set_title('Spectrum')
# save_plot(fig,base_filename=f"Spectrum_RID{rid}")

print(f"声子频率:{peaks} MHz")
#%%
#更新参数
motion_freq = np.array([abs(peaks[6])*1e6,abs(peaks[5])*1e6,abs(peaks[4])*1e6,abs(peaks[3])*1e6,abs(peaks[2])*1e6,
                        abs(peaks[1])*1e6,abs(peaks[0])*1e6])
# motion_freq = np.array([0.900e6,0.936e6,0.786])
parameter.Light_554.Motion_freq = motion_freq
parameter.update_config_json()

#%%
#计算lamb-dicke系数
from scipy.constants import m_u, hbar

m_171 = 171*m_u     #离子质量
wavelength= 554e-9  #操作光波长
w_k = np.array(motion_freq)* 2*np.pi # 声子频率
x0_k= np.sqrt(hbar/(2*m_171*w_k))       

eta_0= 2*2*np.pi/wavelength *x0_k
#%% md
## 选择声子模式编号
#%%
#定义声子编号
phonon_index =6
#%% md
## 蓝边带Rabi
#%%
#定义扫描参数
scan_start = 0e-6
scan_stop = 200e-6
n_scan_point = 11
ion_choice=3
phonon_index=0

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RamanRabiBlue = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'rabi_choice':'Blue',
                          'qubit_index':f'({ion_choice},)',
                    #  'qubit_index':'(0,1,2)',
                          'phonon_index':phonon_index
                          },
                   'class_name': 'RamanRabi',
                   'file': 'repository\\QCMOS_package\\Raman_Rabi.py',
                   'log_level': 30}

#提交实验
rid= submit_exp(RamanRabiBlue,manager)
#%%
eta_0=[0.1,0.1]
#%%
rid=105211
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

# 拟合蓝边带拉比
rabi_time,t_fit,y_fit, _ = Rabi_fit_thermal(x,y[:,0],eta=eta_0[0],callfunc="blue")
_,t_fit,y_fit
#画图
fig,ax = plt.subplots()
ax.plot(t_fit,y_fit,"--")
ax.plot(x, y,'-o')
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('554 Rabi Blue')
# save_plot(fig,base_filename=f"554_Rabi_Blue_RID{rid}")
print(f"rabi time {rabi_time*1e6} us")
#%%
# 更新参数
# t_pi2 = rabi_time/4
parameter = read_parameter()
t_pi2 = 70e-6
parameter.Light_554.pi_2.Blue = t_pi2
parameter.update_config_json()

#%% md
### EIT 功率
#%%

# parameter.Light_369.dds_for_EIT_pi_amp = 0.8
# parameter.Light_369.dds_for_EIT_sigma_amp = 0.35

#%% md
## 蓝边带Ramsey
#%%
#定义扫描参数
scan_start = 0e-6
scan_stop = 5000e-6
n_scan_point =41
ion_choice=5
phonon_index=11
#更新外部修改的config参数
parameter = read_parameter()

#定义expid
RamanRamseyBlue = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'ramsey_choice':'Blue',
                          'qubit_index':f'({ion_choice},)',
                          'phonon_index':phonon_index,
                          'AWG_Mode':'AWG'
                          },
                   'class_name': 'RamanRamsey',
                   'file': 'repository\\QCMOS_package\\Raman_Ramsey.py',
                   'log_level': 30}

# 提交实验
rid= submit_exp(RamanRamseyBlue,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["y_probability"])[:]
x = x[0:len(y)]
#拟合Ramsey
detuning,coherent_time,t_fit,y_fit = Ramsey_fit(x,y[:,ion_choice])

#画图
fig,ax = plt.subplots()
ax.plot(t_fit,y_fit,"--",label = r"$\tau$"+f" = {coherent_time*1000:.5f} ms")
ax.plot(x, y,'-o',markersize=1)
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title(f'Raman Ramsey Blue RID= {rid}')
ax.set_ylim(0,1)
# save_plot(fig,base_filename=f"554_Ramsey_Blue_RID{rid}")
plt.legend()
print(f"失谐：{detuning} Hz")
print(f"相干时间：{coherent_time*1000} ms")
#%%
parameter = read_parameter()
# 更新声子频率
# detuning = 100
parameter.Light_554.AOM_AWG_amp = 0.3
parameter.Light_554.Motion_freq[phonon_index] += detuning
# parameter.Light_554.Motion_freq[phonon_index] -= detuning

parameter.update_config_json()
#%%
parameter.Light_554.Motion_freq = [parameter.Light_554.Motion_freq[1],parameter.Light_554.Motion_freq[0]]

parameter.update_config_json()
#%% md
# MS 门参数校准
#%% md
## MS门红蓝边带Rabi
#%%
# 减弱拉曼光，
parameter = read_parameter()
parameter.Light_554.AOM_AWG_amp = 0.1183
parameter.Experiment.Repeat = 100
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0e-6
scan_stop = 50e-6
n_scan_point = 31

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_red = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'rabi_choice':'Red',
                          'qubit_index':str(qubit_index),
                          'ion_choice': str(qubit_index)
                          },
                   'class_name': 'MSScanTime',
                   'file': 'repository\\MS_gate_exp\\MS_scan_t.py',
                   'log_level': 30}
rid= submit_exp(MS_red,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

# 拟合拉比
rabi_red,t_fit,y_fit,_ = Rabi_fit(x,y[:,qubit_index[0]])
rabi_red2,t_fit2,y_fit2,_ = Rabi_fit(x,y[:,qubit_index[1]])

#画图
fig,ax = plt.subplots()
ax.plot(t_fit,y_fit,"--")
ax.plot(t_fit2,y_fit2,"--")
ax.plot(x, y,'-o')
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('MS red Rabi')
print(f"red rabi time ion0: {rabi_red*1e6} us")
print(f"red rabi time ion1: {rabi_red2*1e6} us")
# save_plot(fig,base_filename=f"MS_red_Rabi_RID{rid}")
#%%
qubit_index = (0,1)
ratio = rabi_red2/rabi_red
parameter.Light_554.AOD_AWG_ratio[qubit_index[0]][qubit_index[1]] *= ratio
parameter.Light_554.AOD_AWG_ratio[qubit_index[1]][qubit_index[0]] *= 1/ratio
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0e-6
scan_stop = 50e-6
n_scan_point = 31

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_blue = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'rabi_choice':'Blue',
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'MSScanTime',
                   'file': 'repository\\MS_gate_exp\\MS_scan_t.py',
                   'log_level': 30}

#提交实验
rid= submit_exp(MS_blue,manager)
#%%
rid=106490
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

# 拟合拉比
rabi_blue,t_fit,y_fit,_ = Rabi_fit(x,y[:,qubit_index[0]])
rabi_blue2,t_fit2,y_fit2,_ = Rabi_fit(x,y[:,qubit_index[1]])
#画图
fig,ax = plt.subplots()
ax.plot(t_fit,y_fit,"--")
ax.plot(t_fit2,y_fit2,"--")
ax.plot(x, y,'-o')
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('MS blue Rabi')
print(f"blue rabi time ion0: {rabi_blue*1e6} us")
print(f"blue rabi time ion1: {rabi_blue2*1e6} us")
save_plot(fig,base_filename=f"MS_blue_Rabi_RID{rid}")
#%%
aom_ratio = rabi_red/rabi_blue
parameter.Light_554.ratio_AOM *= aom_ratio

parameter.update_config_json()
#%% md
## 红蓝强度校准
#%%
parameter = read_parameter()
parameter.Experiment.Repeat = 400
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0.97
scan_stop = 1.03
n_scan_point = 11

#定义操作时间，校准离子编号，寻址离子编号
operation_time = 10e-6
ion_index = 4

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
AOMRatio = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'operation_time': operation_time,
                          'ion_index':ion_index,
                          'qubit_index':f'({ion_index},{ion_index})'
                          },
                   'class_name': 'AomRatio',
                   'file': 'repository\\MS_gate_exp\\AOM_ratio.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(AOMRatio,manager)
#%%
rid = 112319
#%%
# 实验结果读取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[1:]

# 寻找交叉点
crosses = Cross_fit(x,y)

fig,ax = plt.subplots()
for cross in crosses:
    ax.axvline(x = cross,color = "r",linestyle ="--")
print(f"cross at {cross}")
ax.plot(x, y,'-o')
ax.set_xlabel('AOM ratio')
ax.set_ylabel('Probability')
ax.set_title('AOM Ratio scan')

# save_plot(fig,base_filename=f"AOM_Ratio_scan_RID{rid}")
#%%
rid = 109861
#%%
print(cross_all)
#%%
#更新参数
# aom_ratio = crosses[0]
parameter = read_parameter()
aom_ratio = 1.0
parameter.Light_554.ratio_AOM= aom_ratio
parameter.Experiment.Repeat = 400
parameter.update_config_json()
#%% md
## 红蓝AOD scan
#%%
#定义扫描参数
scan_start = -2.0e6
scan_stop =0.5e6
n_scan_point = 201

# 设置操作时间
operation_time  = 1.6e-6

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
AODScan = {'arguments': {'X_scan_range': dict(ty ='RangeScan',
                                              start=scan_start, stop=scan_stop, npoints= n_scan_point),
                        'side_choice':'All',
                        'operation_time':operation_time,
                        'rabi_choice' : "Red",
                        'phonon_index':phonon_index
                                              },
                   'class_name': 'RamanAODScan',
                   'file': 'repository\\Raman_exp\\Raman_AOD_scan.py',
                   'log_level': 30}

#提交实验
rid1= submit_exp(AODScan,manager)

#定义expid
AODScan = {'arguments': {'X_scan_range': dict(ty ='RangeScan',
                                              start=scan_start, stop=scan_stop, npoints= n_scan_point),
                        'side_choice':'All',
                        'operation_time':operation_time,
                        'rabi_choice' : "Blue",
                        'phonon_index':phonon_index
                                              },
                   'class_name': 'RamanAODScan',
                   'file': 'repository\\Raman_exp\\Raman_AOD_scan.py',
                   'log_level': 30}

#提交实验
rid2= submit_exp(AODScan,manager)
#%%
rid1=114872
rid2=114921
#%%
# 实验结果提取
result = get_result(f"{rid1}",root_dir=result_dir)
x1 = np.array(result["datasets"]["x_points"])
y1 = np.array(result["datasets"]["probability"])[:]
x1 = x1[0:len(y1)]

result = get_result(f"{rid2}",root_dir=result_dir)
x2 = np.array(result["datasets"]["x_points"])
y2 = np.array(result["datasets"]["probability"])[:]
x2 = x2[0:len(y2)]

try:
    peaks = AOD_scan_fit(x1,y1)
except Exception as e:
    print(e)
    peaks = [x1[0],x1[-1]]
#画图

for peak in peaks:
    if peak is not None:
        ax.axvline(x = peak,color = "r",linestyle ="--")
        pass
    # print(peak)
print(f"peaks at {peaks} MHz")

try:
    peaks2 = AOD_scan_fit(x2,y2)
except Exception as e:
    print(e)
    peaks2 = [x2[0],x2[-1]]
#画图
# fig,ax = plt.subplots()
for peak in peaks2:
    if peak is not None:
        ax.axvline(x = peak,color = "r",linestyle ="--")
        pass
    # print(peak)
print(f"peaks at {peaks2} MHz")

fig,ax = plt.subplots()
ax.plot(x1, y1[:,:],'-',color='r',alpha=0.5)
ax.plot(x2, y2[:,:],'-',color='b',alpha=0.5)
ax.set_xlabel('AOD frequency (MHz)')
ax.set_ylabel('Probability')
ax.set_title('AOD Scan')

# save_plot(fig,base_filename=f"AOD_Scan_RID{rid}")
#%%
np.array(peaks2)-np.array(peaks)

#%% md
## 两离子做门
#%% md
### 计算MS参数
#%%
from cloud.ms_parameter import ms_parameters

phonon_mode_1 = 0
phonon_mode_2 = 1

f_strench = parameter.Light_554.Motion_freq[phonon_mode_1]
f_COM = parameter.Light_554.Motion_freq[phonon_mode_2]
rate = -17

delta,tau,Rabi_theory = ms_parameters(f_strench=f_strench,f_COM=f_COM,rate=rate,K=2)

print("delta: 2pi*" ,delta/2/np.pi, "Hz")
print("gate time:", tau*1e6 ,"μs")
print("Rabi: ",1/(Rabi_theory/2/np.pi)*1e6,"μs")
#%%
parameter = read_parameter()
parameter.Light_554.MS_phonon_index = phonon_mode_1
parameter.Light_554.MS_time = tau*1
# parameter.Light_554.MS_time = 164e-6
parameter.Light_554.freq_detuning = delta/2/np.pi
# parameter.Light_554.freq_detuning = -5.760e3

parameter.update_config_json()
#%% md
### 固定时间扫失谐
#%%
# 定义扫描参数
scan_start = -8e3
scan_stop = -10e3
n_scan_point = 21

#定义寻址编号
qubit_index = (0,1)

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_f = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan',
                            start=scan_start,
                            stop=scan_stop, 
                            npoints= n_scan_point
                            ),
                      'qubit_index':str(qubit_index),
                      'ion_choice':str(qubit_index),
                      'phonon_index':int(phonon_mode_1),
                          },
                   'class_name': 'MSScanDetuning',
                   'file': 'repository\\MS_gate_exp\\MS_scan_f.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MS_f,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])

# 寻找 01+10 最低点
P_odd = y[:,1]+y[:,2]
# popt,x_fit,y_fit = MS_01_fit(x,P_odd)
detuning_min = x[P_odd.argmin()]
print(f"min P01+P10 at detuning = {detuning_min} kHz" )
fig,ax = plt.subplots()
ax.axvline(x = detuning_min, color = "r", linestyle ="--")
ax.plot(x, y,'-o')
# ax.plot(x_fit,y_fit,'--')
ax.set_xlabel('detuning (kHz)')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan detuning')
# save_plot(fig,base_filename=f"MS_detuning_RID{rid}")
#%%

parameter.Light_554.freq_detuning = detuning_min*1000
# parameter.Light_554.freq_detuning = -5.760e3

parameter.update_config_json()
#%% md
### 固定失谐、时间扫光强
#%%
# 定义扫描参数
scan_start = 0.1
scan_stop = 0.3
n_scan_point = 11
# qubit_index = (0,1)

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_aom = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', 
                               start=scan_start, 
                               stop=scan_stop, 
                               npoints= n_scan_point),
                      'qubit_index':str(qubit_index),
                      'ion_choice':str(qubit_index)
                          },
                   'class_name': 'MSScanAOMAmp',
                   'file': 'repository\\MS_gate_exp\\MS_scan_aom_amp.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MS_aom,manager)
#%%
rid=108066
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])

# 寻找 00和11 交叉点
P = y[:,0:4:3]
crosses = Cross_fit(x,P)

fig,ax = plt.subplots()
for cross in crosses:
    ax.axvline(x = cross,color = "r",linestyle ="--")
if len(crosses)>0:
    print(f" P11 = P00  at aom amplitude = {crosses[0]} " )

ax.plot(x, y,'-o')
ax.set_xlabel('AOM amplitude')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan AOM amplitude')
# save_plot(fig,base_filename=f"MS_AOM_amp_RID{rid}")
#%%
parameter.Light_554.AOM_AWG_amp_ms = crosses[0]
# parameter.Light_554.AOM_AWG_amp_ms = 0.153

parameter.update_config_json()
#%% md
### 测AC stark shift
#%%
parameter = read_parameter()
# parameter.Light_554.AOM_AWG_amp_ms = 0.01
# qubit_index = (4,3)
# phonon_index =1
# aom_ratio = 1.0
# parameter.Light_554.ratio_AOM= aom_ratio
# parameter.update_config_json()
#%%
#定义扫描参数
scan_start =-1000
scan_stop = 1000
n_scan_point = 21

operation_time = 300e-6
detuning = -30e3
#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_centerline = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', 
                               start=scan_start,
                                 stop=scan_stop,
                                   npoints= n_scan_point),
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index),
                          'detuning':detuning,
                          'operation_time':operation_time
                          },
                   'class_name': 'MSScanCenterline',
                   'file': 'repository\\MS_gate_exp\\MS_scan_shift.py',
                   'log_level': 30}
rid= submit_exp(MS_centerline,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[1:]

#拟合Ramsey
detuning,coherent_time,t_fit,y_fit = Ramsey_fit(x,y[:,0])

#画图
fig,ax = plt.subplots()
ax.plot(t_fit,y_fit,"--")
ax.plot(x, y,'-o')
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title('554 Ramsey MS')
# save_plot(fig,base_filename=f"554_Ramsey_MS_RID{rid}")

#%%
parameter = read_parameter()
parameter.Light_554.MS_AC_Stark_shift = 500
parameter.update_config_json()
#%% md
### MS门扫时间
#%%
#定时扫描参数
scan_start = 000e-6
scan_stop = 2000e-6
n_scan_point = 201
# qubit_index = (0,1)
#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MS_t = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'rabi_choice':'Carrier',
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'MSScanTime',
                   'file': 'repository\\MS_gate_exp\\MS_scan_t.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MS_t,manager)
#%%
rid =117413
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])
# print(x)
# print(y)
# 寻找 01+10 最低点
P_odd = y[:,1]+y[:,2]
index_min = P_odd[3:].argmin()+3 #这里加三是为了去掉最前面的点
# index_min =len(P_odd)-1
time_min = x[index_min]
P_odd[3:].min()
print(f"min P01+P10 ={P_odd[index_min]} at time = {time_min} us" )
fig,ax = plt.subplots(figsize = [4,3])
# ax.axvline(x = time_min, color = "r", linestyle ="--")
error = np.sqrt(y*(1-y)/400)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1],np.sqrt(error[:,1]**2),fmt='-o', capsize=3,markersize=4,label ="01")
ax.errorbar(x, y[:,2],np.sqrt(error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('time (us)')
ax.set_ylabel('Probability')
ax.set_ylim(0,1)
ax.set_title(f'MS gate scan time  RID={rid}')
ax.legend(loc = "upper right",framealpha = 0.5)
# save_plot(fig,base_filename=f"MS_time_RID{rid}")
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])
# print(x)
# print(y)
# 寻找 01+10 最低点
P_odd = y[:,1]+y[:,2]
index_min = P_odd[3:].argmin()+3 #这里加三是为了去掉最前面的点
# index_min =len(P_odd)-1
time_min = x[index_min]
P_odd[3:].min()
print(f"min P01+P10 ={P_odd[index_min]} at time = {time_min} us" )
fig,ax = plt.subplots(figsize = [5,5])
# ax.axvline(x = time_min, color = "r", linestyle ="--")
error = np.sqrt(y*(1-y)/400)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('time (us)')
ax.set_ylabel('Probability')
ax.set_ylim(0,1)
ax.set_title(f'MS gate scan time  RID={rid}')
ax.legend(loc = "upper right",framealpha = 0.5)
# save_plot(fig,base_filename=f"MS_time_RID{rid}")
#%%
parameter.Light_554.MS_time = time_min/1e6
parameter.update_config_json()
#%% md
### 校准两比特 pi/2
#%%
parameter = read_parameter()
# parameter.Light_554.pi_2for2ions.Carrier =1.7458094351461418e-06
# qubit_index = (0,6) 
# aod_ratio = 1.0
# parameter.Light_554.AOD_AWG_ratio[qubit_index[0]][qubit_index[1]] = aod_ratio
# parameter.Light_554.AOD_AWG_ratio[qubit_index[1]][qubit_index[0]] = 1/aod_ratio

parameter.Light_554.AOM_AWG_amp = 0.3
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0
scan_stop = 20
n_scan_point = 21

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
cali_pi2 = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan', 
                            start=scan_start, 
                            stop=scan_stop, 
                            npoints= n_scan_point
                            ),
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'Pi2Cali',
                   'file': 'repository\\calibration\\pi2cali.py',
                   'log_level': 30}
rid= submit_exp(cali_pi2,manager)
#%% md
### Parity
#%%
#定义扫描参数
scan_start = 0
scan_stop = 2 * np.pi
n_scan_point = 31

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
Parity = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan', 
                            start=scan_start, 
                            stop=scan_stop, 
                            npoints= n_scan_point
                            ),
                            
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'Parity',
                   'file': 'repository\\MS_gate_exp\\Parity.py',
                   'log_level': 30}
rid= submit_exp(Parity,manager)
#%%
rid = 108910
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])
parity = y[:,0]+y[:,3]-y[:,1]-y[:,2] 

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])
fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/100)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan Parity')
save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%% md
## 多离子做门（PM）
#%%
-1.093181403999053-0.1498530642629749+0.44747098003094965/2
#%% md
### 设置门参数
#%%
phonon_mode_1 = 0
phonon_mode_2 = 1

f_strench = parameter.Light_554.Motion_freq[phonon_mode_1]
f_COM = parameter.Light_554.Motion_freq[phonon_mode_2]
rate = -20

delta,tau,Rabi_theory = ms_parameters(f_strench=f_strench,f_COM=f_COM,rate=rate,K=1)

print("delta: 2pi*" ,delta/2/np.pi, "Hz")
print("gate time:", tau*1e6 ,"μs")
print("Rabi: ",1/(Rabi_theory/2/np.pi)*1e6,"μs")
#%%
parameter = read_parameter()
parameter.Light_554.MS_phonon_index = phonon_mode_1
parameter.Light_554.MS_time = tau*1
# parameter.Light_554.MS_time = 164e-6
parameter.Light_554.freq_detuning = delta/2/np.pi
# parameter.Light_554.freq_detuning = -5.760e3

parameter.update_config_json()
#%%
gate_time = 233.961198e-6
gate_detuning = -8.5484e3
N_modulation = 6
#%%
parameter = read_parameter()
parameter.Light_554.MS_time = gate_time
parameter.Light_554.freq_detuning = gate_detuning
parameter.update_config_json()
#%% md
### MS门扫时间
#%%
#定时扫描参数
scan_start = 00e-6
scan_stop = 800e-6*2
n_scan_point = 41

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MSPM_t = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index),
                          'N_modulation':int(N_modulation)
                          },
                   'class_name': 'MSPM',
                   'file': 'repository\\MS_gate_exp\\MS_gate_PM.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MSPM_t,manager)
#%%
rid=117002
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])
# 寻找 01+10 最低点
P_odd = y[:,1]+y[:,2]
index_min = P_odd[3:].argmin()+3 #这里加三是为了去掉最前面的点
# index_min =len(P_odd)-1
time_min = x[index_min]
P_odd[3:].min()
print(f"min P01+P10 ={P_odd[index_min]} at time = {time_min} us" )
fig,ax = plt.subplots(figsize = [5,5])
# ax.axvline(x = time_min, color = "r", linestyle ="--")
error = np.sqrt(y*(1-y)/400)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('time (us)')
ax.set_ylabel('Probability')
ax.set_ylim(0,1)
ax.set_title(f'MS gate scan time  RID={rid}')
ax.legend(loc = "upper right",framealpha = 0.5)
# save_plot(fig,base_filename=f"MS_time_RID{rid}")
#%% md
### MS门扫光强
#%%
#定时扫描参数
scan_start = 0.0
scan_stop = 1.0
n_scan_point = 21

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
MSPM_aom = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index),
                          'N_modulation':int(N_modulation)
                          },
                   'class_name': 'MSScanAOMAmp',
                   'file': 'repository\\MS_gate_exp\\MS_PM_scan_aom_amp.py',
                   'log_level': 30}
#提交实验
rid= submit_exp(MSPM_aom,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])

# 寻找 00和11 交叉点
P = y[:,0:4:3]
crosses = Cross_fit(x,P)

fig,ax = plt.subplots()
for cross in crosses:
    ax.axvline(x = cross,color = "r",linestyle ="--")
if len(crosses)>0:
    print(f" P11 = P00  at aom amplitude = {crosses[0]} " )

ax.plot(x, y,'-o')
ax.set_xlabel('AOM amplitude')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan AOM amplitude')
# save_plot(fig,base_filename=f"MS_AOM_amp_RID{rid}")
#%%
parameter.Light_554.AOM_AWG_amp_ms = crosses[0]

parameter.update_config_json()
#%% md
### 校准两比特 pi/2
#%%
parameter = read_parameter()
parameter.Light_554.pi_2for2ions.Carrier = 1.80293549e-6

parameter.Light_554.AOM_AWG_amp = 0.3
parameter.update_config_json()
#%%
#定义扫描参数
scan_start = 0
scan_stop = 20
n_scan_point = 21

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
cali_pi2 = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan', 
                            start=scan_start, 
                            stop=scan_stop, 
                            npoints= n_scan_point
                            ),
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'Pi2Cali',
                   'file': 'repository\\calibration\\pi2cali.py',
                   'log_level': 30}
rid= submit_exp(cali_pi2,manager)
#%%
# 实验结果提取
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["probability"])[:]

#画图
fig,ax = plt.subplots()

ax.plot(x, y,'-o')
ax.set_xlabel('$t$ (us)')
ax.set_ylabel('Probability')
ax.set_title(f'554 Pi2fit RID={rid}')

#%%
parameter.Light_554.pi_2for2ions.Carrier = 1.5e-6
parameter.update_config_json()
#%% md
### Parity
#%%
#定义扫描参数
scan_start = 0
scan_stop = 2 * np.pi
n_scan_point = 21

#更新外部修改的config参数
parameter = read_parameter()

#定义expid
Parity = {'arguments': {'X_scan_range':
                          dict(
                            ty ='RangeScan', 
                            start=scan_start, 
                            stop=scan_stop, 
                            npoints= n_scan_point
                            ),
                        
                          'N_modulation':int(N_modulation),
                          'qubit_index':str(qubit_index),
                          'ion_choice':str(qubit_index)
                          },
                   'class_name': 'ParityTest',
                   'file': 'repository\\MS_gate_exp\\Parity_PM.py',
                   'log_level': 30}
rid= submit_exp(Parity,manager)
#%%
result = get_result(f"{rid}",root_dir=result_dir)
x = np.array(result["datasets"]["x_points"])
y = np.array(result["datasets"]["computational_basis_probability"])
parity = y[:,0]+y[:,3]-y[:,1]-y[:,2] 

contrast,x_fit,y_fit,_ = Parity_fit(x[:],parity[:])
fig,ax = plt.subplots()

error = np.sqrt(y*(1-y)/400)
error_parity = np.sqrt(error[:,0]**2+error[:,1]**2+error[:,2]**2+error[:,3]**2)
ax.errorbar(x, y[:,0],error[:,0],fmt='-o', capsize=3,markersize=4,label ="00")
ax.errorbar(x, y[:,1]+y[:,2],np.sqrt(error[:,1]**2+error[:,2]**2),fmt='-o', capsize=3,markersize=4,label ="01+10")
ax.errorbar(x, y[:,3],error[:,3],fmt='-o', capsize=3,markersize=4,label ="11")
ax.set_xlabel('phase')
ax.set_ylabel('Probability')
ax.set_title('MS gate scan Parity')
save_plot(fig,base_filename=f"MS_Parity_prob_RID{rid}")
fig2,ax2 = plt.subplots()
ax2.errorbar(x[:]/np.pi,parity[:],error_parity,fmt='o',capsize=3,markersize=3)
ax2.plot(x_fit/np.pi,y_fit,'--',label = f'contrast = {contrast:2f}')
ax2.set_xlabel('Phase($\pi$)')
ax2.set_ylabel('Parity')
ax.set_ylim(0,1)
ax2.set_ylim(-1,1)
ax2.set_title(f'Parity-Contrast = {contrast:4f} RID={rid}')
ax.legend()
# save_plot(fig2,base_filename=f"MS_Parity_RID{rid}")
#%%
