#%%
import numpy as np
from scipy.optimize import curve_fit
import matplotlib.pyplot as plt
import matplotlib as mpl
import scienceplots
#%% md
## MS Fidelity 线性拟合
#%%
plt.style.use('science')
plt.style.use(['no-latex'])

## 调整模版
params = {
    'figure.dpi': 200,  ## jupyter中显示图片的分辨率
}
mpl.rcParams.update(params)


def fun(x, a, b):
    return a * x + b


x = [1, 3, 5]
p0011 = np.array([0.97, 0.98, 0.97])
parity = np.array([1.847, 1.717, 1.668]) / 2
y = p0011 / 2 + parity / 2

p0 = [0, 1]
popt = curve_fit(fun, x, y, p0=p0)
print(popt)
plt.plot(x, p0011, ".", label="P00+P11")
plt.plot(x, parity, ".", label="Parity")
plt.plot(x, y, "o", label="Fidelity")
plt.plot(x, fun(np.array(x), popt[0], popt[1]), label=f"F = {popt[1]:.4}{popt[0]:.4}N")
plt.xlabel("Number of MS")
plt.ylabel("Fidelity")
plt.legend()

#%% md
## MS 保真度温度图
#%%


A = [
    [0, 0.0302, 0.0095, 0.0183, 0.0117, 0.0097, 0.0091],
    [0.0302, 0, 0.0031, 0.0061, 0.0080, 0.0050, 0.0059],
    [0.0095, 0.0031, 0, 0.0091, 0.0034, 0.0061, 0.0109],
    [0.0183, 0.0061, 0.0091, 0, 0.0043, 0.0062, 0.0073],
    [0.0117, 0.0080, 0.0034, 0.0043, 0, 0.0031, 0.0098],
    [0.0097, 0.0050, 0.0061, 0.0062, 0.0031, 0, 0.0119],
    [0.0091, 0.0059, 0.0109, 0.0073, 0.0098, 0.0119, 0]
]

plt.imshow(A, cmap='hot', interpolation='nearest', vmin=0, vmax=0.035)
plt.colorbar(label='Value')
plt.imshow(100 * (1 - np.array(A)), cmap='Blues', interpolation='nearest', )
plt.colorbar(label='Fidelity')
data = 1 - np.array(A)
# 添加数值标签
for i in range(len(A)):
    for j in range(len(A[0])):
        value = A[i][j] * 100
        # 根据背景颜色选择文字颜色（深色背景用白色，浅色用黑色）
        text_color = 'white' if data[i, j] > 0.99 else 'black'
        plt.text(j, i, f'{value:.2f}',
                 ha='center', va='center',
                 color=text_color, fontsize=6)

# 可选：添加行列标签
# plt.xticks(range(len(A)), labels=[f'Col {i}' for i in range(len(A))])
# plt.yticks(range(len(A)), labels=[f'Row {i}' for i in range(len(A))])

plt.title(' ')
plt.show()
B = A
B[0][1] = B[5][6]
B[1][0] = B[5][6]
B[3][0] = B[3][6]
B[0][3] = B[3][6]

sum_0 = 0
for i in range(7):
    for j in range(i):
        sum_0 += B[i][j]
sum_0 /= 21
print(sum_0)

sum_0 = 0
for i in range(7):
    if i == 0 or i == 6:
        continue
    for j in range(i):
        if j == 0 or j == 6:
            continue
        sum_0 += B[i][j]
sum_0 /= 10
print(sum_0)

time = [
    [0.0, 0, 0, 0, 0, 0, 0],
    [150.8, 0, 0, 0, 0, 0, 0],
    [154.2, 123.8, 0.0, 0, 0, 0, 0],
    [145.7, 119.2, 129.7, 0, 0, 0, 0],
    [154.2, 123.8, 123.8, 129.7, 0, 0, 0],
    [142.5, 119.2, 123.8, 119.2, 129.7, 0, 0],
    [119.1, 142.5, 154.2, 176.0, 154.2, 150.8, 0]
]
plt.imshow((np.array(time)), cmap='Blues', interpolation='nearest', )
plt.colorbar(label='Fidelity')
data = np.array(time)
# 添加数值标签
for i in range(len(A)):
    for j in range(len(A[0])):
        value = time[i][j]
        # 根据背景颜色选择文字颜色（深色背景用白色，浅色用黑色）
        text_color = 'white' if data[i, j] > 100 else 'black'
        plt.text(j, i, f'{value:.0f}',
                 ha='center', va='center',
                 color=text_color, fontsize=6)

# 可选：添加行列标签
# plt.xticks(range(len(A)), labels=[f'Col {i}' for i in range(len(A))])
# plt.yticks(range(len(A)), labels=[f'Row {i}' for i in range(len(A))])

plt.title(' ')
plt.show()
import numpy as np
import matplotlib.pyplot as plt

# 定义上三角数据
A = [
    [0, 0.0112, 0.0095, 0.0183, 0.0117, 0.0097, 0.0091],
    [0.0, 0, 0.0031, 0.0061, 0.0080, 0.0050, 0.0059],
    [0.0, 0.0, 0, 0.0091, 0.0034, 0.0061, 0.0109],
    [0.0, 0.00, 0.00, 0, 0.0043, 0.0062, 0.0073],
    [0.0, 0.00, 0.00, 0.00, 0, 0.0031, 0.0098],
    [0.00, 0.0, 0.0, 0.00, 0.00, 0, 0.0119],
    [0.00, 0.00, 0.0, 0.00, 0.00, 0.0, 0]
]

# 定义下三角数据
time = [
    [0.0, 0, 0, 0, 0, 0, 0],
    [150.8, 0, 0, 0, 0, 0, 0],
    [154.2, 123.8, 0.0, 0, 0, 0, 0],
    [145.7, 119.2, 129.7, 0, 0, 0, 0],
    [154.2, 123.8, 123.8, 129.7, 0, 0, 0],
    [142.5, 119.2, 123.8, 119.2, 129.7, 0, 0],
    [119.1, 142.5, 154.2, 176.0, 154.2, 150.8, 0]
]

# 转换为numpy数组
upper = (1 - np.array(A)) * 100
lower = np.array(time)

# 创建组合矩阵
combined = np.zeros_like(upper)
combined[np.triu_indices_from(combined, k=1)] = upper[np.triu_indices_from(upper, k=1)]
combined[np.tril_indices_from(combined, k=-1)] = lower[np.tril_indices_from(lower, k=-1)]

# 创建掩码
mask_upper = np.ma.masked_where(upper == 0, upper)
mask_lower = np.ma.masked_where(lower == 0, lower)

# 绘制上三角（蓝色）
im_upper = plt.imshow(mask_upper, cmap='Blues', interpolation='nearest', vmax=np.max(upper))

# 绘制下三角（红色）
# im_lower = plt.imshow(mask_lower, cmap='Reds', interpolation='nearest',  vmax=np.max(lower))

# 添加colorbar（分别对应上下三角）
cbar_upper = plt.colorbar(im_upper, fraction=0.046, pad=0.04)
cbar_upper.set_label('Fidelity', fontsize=10)
# cbar_lower = plt.colorbar(im_lower, fraction=0.046, pad=0.15)
# cbar_lower.set_label('Gate time', fontsize=10)

# 添加数值标签
for i in range(combined.shape[0]):
    for j in range(combined.shape[1]):
        if combined[i, j] > 0:  # 只标注非零值
            # 上三角文字颜色
            if i < j:
                text_color = 'white' if upper[i, j] > (np.max(upper) + np.min(upper)) / 2 else 'black'
            # 下三角文字颜色
            else:
                text_color = 'white' if lower[i, j] > (np.max(lower) + np.min(lower)) / 2 else 'black'

            plt.text(j, i, f'{combined[i, j]:.1f}' if i > j else f'{combined[i, j]:.2f}',
                     ha='center', va='center',
                     color=text_color, fontsize=7)

# 设置坐标轴
plt.xticks(range(combined.shape[0]), labels=[f'{i}' for i in range(combined.shape[0])], fontsize=9)
plt.yticks(range(combined.shape[0]), labels=[f'{i}' for i in range(combined.shape[0])], fontsize=9)

plt.title('', fontsize=12)
plt.tight_layout()
plt.show()