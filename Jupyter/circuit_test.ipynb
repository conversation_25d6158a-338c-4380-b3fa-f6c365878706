#%% md
# 量子线路实验 demo
#%%
from jupyter_function import *
from qiskit import QuantumCircuit
from qiskit.compiler import transpile
from modules.circuit_to_shot import circuit2shot
from pprint import pprint
manager = ARTIQManager()
parameter = read_parameter()

def submit_demo_exp(route, points_info, ion_choice = (0,), adaptive_condition = "False", is_circuit = False):
    """
    提交 demo 实验
    
    Parameters:
    --------------
    
    route: 扫描点数据
    points_info: 用于画图的扫描点数据
    ion_choice: 选择测量的离子索引
    adaptive_condition: 自适应条件
    is_circuit: 是不是线路
    """
    exp = {'arguments': {'route': route,
                        'points_info': points_info,
                        'ion_choice':str(ion_choice),
                        "adaptive_condition":adaptive_condition,
                        "is_circuit":is_circuit
                        },
                    'class_name': 'RouteDemo',
                    'file': 'repository\\demo\\demo.py',
                    'log_level': 30}
    # 提交实验
    pprint(exp)
    rid = submit_exp(exp, manager)
    return rid
#%%
qc = QuantumCircuit(7,7)
qc.h(1)
qc.cx(1,2)
# qc.rzz(np.pi/2,2,4)
qc.cx(1,3)
qc.cx(1,4)
qc.cx(1,5)

# qc.x(4)
# print(qc)

qc_trans = transpile(qc,basis_gates=["rzz","rx","ry"])

# print(qc_trans)

route = circuit2shot(qc_trans)
pprint(route)
#%%
points_info =[
    {
        "points": np.linspace(0.0,np.pi*2,2)
    }
]

route = [
    circuit2shot(qc)
]
rid = submit_demo_exp(route,points_info,is_circuit=True, ion_choice=(2,3,4))
#%% md
## 通过云服务来提交
#%%
from cloud.client import *


import uuid

#%%

client = QuantumClient(base_url="http://127.0.0.1:18001")
#%%
chip_info = client.get_chip_config()
pprint(chip_info)


#%%
# 提交实验
task_id = str(uuid.uuid4())

try:
    result = client.submit_experiment(
            circuit=str(route),
            ion_index=(2,3,4),
            repeats=200,
            task_id=task_id
        )
    print("提交成功")
    rid = result["rid"]
except Exception as e:
    print(f"实验提交失败: {e}")
    exit(1)
#%%
