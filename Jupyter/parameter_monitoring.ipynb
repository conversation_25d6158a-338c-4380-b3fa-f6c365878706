#%% md
# 初始化
#%%

%matplotlib inline 
from jupyter_function import *
#%%
manager = ARTIQManager()
#%%
parameter = read_parameter()
#%% md
# Rabi频率监测
#%% md
## 1.25 Rabi time Rabi
#%%
# 提交rabi任务
scan_start = 30.0e-6
scan_stop = 30.001e-6
n_scan_point = 10001

# 更新外部修改的config参数
parameter = read_parameter()

RamanRabi = {'arguments': {'X_scan_range':
                          dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                          'rabi_choice':'Carrier',
                          'side_choice':'All',
                          'qubit_index':'(3,6)'
                          },
                   'class_name': 'RamanRabi',
                   'file': 'repository\\Raman_exp\\Raman_Rabi.py',
                   'log_level': 30}
rid= submit_exp(RamanRabi,manager)
#%%
111669 
repeat 1000
5.25T
16：25-19：45
#%% md
## 100us Rabi sin fit
#%%
def submit_rabi_function():
    # 定义扫描参数
    scan_start = 0e-6
    scan_stop = 20e-6
    n_scan_point = 81

    # 更新外部修改的config参数
    parameter = read_parameter()

    #定义expid
    RamanRabi = {'arguments': {'X_scan_range':
                            dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                            'rabi_choice':'Carrier',
                            'side_choice':'All',
                            'qubit_index':'(5,)'
                            },
                    'class_name': 'RamanRabi',
                   'file': 'repository\\Raman_exp\\Raman_Rabi.py',
                    'log_level': 30}
    rid= submit_exp(RamanRabi,manager)
    # 实验结果提取
    result = get_result(f"{rid}",root_dir=result_dir)
    x = np.array(result["datasets"]["x_points"])
    y = np.array(result["datasets"]["probability"])[:]

    # 拟合拉比
    try:
        rabi_raman,t_fit,y_fit,popt = Rabi_fit(x,y[:,0])
        # rabi_raman2,t_fit,y_fit,popt = Rabi_fit(x,y[:,1])
    except:
        rabi_raman=-1

    print(f"rabi time: {rabi_raman}")
    # print(f"rabi time: {rabi_raman2}")
    return [rabi_raman]#,rabi_raman2]

#%%
file_name  = "results_rabi_20250416.data"
while True:
    result = submit_rabi_function()
    write_to_file(file_name,result)
#%% md
# 声子频率监测
#%% md
## 任务提交函数
#%%
def rabi_blue_submit(phonon_index,ion_choice=0):
     # 校准 pi/2 时间
    scan_start = 0e-6
    scan_stop = 300e-6
    n_scan_point = 21

    # 更新外部修改的config参数
    parameter = read_parameter()
    
    RabiBlue = {'arguments': {'X_scan_range':
                            dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                            'rabi_choice':'Blue',
                            'side_choice':'All',
                            'phonon_index': int(phonon_index),
                            'qubit_index':f'({ion_choice},)'
                            },
                    'class_name': 'RamanRabi',
                    'file': 'repository\\Raman_exp\\Raman_Rabi.py',
                    'log_level': 30}
    rid= submit_exp(RabiBlue, manager)
    
    # 实验结果提取
    result = get_result(f"{rid}",root_dir=result_dir)
    x = np.array(result["datasets"]["x_points"])
    y = np.array(result["datasets"]["probability"])[:]
    
    rabi_blue = x[y[:,ion_choice].argmax()]/2*1e-6
    parameter.Light_554.pi_2.Blue = rabi_blue
    parameter.update_config_json()
    return rabi_blue
def ramsey_blue_submit(phonon_index,ion_choice = 0):
     # 校准声子频率
    scan_start = 0e-6
    scan_stop = 2000e-6
    n_scan_point = 21
    #更新外部修改的config参数
    parameter = read_parameter()
    RamseyBlue = {'arguments': {'X_scan_range':
                            dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                            'phonon_index': int(phonon_index),
                            "Ramsey_choice":"Blue",
                            'qubit_index':f'({ion_choice},)'
                            },
                    'class_name': 'RamanRamsey',
                    'file': 'repository\\Raman_exp\\Raman_Ramsey.py',
                    'log_level': 30}
    rid= submit_exp(RamseyBlue,manager)
    

    # 实验结果提取
    result = get_result(f"{rid}",root_dir=result_dir)
    x = np.array(result["datasets"]["x_points"])
    y = np.array(result["datasets"]["probability"])[:]
    if min(y[:,0])>0.5:
        return 0,rid
    #拟合Ramsey
    try:
        detuning,coherent_time,t_fit,y_fit = Ramsey_fit(x,y[:,ion_choice])
    except:
        detuning = -1
    return detuning,rid

#%% md
### 拟合并校准
#%%
def phonon_cali_function(phonon_index):
    
    detuning = ramsey_blue_submit(phonon_index)
    print(f"detuning: {detuning} Hz")
    if detuning == 0:
        return parameter.Light_554.Motion_freq[phonon_index],detuning
    parameter.Light_554.Motion_freq[phonon_index] -= detuning
    parameter.update_config_json()

    detuning_new = ramsey_blue_submit(phonon_index)
    print(f"detuning: {detuning_new} Hz")
    if detuning_new > detuning :
        parameter.Light_554.Motion_freq[phonon_index] += detuning_new
        parameter.update_config_json()
    return parameter.Light_554.Motion_freq[phonon_index],detuning_new
#%% md
### 只拟合不校准
#%%
def phonon_cali_function(phonon_index,ion_choice = 0):
    
    detuning,rid = ramsey_blue_submit(phonon_index,
                                  ion_choice=ion_choice)
    print(f"detuning: {detuning} Hz")

    return  detuning ,0 ,rid
#%% md
## 声子频率监测
#%%
# file_name_com = "result_com_freq_20250317_0.data"
# file_name_stretch = "result_stretch_freq_20250315_0.data"
file_name = "result_7ions_mode7_freq_20250416_0.data"
#%%
parameter = read_parameter()

while True:
    result,detuning,rid = phonon_cali_function(0,ion_choice=2)
    if detuning ==0:
        write_to_file_with_rid(file_name,result,rid)
    # result,detuning = phonon_cali_function(1)
    # if detuning ==0:
    #     write_to_file(file_name_com,result)
    time.sleep(1)
#%% md
## Tickle
#%% md
### 暂无tickle实验
#%% md
# Zeeman能级频率监测
#%%
def ramsey_submit_MWZeeman():
     # 校准Zeeman能级频率
    
    scan_start = 0
    scan_stop = 20000e-6
    n_scan_point = 31

    # 更新外部修改的config参数
    read_parameter()

    RamseyMW = {'arguments': {'X_scan_range': 
                                dict(ty ='RangeScan',start=scan_start, stop=scan_stop, npoints=n_scan_point),
                                'zeeman_choice':"-"},
                    'class_name': 'RamseyMW',
                    'file': 'repository\\mw_exp\\mw_ramsey.py',
                    'log_level': 30}
    rid= submit_exp(RamseyMW)
        
    # 实验结果提取
    result = get_result(f"{rid}",root_dir=result_dir)
    x = np.array(result["datasets"]["x_points"])
    y = np.array(result["datasets"]["probability"])[:]
    if min(y[:,0])>0.3:
        return 0
    #拟合Ramsey
    detuning,coherent_time,t_fit,y_fit = Ramsey_fit(x,y[:,0])
    return detuning
def ZeemanFre_cali_function():

    detuning =ramsey_submit_MWZeeman()
    print(f"detuning: {detuning} Hz")
    if detuning == 0:
        return parameter.Signal_MW.zeeman_n,detuning
    if detuning < 1000:
        parameter.Signal_MW.zeeman_n -= detuning
        parameter.update_config_json()

    detuning_new = ramsey_submit_MWZeeman()
    print(f"detuning: {detuning_new} Hz")
    if detuning_new > detuning and detuning_new < 1000 :
        parameter.Signal_MW.zeeman_n+= detuning_new
        parameter.update_config_json()
    return parameter.Signal_MW.zeeman_n,detuning_new
#%%
file_name_ZeemanFre = "result_Zeeman_freq_20250108_1.data"
while True:
    result,detuning = ZeemanFre_cali_function()
    if detuning ==0:
        write_to_file(file_name_ZeemanFre,result)

    time.sleep(0.1)
#%% md
# Raman光频率监测
#%%
file_name_RamanFre = "result_Raman_freq_20241115.data"
#%%
def ramsey_submit_Raman_Carrier():
     # 校准554Raman频率
    scan_start = 0e-6
    scan_stop = 10000e-6
    n_scan_point = 11

    #更新外部修改的config参数
    read_parameter()

    #定义expid
    RamanRamsey = {'arguments': {'X_scan_range':
                            dict(ty ='RangeScan', start=scan_start, stop=scan_stop, npoints= n_scan_point),
                            'Ramsey_choice':'Carrier'
                            },
                    'class_name': 'RamanRamsey',
                    'file': 'repository\\Raman_exp\\Raman_Ramsey.py',
                    'log_level': 30}
    rid= submit_exp(RamanRamsey)
    
        
    # 实验结果提取
    result = get_result(f"{rid}",root_dir=result_dir)
    x = np.array(result["datasets"]["x_points"])
    y = np.array(result["datasets"]["probability"])[:]

    if min(y[:,0])>0.6:
        return 0

    #拟合Ramsey
    detuning,coherent_time,t_fit,y_fit = Ramsey_fit(x,y[:,0])
    return detuning
#%%
while True:
    detuning = ramsey_submit_Raman_Carrier()
    
    write_to_file(file_name_RamanFre,detuning)

    time.sleep(0.1)