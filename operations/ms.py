from artiq.language import Experiment
from modules.config import Config, LOADED_PARAMETER
from .base import BaseOperation

parameter = LOADED_PARAMETER()
class MS(BaseOperation):
    def init_default_attributes(self):
        parameter = LOADED_PARAMETER()
        main_mode = parameter.Light_554.MS_phonon_index[self.qubit_index[0]][self.qubit_index[1]]
        self.check_list = [
            # f"MotionFreq_{int(main_mode)}",
            # f"MSParaCalc_{self.qubit_index[0]}_{self.qubit_index[1]}",
            # f"MSAOMAmp_{self.qubit_index[0]}_{self.qubit_index[1]}",
        ]

        self.attributes = {
            "qubit_index":"qubit_index",
            "time":"operation_time",
            "aom_amp":"aom_amp",
            "aom_ratio":"aom_ratio",
            "detuning":"detuning",
            "phonon_index":"phonon_index",
            "ac_stark_shift":"ac_stark_shift",
            "phase":"phase",
            "rabi_choice":"rabi_choice",
        }

        for key in self.attributes:
            setattr(self, key, None)

    def convert_to_operation(self):
        # return super().convert_to_operation("MSOperation")
        return super().convert_to_operation("MS_AM")