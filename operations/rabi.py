from .base import BaseOperation

class Rabi(BaseOperation):
    def init_default_attributes(self):
        self.check_list = [

        ]

        self.attributes = {
            "qubit_index": "qubit_index",
            "time": "rabi_time",
            "detuning": "detuning",
            "theta": "theta",
            "phase": "phase",
            "phonon_index": "phonon_index",
            "phonon_frequency": "phonon_frequency",
            "aom_amp": "aom_amp",
            "rabi_choice": "rabi_choice",
            "side_choice": "side_choice",
            "eta":"eta"
        }
        for key in self.attributes:
            setattr(self, key, None)


    def convert_to_operation(self):
        return super().convert_to_operation("Raman_Rabi")


class AODScan(BaseOperation):
    def init_default_attributes(self):
        self.check_list = []
        self.attributes = {
            "qubit_index": "qubit_index",
            "time":"operation_time",
            "frequency":"frequency"
        }
    def convert_to_operation(self):
        return super().convert_to_operation("AOD_Scan")

class AODTest(BaseOperation):
    def init_default_attributes(self):
        self.check_list = []
        self.attributes = {
            "qubit_index": "qubit_index",
            "time":"operation_time",
            "amp":"amp",
            "phase":"phase"
        }
    def convert_to_operation(self):
        return  super().convert_to_operation("AOD_Test")