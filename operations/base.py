import numpy as np

class BaseOperation:
    def __init__(self, **kwargs):

        self.qubit_index = kwargs.get("qubit_index", (0,))
        self.init_default_attributes()
        self.process_parameters(**kwargs)

    def init_default_attributes(self):
        """初始化默认参数（子类需覆盖）"""
        self.check_list = []
        self.attributes = {}

    def process_parameters(self, **kwargs):
        """处理传入参数（子类需覆盖）"""
        for key, value in kwargs.items():
            if key == "samplingdense":
                continue
            if key not in self.attributes:
                raise KeyError(f"key {key} not in attributes")
            setattr(self, key, value)


    def convert_to_operation(self,op_name:str):
        """转换为Operation Manager支持的格式"""
        operation = {}

        operation["gate"] = op_name

        for key in self.attributes:
            if getattr(self, key) is not None:
                operation[self.attributes[key]] = getattr(self, key)

        return operation
