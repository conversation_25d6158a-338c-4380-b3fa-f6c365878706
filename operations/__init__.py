import importlib
from pathlib import Path
from .base import BaseOperation

# 自动发现所有操作类
_operation_classes = {}
for module_file in Path(__file__).parent.glob("*.py"):
    if module_file.name not in ["__init__.py", "base.py"]:
        module_name = f"operations.{module_file.stem}"
        module = importlib.import_module(module_name)
        for name, obj in module.__dict__.items():
            if isinstance(obj, type) and issubclass(obj, BaseOperation) and obj != BaseOperation:
                _operation_classes[name] = obj

def get_operation_class(name):
    return _operation_classes.get(name)