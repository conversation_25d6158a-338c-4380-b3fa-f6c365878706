from .base import BaseOperation

class Idle(BaseOperation):
    def init_default_attributes(self):
        self.check_list = []
        self.attributes = {
            "qubit_index": "qubit_index",
            "time": "idle_time",
        }

        for key in self.attributes:
            setattr(self, key, None)

    def convert_to_operation(self):
        return super().convert_to_operation("Idle")
