from .base import BaseOperation

class Rphi(BaseOperation):
    def init_default_attributes(self):
        self.check_list = [
            f"RabiTime_{self.qubit_index[0]}"
        ]

        self.attributes = {
            "qubit_index": "qubit_index",
            "theta": "theta",
            "phi": "phi"
        }
        for key in self.attributes:
            setattr(self, key, None)

    def convert_to_operation(self):
        return super().convert_to_operation("Rphi")

class Rzz(BaseOperation):
    def init_default_attributes(self):
        self.check_list = [
            f"RabiTime_{self.qubit_index[0]}",
            f"RabiTime_{self.qubit_index[1]}",
            f"MSFidelity_{self.qubit_index[0]}_{self.qubit_index[1]}",
            f"MSShift_{self.qubit_index[0]}_{self.qubit_index[1]}",
            f"MSPhase_{self.qubit_index[0]}_{self.qubit_index[1]}",
        ]

        self.attributes = {
            "qubit_index": "qubit_index",
        }

    def convert_to_operation(self):
        return super().convert_to_operation("Rzz")