from qiskit.transpiler.basepasses import TransformationPass
from qiskit.dagcircuit import DAGOpNode,DAGCircuit
from qiskit.circuit.library import RXXGate, RYYGate, RZZGate,RXGate,RYGate,RZGate
from qiskit import QuantumCircuit
from qiskit.quantum_info import Operator
from qiskit.transpiler import PassManager
import numpy as np

class RaaRbRbToRbRbRcc(TransformationPass):
    """Replace Raa → Rb → Rb with Rb → Rb → Rcc (with multiple iterations)"""
    def __init__(self,gate_dict =None):
        super().__init__()
        if gate_dict is None:
            self.gate_dict = {
                "rxx": {"rx": RXXGate, "ry": RZZGate, "rz": RYYGate},
                "ryy": {"rx": RZZGate, "ry": RYYGate, "rz": RXXGate},
                "rzz": {"rx": RYYGate, "ry": RXXGate, "rz": RZZGate},
            }
        else:
            self.gate_dict = gate_dict
    def run(self, dag):
        changed = True
        iteration = 0
        max_iterations = 1000  # 防止无限循环

        gate_dict = self.gate_dict

        while changed and iteration < max_iterations:
            changed = False
            iteration += 1

            for node in list(dag.op_nodes()):  # 使用list()防止迭代时修改
                if node not in dag.op_nodes():  # 节点可能已被前次迭代删除
                    continue

                if node.op.name not in gate_dict:
                    continue

                # 获取后继节点
                successors = list(dag.successors(node))
                rb_nodes = []

                for succ in successors:
                    if (isinstance(succ, DAGOpNode) and
                        succ.op.name in ("rx", "ry", "rz") and
                        any(np.isclose(p, np.pi/2) or np.isclose(p, -np.pi/2)
                        for p in succ.op.params)):
                        rb_nodes.append(succ)
                        if len(rb_nodes) == 2:
                            break

                if len(rb_nodes) != 2:
                    continue

                # 检查两Rb门是否同类型同参数
                if not (rb_nodes[0].op.name == rb_nodes[1].op.name and
                       np.isclose(rb_nodes[0].op.params[0], rb_nodes[1].op.params[0])):
                    continue

                # 创建替换用的新DAG
                new_dag = DAGCircuit()
                new_dag.add_qubits(dag.qubits)

                # 按原始顺序添加Rb门
                for rb_node in sorted(rb_nodes, key=lambda x: x._node_id):
                    new_dag.apply_operation_back(rb_node.op, rb_node.qargs)

                # 添加Rcc门
                rb_type = rb_nodes[0].op.name
                rcc_gate = gate_dict[node.op.name][rb_type](node.op.params[0])
                new_dag.apply_operation_back(rcc_gate, node.qargs)

                # 替换节点
                dag.substitute_node_with_dag(node, new_dag)

                # 移除原始Rb节点
                for rb_node in rb_nodes:
                    if rb_node in dag.op_nodes():  # 再次检查是否存在
                        dag.remove_op_node(rb_node)

                changed = True
                break  # 修改后重新开始扫描

        if iteration == max_iterations:
            print(f"Warning: RaaRbRbToRbRbRcc reached max iterations ({max_iterations})")

        return dag
class SplitRaPi(TransformationPass):
    """Split Ra(pi) into two Ra(pi/2) gates."""

    def run(self, dag):
        for node in list(dag.op_nodes()):
            if (isinstance(node, DAGOpNode)
                and (node.op.name == "rx"  or node.op.name == "ry")
                and (np.isclose(node.op.params[0], np.pi) or np.isclose(node.op.params[0], -np.pi))
            ):

                qarg = node.qargs[0]

                # 创建新的 DAG 来存放拆分后的门
                new_dag = DAGCircuit()
                new_dag.add_qubits([qarg])

                # 添加两个 RX(pi/2)
                new_dag.apply_operation_back(node.op.__class__(node.op.params[0]/2), [qarg])
                new_dag.apply_operation_back(node.op.__class__(node.op.params[0]/2), [qarg])

                # 替换原始节点
                dag.substitute_node_with_dag(node, new_dag)

        return dag

class SortRaaZYX(TransformationPass):
    """ Sort RZZ RYY RXX"""
    def run(self, dag):
        changed = True
        iteration = 0
        max_iterations = 1000  # 防止无限循环
        gate_dict = {
            'rxx': ('ryy', 'rzz'),
            'ryy': ('rzz',)
        }
        while changed and iteration < max_iterations:
            changed = False
            iteration += 1

            for node in list(dag.op_nodes()):  # 使用list()防止迭代时修改
                if node not in dag.op_nodes():  # 节点可能已被前次迭代删除
                    continue

                if node.op.name not in gate_dict:
                    continue

                # 获取后继节点
                successors = list(dag.successors(node))
                raa_nodes = []
                for succ in successors:
                    if (isinstance(succ, DAGOpNode) and
                            succ.op.name in gate_dict[node.op.name]
                    ):
                        raa_nodes.append(succ)
                        break
                if len(raa_nodes) == 0:
                    continue

                # 创建替换用的新DAG
                new_dag = DAGCircuit()
                new_dag.add_qubits(dag.qubits)

                # 按添加Rzz门
                for raa_node in raa_nodes:
                    new_dag.apply_operation_back(raa_node.op, raa_node.qargs)

                # 添加Rcc门
                new_dag.apply_operation_back(node.op, node.qargs)
                # 替换节点
                dag.substitute_node_with_dag(node, new_dag)

                # 移除原始Rb节点
                for raa_node in raa_nodes:
                    if raa_node in dag.op_nodes():  # 再次检查是否存在
                        dag.remove_op_node(raa_node)

                changed = True
                break  # 修改后重新开始扫描

        if iteration == max_iterations:
            print(f"Warning: PutRzzFirst reached max iterations ({max_iterations})")

class MergeRaa(TransformationPass):
    """ Merge Rxx or Ryy  or Rzz"""
    def run(self, dag):
        changed = True
        iteration = 0
        max_iterations = 1000  # 防止无限循环
        while changed and iteration < max_iterations:
            changed = False
            iteration += 1
            # 用于记录已经处理过的节点
            processed_nodes = set()

            for node in dag.op_nodes():
                if node in processed_nodes:
                    continue

                # 只处理 Raa 门
                if isinstance(node, DAGOpNode) and node.op.name in ("rxx","ryy","rzz"):
                    # 获取所有直接后继节点
                    successors = list(dag.successors(node))

                    # 寻找相邻的 RXX 门（相同的量子位）
                    for successor in successors:
                        if (isinstance(successor, DAGOpNode) and
                           successor.op.name == node.op.name and
                           set(successor.qargs) == set(node.qargs)
                        ):

                            # 计算合并后的角度（取模 2π）
                            total_angle = (node.op.params[0] + successor.op.params[0] ) % (2 * np.pi)

                            # 创建新的 DAGCircuit 用于替换
                            new_dag = DAGCircuit()
                            new_dag.add_qubits(dag.qubits)
                            new_dag.add_clbits(dag.clbits)

                            # 如果合并后角度不为0，则添加合并后的门
                            if not np.isclose(total_angle, 0):
                                new_dag.apply_operation_back(
                                    node.op.__class__(total_angle),
                                    node.qargs,
                                    node.cargs
                                )

                            # 用新DAG替换原节点
                            dag.substitute_node_with_dag(node, new_dag)
                            dag.remove_op_node(successor)

                            processed_nodes.add(successor)
                            changed = True
                            break

                    processed_nodes.add(node)

        return dag
class RaaPi2RaRa(TransformationPass):
    """Replace Raa(pi) with Ra(pi)Ra(pi)"""
    def run(self, dag):
        gate_dict = {
            'rxx':RXGate,
            'ryy':RYGate,
            'rzz':RZGate,
        }
        gate_dict2 = {
            'rxx':RXXGate,
            'ryy':RYYGate,
            'rzz':RZZGate,
        }
        for node in list(dag.op_nodes()):
            if (
                isinstance(node, DAGOpNode) and
                node.op.name in gate_dict and
                (np.isclose(node.op.params[0], np.pi) or np.isclose(node.op.params[0], -np.pi))
            ):
                q0,q1 = node.qargs
                # 创建新的 DAG 来存放拆分后的门
                new_dag = DAGCircuit()
                new_dag.add_qubits([q0,q1])

                # 添加两个 RX(pi/2)
                gate = gate_dict[node.op.name]
                new_dag.apply_operation_back(gate(node.op.params[0]), [q0])
                new_dag.apply_operation_back(gate(node.op.params[0]), [q1])

                # 替换原始节点
                dag.substitute_node_with_dag(node, new_dag)

            if (
                isinstance(node, DAGOpNode) and
                node.op.name in gate_dict and
                np.isclose(node.op.params[0], np.pi * 3/2)
            ):
                q0,q1 = node.qargs
                # 创建新的 DAG 来存放拆分后的门
                new_dag = DAGCircuit()
                new_dag.add_qubits([q0,q1])

                # RXX(3pi/2)->RXX(-pi/2)
                gate2 = gate_dict2[node.op.name]

                new_dag.apply_operation_back(gate2(-np.pi/2),[q0,q1])

                # 替换原始节点
                dag.substitute_node_with_dag(node, new_dag)
            if (
                isinstance(node, DAGOpNode) and
                node.op.name in gate_dict and
                (np.isclose(node.op.params[0], np.pi * 1/2) or np.isclose(node.op.params[0], -np.pi * 3/2))
            ):
                q0,q1 = node.qargs
                # 创建新的 DAG 来存放拆分后的门
                new_dag = DAGCircuit()
                new_dag.add_qubits([q0,q1])

                # 添加两个 RX(-pi),一个RXX(-pi/2)
                gate = gate_dict[node.op.name]
                gate2 = gate_dict2[node.op.name]

                new_dag.apply_operation_back(gate(-np.pi), [q0])
                new_dag.apply_operation_back(gate(-np.pi), [q1])
                new_dag.apply_operation_back(gate2(-np.pi/2),[q0,q1])

                # 替换原始节点
                dag.substitute_node_with_dag(node, new_dag)

class RzztoRxx(TransformationPass):
    """Replace Rzz with R-yR-yRxxRyRy"""
    def run(self, dag):
        for node in list(dag.op_nodes()):
            if (isinstance(node, DAGOpNode)
                and (node.op.name == "rzz")
            ):

                q0,q1 = node.qargs
                # 创建新的 DAG 来存放拆分后的门
                new_dag = DAGCircuit()
                new_dag.add_qubits([q0,q1])

                # 添加两个 RX(pi/2)
                new_dag.apply_operation_back(RYGate(-np.pi/2), [q0])
                new_dag.apply_operation_back(RYGate(-np.pi/2), [q1])
                new_dag.apply_operation_back(RXXGate(node.op.params[0]), [q0,q1])
                new_dag.apply_operation_back(RYGate(np.pi/2), [q0])
                new_dag.apply_operation_back(RYGate(np.pi/2), [q1])
                # 替换原始节点
                dag.substitute_node_with_dag(node, new_dag)

        return dag