import json
import logging
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Optional
from enum import Enum

import numpy as np
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import J<PERSON>NResponse
from fastapi.middleware import Middleware  # noqa: F401
from pydantic import BaseModel, Field, ValidationError  # noqa: F401
from starlette.middleware.base import BaseHTTPMiddleware

# 配置芯片参数文件地址
CHIP_CONFIG_DIRECTORY = Path(__file__).resolve().parent.parent  # 获取根目录的路径
CHIP_CONFIG_FILE_PATH = str(
    CHIP_CONFIG_DIRECTORY / "chip_info.json"
)  # 获取当前脚本同路径下的 json 文件路径


# 配置日志
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

file_handler = logging.FileHandler(
    log_dir / f"api_{datetime.now().strftime('%Y%m%d')}.log"
)
stream_handler = logging.StreamHandler()

formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
file_handler.setFormatter(formatter)
stream_handler.setFormatter(formatter)

logger.addHandler(file_handler)
logger.addHandler(stream_handler)


# 修改中间件定义
class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.perf_counter()

        # 读取并替换请求体
        body = await request.body()
        request._body = body

        request_info = {
            "timestamp": datetime.utcnow().isoformat(),
            "method": request.method,
            "url": str(request.url),
            "client_ip": request.client.host,
            "request_body": body.decode("utf-8") if body else None,
        }

        response = await call_next(request)

        process_time = time.perf_counter() - start_time
        log_entry = {
            **request_info,
            "status_code": response.status_code,
            "process_time": f"{process_time:.3f}s",
        }

        logger.info(json.dumps(log_entry, indent=2))
        return response


# 修改 FastAPI 应用创建方式
app = FastAPI()
app.add_middleware(LoggingMiddleware)

# 导入 ARTIQManager 和 get_result
from artiq_manager import ARTIQManager, get_result  # noqa: E402

artiq_manager = ARTIQManager()


# 实验参数模型
class ExpArgs(BaseModel):
    circuit: str = Field(..., description="量子电路描述")
    ion_index: str = Field(..., description="量子比特数量")
    repeats: int = Field(..., description="重复次数")


# 请求体模型
class SubmitRequest(BaseModel):
    version: str = Field(..., description="API 版本")
    task_id: str = Field(..., description="唯一任务标识")
    arguments: ExpArgs = Field(..., description="实验参数")

    class Config:
        json_schema_extra = {
            "example": {
                "version": "1.0",
                "task_id": "11D919FA044846F3B4DF453A827AE901",
                "arguments": {
                    "circuit": "X 0; CNOT 0 1",
                    "num_qubits": 2,
                    "repeats": 1000,
                },
            }
        }


# 响应体模型
class SubmitResponse(BaseModel):
    submitted_at: datetime = Field(..., description="任务提交时间戳")
    task_id: str = Field(..., description="唯一任务标识")
    status_code: int = Field(..., description="状态码")
    status_info: str = Field(..., description="状态描述")
    version: str = Field(..., description="API 版本")
    rid: int = Field(..., description="实验运行 ID")

    class Config:
        json_schema_extra = {
            "example": {
                "submitted_at": "2023-10-01T12:00:00Z",
                "task_id": "11D919FA044846F3B4DF453A827AE901",
                "status_code": 200,
                "status_info": "",
                "version": "1.0",
                "rid": 51,
            }
        }


# 提交实验的 API 端点
# @app.post("/v1/experiment/submit", response_model=SubmitResponse)
# async def submit_experiment(request: SubmitRequest):
#     try:
#         # exp_config = {
#         #     "arguments": request.arguments.model_dump(),
#         #     "class_name": "CircuitShot",
#         #     "file": str(Path("repository") / "circuit_exp/circuit_shot.py"),
#         #     "log_level": logging.INFO,
#         # }
#         exp_config = {
#             "arguments": request.arguments.model_dump(),
#             "class_name": "CircuitShot",
#             "file": str(Path("repository") / "QCMOS_package/circuit_shot_qcmos.py"),
#             "log_level": logging.INFO,
#         }
#         logger.info(f"实验配置: {exp_config}")
#
#         rid = artiq_manager.submit_circuit(exp_config)
#         logger.info(f"实验已提交， RID: {rid}")
#
#         current_time = datetime.now(timezone.utc)
#
#         return SubmitResponse(
#             submitted_at=current_time,
#             task_id=request.task_id,
#             status_code=200,
#             status_info="实验提交成功",
#             version=request.version,
#             rid=rid,
#         )
#     except Exception as e:
#         logger.error(f"提交实验时出错: {e}")
#         raise HTTPException(status_code=500, detail=f"实验提交失败: {str(e)}")

@app.post("/v1/experiment/submit", response_model=SubmitResponse)
async def submit_experiment(request: SubmitRequest):
    try:
        # exp_config = {
        #     "arguments": request.arguments.model_dump(),
        #     "class_name": "CircuitShot",
        #     "file": str(Path("repository") / "circuit_exp/circuit_shot.py"),
        #     "log_level": logging.INFO,
        # }
        model = request.arguments.model_dump()
        points_info =[{
        "points": np.linspace(0.0,np.pi*2,1)
        }]
        # logger.info("线路信息")
        # logger.info(eval(model["circuit"]))
        # tp = type(eval(model["circuit"]))
        # logger.info(tp)

        exp_config = {
            "arguments": {"route": eval(model["circuit"]),
                        "points_info": points_info,
                        "ion_choice":str(model["ion_index"]),
                        "adaptive_condition" : "False",
                        "is_circuit": True,
                        "repeat": model["repeats"]
                        },
            'class_name': 'RouteDemo',
            'file': 'repository\\demo\\demo.py',
            "log_level": logging.INFO,
        }
        logger.info(f"实验配置: {exp_config}")

        rid = artiq_manager.submit_circuit(exp_config)
        logger.info(f"实验已提交， RID: {rid}")

        current_time = datetime.now(timezone.utc)

        return SubmitResponse(
            submitted_at=current_time,
            task_id=request.task_id,
            status_code=200,
            status_info="实验提交成功",
            version=request.version,
            rid=rid,
        )
    except Exception as e:
        logger.error(f"提交实验时出错: {e}")
        raise HTTPException(status_code=500, detail=f"实验提交失败: {str(e)}")

# 自定义 JSON 编码器
def numpy_encoder(obj):
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.integer, np.int_)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64)):
        return float(obj)
    elif isinstance(obj, bytes):
        return obj.decode()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    else:
        raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")


# 获取实验结果的 API 端点
@app.get("/v1/experiment/get_exp_result")
async def get_result_api(target_id: str, root_dir: str = "results"):
    try:
        logger.info(f"尝试获取实验结果: target_id={target_id}, root_dir={root_dir}")
        result = get_result(target_id, root_dir)

        if result is None:
            logger.warning(f"未找到实验结果: target_id={target_id}")
            raise HTTPException(status_code=404, detail="结果未找到")

        logger.info(f"成功获取实验结果: target_id={target_id}")
        return JSONResponse(
            content=json.loads(json.dumps(result, default=numpy_encoder))
        )

    except Exception as e:
        logger.error(f"获取结果时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取实验结果失败: {str(e)}")


# 实验状态模型
class ExperimentStatus(BaseModel):
    status_code: int = Field(..., description="状态码")
    status_info: str = Field("", description="状态描述")
    experiments: Dict[str, Dict[str, str]] = Field(..., description="实验状态列表")

    class Config:
        json_schema_extra = {
            "example": {
                "status_code": 200,
                "status_info": "",
                "experiments": {
                    "51": {"status": "running"},
                    "52": {"status": "preparing"},
                },
            }
        }


# 运行状态枚举
class RunStatus(Enum):
    """实验运行状态枚举"""

    pending = 0  # 等待中
    flushing = 1  # 刷新中
    preparing = 2  # 准备中
    prepare_done = 3  # 准备完成
    running = 4  # 运行中
    run_done = 5  # 运行完成
    analyzing = 6  # 分析中
    deleting = 7  # 删除中
    paused = 8  # 已暂停


# 获取实验状态的 API 端点
@app.get("/v1/experiment/status", response_model=ExperimentStatus)
async def get_experiments_status():
    """获取所有实验的状态

    Returns
    -------
    ExperimentStatus
        包含所有实验状态的响应对象

    可能的状态值:
    - pending (0): 等待中
    - flushing (1): 刷新中
    - preparing (2): 准备中
    - prepare_done (3): 准备完成
    - running (4): 运行中
    - run_done (5): 运行完成
    - analyzing (6): 分析中
    - deleting (7): 删除中
    - paused (8): 已暂停
    """
    try:
        logger.info("正在获取实验状态...")

        # 获取原始状态数据
        status_dict = artiq_manager.get_status()
        logger.debug(f"原始状态数据: {status_dict}")

        # 简化状态信息
        simplified_status = {
            str(rid): {"status": info["status"]} for rid, info in status_dict.items()
        }
        logger.debug(f"简化后的状态数据: {simplified_status}")

        # 统计不同状态的实验数量
        status_counts = {status.name: 0 for status in RunStatus}
        for info in simplified_status.values():
            status = info["status"]
            if isinstance(status, (int, str)):
                try:
                    if isinstance(status, str):
                        status_enum = RunStatus[status]
                    else:
                        status_enum = RunStatus(status)
                    status_counts[status_enum.name] += 1
                except (ValueError, KeyError):
                    logger.warning(f"未知的状态值: {status}")
                    status_counts["unknown"] = status_counts.get("unknown", 0) + 1

        # 记录详细的状态统计
        logger.info("实验状态统计:")
        for status_name, count in status_counts.items():
            if count > 0:  # 只记录有实验的状态
                logger.info(f"- {status_name}: {count} 个实验")

        logger.info(f"当前共有 {len(simplified_status)} 个实验")

        # 检查是否有异常状态
        active_experiments = sum(
            count
            for status, count in status_counts.items()
            if status in ["running", "preparing", "analyzing"]
        )
        if active_experiments > 0:
            logger.info(f"当前有 {active_experiments} 个实验正在处理中")

        response = ExperimentStatus(
            status_code=200,
            status_info="获取实验状态成功",
            experiments=simplified_status,
        )

        return response

    except Exception as e:
        logger.error(f"获取实验状态时出错: {e}", exc_info=True)
        logger.error(f"错误类型: {type(e).__name__}")
        raise HTTPException(status_code=500, detail=f"获取实验状态失败: {str(e)}")


# 芯片配置信息模型
class QuantumChipArch(BaseModel):
    chip: str = Field("Matrix-2", alias="Chip", description="芯片名称")
    chip_status: Optional[str] = Field(None, alias="ChipStatus", description="芯片状态")
    qubit_count: int = Field(..., alias="QubitCount", description="量子比特的总数")
    max_shots: int = Field(..., alias="MaxShots", description="最大测量次数")
    max_circuit_layer: int = Field(
        ..., alias="MaxCircuitLayer", description="最大线路层数"
    )
    last_update_time: int = Field(
        ..., alias="LastUpdateTime", description="最后更新时间戳"
    )
    adj_matrix: list[list[float]] = Field(
        ..., alias="AdjMatrix", description="保真度矩阵"
    )
    qubit_params: Dict[str, Dict] = Field(
        ..., alias="QubitParams", description="量子比特参数"
    )
    available_qubits: list[int] = Field(
        ..., alias="AvailableQubits", description="可用比特"
    )
    basic_q_gate: list[str] = Field(
        ..., alias="BasicQGate", description="支持的基础逻辑门"
    )

    class Config:
        # 允许使用字段名或 alias 填充数据
        allow_population_by_field_name = True
        json_schema_extra = {
            "example": {
                "Chip": "Matrix2",
                "QubitCount": 2,
                "AdjMatrix": [[0.0, 0.993], [0.993, 0.0]],
                "QubitParams": {
                    "0": {
                        "ReadoutFidelity": 0.9500,
                        "FidelityMat": [[0.9700, 0.0300], [0.0500, 0.9500]],
                        "SingleGateFidelity": 0.9980,
                    },
                    "1": {
                        "ReadoutFidelity": 0.9480,
                        "FidelityMat": [[0.9650, 0.0400], [0.0350, 0.9600]],
                        "SingleGateFidelity": 0.9970,
                    },
                },
                "AvailableQubits": [0, 1],
                "BasicQGate": ["RPhi", "MS"],
                "LastUpdateTime": 1747200000000,
                "MaxShots": 1000,
                "MaxCircuitLayer": 20,
            }
        }


# 获取芯片当前配置信息
@app.get("/v1/experiment/get_chip_config", response_model=QuantumChipArch)
async def get_chip_config():
    """获取芯片配置信息

    Returns
    -------
    ChipConfig
        芯片信息对象
    """
    try:
        logger.info("获取芯片当前配置信息...")

        # 芯片配置信息文件路径
        with open(CHIP_CONFIG_FILE_PATH, "r") as f:
            chip_info = json.load(f)
            quantum_chip_arch = chip_info.get("QuantumChipArch")
            return QuantumChipArch.model_validate(quantum_chip_arch)

    except Exception as e:
        logger.error(f"获取芯片当前配置信息时出错: {e}", exc_info=True)
        logger.error(f"错误类型: {type(e).__name__}")
        raise HTTPException(status_code=500, detail=f"获取片当前配置信息失败: {str(e)}")


# 运行应用程序
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=18001)
