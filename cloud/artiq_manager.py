import os
import sys
from pathlib import Path
from venv import logger

# from sipyco.pc_rpc import (Client, AsyncioClient,BestEffortClient, AutoTarget)
import artiq.frontend.artiq_client as artiq_client
import logging
from pprint import pprint
import time
import h5py
from typing import Optional, Dict

__all__ = ["ARTIQManager", "get_result","get_result_time"]


class ARTIQManager:
    def __init__(self, server_address="::1", port=3251):
        # 初始化连接到 ARTIQ 主机的客户端
        self.server = server_address
        self.port = port
        self.scheduler = artiq_client.Client(server_address, port, "schedule")
        self.dataset_db = artiq_client.Client(server_address, port, "dataset_db")
        self.experiment_db = artiq_client.Client(server_address, port, "experiment_db")
        # self.config = artiq_client.Client(server_address, port, "master_config")
        self.device_db = artiq_client.Client(server_address, port, "device_db")

    def submit(self, expid: dict):
        """提交任务代码"""
        # # 检查调度器状态，如果为空则提交实验
        # if not self.scheduler.get_status():
        #     rid = self.scheduler.submit(
        #         pipeline_name="main",
        #         expid=expid,
        #         priority=0,
        #         due_date=None,
        #         flush=False,
        #     )
        #     self.expid = expid
        #     # print("rid:", rid)
        #     return rid
        # else:
        #     return "Other experiment is running"
        
        # 提交实验至artiq
        rid = self.scheduler.submit(
            pipeline_name="main",
            expid=expid,
            priority=0,
            due_date=None,
            flush=False,
        )
        self.expid = expid
        # print("rid:", rid)
        return rid
    def submit_circuit(self, expid: dict):
        """提交量子电路实验"""
        rid = self.scheduler.submit(
            pipeline_name="main",
            expid=expid,
            priority=0,
            due_date=None,
            flush=False,
        )
        return rid

    def monitor_experiment(self, rid=None):
        """
        监控特定实验的状态，直到实验完成
        判断实验是否正常结束，如果意外结束尝试重新提交实验
        """

        # if _rid in self.scheduler.get_status():
        #     return {"status": "Experiment on doing..."}
        # else:
        #     # 1. 读取项目根目录下的 last_rid.pyon 文件，获取最后一个实验的编号
        #     self_rid = self._get_last_rid()
        #     # 2. 比较当前实验编号和最后一个实验编号，如果小于该实验编号，则说明实验尚未执行，否则说明实验执行完毕
        #     if self_rid < _rid:
        #         return {"status": "Experiment has not been done!"}
        #     else:
        #         return {"status": "Experiment done! You can get result now."}
        if rid is None:
            _rid = self._get_last_rid()
        elif isinstance(rid, int):
            _rid = rid
        else:
            raise TypeError(f"{rid} is not a experiment RID.")

        for i_submit in range(100):  # 最多重新提交100次
            while _rid in self.scheduler.get_status():
                time.sleep(0.5)
            # if self.get_dataset("ion_lost"):
            #     print("Ions lost, resumbmit the experiment")
            #     _rid = self.scheduler.submit(
            #         pipeline_name="main",
            #         expid=self.expid,
            #         priority=0,
            #         due_date=None,
            #         flush=False,
            #     )
            # else:
            break
        else:
            return {"status": "Ions lost,resubmit failed", "rid": _rid}
        return {"status": "Experiment done!", "rid": _rid}

    def get_status(self):
        """
        获取 pipeline 中的实验状态
        """
        status_dict = self.scheduler.get_status()
        # 只保留子字典中的 status 字段
        return status_dict

    def get_dataset(self, dataset):
        """获取实验结果"""
        return self.dataset_db.get(dataset)

    def get_exp_list(self):
        """获取所有已注册实验的 expid 字典，返回原始未修正的格式。

        此方法返回的是原始格式的实验参数，需要手动根据参数类型进行转换才能使用。

        Returns
        -------
        dict
            包含所有注册实验的原始 expid 字典。

        Notes
        -----
        返回的参数需要根据以下规则手动转换：
        - StringValue: 需转换为字符串
        - EnumerationValue: 需转换为字符串
        - NumberValue: 需转换为数字
        - Scannable: 需提取 default 中的 RangeScan 配置
        - BooleanValue: 需转换为布尔值

        Examples
        --------
        >>> manager = ARTIQManager()
        >>> raw_expid = manager.get_exp_list()
        原始格式示例: 
        expid =  {'arguments': {'PMT_Select_Channels': {'default': '',
                                                              'ty': 'StringValue'},
                                      'Task': {'choices': ['1', '2', '3', '4'],
                                               'ty': 'EnumerationValue'},
                                      'Task_num': {'default': 0,
                                                   'max': 2,
                                                   'min': 0,
                                                   'ndecimals': 0,
                                                   'scale': 1.0,
                                                   'step': 1,
                                                   'ty': 'NumberValue',
                                                   'type': 'auto',
                                                   'unit': ''},
                                      'f_scan': {'default': [{'npoints': 50,
                                                              'randomize': False,
                                                              'seed': None,
                                                              'start': -20000000.0,
                                                              'stop': 20000000.0,
                                                              'ty': 'RangeScan'}],
                                                 'global_max': 25000000.0,
                                                 'global_min': -25000000.0,
                                                 'global_step': 1000000.0,
                                                 'ndecimals': 2,
                                                 'scale': 1000000.0,
                                                 'ty': 'Scannable',
                                                 'unit': 'MHz'},
                                      'ion_num_1': {'default': True,
                                                    'ty': 'BooleanValue'}},
                        'class_name': 'ParameterTest',
                        'file': 'repository\\param_in\\parameter_test.py',
                        'log_level': 30},
        修正的 expid:
        expid = {'arguments': {'PMT_Select_Channels': '[[12,13,14], [21,22,21]]', #  StringValue
                       'Task': "1",  # 'EnumerationValue'
                       'Task_num': 20,  #  'NumberValue'
                       'f_scan': {'npoints': 50,
                                  'randomize': False,
                                  'seed': None,
                                  'start': -20000000.0,
                                  'stop': 20000000.0,
                                  'ty': 'RangeScan'},  # 'Scannable'
                       'ion_num_1': True}, # 'BooleanValue'
         'class_name': 'ParameterTest',
         'file': 'repository\\param_in\\parameter_test.py',
         'log_level': 30}
        See Also
        --------
        _extract_info_from_exp_list : 用于提取基本实验信息的辅助方法

        Notes
        -----
        参数转换规则详解：
        1. StringValue:
        - 原始: {'default': '', 'ty': 'StringValue'}
        - 转换后: 直接使用字符串值

        2. EnumerationValue:
        - 原始: {'choices': ['1', '2', '3'], 'ty': 'EnumerationValue'}
        - 转换后: 选择其中一个值作为字符串

        3. NumberValue:
        - 原始: {
            'default': 0,
            'max': 2,
            'min': 0,
            'ty': 'NumberValue',
            'scale': 1.0
            }
        - 转换后: 一个数值

        4. Scannable:
        - 原始: 包含完整的扫描配置
        - 转换后: 保留 RangeScan 相关配置

        5. BooleanValue:
        - 原始: {'default': True, 'ty': 'BooleanValue'}
        - 转换后: True 或 False
        """
        _exp_list = self.experiment_db.show_exp_list()  # 获取一个混乱的未经处理的原始实验列表
        processed_list = self._extract_info_from_exp_list(_exp_list)  # 处理成想要的格式
        return processed_list

    @staticmethod
    def _extract_info_from_exp_list(dictionary):
        """从实验列表字典中提取信息并构建 expid 配置字典。

        将原始实验列表字典转换为 ARTIQ 主管理器可用的 expid 格式。每个实验条目
        包含文件路径、类名、日志级别和参数信息。

        Parameters
        ----------
        dictionary : dict
            原始实验列表字典，其中包含实验的基本信息。
            格式为 {exp_name: {"file": str, "class_name": str, "arginfo": OrderedDict}}

        Returns
        -------
        dict
            转换后的 expid 配置字典。
            格式为 {exp_name: {
                "file": str,          # 添加了 "repository\\" 前缀的文件路径
                "class_name": str,    # 实验类名
                "log_level": int,     # 日志级别（默认为 WARNING）
                "arguments": dict     # 从 arginfo 提取的参数字典
            }}

        Notes
        -----
        - 文件路径会自动添加 "repository\\" 前缀
        - arginfo 中的参数值使用第一个元素 (arginfo[key][0])
        - 日志级别默认设置为 WARNING
        """
        extracted_info = {}
        for key, value in dictionary.items():
            file = value.get("file", "N/A")

            file_with_prefix = "repository\\" + file

            class_name = value.get("class_name", "N/A")
            arginfo_ordered_dict = value.get("arginfo", {})

            # 将OrderedDict转换为普通的字典
            arginfo = {
                arg_name: arg_value[0]
                for arg_name, arg_value in arginfo_ordered_dict.items()
            }

            extracted_info[key] = {
                "file": file_with_prefix,
                "class_name": class_name,
                "log_level": logging.WARNING,
                "arguments": arginfo,
            }
        return extracted_info

    @staticmethod
    def _get_last_rid():
        """读取最后一次实验的运行 ID (RID)。

        从项目根目录下的 last_rid.pyon 文件中读取上一次实验的运行 ID。
        该文件存储了一个整数值，代表最近一次实验的唯一标识符。

        Returns
        -------
        int or None
            如果文件存在且内容有效，返回文件中存储的整数 RID。
            如果文件不存在或无法读取，返回 None。

        Notes
        -----
        - 文件路径默认为项目根目录下的 'last_rid.pyon'
        - 如果配置文件位于 modules 文件夹下，可以使用 '../last_rid.pyon' 作为路径
        - 文件内容应为可转换为整数的字符串

        Examples
        --------
        >>> rid = _get_last_rid()
        >>> if rid is not None:
        ...     print(f"Last experiment RID: {rid}")
        ... else:
        ...     print("Could not retrieve last RID")
        """
        # file_path = "last_rid.pyon"  # 假设文件存储在项目根目录下
        file_path = '../last_rid.pyon'

        if not os.path.exists(str(file_path)):
            print("文件不存在:" + {}.__format__(file_path))
            return

        with open(str(file_path), "r") as file:
            content = file.read()  # 读到的是一个字符串，要转换成 int

        return int(content)


def get_result(target_id: str, root_dir: str = "results") -> Optional[Dict]:
    """从 HDF5 文件中读取指定实验 ID 的结果数据。

    从指定目录中查找并读取对应实验 ID 的 HDF5 文件，将其内容转换为 Python 字典格式。

    Parameters
    ----------
    target_id : str
        要查找的实验 ID。必须是非空字符串。
    root_dir : str, optional
        存放结果文件的根目录，默认为 'results'。

    Returns
    -------
    Optional[Dict]
        如果找到文件：
            返回包含实验数据的字典，格式为：
            {
                'archive': dict,          # 存档信息
                'artiq_version': str,     # ARTIQ 版本
                'datasets': dict,         # 实验数据集
                'expid': dict,           # 实验配置信息
                'rid': int,              # 实验运行 ID
                'run_time': float,       # 运行时间戳
                'start_time': float      # 开始时间戳
            }
        如果未找到文件或发生错误：
            返回 None

    Raises
    ------
    ValueError
        当 target_id 为空或不是字符串类型时

    Notes
    -----
    - 文件路径基于脚本所在目录的上级目录
    - HDF5 文件名应以补零后的实验 ID 开头
    - 如果找到多个匹配文件，使用第一个匹配项

    Examples
    --------
    >>> result = get_result("24")
    >>> if result is not None:
    ...     print(f"Experiment RID: {result['rid']}")
    ...     print(f"Datasets: {list(result['datasets'].keys())}")
    ... else:
    ...     print("Result not found")

    See Also
    --------
    _find_files_with_id : 查找匹配的 HDF5 文件
    _hdf5_to_dict : 将 HDF5 对象转换为字典
    """
    if not target_id or not isinstance(target_id, str):
        raise ValueError("target_id 必须是非空字符串")

    try:
        script_dir = os.path.abspath(os.path.dirname(__file__))
        root_path = os.path.join(os.path.dirname(script_dir), root_dir)

        paths = _find_files_with_id(target_id, root_path)
        if not paths:
            return None

        with h5py.File(paths[0], "r") as file:
            return _hdf5_to_dict(file)
    except Exception as e:
        print(f"处理文件时发生错误: {str(e)}")
        return None

def get_result_time(target_id:str, root_dir='results'):
    """给定 ID 和根目录，找到对应的 HDF5 文件，并返回其创建时间。"""
    script_dir = os.path.abspath(sys.argv[0])  # 获取当前脚本的绝对路径
    work_dir = os.path.dirname(os.path.dirname(script_dir))  # 获取当前脚本所在路径的上两层路径
    # 将该路径与 root_dir 拼接来得到 root——dir 的绝对路径
    root_dir = os.path.join(work_dir, root_dir)
    paths = _find_files_with_id(target_id, root_dir)
    if not paths:
        print(f"No files found with ID {target_id}")
        return None  # 如果没有找到文件，返回 None

    # 获取文件的创建时间（或者文件元数据更改时间）
    creation_time = os.path.getctime(paths[0])
    # 将创建时间转换为可读格式
    formatted_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(creation_time))
    return formatted_time

def _pad_id(target_id, total_length=9):
    """补全 ID 到指定长度。"""
    return target_id.zfill(total_length)


def _find_files_with_id(target_id, root_dir):
    """在指定路径下寻找满足条件的 h5 文件"""
    paths = []
    padded_id = _pad_id(target_id)  # 补全 ID
    for root, _, files in os.walk(root_dir):
        for file in files:
            if file.startswith(padded_id):
                paths.append(os.path.join(root, file))
    return paths


def _hdf5_to_dict(hdf5_obj):
    """递归转换 HDF5 对象到字典。"""
    result = {}
    for key in hdf5_obj.keys():
        if isinstance(hdf5_obj[key], h5py.Dataset):  # 如果是数据集，直接读取数据
            result[key] = hdf5_obj[key][()]
        else:  # 如果是组，递归调用
            result[key] = _hdf5_to_dict(hdf5_obj[key])
    return result


if __name__ == "__main__":
    # 使用示例
    target_id = "24"
    result = get_result(target_id)
    pprint(result)
