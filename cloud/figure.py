
import os
from datetime import datetime
def save_plot(fig, base_folder='figure', base_filename='plot'):
    """
    保存图像到指定的'figure'文件夹下的日期文件夹，并在文件名后添加时间戳。
    
    参数:
    fig (matplotlib.figure.Figure): 要保存的matplotlib图像对象
    base_folder (str, 可选): 保存图像的根文件夹名称。默认值为'figure'。
    base_filename (str, 可选): 图像文件的基本名称。默认值为'plot'。
    """
    # 获取当天日期并格式化为字符串

    
    today_date = datetime.now().strftime('%Y-%m-%d')
    
    # 构建完整的文件夹路径
    folder_path = os.path.join(base_folder, today_date)

    # 检查文件夹是否存在，如果不存在则新建文件夹
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

    # 获取当前时间并格式化为时间戳字符串
    timestamp = datetime.now().strftime('%H-%M-%S')

    # 图像文件保存路径，包含时间戳
    file_path = os.path.join(folder_path, f'{base_filename}_{timestamp}.png')

    # 保存图像
    fig.savefig(file_path)

    print(f'图像已保存到: {file_path}')