from scipy.optimize import fsolve
from scipy.constants import m_u, hbar
import numpy as np
def equilibrium_equation(u):
    '''
    用于求离子链平衡方程的值, 以供迭代求解平衡位置

    输入： 离子链无量纲位置 u: 1*n 的向量
    输出： n个平衡方程的值: 1*n 向量, 输出为0向量时对应输入为平衡位置。
    '''
    #初始化数据
    n = len(u)
    e = np.zeros(n)

    #依次计算n个方程的值
    for i_equation in range(n):
        for i_tern in range(n):
            if i_tern == i_equation:
                e[i_equation] += u[i_tern]
            elif i_tern < i_equation:
                e[i_equation] += -1/(u[i_tern]-u[i_equation])**2
            elif i_tern > i_equation:
                e[i_equation] += 1/(u[i_tern]-u[i_equation])**2
    return np.array(e)

def solve_equilibrium(n):
    '''
    求解离子链平衡位置， 通过求解平衡方程=0 ,使用 scipy.optimize.fsolve

    输入: n 离子数量,整数
    输出: u0  离子平衡位置,长度为n的np.array
    '''
    u = np.arange(n)/n**0.58
    u = u - np.average(u)
    u0 = fsolve(equilibrium_equation, u)
    
    return u0

def mode_x(n,beta): 
    '''
    求解径向模式
    输入:   n 离子数量
            beta 阱参数omega_x/omega_z
    '''
    
    A = np.zeros((n,n)) #用于存放矩阵
    u = solve_equilibrium(n)#求解平衡位置
    for i in range(0,n): #计算矩阵元
        A[i][i] =  beta**2
        for j in range(0,n):
            if i != j:
                A[i][j]  =  1/abs(u[i]-u[j])**3
                A[i][i] += -1/abs(u[i]-u[j])**3
    
    #计算矩阵的本征值和本征向量
    eigenval,eigenvec = np.linalg.eig(A)
    index = np.argsort( eigenval)
    eigenval = eigenval[index]
    eigenvec = eigenvec.T[index].T
    
    return eigenval,eigenvec
def mode_z(n):
    '''
    求解轴向模式
    输入:   n 离子数量
    '''
    
    A = np.zeros((n,n)) #用于存放矩阵
    u = solve_equilibrium(n)#求解平衡位置
    for i in range(0,n): #计算矩阵元
        A[i][i] =  1
        for j in range(0,n):
            if i != j:
                A[i][j]  =  -2/abs(u[i]-u[j])**3
                A[i][i] += 2/abs(u[i]-u[j])**3
    
    #计算矩阵的本征值和本征向量
    eigenval,eigenvec = np.linalg.eig(A)
    index = np.argsort( eigenval)
    eigenval = eigenval[index]
    eigenvec = eigenvec.T[index].T
    return eigenval,eigenvec

def ms_parameters(f_strench,f_COM,rate,K=1):
    '''
    计算MS门的参数
    '''
    w_k = np.array([f_strench,f_COM])* 2*np.pi # 声子频率
    m_171 = 171*m_u     #离子质量
    wavelength= 532e-9  #操作光波长

    # phase_laser 用于描述激光相位：[[phi_red_0,phi_blue_0],[phi_red_1,phi_blue_1]]
    phase_laser = [[0.0*np.pi,0*np.pi],[0*np.pi,0*np.pi]]

    #Lamb-Dicke系数
    x0_k= np.sqrt(hbar/(2*m_171*w_k))       
    eta_k= 2*2*np.pi/wavelength *x0_k

    # 运动模式计算
    e_x,v_x = mode_x(2,2)
    b_jk = v_x
    g_jk = b_jk*eta_k

    # 频率设置
    # rate为两声子模式失谐比（可正可负）
    delta=(w_k[1]-w_k[0])/(rate+1)
    mu = w_k[0]+delta
 
    # 门时间计算
    tau = 2*np.pi/abs(delta)*K

    # 拉比频率计算

    temp = 0
    c_phase = np.cos((phase_laser[0][0]-phase_laser[0][1])/2-(phase_laser[1][0]-phase_laser[1][1])/2)
    for i in range(2):
        temp += g_jk[0][i]*g_jk[1][i]*(np.sin((mu-w_k[i])*tau)/(mu-w_k[i])-tau)/(2*(mu-w_k[i]))*c_phase
    Rabi_theory = np.sqrt(np.pi/abs(4*temp))
    # print("Rabi: 2pi*",Rabi_theory/2/np.pi,"Hz")
    

    return delta,tau,Rabi_theory