import numpy as np
#Import Qiskit classes
# qiskit = 1.3.2
# qiskit-experiments = 0.8.1
from qiskit import  QuantumCircuit
from qiskit_experiments.library import StandardRB,InterleavedRB
from qiskit.compiler import transpile
from qiskit.quantum_info import Clifford

class CircuitGenerator():
    def __init__(self,length = 0) :
        self.circuit_length = length

    def set_length(self,length):
        self.circuit_length = length

    def random_generate_1qubit(self):
        # 创建一个单比特的RB实验
        rb_exp = StandardRB(physical_qubits=[0], lengths=[self.circuit_length], num_samples=1)
        # 生成RB线路
        rb_circuit = rb_exp.circuits()[0]  # 获取第一个RB线路
        qc = transpile(rb_circuit, basis_gates=['rx', 'ry', 'cx'], optimization_level=3)
        circuit=[]
        for gate in qc:
            if gate.operation.name in ['rx','ry'] :
                n = int(gate.operation.params[0]*2.01/np.pi)
                for i in range(abs(n)):
                    if n > 0:
                        circuit.append(gate.operation.name)
                    else:
                        circuit.append('-'+gate.operation.name)
        return circuit
    def random_generate_2qubit(self):
        # 创建一个单比特的RB实验
        rb_exp = StandardRB(physical_qubits=[0], lengths=[self.circuit_length], num_samples=1)
        # 生成RB线路
        rb_circuit = rb_exp.circuits()[0]  # 获取第一个RB线路

        # 找到最后第二个 barrier 的索引
        barrier_indices = [i for i, instruction in enumerate(rb_circuit) if instruction.operation.name == "barrier"]
        if len(barrier_indices) > 1:
            last_barrier_index = barrier_indices[-2]
            # 丢弃最后第二个 barrier 之后的部分
            truncated_circuit = QuantumCircuit(1)
            for instruction in rb_circuit[:last_barrier_index+1]:
                truncated_circuit.append(instruction.operation, instruction.qubits)
        else:
            truncated_circuit = rb_circuit  # 如果没有两个 barrier，则保留整个线路

        qc = transpile(truncated_circuit, basis_gates=['rx', 'ry', 'cx'], optimization_level=3)
        print(qc)
        circuit = []
        circuit2qubits_no_barrier = QuantumCircuit(2)
        circuit2qubits = QuantumCircuit(2)
        for instruction in qc:
            if instruction.name == "barrier":
                circuit.append("ms")
                circuit2qubits.rxx(np.pi/2, 0, 1)
                circuit2qubits_no_barrier.rxx(np.pi/2, 0, 1)
                circuit2qubits.append(instruction.operation, [0])
                circuit2qubits.append(instruction.operation, [1])
            else:
                n = int(instruction.operation.params[0]*2.01/np.pi)
                for i in range(abs(n)):
                    if n > 0:
                        circuit.append(instruction.operation.name)
                    else:
                        circuit.append('-'+instruction.operation.name)
                circuit2qubits.append(instruction.operation, [0])
                circuit2qubits.append(instruction.operation, [1])
                circuit2qubits_no_barrier.append(instruction.operation, [0])
                circuit2qubits_no_barrier.append(instruction.operation, [1])
        qc = transpile(circuit2qubits_no_barrier, basis_gates=['rx', 'ry', 'rxx'], optimization_level=3).inverse()
        result_circuit = circuit2qubits.compose(qc)
        print(result_circuit)
        for gate in qc:
            if gate.operation.name in ['rx', 'ry']:
                circuit.append([gate.operation.name, qc.find_bit(gate.qubits[0]).index, gate.operation.params[0]])
            if gate.operation.name in ['rxx']:
                circuit.append("ms")
        return circuit


def generate_rb1(length:int):
    # 创建一个单比特的RB实验
    rb_exp = StandardRB(physical_qubits=[0], lengths=[length], num_samples=1)
    # 生成RB线路
    rb_circuit = rb_exp.circuits()[0]  # 获取第一个RB线路
    qc = transpile(rb_circuit, basis_gates=['rx', 'ry', 'cx'], optimization_level=3)
    return qc

from cloud.RB_transpile import (
    SplitRaPi,
    RaaRbRbToRbRbRcc,
    SortRaaZYX,
    MergeRaa,
    RaaPi2RaRa,
    RzztoRxx
)
from qiskit.transpiler import PassManager
from qiskit.circuit.library import RXXGate, RYYGate, RZZGate,RXGate,RYGate,RZGate

def get_rb2_from_rb1(rb_circuit:QuantumCircuit):

    barrier_indices = [i for i, instruction in enumerate(rb_circuit) if instruction.operation.name == "barrier"]
    if len(barrier_indices) < 2:
        # 如果 barrier 数量不足，抛出明确的异常
        raise ValueError(
            f"Expected at least 2 barriers, but found {len(barrier_indices)}. "
            "The circuit must contain at least 2 barriers to split into Clifford and inverse parts."
        )

    last_barrier_index = barrier_indices[-2]
    # 以倒数第二个 barrier 为分界，分割 Clifford线路与逆操作
    clifford_circuit = QuantumCircuit(1)
    for instruction in rb_circuit[:last_barrier_index+1]:
        clifford_circuit.append(instruction.operation, instruction.qubits)


    #有barrier版本用于输出，无barrier版本用于编译
    rb2_circuit = QuantumCircuit(2)
    rb2_circuit_no_barrier = QuantumCircuit(2)

    for instruction in clifford_circuit:
        if instruction.name == "barrier":
            rb2_circuit.rxx(np.pi/2, 0, 1)
            rb2_circuit_no_barrier.rxx(np.pi/2, 0, 1)
            rb2_circuit.append(instruction.operation, [0])
            rb2_circuit.append(instruction.operation, [1])
        else:
            rb2_circuit.append(instruction.operation, [0])
            rb2_circuit.append(instruction.operation, [1])
            rb2_circuit_no_barrier.append(instruction.operation, [0])
            rb2_circuit_no_barrier.append(instruction.operation, [1])
    # 编译RB2： 拆分Ra，移动Raa到线路最后，Raa按照ZYX顺序排序，合并Raa
    pm = PassManager([SplitRaPi(),RaaRbRbToRbRbRcc(),SortRaaZYX(),MergeRaa()])
    optimized_qc1 = pm.run(rb2_circuit_no_barrier)
    # 将RaaPi拆分成RaPiRaPi，再进行一轮编译
    pm = PassManager([RaaPi2RaRa(),SplitRaPi(),RaaRbRbToRbRbRcc(),SortRaaZYX()])
    optimized_qc2 = pm.run(optimized_qc1)

    #将编译后的RB2拆成单比特和两比特两个线路，单比特用qiskit自带的transpile进行编译
    rb2_2qubit = QuantumCircuit(2)
    rb2_1qubit = QuantumCircuit(2)
    for instruction in optimized_qc2:
        if instruction.name in ["rxx","ryy","rzz"]:
            rb2_2qubit.append(instruction.operation, [0,1])
        if instruction.name in ["rx","ry","rz"]:
            rb2_1qubit.append(instruction.operation, instruction.qubits)
    rb2_1qubit_tanspiled = transpile(rb2_1qubit, basis_gates=['rx', 'ry', 'rxx'], optimization_level=3)

    #合并单比特和两比特线路，并取逆
    inverse_circuit = rb2_1qubit_tanspiled.compose(rb2_2qubit).inverse()

    #在RB2末尾加上inversed_circuit,生成最终的实验使用的RB线路
    combined_circuit = rb2_circuit.compose(inverse_circuit)

    # 处理Rzz:若后面有单比特门，则移动一下变成Ryy或Rxx，若没有，则用RyRxxR-y替换
    dict_zz = {
        "rzz": {"rx": RYYGate, "ry": RXXGate, "rz": RZZGate},
    }
    pm = PassManager([SplitRaPi(),RaaRbRbToRbRbRcc(dict_zz),RzztoRxx()])
    final_circuit = pm.run(combined_circuit)
    # final_qc = combined
    return final_circuit
def get_circuit_from_rb2(qc:QuantumCircuit):
    circuit = []
    for instruction in qc:
        if (instruction.operation.name in ['rx', 'ry', 'rxx', 'ryy'] and
                0 in [qc.qubits.index(q) for q in instruction.qubits]):
            n = int(instruction.operation.params[0] * 2.01 / np.pi)
            for i in range(abs(n)):
                if n > 0:
                    circuit.append(instruction.operation.name)
                else:
                    circuit.append('-' + instruction.operation.name)
        if (instruction.operation.name) in ["barrier"]:
            circuit.append("Idle")
    return circuit


if __name__ == "__main__":
    cg = CircuitGenerator(10)
    print(cg.random_generate_2qubit())