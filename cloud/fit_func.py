import numpy as np
import pandas as pd
from scipy.optimize import curve_fit,minimize
from scipy.signal import find_peaks, savgol_filter
from scipy.signal.windows import blackman<PERSON><PERSON>
from scipy.special import eval_genlaguerre

import numpy as np
from scipy.optimize import curve_fit
from scipy.signal import savgol_filter, find_peaks


################################## 拟合命令 ############################################
def decay_fit(t, y):
    """
    拟合指数衰减曲线 y = A * exp(-x / tau) + B，并返回相干时间 tau。
    假设数据从 1 衰减到 0，初始猜测 A=1, B=0, tau=1。

    参数:
        x_data (array-like): x 轴数据（时间/自变量）
        y_data (array-like): y 轴数据（信号值）

    返回:
        tau (float): 相干时间（衰减率的倒数）
        popt (array): 拟合参数 [A, tau, B]
        pcov (array): 参数的协方差矩阵
    """
    # 定义指数衰减函数
    def exp_decay(x, A, tau, B):
        return A * np.exp(-x / tau) + B

    # 初始猜测 (A=1, tau=1, B=0)
    p0 = [1.0, 1.0, 0.0]

    # 进行拟合
    popt, pcov = curve_fit(exp_decay, t, y, p0=p0)

    # 提取相干时间 tau
    tau = popt[1]

    return tau, popt, pcov

def eta_fit(t,y,Omega0,nbar = 0,eta0 = 0.1):
    def fit_func(t,eta):
        N= 20
        n = np.arange(N).reshape(N, 1)
        # 向量化计算
        vec_laguerre = np.vectorize(eval_genlaguerre)
        omega = Omega0 * eta * (1 + n) ** 0.5 * np.exp(-eta ** 2) * vec_laguerre(n,1,eta ** 2)
        Pn = thermalPn(n, nbar)
        y = np.sum(Pn * np.sin(omega * t) ** 2,axis = 0)
        return y
    p0 = (eta0,)
    popt, pcov = curve_fit(fit_func,t,p0 = p0)
    return popt[0]

# def Rabi_fit(t,y):
#     amp = y.max()-y.min()
#     a0 = y.mean()
#     data_fft = abs(np.fft.fft(y))
#     pos = max(enumerate(data_fft[1:int(len(t) / 2)]), key=lambda x: x[1])[0]
#     xmin = t[0]
#     xmax = t[-1]
#     xscale = xmax - xmin
#     freq = (pos + 1)/xscale
#
#     p0 = (1/2,  freq, 1/2, 1.5*np.pi, 1000) # amp, freq, a0, phi0,tau
#     # ,bounds=([0.4,0.4,0,0,100],[0.5,np.inf,0.6,np.inf,np.inf])
#     popt, pcov = curve_fit(Sin,t,y,p0 =p0,maxfev = 10000)
#
#     t_fit = np.linspace(t[0],t[-1],1001)
#     y_fit = Sin(t_fit,popt[0],popt[1],popt[2],popt[3],popt[4])
#     rabi_time = abs(round(1/popt[1]/1e6,9))
#
#     return rabi_time,t_fit, y_fit,popt


# def Rabi_fit(t, y):
#     """
#     改进版的拉比振荡拟合函数（保持原始输入输出接口）
#
#     参数:
#         t : array_like - 时间点数组
#         y : array_like - 信号值数组
#
#     返回:
#         rabi_time : float - 拉比时间(μs)
#         t_fit : ndarray - 拟合时间点
#         y_fit : ndarray - 拟合曲线
#         popt : ndarray - 拟合参数 [amp, freq, a0, phi0, tau]
#     """
    # # 1. 数据预处理（去NaN+基线校正）
    # valid_mask = np.isfinite(y)
    # t_clean, y_clean = t[valid_mask], y[valid_mask]
    #
    # try:
    #     baseline = savgol_filter(y_clean, window_length=min(51, len(y_clean)//2*2+1), polyorder=3)
    #     y_corrected = y_clean - baseline
    # except:
    #     y_corrected = y_clean.copy()
    #
    # # 2. 初始参数估计（鲁棒方法）
    # amp_est = np.percentile(y_corrected, 95) - np.percentile(y_corrected, 5)
    # a0_est = np.median(y_corrected)
    #
    # # 频率估计（FFT+峰值检测）
    # fft_vals = np.abs(np.fft.fft(y_corrected * np.hanning(len(y_corrected))))
    # freqs = np.fft.fftfreq(len(y_corrected), d=np.diff(t_clean).mean())
    # pos = np.argmax(fft_vals[(freqs > 0) & (freqs < 1e8)])  # 限制合理频率范围
    # freq_est = freqs[freqs > 0][pos]
    #
    # # 3. 定义拟合函数（与原始保持一致）
    # def Sin(t, amp, freq, a0, phi0, tau):
    #     return a0 + amp * np.exp(-t/tau) * np.sin(2*np.pi*freq*t + phi0)
    #
    # # 4. 带约束的拟合
    # p0 = (
    #     amp_est/2,
    #     max(freq_est, 1e3),  # 防止频率为0
    #     a0_est,
    #     1.5*np.pi,
    #     (t_clean[-1]-t_clean[0])*2
    # )
    #
    # bounds = (
    #     [0, 1e3, -np.inf, 0, 0],           # 下限
    #     [amp_est*2, np.inf, np.inf, 2*np.pi, np.inf]  # 上限
    # )
    #
    # try:
    #     popt, pcov = curve_fit(
    #         Sin, t_clean, y_corrected,
    #         p0=p0, bounds=bounds, maxfev=10000
    #     )
    # except:
    #     popt = p0  # 失败时回退到初始估计
    #
    # # 5. 生成与原始函数完全一致的输出
    # t_fit = np.linspace(t[0], t[-1], 1001)
    # y_fit = Sin(t_fit, *popt)
    # rabi_time = abs(round(1/popt[1]/1e6, 9))  # 保留9位小数
    #
    # return rabi_time, t_fit, y_fit, popt

# def Rabi_fit(t,y):
#     amp = y.max()-y.min()
#     a0 = y.mean()
#     data_fft = abs(np.fft.fft(y))
#     pos = max(enumerate(data_fft[1:int(len(t) / 2)]), key=lambda x: x[1])[0]
#     xmin = t[0]
#     xmax = t[-1]
#     xscale = xmax - xmin
#     freq = (pos + 1)/xscale
#
#     p0 = (1/2,  freq, 1/2, 1.5*np.pi, 1000) # amp, freq, a0, phi0,tau
#     # ,bounds=([0.4,0.4,0,0,100],[0.5,np.inf,0.6,np.inf,np.inf])
#     popt, pcov = curve_fit(Sin,t,y,p0 =p0,maxfev = 10000)
#
#     t_fit = np.linspace(t[0],t[-1],1001)
#     y_fit = Sin(t_fit,popt[0],popt[1],popt[2],popt[3],popt[4])
#     rabi_time = abs(round(1/popt[1]/1e6,9))
#
#     return rabi_time,t_fit, y_fit,popt


def Rabi_fit(t, y):
    """
    无衰减Rabi拟合，接口不变
    """
    # 平滑减少高频噪声
    if len(y) > 11:
        y_smooth = savgol_filter(y, 11, 3)
    else:
        y_smooth = y.copy()
    t = np.asarray(t)
    y_smooth = np.asarray(y_smooth)

    # 估算幅度、偏置
    amp = (y_smooth.max() - y_smooth.min()) / 2
    a0 = y_smooth.mean()

    # FFT估主频（单位Hz）
    dt = np.mean(np.diff(t))
    if not np.allclose(np.diff(t), dt, rtol=1e-2):
        t_uniform = np.linspace(t[0], t[-1], len(t))
        y_uniform = np.interp(t_uniform, t, y_smooth)
        y_fft = y_uniform
        dt = (t[-1] - t[0]) / (len(t) - 1)
    else:
        y_fft = y_smooth

    fft_freq = np.fft.fftfreq(len(y_fft), d=dt)
    fft_data = np.abs(np.fft.fft(y_fft))
    mask = (fft_freq > 0)
    idx = np.argmax(fft_data[mask])
    freq_guess = fft_freq[mask][idx]
    phi0_guess = 0

    p0 = (amp, freq_guess, a0, phi0_guess)
    bounds = (
        [0, freq_guess * 0.3, y_smooth.min(), -2 * np.pi],
        [amp * 2, freq_guess * 3, y_smooth.max(), 2 * np.pi],
    )
    def _model(t, amp, freq, a0, phi0):
        """无衰减的Rabi振荡模型"""
        return amp * np.cos(2 * np.pi * freq * t + phi0) + a0
    try:
        popt, pcov = curve_fit(
            _model, t, y, p0=p0, bounds=bounds, maxfev=10000
        )
    except Exception:
        popt = np.array([amp, freq_guess, a0, phi0_guess])

    t_fit = np.linspace(t[0], t[-1], 1001)
    y_fit = _model(t_fit, *popt)
    rabi_time = abs(round(1 / popt[1] / 1e6, 9)) if popt[1] > 0 else None
    print(f"popt: {popt}")
    return rabi_time, t_fit, y_fit, popt


def Rabi_fit_multymode(t,y):
    #定义拟合函数
    def Pt(t,A,B,P0):
        return (1-(1-(A*(B*t)**2)*np.cos(B*t)))/2+ P0
    #计算拟合初始值
    A0 = 0,

    #FT计算拟合初始频率
    data_fft = abs(np.fft.fft(y))
    pos = max(enumerate(data_fft[1:int(len(t) / 2)]), key=lambda x: x[1])[0]
    xmin = t[0]
    xmax = t[-1]
    xscale = xmax - xmin
    freq = (pos + 1) / xscale
    B0 = freq

    P0 = 0
    p0 = (A0,B0,P0)
    #curve_fit拟合
    popt, pcov = curve_fit(Pt,t,y,p0 = p0,bounds=([0,0,0],[np.inf,np.inf,1]))
    A,B,P_0 = popt
    return A,B,P_0

def Parity_fit(theta,y):
    amp = y.max()-y.min()
    a0 = y.mean()
    freq = 1/np.pi
    p0 = (amp, freq,  a0, 0) # amp, freq, a0, phi0
    popt, pcov = curve_fit(Sin_parity,theta,y,p0 =p0)

    theta_fit = np.linspace(theta[0],theta[-1],1001)
    y_fit = Sin_parity(theta_fit,popt[0],popt[1],popt[2],popt[3])
    contrast = abs(popt[0])
    return contrast, theta_fit, y_fit ,popt

def Parity_fit_MLE(x, y):
    x_data = x  # 假设x和y已经定义
    y_data = y
    r = 100

    # 定义拟合函数形式
    def model(x, a, b, c):
        return a * np.sin(2 * x + b) + c

    # 定义负对数似然函数
    def neg_log_likelihood(params, x, y, r):
        a, b, c = params
        p = model(x, a, b, c)
        p = np.clip(p, 1e-10, 1 - 1e-10)  # 确保p在(0,1)范围内，避免log(0)或log(1)
        # 计算负对数似然
        return -np.sum(y * r * np.log(p + 1e-10) + (r - y * r) * np.log(1 - p + 1e-10))

    # 初始猜测
    initial_guess = [0.95, 0, 0.01]

    # 使用scipy的minimize函数进行优化
    # 尝试不同的优化方法，直到找到成功的方法
    methods = ['Powell']
    result = None

    for method in methods:
        try:
            result = minimize(neg_log_likelihood, initial_guess, args=(x_data, y_data, r),
                              method=method, options={'disp': True, 'maxiter': 10000, 'tol': 1e-8})
            if result.success:
                print(f"优化成功，使用的方法是：{method}")
                break
        except Exception as e:
            print(e)
            continue

    if result:  # and result.success:
        a_est, b_est, c_est = result.x
        print(f"拟合结果: a = {a_est:.4f}, b = {b_est:.4f}, c = {c_est:.4f}")
        theta_fit = np.linspace(x[0], x[-1], 1001)
        y_fit  = model(theta_fit, a_est, b_est, c_est)
        contrast = abs(a_est*2)
        popt = [a_est, b_est, c_est]
        return contrast, theta_fit, y_fit ,popt
    else:
        print("优化失败，请检查数据和模型设置。")



def Ramsey_fit(t,y):
    amp = y.max()-y.min()
    a0 = y.mean()
    data_fft = abs(np.fft.fft(y))
    pos = max(enumerate(data_fft[1:int(len(t) / 2)]), key=lambda x: x[1])[0]
    xmin = t[0]
    xmax = t[-1]
    xscale = xmax - xmin
    freq = (pos + 1)/xscale

    p0 = (amp,  freq, a0, np.pi/2, 100000) # amp, freq, a0, phi0
    popt, pcov = curve_fit(Sin,t,y,p0 =p0,bounds=([0,0,-np.inf,-np.inf,0],[1,np.inf,np.inf,np.inf,np.inf]))
    t_fit = np.linspace(t[0],t[-1],1001)
    y_fit = Sin(t_fit,popt[0],popt[1],popt[2],popt[3],popt[4])
    detuning = abs(round(popt[1]*1e6,1))
    coherrence_time = popt[4]*1e-6
    return detuning,coherrence_time,t_fit, y_fit


def Rabi_fit_thermal(t,y,nbar=4,eta = 0.1233135,r0=0.002,callfunc='Carrier',callprint = True):
    '''
    对实验数据进行拟合，使用热态声子态的Rabi振荡拟合Carrier Rabi,得到声子数
    
    Inputs:
        y = 1d array Rabi_Counts
        t = 1d array Time
        r0: it is fine to use 0.002
    Outputs:
        popt2 for decay fit, popt3 for themal state fit
    '''
    # 1.首先对数据进行初步分析得到initial guess
    init_guess = Rabi_sin_init_guess(t,y)

    T = init_guess[0]*2.0  # Rabitime = 2pi time
    Omega0 = 2*np.pi/T  #曲线振荡频率
    DecayT=5*T  #曲线decay时间tao
    t0 = 0  #曲线起始点偏移，默认为0即可

    contrast = init_guess[1]  #振荡曲线的对比度
    Curveoffset=init_guess[2]  #振荡曲线的偏置
    
    # 2. 使用指数decay的sin函数拟合，得到更精确的
    popt2 = np.ones(5)
    pcov2 = np.diag(np.ones(5))
    perr2 = np.sqrt(np.diag(pcov2))
    try:
        print ('decay')
        #param_bounds=([-inf,100,1,0.1,0,0],[inf,1000000,100,2,1,1])
        params_0 = [t0,Omega0,DecayT,contrast,Curveoffset]  # t0,Omega0,DecayT,contrast,Curveoffset
        popt2,pcov2 = curve_fit(lambda t, *p_0: exp_decay(t, p_0), t, y, p0=params_0)#,bounds=param_bounds
        perr2 = np.sqrt(np.diag(pcov2))
        t0=popt2[0]
        Omega0=popt2[1]
        contrast = popt2[3]  #振荡曲线的对比度
        Curveoffset=popt2[4]  #振荡曲线的偏置     
    except:
        pass
    
    # 3. 拟合声子数
    if callfunc == 'carrier':fitfunc=Carrier_fit_func
    elif callfunc == 'blue':fitfunc=BSB_fit_func
    elif callfunc == 'red':fitfunc=RSB_fit_func
        
    params_0 = [t0,Omega0, nbar,contrast,Curveoffset,r0]  # [t0,Omega0,nbar,contrast,Curveoffset]
    
    popt3, pcov3 = curve_fit(lambda t, *p_0: fitfunc(t, eta, p_0), t, y, p0=params_0)#,bounds=param_bounds
    perr3 = np.sqrt(np.diag(pcov3))
    

    #4. print 结果
    fitting_result = pd.DataFrame(['%.3f±%.3f'%(2*np.pi/popt3[1],2*np.pi/perr3[1]),'%.3f±%.3f'%(popt2[2],perr2[2])]+
                                  ['%.3f±%.3f'%(popt3[ii],perr3[ii]) for ii in range(len(popt3))]).T
    fitting_result.columns=['Rabitime_us','Decay time','t0','Omega0', 'nbar', 'contrast','Curveoffset','r0']
    
#     fitting_result=['Rabitime_us:','%.3f'%(2*np.pi/popt3[1]),'Decay time','%.3f'%popt2[2]]
#     item_names= ['t0:','Omega0:', 'nbar:', 'contrast:','Curveoffset:','r0']
#     for ii,item in enumerate(popt3):
#         fitting_result.append(item_names[ii])
#         fitting_result.append('%.3f±%.3f'%(popt3[ii],perr[ii]))
    t_fit = np.linspace(t[0],t[-1],1001)
    y_fit = fitfunc(t_fit,eta,popt3)
    rabi_time = np.pi*2/popt3[1]*1e-6
    return rabi_time,t_fit,y_fit,popt3
def Rabi_Blue_fit(t,y):
    def BSB(x,eta_Omega0,nbar):
        N = 20
        n = np.arange(N).reshape(N,1)
        omega = eta_Omega0 * (1+n)**0.5
        Pn = thermalPn(n,nbar)

        yn = Pn*(1-np.cos(omega/2*x)**2)
        y = np.sum(yn,axis=0)
        return y
    y = np.array(y)
    pi2_time = x[np.argmax(y)]
    etaOmega0 = np.pi/2/pi2_time
    nbar0 = 0
    p0 = (etaOmega0,nbar0)
    popt,pcov = curve_fit(BSB,x,y,p0=p0,bounds=([0,0],[np.inf,np.inf]))
    t_fit = np.linspace(t[0],t[-1],1001)
    y_fit = BSB(t_fit,popt[0],popt[1])
    return popt,t_fit,y_fit
def Peak_fit(x,y, only_highest = False):
    y = y.T
    fit_params = []
    peak_list = []
    for y_i in y:    
        peaks, properties = find_peaks(y_i,height = 0)
        if only_highest:
            highest_peak_index = peaks[np.argmax(properties['peak_heights'])]
            highest_peak_height = y_i[highest_peak_index]

            peak_region = np.arange(highest_peak_index-5,highest_peak_index+6)
            peak_region = peak_region[(peak_region >=0 )&(peak_region < len(y_i))]

            y_peak = y_i[peak_region]
            x_peak = x[peak_region]

            p0 = [x[highest_peak_index],1,highest_peak_height]
            try:
                popt,_ = curve_fit(lorentzian,x_peak,y_peak, p0 = p0)
                fit_params.append(popt)
                peak_list.append(popt[0])
            except:
                print("拟合失败")
                fit_params.append(None)
                peak_list.append(x[highest_peak_index])
        else:
            for peak_index in peaks:
                peak_region = np.arange(peak_index-5,peak_index+6)
                peak_region = peak_region[(peak_region >=0 )&(peak_region < len(y_i))]

                y_peak = y_i[peak_region]
                x_peak = x[peak_region]
                p0 = [x[peak_index],1,y_i[peak_index]]


                try:
                    popt,_ = curve_fit(lorentzian,x_peak,y_peak, p0 = p0)
                    fit_params.append(popt)
                    peak_list.append(popt[0])
                except:
                    print("拟合失败")
                    fit_params.append(None)
                    peak_list.append(x[peak_index])
    return peak_list
def AOD_scan_fit(x,y,fit_range = 10):
    y = y.T
    fit_params = []
    peak_list = []
    for y_i in y:    
        peaks, properties = find_peaks(y_i,height = 0.2)
        highest_peak_index = peaks[np.argmax(properties['peak_heights'])]
        highest_peak_height = y_i[highest_peak_index]

        peak_region = np.arange(highest_peak_index-fit_range,highest_peak_index+fit_range+1)
        peak_region = peak_region[(peak_region >=0 )&(peak_region < len(y_i))]

        y_peak = y_i[peak_region]
        x_peak = x[peak_region]

        p0 = [x[highest_peak_index],1,highest_peak_height]
        try:
            popt,_ = curve_fit(lorentzian,x_peak,y_peak, p0 = p0,
                               bounds=([0,0,0],[np.inf,np.inf,np.inf]))
            fit_params.append(popt)
            peak_list.append(popt[0])
        except:
            print("拟合失败")
            fit_params.append(None)
            peak_list.append(x[highest_peak_index])
    return peak_list
def Spectrum_fit(x, y):
    fit_params = []
    peak_list = []
    peaks, properties = find_peaks(y, height=0.25)
    for peak_index in peaks:
        peak_region = np.arange(peak_index - 5, peak_index + 6)
        peak_region = peak_region[(peak_region >= 0) & (peak_region < len(y))]

        y_peak = y[peak_region]
        x_peak = x[peak_region]
        p0 = [x[peak_index], 1, y[peak_index]]

        try:
            popt, _ = curve_fit(lorentzian, x_peak, y_peak, p0=p0, bounds=([-np.inf,0,0],[np.inf,np.inf,np.inf]))
            fit_params.append(popt)
            peak_list.append(popt[0])
        except:
            print("拟合失败")
            fit_params.append(None)
            peak_list.append(x[peak_index])
    return peak_list

def Cross_fit(x,y,qubit_index=(0,1)):
    #计算两条线段的交叉点
    def calculate_cross(x1,x2,y11,y12,y21,y22):
        return (x2*(y11-y21)+x1*(y22-y12))/(y11-y21+y22-y12)
    cross = []
    q0=qubit_index[0]
    q1 = qubit_index[1]
    for i in range(len(x)-1):
        dy1 = y[i][q0]-y[i][q1]
        dy2 = y[i+1][q0]-y[i+1][q1]
        if dy1*dy2 <=0:
            x_cross = calculate_cross(x[i],x[i+1],y[i][q0],y[i+1][q0],y[i][q1],y[i+1][q1])
            cross.append(x_cross)
    return cross

def Cross_fit_Y(x,y,qubit_index=(0,1)):
    #计算两条线段的交叉点
    def calculate_cross(x1,x2,y11,y12,y21,y22):
        return (y11*y22-y12*y21)/(y11-y21+y22-y12)
    cross = []
    q0=qubit_index[0]
    q1 = qubit_index[1]
    for i in range(len(x)-1):
        dy1 = y[i][q0]-y[i][q1]
        dy2 = y[i+1][q0]-y[i+1][q1]
        if dy1*dy2 <=0:
            y_cross = calculate_cross(x[i],x[i+1],y[i][q0],y[i+1][q0],y[i][q1],y[i+1][q1])
            cross.append(y_cross)
    return cross

def MS_01_fit(x,y):
    t_g = abs(2*np.pi / x[y.argmin()])
    eta_omega = 0.1*30 # 默认Lamb-Dicke系数0.1,拉比30kHz，
    p0 = (t_g, eta_omega , 0) # t_gate,eta_omega,d_center
    popt, pcov = curve_fit(MS_p01,x,y,p0 =p0)

    detuning_fit = np.linspace(x[0],x[-1],1001)
    y_fit = MS_p01(detuning_fit,popt[0],popt[1],popt[2])

    return popt,detuning_fit, y_fit

#################################### 中间代码 #########################################
def Rabi_sin_init_guess(x,y):
    '''
    得到rabi拟合initial guess
    '''
    contrast = y.max()-y.min()
    ymean = y.mean()
    offset = ymean -0.5
    fy = np.fft.rfft((y-ymean)*blackmanharris(y.size))
    i = np.argmax(np.abs(fy))
    pi_time = y.size /2/i*(x[1]-x[0])
    # print ('pi_time:',pi_time,'contrast:',contrast,'offset:',offset)
    return pi_time,contrast,offset

def thermalPn(n,nbar):
    '''
    声子热态的概率分布
    '''
    return (nbar/(1.0+nbar))**n/(nbar+1.0)
def ThermalDist(t, t0, Omega0, nbar, eta):
    '''
    热态对应的载波rabi
    '''
    N = int(nbar*20)  #声子范围
    if N <20: 
        N = 20
    n = np.arange(N).reshape(N,1)
    omega = Omega0 * (1-eta**2*n)
    Pn = thermalPn(n,nbar)
    yn = Pn*np.sin(omega/2.0*(t-t0))**2
    return np.sum(yn,axis=0)
# Pn distribution
def Carrier_fit_func(t,eta,*args):
    '''
    热态下Carrier的Rabi拟合函数
    args:
    [t0,Omega0,nbar,contrast,Curveoffset,r0]
    '''
    t0     = args[0][0]
    Omega0 = args[0][1]
    nbar = args[0][2]
    contrast = args[0][3]
    Curveoffset = args[0][4]
    
    return contrast*ThermalDist(t, t0, Omega0, nbar, eta)+Curveoffset+(1-contrast)/2.0

def BSB_fit_func(t,eta,*args):
    '''
    热态下BSB的Rabi拟合函数
    args:
    [t0,Omega0,nbar,contrast,Curveoffset]
    '''

    t0     = args[0][0]
    Omega0 = args[0][1]
    nbar = args[0][2]
    contrast = args[0][3]
    Curveoffset = args[0][4]
    
    N = int(nbar*20)  #声子范围
    if N <20: 
        N = 20
    n = np.arange(N).reshape(N,1)
    omega = Omega0 * (1+n)**0.5
    Pn = thermalPn(n,nbar)
    r0 = 0.002
    rn=r0*(n+1)**0.7
    #https://journals.aps.org/prl/pdf/10.1103/PhysRevLett.76.1796  rn=r0(n+1)**0.7
    yn = 0.5*Pn*(1-contrast*np.exp(-(t-t0)*rn)*np.cos(omega*(t-t0)))
    
    return np.sum(yn,axis=0)
def RSB_fit_func(t,eta,*args):
    '''
    热态下RSB的Rabi拟合函数
    args:
    [t0,Omega0,nbar,contrast,Curveoffset]
    '''

    t0     = args[0][0]
    Omega0 = args[0][1]
    nbar = args[0][2]
    contrast = args[0][3]
    Curveoffset = args[0][4]
    
    N = int(nbar*20)  #声子范围
    if N <20: 
        N = 20
    n = np.arange(N).reshape(N,1)
    omega = Omega0 * (n)**0.5
    Pn = thermalPn(n,nbar)
    r0 = 0.002
    rn=r0*(n)**0.7
    #https://journals.aps.org/prl/pdf/10.1103/PhysRevLett.76.1796  rn=r0(n+1)**0.7
    yn = 0.5*Pn*(1-1*np.exp(-(t-0)*rn)*np.cos(omega*(t-0)))
    
    return np.sum(yn,axis=0)

####################################### 基本曲线 #############################################
def MS_p01(delta,t_gate,eta_Rabi_Raman,d_center): 

    alpha=eta_Rabi_Raman/2*(np.e**(1j*(delta-d_center)*t_gate)-1)/(1j*(delta-d_center))

    p_01 = 1/4*(1-np.e**(-abs(4*alpha)**2*(0.5)))

    return p_01
    

def exp_decay(t,*args):
    '''
    指数dicay的sin拟合函数
    args:
    [t0,Omega0,DecayT,contrast,offset]
    '''
    t0=args[0][0]
    Omega0 = args[0][1]
    DecayT = args[0][2]
    contrast = args[0][3]*np.exp(-(t-t0)/DecayT)
    Curveoffset = args[0][4]
    return (1-contrast)/2.+contrast*np.sin(Omega0/2.0*(t-t0))**2+Curveoffset


def Gaussian(x, mu, sigma, a, a0):
    return a * (1 / (sigma * np.sqrt(2 * np.pi))) * np.exp(-((x - mu)**2) / (2 * sigma**2)) + a0

# def Gaussian_double(x, mu_1, mu_2, sigma, a, a0): # 还是不应该用两个高斯拟合，模型仅用于确定光斑参数
#     return Gaussian(x, mu_1, sigma, a, a0) + Gaussian(x, mu_2, sigma, a, a0)

def Sin(x, amp, freq, a0, phi0,gamma):
    return amp*np.exp(-x/gamma)*np.sin(2*np.pi*freq*x + phi0) + a0

def Sin_parity(x, amp, freq, a0, phi0):
    return amp/2 * np.sin(2*np.pi* freq *x + phi0) + a0

def lorentzian(x,x0,gamma,A):
        return A*(gamma/((x-x0)**2+gamma**2))