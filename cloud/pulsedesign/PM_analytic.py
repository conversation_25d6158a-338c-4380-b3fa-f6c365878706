import numpy as np
from scipy.constants import m_u, hbar
from scipy.linalg import null_space
from scipy.optimize import minimize
from cloud.pulsedesign.pulse_design_function import *
from modules.config import LOADED_PARAMETER
import dill

def update_phi(phi_half,*args):
    paras, = args

    # 构建完整的 phi 序列，满足 phi[k] = -phi[N-k-1]
    phi = np.zeros(paras.N)
    half_len = len(phi_half)
    phi[:half_len] = phi_half
    phi[half_len:] = -phi_half[::-1]  # 对称性约束
    paras.phi = phi

    return paras

def compute_coefficients(t, omega, mu):
    """
    向量化计算 ∫ cos(μt-φ) cos(ωt) dt系数矩阵A和B，
    使得积分结果为 A*cos(phi) + B*sin(phi)

    参数:
        t : 长度为M的时间向量
        omega : 长度为K的频率向量
        mu : 固定频率参数

    返回:
        A : K x (M-1) 的矩阵 (cosphi的系数)
        B : K x (M-1) 的矩阵 (sinphi的系数)
    """
    t_i = t[:-1, np.newaxis]  # (M-1,1)
    t_ip1 = t[1:, np.newaxis]  # (M-1,1)
    w_j = omega[np.newaxis, :]  # (1,K)

    delta = mu - w_j  # (M-1,K)
    sum_freq = mu + w_j  # (M-1,K)

    # 计算A系数 (cosphi的系数)
    Am = (np.sin(delta * t_ip1) - np.sin(delta * t_i)) / (2 * delta)
    Ap = (np.sin(sum_freq * t_ip1) - np.sin(sum_freq * t_i)) / (2 * sum_freq)
    # Ap = 0
    A = Am + Ap
    # 计算B系数 (sinphi的系数)
    Bm = -(np.cos(delta * t_ip1) - np.cos(delta * t_i)) / (2 * delta)
    Bp = -(np.cos(sum_freq * t_ip1) - np.cos(sum_freq * t_i)) / (2 * sum_freq)
    # Bp = 0
    B = Bm + Bp
    return A.T, B.T


def compute_tcos_sin_coeffs(t, omega, mu):
    """
    计算 ∫ t cos(μt-φ) sin(ωt) dt 的系数矩阵A和B，结果为 A*cosφ + B*sinφ

    参数:
        t : 长度为M的时间向量
        omega : 长度为K的频率向量 (ω)
        mu : 固定频率参数 (μ)

    返回:
        A : (M-1) x K 的矩阵 (cosφ的系数)
        B : (M-1) x K 的矩阵 (sinφ的系数)
    """
    t_i = t[:-1, np.newaxis]  # (M-1,1)
    t_ip1 = t[1:, np.newaxis]  # (M-1,1)
    w = omega[np.newaxis, :]  # (1,K)

    # 计算中间变量
    k_p = mu + w  # μ+ω
    k_m = mu - w  # μ-ω

    # 预计算各项
    sin_p_ti = np.sin(k_p * t_i)
    sin_p_tip1 = np.sin(k_p * t_ip1)
    cos_p_ti = np.cos(k_p * t_i)
    cos_p_tip1 = np.cos(k_p * t_ip1)

    sin_m_ti = np.sin(k_m * t_i)
    sin_m_tip1 = np.sin(k_m * t_ip1)
    cos_m_ti = np.cos(k_m * t_i)
    cos_m_tip1 = np.cos(k_m * t_ip1)

    # 处理k_p=0和k_m=0的情况
    mask_p = np.isclose(k_p, 0)
    mask_m = np.isclose(k_m, 0)

    # 计算A系数 (cosφ的系数)
    termA_p = np.where(mask_p,
                       -0.25 * t_ip1 ** 2 + 0.25 * t_i ** 2,
                       -(t_ip1 * cos_p_tip1 - t_i * cos_p_ti) / k_p + (sin_p_tip1 - sin_p_ti) / k_p ** 2)

    termA_m = np.where(mask_m,
                       -0.25 * t_ip1 ** 2 + 0.25 * t_i ** 2,
                       (t_ip1 * cos_m_tip1 - t_i * cos_m_ti) / k_m - (sin_m_tip1 - sin_m_ti) / k_m ** 2)
    # termA_p = 0
    A = 0.5 * (termA_p + termA_m)

    # 计算B系数 (sinφ的系数)
    termB_p = np.where(mask_p,
                       0.25 * (t_ip1 ** 2 - t_i ** 2),
                       -(t_ip1 * sin_p_tip1 - t_i * sin_p_ti) / k_p - (cos_p_tip1 - cos_p_ti) / k_p ** 2)
    # termB_p = 0
    termB_m = np.where(mask_m,
                       0.25 * (t_ip1 ** 2 - t_i ** 2),
                       (t_ip1 * sin_m_tip1 - t_i * sin_m_ti) / k_m + (cos_m_tip1 - cos_m_ti) / k_m ** 2)

    B = 0.5 * (termB_p + termB_m)

    return A.T, B.T

class PulseDesigner:
    def __init__(self, ion_index, gate_time, N_ions, N_modulation, phonon_freq, drive_freq):
        self.ion_index = ion_index
        self.gate_time = gate_time
        self.N_ions = N_ions

        self.N_modulation = N_modulation
        self.t_modulation = self.gate_time / self.N_modulation

        self.omega = np.array(phonon_freq)* 2 * np.pi
        N_mu = int(drive_freq * gate_time)
        self.mu = (2*N_mu+1)*np.pi/gate_time

        self.null_space = self.calculate_null_space()
        self.eta = self.lamb_dick_parameter()
        self.paras = Parameters(
            self.omega *1e-6,
            self.mu *1e-6,
            self.N_modulation,
            self.gate_time * 1e6,
            np.zeros(self.N_modulation),
            np.zeros(self.N_modulation)+1.0,
            self.eta,
            self.ion_index
        )

        for i in range(5):
            A = 0
            for _ in range(10):
                phi_half = self.optimize_phi_half()
                A_phi = pst_area(phi_half,update_phi,self.paras)
                if A_phi > A:
                    A = A_phi
                    print("Area :",A)
                    self.phi = self.paras.phi
                if A >2 :
                    break

            Rabi = np.sqrt(abs(np.pi/2 / A)) * 1e6
            self.Rabi = Rabi
            total1 = pst_displacement(self.phi[:self.N_modulation // 2], update_phi, self.paras)
            total11 = pst_robust(self.phi[:self.N_modulation // 2], update_phi, self.paras)
            total2 = pst_area(self.phi[:self.N_modulation // 2], update_phi, self.paras)

            print("Phase space displacement :", total1)
            if total1 < 0.3:
                break

        print("Phase space displacement :", total1)
        print("Time average displacement:", total11)
        print("Area :", total2)
        print("Rabi Required: ",Rabi / 2 / np.pi)

    def lamb_dick_parameter(self):
        N = self.N_ions
        omega_p = self.omega
        _, x_mode = mode_x(N, 5)
        lambda_raman = 532e-9
        x0_k = np.sqrt(hbar / (2 * m_u * 171 * omega_p))
        eta_k = 2 * 2 * np.pi / lambda_raman * x0_k
        eta = x_mode * eta_k
        return eta

    def calculate_null_space(self,o0 = None,o1 = None):
        n_half = int(self.N_modulation / 2)
        tau = self.gate_time
        mu =self.mu
        if o0 is None:
            o0 = self.omega
        if o1 is None:
            o1 = self.omega

        self.o0 = o0
        self.o1 = o1

        t = np.linspace(0, tau, 2 * n_half + 1)
        t_half = t[:n_half + 1]
        A, B = compute_coefficients(t_half, o0, mu)
        M0 = np.hstack((A, B))
        M0 = M0

        A1, B1 = compute_tcos_sin_coeffs(t_half, o1 , mu)
        M1 = np.hstack((A1, B1))
        M1 = M1 / tau

        M = np.vstack((M0, M1))
        null_space_basis = null_space(M)
        return null_space_basis

    def calculate_xy(self,a):
        n_half = int(self.N_modulation / 2)
        XY = np.sum(a * self.null_space, axis=1)
        X = XY[:n_half]
        Y = XY[n_half:]
        D = X ** 2 + Y ** 2 - 1
        return D, X, Y

    def solve_phi(self,a):
        D,_,_ = self.calculate_xy(a)
        return sum(D**2)
    def optimize_phi_half(self):
        n_half = int(self.N_modulation / 2)
        init_a = np.random.uniform(0, 1, 2 * n_half - len(self.o0) - len(self.o1))

        result = minimize(self.solve_phi, init_a, )
        _, X, Y = self.calculate_xy(result.x)
        phi_half = np.arctan2(Y,X)[::-1]

        return phi_half

if __name__ == '__main__':
    parameter = LOADED_PARAMETER()
    qubit_index = (2,3)
    N_modulation = 28
    phonon_index = 0
    gate_frequency = parameter.Light_554.Motion_freq[phonon_index] -12000
    gate_time = 200e-6
    modulator = PulseDesigner(qubit_index,
                              gate_time,
                              len(parameter.Light_554.Motion_freq),
                              N_modulation,
                              parameter.Light_554.Motion_freq,
                              gate_frequency)
    parameter.Light_554.freq_detuning = modulator.mu/2/np.pi-parameter.Light_554.Motion_freq[phonon_index]

    print("detuning:",parameter.Light_554.freq_detuning)
    with open('PM_modulator.pkl', 'wb') as f:
        dill.dump(modulator, f)

    parameter.update_config_json()