try:
    from scipy.optimize import fsolve, brentq
    from scipy.constants import m_u, hbar
    from scipy import linalg
    from scipy.linalg import null_space
    import numpy as np
    import pickle
    from sympy import symbols, lambdify
    import os
    import inspect
    from collections import namedtuple
except:
    pass
def equilibrium_equation(u):
    '''
    用于求离子链平衡方程的值, 以供迭代求解平衡位置

    输入： 离子链无量纲位置 u: 1*n 的向量
    输出： n个平衡方程的值: 1*n 向量, 输出为0向量时对应输入为平衡位置。
    '''
    # 初始化数据
    n = len(u)
    e = np.zeros(n)

    # 依次计算n个方程的值
    for i_equation in range(n):
        for i_tern in range(n):
            if i_tern == i_equation:
                e[i_equation] += u[i_tern]
            elif i_tern < i_equation:
                e[i_equation] += -1 / (u[i_tern] - u[i_equation]) ** 2
            elif i_tern > i_equation:
                e[i_equation] += 1 / (u[i_tern] - u[i_equation]) ** 2
    return np.array(e)


def solve_equilibrium(n):
    '''
    求解离子链平衡位置， 通过求解平衡方程=0 ,使用 scipy.optimize.fsolve

    输入: n: 离子数量,整数
    输出: u0  离子平衡位置,长度为n的np.array
    '''
    u = np.arange(n) / n ** 0.58
    u = u - np.average(u)
    u0 = fsolve(equilibrium_equation, u)

    return u0


def mode_x(n, beta):
    '''
    求解径向模式
    输入:   n: 离子数量
            beta: 阱参数omega_x/omega_z
    '''

    A = np.zeros((n, n))  # 用于存放矩阵
    u = solve_equilibrium(n)  # 求解平衡位置
    for i in range(0, n):  # 计算矩阵元
        A[i][i] = beta ** 2
        for j in range(0, n):
            if i != j:
                A[i][j] = 1 / abs(u[i] - u[j]) ** 3
                A[i][i] += -1 / abs(u[i] - u[j]) ** 3

    # 计算矩阵的本征值和本征向量
    eigenval, eigenvec = np.linalg.eig(A)
    index = np.argsort(eigenval)
    eigenval = eigenval[index]
    eigenvec = eigenvec.T[index].T

    return eigenval, eigenvec


def mode_z(n):
    '''
    求解轴向模式
    输入:   n: 离子数量
    '''

    A = np.zeros((n, n))  # 用于存放矩阵
    u = solve_equilibrium(n)  # 求解平衡位置
    for i in range(0, n):  # 计算矩阵元
        A[i][i] = 1
        for j in range(0, n):
            if i != j:
                A[i][j] = -2 / abs(u[i] - u[j]) ** 3
                A[i][i] += 2 / abs(u[i] - u[j]) ** 3

    # 计算矩阵的本征值和本征向量
    eigenval, eigenvec = np.linalg.eig(A)
    index = np.argsort(eigenval)
    eigenval = eigenval[index]
    eigenvec = eigenvec.T[index].T
    return eigenval, eigenvec


def dagger(matrix):
    """用于计算矩阵的共轭转置"""
    return np.conj(matrix).T


class PulseDesigner:

    def __init__(self, wk: np.ndarray, lambda_raman=532e-9):
        """
        wk: 声子频率的列表, np.ndarray
        lambda_raman: 拉曼光波长
        """
        self.basis = None
        self.strCurrFile = os.path.abspath(inspect.stack()[0][1])
        self.strPathDllFolder = os.path.dirname(self.strCurrFile)
        self.wp = np.array(wk)
        self.lambda_raman = lambda_raman
        self.n = len(self.wp)
        _, self.x_mode = mode_x(self.n, 1)
        self._calculate_eta(self.wp)

    def _calculate_eta(self, omega: np.ndarray) -> np.ndarray:
        if len(omega) != self.n:
            raise ValueError(
                f'number of mode not same as the ion number {self.n}'
            )
        x0_k = np.sqrt(hbar / (2 * m_u * 171 * omega))
        eta_k = 2 * 2 * np.pi / self.lambda_raman * x0_k
        self.g_jk = self.x_mode * eta_k
        return self.g_jk

    def Get_Matrix_M(self,
                     BasisList: np.ndarray,
                     TotalTime: float,
                     ) -> np.ndarray:
        r"""
        计算 M 矩阵。
        :param BasisList: 傅立叶基函数的列表，元素为其序号
        :param TotalTime: 演化的总时长
        :return: M 矩阵，shape = P * NumOfFourierBasis
        """
        self.basis = np.array([i / TotalTime for i in BasisList])
        t, tau, wp = symbols('t tau omega_p', positive=True)
        n, p = symbols('n p', integer=True, postive=True)

        # 读取表达式
        file_path = os.path.join(self.strPathDllFolder, "M_pn_expression.dat")
        with open(file_path, 'rb') as inf:
            M_pn = pickle.load(inf)
        # 根据sympy结果，生成numpy 函数
        M_function = lambdify((wp, tau, p, n), M_pn, 'numpy')

        # 读取表达式
        file_path = os.path.join(self.strPathDllFolder, "M1_pn_expression.dat")
        with open(file_path, 'rb') as inf:
            M1_pn = pickle.load(inf)
        # 根据sympy结果，生成numpy 函数
        M1_function = lambdify((wp, tau, p, n), M1_pn, 'numpy')

        # 读取表达式
        file_path = os.path.join(self.strPathDllFolder, "M2_pn_expression.dat")
        with open(file_path, 'rb') as inf:
            M2_pn = pickle.load(inf)
        # 根据sympy结果，生成numpy 函数
        M2_function = lambdify((wp, tau, p, n), M2_pn, 'numpy')

        wp_list = self.wp
        # 生成矩阵网格，便于下一步带入函数
        p = range(len(wp_list))
        n = BasisList  # n 的标号从零开始
        P, N = np.meshgrid(p, n, indexing='ij')

        self.M = M_function(wp_list[:, np.newaxis], TotalTime, P, N)
        self.M1 = M1_function(wp_list[:, np.newaxis], TotalTime, P, N)
        self.M2 = M2_function(wp_list[:, np.newaxis], TotalTime, P, N)
        self.M_zero = null_space(np.vstack((self.M, self.M1, self.M2)), rcond=1e-18)

    def Get_Matrix_M_N(self,
                       NumOfFourierBasis: int,
                       TotalTime: float,
                       ) -> np.ndarray:
        r"""
        计算 M 矩阵。
        :param NumOfFourierBasis: 傅立叶基函数的个数
        :param TotalTime: 演化的总时长
        :return: M 矩阵，shape = P * NumOfFourierBasis
        """
        basis_list = np.arange(NumOfFourierBasis) + 1
        self.Get_Matrix_M(BasisList=basis_list, TotalTime=TotalTime)

    def Get_D_mn(self,
                 AddressingID,
                 BasisList: list,
                 TotalTime: float,
                 ) -> np.ndarray:
        """
        通过D_pmn 计算 D_mn 矩阵。
        :param AddressingID: 寻址的离子，目前是两个离子。
        :param BasisList: 傅立叶基函数的列表，元素为其编号
        :param TotalTime: 演化的总时长
        :return: D_nmp 矩阵,shape = P * NumOfFourierBasis *  NumOfFourierBasis
        """
        i, j = AddressingID
        wp_list = self.wp
        LambDickeMatrix = self.g_jk

        def Get_D_pmn(wp_list: np.ndarray,
                      BasisList: list,
                      TotalTime: float,
                      ) :
            r"""
            将积分的解析形式推导出来，计算 D 矩阵。

            :param wp_list: 声子模式的角频率列表, 数组大小对应声子模式数目
            :param NumOfFourierBasis: 傅立叶基函数的个数
            :param TotalTime: 演化的总时长
            :return: D_nmp 矩阵，shape = P * NumOfFourierBasis *  NumOfFourierBasis
            """
            # Define the variables
            t, t1, tau, wp = symbols('t t1 tau omega_p', positive=True)
            n, m = symbols('n m', integer=True, postive=True)

            file_path = os.path.join(self.strPathDllFolder, "D_nmp_expression.dat")
            with open(file_path, 'rb') as inf:
                D_nmp = pickle.load(inf)
            # 根据sympy结果，生成numpy 函数
            D_function = lambdify((wp, tau, m, n), D_nmp, 'numpy')

            # 生成矩阵网格，便于下一步带入函数
            p = range(len(wp_list))
            n = BasisList  # n 的标号从零开始
            P, M, N = np.meshgrid(p, n, n, indexing='ij')

            return D_function(wp_list[:, np.newaxis, np.newaxis], TotalTime, M, N)

        D_pnm = Get_D_pmn(wp_list, BasisList, TotalTime)
        eta_i = LambDickeMatrix[i][:, np.newaxis, np.newaxis]
        eta_j = LambDickeMatrix[j][:, np.newaxis, np.newaxis]
        self.D_mn = np.sum(eta_i * eta_j * D_pnm, axis=0)

    def Get_D_mn_N(self,
                   AddressingID,
                   NumOfFourierBasis: int,
                   TotalTime: float,
                   ) :
        """
        通过D_pmn 计算 D_mn 矩阵。
        :param AddressingID: 寻址的离子，目前是两个离子。
        :param NumOfFourierBasis: 傅立叶基函数的个数
        :param TotalTime: 演化的总时长
        :return: D_nmp 矩阵，shape = P * NumOfFourierBasis *  NumOfFourierBasis
        """
        basis_list = np.arange(NumOfFourierBasis) + 1
        self.Get_D_mn(AddressingID, basis_list, TotalTime)

    def solve_ADAb(self, b=np.pi / 8):
        """
        同时解决 A'DA = b 和 功率最小化脉冲

        :param b: 目标演化相位
        :return: 功率最小化的脉冲
        """
        M_zero_inside = self.M_zero
        D_matrix_inside = self.D_mn
        # 定义 D' 和 B 矩阵
        D_prime = dagger(M_zero_inside) @ D_matrix_inside @ M_zero_inside
        B = dagger(M_zero_inside) @ M_zero_inside

        # 求 D'^{-1} B
        DB = linalg.inv(D_prime) @ B

        # 对 DB 进行特征分解
        eig_vals, eig_vecs = linalg.eig(DB)

        # 找到最小的特征值对应的索引
        min_index = np.argmin(abs(eig_vals))

        # 找到最小特征值对应的特征向量
        min_vec = eig_vecs[:, min_index]
        min_vec = min_vec[:, np.newaxis]  # 前面索引出来的向量是行向量，要改成列向量

        # 将该特征向量对应回 M 矩阵 0 空间中的向量
        A_min_vec = M_zero_inside @ min_vec

        # 求振幅相关的比例系数
        conf = np.sqrt(b / abs(dagger(A_min_vec) @ D_matrix_inside @ A_min_vec))  # abs() 中的值是一个负数, 在前面添加虚数单位来去除绝对值的影响

        # 解为 M_zero * C * x
        self.A_min = A_min_vec @ conf  # 可以确定的是，这一项给出最小功率脉冲的振幅参数

        # Phase_error = dagger(A_min) @ D_matrix @ A_min - Obj_phase


    def g(self, t):
        # dt 为振幅采样间隔，通过幅度解调优化结果
        # dt = 2e-7
        # t = int(t/dt)*dt
        basis = np.array([np.sin(2 * np.pi * (i) * t  ) for i in self.basis])  # 求和从1开始
        gt = basis @ self.A_min
        return gt
    def demodulation(self,TotalTime,endTime):
        demodulate = DemodulationFreAmp(self.A_min,TotalTime,endTime)
        self.demodu_result = demodulate.demodulate_new()

class DemodulationFreAmp:
    """
    该类用于将傅立叶基函数解调成频率、幅度调制的形式。工作流如下：
    1. 由一组傅立叶系数向量构造控制场 g 函数；
    2. 找 g 函数的所有零点，并写入一组一维向量；
    3. 由 0 点计算相临 0 点之间的频率（每两个 0 点之间的频率被视为常数），并写入一组频率向量；
    4. 由 0 点向量和频率向量计算振幅向量；
    5. 绘图。
    """

    def __init__(self, amp_vector, total_time,end_time):
        """
        构造函数
        :param amp_vector: 一组傅立叶振幅向量
        :param total_time: 总的门持续时间
        """
        self.amp_vector = amp_vector
        self.total_time = total_time
        self.end_time = end_time
        self.num_of_fourier_basis = len(amp_vector)
        self.zero_point = None  # 零点向量
        self.fre_point = None  # 单片脉冲频率
        self.amp_point = None  # 单片脉冲振幅
        self.zero_steps = None  # 相临零点间距/单片脉冲长度

    def g(self, t):
        """将所有傅立叶频率分量加起来，得到整个 g 脉冲函数"""
        result_temp = np.sum([self.amp_vector[n] * np.sin(2 * np.pi * (n + 1) * t / self.total_time) for n in
                              range(self.num_of_fourier_basis)])
        return result_temp

    def find_zero(self, steps=5000):
        """
        找 g 函数的的所有零点
        :param steps: 用于找 0 点的离散化个数
        :return: 零点向量，假设零点是 N 维
        """
        t_step = np.linspace(0, self.end_time, steps)
        zeros_temp = np.array([])  # 零点数组要是 ndarray，以方便处理
        for j in range(steps - 1):
            a, b = t_step[j], t_step[j + 1]
            if np.sign(self.g(a)) != np.sign(self.g(b)):
                zero_temp = brentq(self.g, a, b)  # 采用 brentq 算法寻找零点
                zeros_temp = np.append(zeros_temp, zero_temp)  # 将找到的零点添加到零点数组里
        self.zero_point = zeros_temp
        return self.zero_point

    def compute_fre(self):
        """
        计算相邻零点间的频率参数，并写入解调程序的属性之中。

        :return:
        """
        self.zero_steps = np.diff(self.zero_point)
        self.fre_point = np.pi / self.zero_steps / 2 / np.pi  # np.diff(zero_t) 获取 0 点向量相临两个的差值，并借此计算每两个 0 点间的脉冲频率
        return self.fre_point

    def g_prime(self, t):
        """计算 g 函数对时间的导数"""
        return np.sum(
            [self.amp_vector[n] * (2 * np.pi * (n + 1) / self.total_time) * np.cos(
                2 * np.pi * (n + 1) * t / self.total_time) for
             n in
             range(self.num_of_fourier_basis)])

    def compute_amp(self):
        """计算相临零点间的振幅向量"""
        amp_temp = np.array([])
        for j in range(len(self.fre_point)):
            # amp = (-1) ** j * self.g_prime((self.zero_point[j] + self.zero_point[j + 1]) / 2) / self.fre_point[j]
            amp = (-1) ** j * (-self.g_prime(self.zero_point[j]) / 2 + self.g_prime(self.zero_point[j + 1]) / 2) / \
                  self.fre_point[j] / 2 / np.pi
            amp_temp = np.append(amp_temp, amp)
        self.amp_point = amp_temp
        return self.amp_point

    def plot(self):
        """绘图"""
        fig, axs = plt.subplots(2, 1, figsize=(10, 8))

        # 设置一个颜色主题，保持图形的颜色协调
        color_theme = plt.get_cmap('Set1')

        axs[0].step(self.zero_point, np.append(self.fre_point, np.nan), where='post', color=color_theme(0))
        axs[0].set_title("Frequency VS Time", fontweight='bold', fontsize=14)
        axs[0].set_xlabel("Time:s", fontsize=12)
        axs[0].set_ylabel("Frequency", fontsize=12)
        axs[0].grid(True)  # 加入网格

        axs[1].step(self.zero_point, np.append(self.amp_point, np.nan), where='post', color=color_theme(1))

        axs[1].set_title("Amplitude VS Time", fontweight='bold', fontsize=14)
        axs[1].set_xlabel("Time:s", fontsize=12)
        axs[1].set_ylabel("Amplitude ", fontsize=12)
        axs[1].grid(True)  # 加入网格

        # 调整两个子图之间的间隔
        plt.subplots_adjust(hspace=0.5)
        plt.tight_layout()
        plt.show()

    def demodulate(self):
        """把上面几个函数集成起来用，并返回核心数据"""
        self.find_zero()
        self.compute_fre()
        self.compute_amp()
        self.plot()

        # data_temp = np.column_stack((self.zero_point, self.fre_point, self.amp_point))
        # return data_temp

    def result(self):
        """
        接收类实例的属性，获取解调后的脉冲参数，并返回
        :return: self.zero_point, self.fre_point, self.amp_point
        """
        Demodulate = namedtuple('Demodulate', ['zero_point', 'fre_point', 'amp_point', 'zero_steps'])
        return Demodulate(self.zero_point, self.fre_point, self.amp_point, self.zero_steps)

    def demodulate_new(self):
        """把上面几个函数集成起来用，并返回核心数据。时间轴不是 0 点，而是每一片的长度。"""
        self.find_zero()
        self.compute_fre()
        self.compute_amp()
        answer = self.result()  # 将解调脉冲数据封装到一个子类的实例中，并返回
        return answer

if __name__ == "__main__":
    from scipy.integrate import quad
    import matplotlib.pyplot as plt

    e_x, _ = mode_x(2, 5)
    # omega_p = np.array(e_x * 5.111e4) * 2 * np.pi
    omega_p = np.array([2350531.780,2415213.159])*2*np.pi
    tau = 200e-6
    N_A = 1000
    ion_index = (0, 1)
    phi_ms = np.pi / 8
    basis_list = np.arange(N_A) + 1

    p = PulseDesigner(omega_p)
    p.Get_Matrix_M(basis_list, tau)
    p.Get_D_mn(ion_index, basis_list, tau)
    p.solve_ADAb(phi_ms)
    A_alpha = p.A_min
    basis = p.basis
    # plt.plot(basis, A_alpha)

    p.demodulation(tau,tau)
    # print(p.demodu_result)
    import bisect

    result_demodu = p.demodu_result
    def g(t):
        index = bisect.bisect_right(result_demodu.zero_point, t) - 1
        if index > len(result_demodu.zero_point) - 2:
            index -= 1
        amp = result_demodu.amp_point[index]
        freq = result_demodu.fre_point[index]
        t0 = result_demodu.zero_point[index]
        phi0 = freq * 2 * np.pi * t0 + index * np.pi
        gt = np.sin(t * 2 * np.pi * freq - phi0) * amp
        return gt


    t_plot = np.linspace(0, tau, 2001)
    gs = [g(t) / 1e6 / 2 / np.pi for t in t_plot]
    plt.plot(t_plot * 1e6, gs, linewidth=0.2)
    plt.ylabel(r'Carrier Rabi Freq. (MHz)')
    plt.show()
    def g_t(t):
        # dt 为振幅采样间隔，通过幅度解调优化结果
        # dt = 2e-7
        # t = int(t/dt)*dt
        basis = np.array([np.sin(2 * np.pi * (i) * t) for i in p.basis])  # 求和从1开始

        gt = basis @ A_alpha * 0.5
        return gt[0]
    # 定义被积函数
    def integrand(t, omega):
        return g_t(t) * np.exp(1j * omega * t)


    def complex_integral(t, omega):
        real_part = quad(lambda t: np.real(integrand(t, omega)), 0, t, limit=300)[0]
        imag_part = quad(lambda t: np.imag(integrand(t, omega)), 0, t, limit=300)[0]
        return real_part + 1j * imag_part


    # # 计算strench模在相空间的轨迹
    # omega = omega_p[1]
    # t_values = np.linspace(0, tau, 51)  # 时间范围

    # # 计算复平面上的轨迹
    # trajectory = [complex_integral(t, omega) for t in t_values]
    # plt.figure(figsize=(8, 8))
    # plt.plot(np.real(trajectory), np.imag(trajectory), label=r"$\int g(t) e^{i \omega t} dt$")
    # plt.xlabel('Real part')
    # plt.ylabel('Imaginary part')
    # plt.title('Trajectory in Complex Plane')
    # plt.axhline(0, color='black', lw=0.5)
    # plt.axvline(0, color='black', lw=0.5)
    # plt.grid(True)
    # plt.legend()
    # plt.show()

