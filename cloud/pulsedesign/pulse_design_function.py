from scipy.optimize import fsolve
import numpy as np


def equilibrium_equation(u):
    '''
    用于求离子链平衡方程的值, 以供迭代求解平衡位置

    输入： 离子链无量纲位置 u: 1*n 的向量
    输出： n个平衡方程的值: 1*n 向量, 输出为0向量时对应输入为平衡位置。
    '''
    # 初始化数据
    n = len(u)
    e = np.zeros(n)

    # 依次计算n个方程的值
    for i_equation in range(n):
        for i_tern in range(n):
            if i_tern == i_equation:
                e[i_equation] += u[i_tern]
            elif i_tern < i_equation:
                e[i_equation] += -1 / (u[i_tern] - u[i_equation]) ** 2
            elif i_tern > i_equation:
                e[i_equation] += 1 / (u[i_tern] - u[i_equation]) ** 2
    return np.array(e)


def solve_equilibrium(n):
    '''
    求解离子链平衡位置， 通过求解平衡方程=0 ,使用 scipy.optimize.fsolve

    输入: n: 离子数量,整数
    输出: u0  离子平衡位置,长度为n的np.array
    '''
    u = np.arange(n) / n ** 0.58
    u = u - np.average(u)
    u0 = fsolve(equilibrium_equation, u)

    return u0


def mode_x(n, beta):
    '''
    求解径向模式
    输入:   n: 离子数量
            beta: 阱参数omega_x/omega_z
    '''

    A = np.zeros((n, n))  # 用于存放矩阵
    u = solve_equilibrium(n)  # 求解平衡位置
    for i in range(0, n):  # 计算矩阵元
        A[i][i] = beta ** 2
        for j in range(0, n):
            if i != j:
                A[i][j] = 1 / abs(u[i] - u[j]) ** 3
                A[i][i] += -1 / abs(u[i] - u[j]) ** 3

    # 计算矩阵的本征值和本征向量
    eigenval, eigenvec = np.linalg.eig(A)
    index = np.argsort(eigenval)
    eigenval = eigenval[index]
    eigenvec = eigenvec.T[index].T

    return eigenval, eigenvec


def mode_z(n):
    '''
    求解轴向模式
    输入:   n: 离子数量
    '''

    A = np.zeros((n, n))  # 用于存放矩阵
    u = solve_equilibrium(n)  # 求解平衡位置
    for i in range(0, n):  # 计算矩阵元
        A[i][i] = 1
        for j in range(0, n):
            if i != j:
                A[i][j] = -2 / abs(u[i] - u[j]) ** 3
                A[i][i] += 2 / abs(u[i] - u[j]) ** 3

    # 计算矩阵的本征值和本征向量
    eigenval, eigenvec = np.linalg.eig(A)
    index = np.argsort(eigenval)
    eigenval = eigenval[index]
    eigenvec = eigenvec.T[index].T
    return eigenval, eigenvec


def dagger(matrix):
    """用于计算矩阵的共轭转置"""
    return np.conj(matrix).T


class Parameters():
    def __init__(self, nu, mu, N, tau, phi, rabi, eta, Obj_addressing):
        self.nu = np.array(nu)
        self.mu = mu
        self.N = N
        self.tau = tau
        self.phi = phi
        self.rabi = rabi
        self.eta = eta
        self.Obj_addressing = Obj_addressing
        self.delta = self.nu - self.mu
        self.sum_freq = self.nu + self.mu
        self.ts = tau / N

    def findt(self, t):
        t = t
        Ns = int((t // self.ts))
        if abs(t % self.ts) < 1e-10:  # 判断是否整除（考虑浮点误差）
            Ns = Ns - 1
        if Ns < 0:
            Ns = 0
        return Ns

    def deltak1k2(self, k1, k2):
        return self.phi[k1] - self.phi[k2]


# 计算相空间位移的中间函数
def Tct(m, t, paras: Parameters):
    Ns = paras.findt(t)

    def primitive(t):
        A_m = (-np.cos(paras.delta[m] * t - paras.tau / 2 * paras.nu[m]) / paras.delta[m]) * paras.rabi[Ns]
        A_p = (-np.cos(paras.sum_freq[m] * t - paras.tau / 2 * paras.nu[m]) / paras.sum_freq[m]) * paras.rabi[Ns]
        return A_m + A_p

    return primitive(t) - primitive(Ns * paras.ts)


def Tst(m, t, paras: Parameters):
    Ns = paras.findt(t)

    def primitive(t):
        B_m = (-np.sin(paras.delta[m] * t - paras.tau / 2 * paras.nu[m]) / paras.delta[m]) * paras.rabi[Ns]
        B_p = (-np.sin(paras.sum_freq[m] * t - paras.tau / 2 * paras.nu[m]) / paras.sum_freq[m]) * paras.rabi[Ns]
        return B_m + B_p

    return primitive(t) - primitive(Ns * paras.ts)


def Ts(m, k, paras: Parameters):
    return Tst(m, (k + 1) * paras.ts, paras)


def Tc(m, k, paras: Parameters):
    return Tct(m, (k + 1) * paras.ts, paras)


def djm(m, paras: Parameters):
    d_re = 0
    d_im = 0
    for k in range(paras.N):
        d_re += Tc(m, k, paras) * np.cos(paras.phi[k]) + Ts(m, k, paras) * np.sin(paras.phi[k])
        d_im += Ts(m, k, paras) * np.cos(paras.phi[k]) - Tc(m, k, paras) * np.sin(paras.phi[k])
    return d_re, d_im


def djmt(m, t, paras: Parameters):
    d_re = 0
    d_im = 0
    Ns = paras.findt(t)
    for k in range(Ns):
        d_re += Tc(m, k, paras) * np.cos(paras.phi[k]) + Ts(m, k, paras) * np.sin(paras.phi[k])
        d_im += Ts(m, k, paras) * np.cos(paras.phi[k]) - Tc(m, k, paras) * np.sin(paras.phi[k])
    d_re += Tct(m, t, paras) * np.cos(paras.phi[Ns]) + Tst(m, t, paras) * np.sin(paras.phi[Ns])
    d_im += Tst(m, t, paras) * np.cos(paras.phi[Ns]) - Tct(m, t, paras) * np.sin(paras.phi[Ns])
    return d_re, d_im


#添加频率鲁棒性条件的中间函数
def Tst1(m, t, paras: Parameters):
    Ns = paras.findt(t)

    def primitive(t):
        return -np.cos(paras.delta[m] * t) * t * paras.rabi[Ns]

    return primitive(t) - primitive(Ns * paras.ts)


def Tct1(m, t, paras: Parameters):
    Ns = paras.findt(t)

    def primitive(t):
        return -np.sin(paras.delta[m] * t) * t * paras.rabi[Ns]

    return primitive(t) - primitive(Ns * paras.ts)


def Ts1(m, k, paras: Parameters):
    return Tst1(m, (k + 1) * paras.ts, paras)


def Tc1(m, k, paras: Parameters):
    return Tct1(m, (k + 1) * paras.ts, paras)


def djm1(m, paras: Parameters):
    d_re = 0
    d_im = 0
    for k in range(paras.N):
        d_re += Ts1(m, k, paras) * np.cos(paras.phi[k]) + Tc1(m, k, paras) * np.sin(paras.phi[k])
        d_im += Tc1(m, k, paras) * np.cos(paras.phi[k]) - Ts1(m, k, paras) * np.sin(paras.phi[k])
    return d_re, d_im


# 计算相空间位移积累相位的中间函数
def theta(m, k1, k2, paras: Parameters):
    d = paras.delta[m]
    ts = paras.ts
    D = paras.deltak1k2(k1, k2)
    A = paras.rabi[k1] * paras.rabi[k2]
    theta1 = (np.sin(d * (k1 - k2 + 1) * ts - D) - np.sin(d * (k1 - k2) * ts - D)) / (d ** 2)
    theta2 = -ts / d * np.cos(D) if k1 == k2 else (np.sin(d * (k1 - k2 - 1) * ts - D) - np.sin(
        d * (k1 - k2) * ts - D)) / (d ** 2)
    return A * (theta1 + theta2)


def pst_area_signed(update_data, update_func, *args):
    paras = update_func(update_data, *args)
    total = 0
    M = len(paras.nu)
    for m in range(M):
        for k1 in range(paras.N):
            for k2 in range(k1 + 1):
                total += paras.eta[paras.Obj_addressing[0]][m] * paras.eta[paras.Obj_addressing[1]][m] * theta(m, k1,
                                                                                                               k2,
                                                                                                               paras)
    return total

# 根据参数计算相空间积累面积
def pst_area(update_data, update_func, *args):
    total = pst_area_signed(update_data, update_func, *args)
    return abs(total)


def theta_grad(m, k1, k2, paras: Parameters):
    d = paras.delta[m]
    ts = paras.ts
    D = paras.deltak1k2(k1, k2)
    theta1 = (np.sin(d * (k1 - k2 + 1) * ts - D) - np.sin(d * (k1 - k2) * ts - D)) / (d ** 2)
    theta2 = -ts / d * np.cos(D) if k1 == k2 else (np.sin(d * (k1 - k2 - 1) * ts - D) - np.sin(
        d * (k1 - k2) * ts - D)) / (d ** 2)
    return [(theta1 + theta2) * paras.rabi[k2], (theta1 + theta2) * paras.rabi[k1]]


def pst_area_grad(update_data, update_func, *args):
    paras = update_func(update_data, *args)
    M = len(paras.nu)
    total_grad = np.zeros(paras.N)
    for m in range(M):
        for k1 in range(paras.N):
            for k2 in range(k1 + 1):
                grad1, grad2 = theta_grad(m, k1, k2, paras)
                grad1 *= paras.eta[paras.Obj_addressing[0]][m] * paras.eta[paras.Obj_addressing[1]][m]
                grad2 *= paras.eta[paras.Obj_addressing[0]][m] * paras.eta[paras.Obj_addressing[1]][m]
                total_grad[k1] += grad1
                total_grad[k2] += grad2

    # print("total grad:", total_grad)
    return total_grad


# 根据参数计算
def pst_displacement(update_data, update_func, *args):
    paras = update_func(update_data, *args)
    # 计算 total1
    total = 0
    M = len(paras.nu)
    for m in range(M):
        d_re, d_im = djm(m, paras)
        total += d_re ** 2 + d_im ** 2

    return total  # total1 <= 0.01


def pst_robust(update_data, update_function, *args):
    paras = update_function(update_data, *args)

    # 计算 total1
    total = 0
    M = len(paras.nu)
    for m in range(M):
        d_re, d_im = djm1(m, paras)
        total += d_re ** 2 + d_im ** 2
    return total


import matplotlib.pyplot as plt
from matplotlib.collections import LineCollection


def draw_pst(paras: Parameters):
    nu = paras.nu
    tau = paras.tau
    M = len(nu)
    N_t = 2001
    djm_values = np.zeros((M, 2, N_t), dtype=float)  #存储djm的实部和虚部
    t_plot = np.linspace(0.001, tau, N_t)
    for m in range(M):
        for i in range(N_t):
            d_re, d_im = djmt(m, t_plot[i], paras)
            djm_values[m, 0, i] = d_re
            djm_values[m, 1, i] = d_im

        x = djm_values[m, 0, :]
        y = djm_values[m, 1, :]
        points = np.array([x, y]).T.reshape(-1, 1, 2)
        segments = np.concatenate([points[:-1], points[1:]], axis=1)

        #创建颜色映射
        cmap = plt.get_cmap('viridis')  #选择颜色映射
        norm = plt.Normalize(0, len(x))  #归一化到点的编号范围

        #创建LineCollection
        lc = LineCollection(segments, cmap=cmap, norm=norm)
        lc.set_array(np.arange(len(x)))  #设置颜色随点的编号变化

        #绘图
        fig, ax = plt.subplots(figsize=(4, 4))
        plt.axhline(0, color='black', linewidth=0.5, linestyle='--')  #实轴
        plt.axvline(0, color='black', linewidth=0.5, linestyle='--')  #虚轴
        line = ax.add_collection(lc)
        #fig.colorbar(line,ax=ax,label='PointIndex')
        lim = np.max(abs(djm_values[m, :, :]))
        #lim=15
        ax.set_title('$d_{jm}$ in the Complex Plane')
        #设置坐标轴范围
        ax.set_xlim(-lim, lim)
        ax.set_ylim(-lim, lim)
        plt.show()
