import numpy as np
from scipy.constants import m_u, hbar
from scipy.linalg import null_space
from scipy.optimize import minimize, dual_annealing
from cloud.pulsedesign.pulse_design_function import *
from modules.config import LOADED_PARAMETER, SingletonMeta
import dill

import time
import torch
import torch.optim as optim

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def update_rabi(rabi_half,*args):
    paras, = args
    rabi_half = np.array(rabi_half)
    rabi = np.zeros(paras.N)
    half_len = len(rabi_half)
    rabi_half = rabi_half/max(abs(rabi_half))
    rabi[:half_len] = rabi_half
    rabi[half_len:] = rabi_half[::-1]
    paras.rabi = rabi
    return paras

def compute_cos_coeffs(t, omega, mu):
    """
    向量化计算 ∫ cos（(omega-mu)*t） dt系数矩阵A，

    参数:
        t : 长度为M的时间向量
        omega : 长度为K的频率向量
        mu : 固定频率参数

    返回:
        A : K x (M-1) 的矩阵 (实部的系数)
    """
    t_i = t[:-1, np.newaxis]  # (M-1,1)
    t_ip1 = t[1:, np.newaxis]  # (M-1,1)

    w_j = omega[np.newaxis, :]  # (1,K)

    delta = mu - w_j  # (M-1,K)
    sum_freq = mu + w_j  # (M-1,K)

    # 计算A系数 (实部的系数)
    Am = (np.sin(delta * t_ip1) - np.sin(delta * t_i)) / (2 * delta)
    Ap = (np.sin(sum_freq * t_ip1) - np.sin(sum_freq * t_i)) / (2 * sum_freq)
    Ap = 0

    A = Am + Ap

    return A.T


def compute_tsin_coeffs(t, omega, mu):
    """
    计算 ∫ t sin（(omega-mu)*t） dt 的系数矩阵B，]

    参数:
        t : 长度为M的时间向量
        omega : 长度为K的频率向量 (ω)
        mu : 固定频率参数 (μ)

    返回:

        B : (M-1) x K 的矩阵 (虚部的系数)
    """
    t_i = t[:-1, np.newaxis]  # (M-1,1)
    t_ip1 = t[1:, np.newaxis]  # (M-1,1)

    w = omega[np.newaxis, :]  # (1,K)

    # 计算中间变量
    k_p = mu + w  # μ+ω
    k_m = mu - w  # μ-ω

    # 计算A系数 (实部的系数)
    termA_p = (t_ip1 * np.cos(k_p * t_ip1) - t_i * np.cos(k_p * t_i)) / k_p - (
                np.sin(k_p * t_ip1) - np.sin(k_p * t_i)) / k_p ** 2

    termA_m = -(t_ip1 * np.cos(k_m * t_ip1) - t_i * np.cos(k_m * t_i)) / k_m + (
                np.sin(k_m * t_ip1) - np.sin(k_m * t_i)) / k_m ** 2


    termA_p = 0
    A = 0.5 * (termA_p + termA_m )

    return A.T


class AMPulseDesigner:
    def __init__(self, ion_index, phonon_index, gate_time, N_ions, phonon_freq, detuning, main_mode=0):
        self.ion_index = ion_index
        self.phonon_index = phonon_index
        self.gate_time = gate_time
        self.N_ions = N_ions
        self.main_mode = main_mode
        self.omega = np.array(phonon_freq)* 2 * np.pi
        self.eta = self.lamb_dick_parameter()

        a = self.eta[ion_index[0],phonon_index]*self.eta[ion_index[1],phonon_index]

        drive_freq = phonon_freq[phonon_index]+np.sign(a)*detuning 

        N_mu = int(drive_freq * gate_time)
        self.mu = (2*N_mu+1)*np.pi/gate_time

        self.success = False

    def lamb_dick_parameter(self):
        N = self.N_ions
        omega_p = self.omega
        _, x_mode = mode_x(N, 5)
        lambda_raman = 532e-9
        x0_k = np.sqrt(hbar / (2 * m_u * 171 * omega_p))
        eta_k = 2 * 2 * np.pi / lambda_raman * x0_k
        eta = x_mode * eta_k
        return eta

    def calculate_null_space(self, o0 = None, o1 = None):
        n_half = int(self.N_modulation / 2)
        tau = self.gate_time
        mu = self.mu
        if o0 is None:
            o0 = self.omega
        if o1 is None:
            o1 = np.array([self.omega[self.main_mode]])

        self.o0 = o0
        self.o1 = o1

        t = np.linspace(0, tau, 2 * n_half + 1)
        t_half = t[:n_half + 1]
        A = compute_cos_coeffs(t_half, o0, mu)
        M0 = A
        B1 = compute_tsin_coeffs(t_half, o1 , mu)
        M1 = B1
        M1 = M1 / tau
        #
        M = np.vstack((M0, M1))
        # M = M0
        null_space_basis = null_space(M)
        #null_space_basis, _ = la.qr(null_space_basis)
        return null_space_basis

    def calculate_area(self, a):
        amp = self.calculate_rabi(a)
        area = pst_area_signed(amp, update_rabi, self.paras)
        # print("Area :", area)
        return area

    def calculate_area_grad(self, a, area0):
        eps = 1e-6
        amp = self.calculate_rabi(a)

        N_amp = len(amp)
        area_grad = pst_area_grad(amp, update_rabi, self.paras)
        area_grad = area_grad[:N_amp] * -2 * np.sign(area0)
        # print("area0:", area0)

        for i in range(N_amp):
            if abs(abs(amp[i]) - 1.0) < eps:
                amp[i] += eps
                area_grad[i] = (abs(area0) - pst_area(amp, update_rabi, self.paras)) / eps
                amp[i] -= eps

            # amp[i] += eps
            # tmp = (abs(area0) - pst_area(amp, update_rabi, self.paras)) / eps
            # amp[i] -= eps
            # if abs(tmp - area_grad[i]) > eps:
            #     print("different!", i, amp[i])
            #     area_grad[i] = tmp

        area_grad = area_grad[::-1] @ self.null_space
        area_grad /= max(abs(self.null_space @ a))
        return area_grad

    def calculate_area_grad_bf(self, a):
        eps = 1e-6
        area0 = self.calculate_area(a)
        N_vars = self.N_modulation // 2 - len(self.o0) - len(self.o1)
        area_grad = np.zeros(N_vars)
        for i in range(N_vars):
            a[i] += eps
            area_grad[i] = -(abs(self.calculate_area(a)) - abs(area0)) / eps
            a[i] -= eps
        return area_grad

    def calculate_rabi(self, a):
        amp = np.sum(a * self.null_space, axis=1)
        amp = amp / max(abs(amp))
        amp = amp[::-1]
        return amp

    def optimize_rabi_half(self, goal=None):
        N_vars = self.N_modulation // 2 - len(self.o0) - len(self.o1)
        # print(N_vars, self.o0, self.o1)
        a = torch.tensor(np.random.uniform(0, 1, N_vars), requires_grad=True)
        # print(a)
        optimizer = optim.Adam([a], lr=0.1)
        # print(optimizer)

        num_epochs = 100

        for epoch in range(num_epochs):
            # print("zero_grad")
            optimizer.zero_grad()
            a_ = a.detach().numpy()
            area0 = self.calculate_area(a_)
            area = -abs(area0)
            if goal and abs(area) > goal:
                self.success = True
                break

            # print("area_grad")
            area_grad = self.calculate_area_grad(a_, area0)
            # area_grad_bf = self.calculate_area_grad_bf(a_)
            # dif = max(abs(area_grad - area_grad_bf))
            # if dif > 1e-3:
            #     print("dif = %f" % dif)
            #     print("comparing", area_grad, '\n', area_grad_bf)

            a.grad = torch.tensor(area_grad)
            optimizer.step()

            if epoch % 10 == 0:
                print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {area}')
                #draw_pst(self.paras, main_mode=int(parameter.Light_554.MS_phonon_index[self.ion_index[0]][self.ion_index[1]]))

        rabi_half = self.calculate_rabi(a.detach().numpy())
        return rabi_half

    def run(self, N_range, goal=5.0):
        self.goal = goal
        self.area = 0
        self.rabi_half = np.array([])
        best_paras = None

        for N_modulation in N_range:
            print('N_modulation:', N_modulation)
            self.N_modulation = N_modulation
            self.null_space = self.calculate_null_space()
            self.eta = self.lamb_dick_parameter()
            self.paras = Parameters(
                self.omega * 1e-6,
                self.mu * 1e-6,
                self.N_modulation,
                self.gate_time * 1e6,
                np.zeros(self.N_modulation),
                np.zeros(self.N_modulation)+1.0,
                self.eta,
                self.ion_index
            )

            # print("rabi_half")
            rabi_half = self.optimize_rabi_half(goal=self.goal)
            # print("area")
            area = pst_area(rabi_half, update_rabi, self.paras)

            if area > self.area:
                self.rabi_half = rabi_half
                self.area = area
                best_paras = self.paras
            if self.success:
                break

        print("Area :", self.area)
        self.paras = best_paras
        self.rabi = self.paras.rabi
        self.Rabi = np.sqrt(abs(np.pi / 2 / self.area)) * 1e6
        self.t_modulation = self.gate_time / self.paras.N

        total1 = pst_displacement(self.rabi_half, update_rabi, self.paras)
        total11 = pst_robust(self.rabi_half, update_rabi, self.paras)
        total2 = pst_area(self.rabi_half, update_rabi, self.paras)

        self.total1 = total1

        print("Phase space displacement :", total1)
        print("Time average displacement:", total11)
        print("Area :", total2)
        print("Rabi Required: ", self.Rabi / 2 / np.pi)


if __name__ == '__main__':
    SingletonMeta._instances = {}
    parameter = LOADED_PARAMETER()
    N_ions = len(parameter.Light_554.Motion_freq)
    area_list = np.zeros((N_ions, N_ions))

    for i in range(N_ions):
        for j in range(i + 1, N_ions):
            SingletonMeta._instances = {}
            parameter = LOADED_PARAMETER()
            phonon_index = int(parameter.Light_554.MS_phonon_index[i][j])
            gate_frequency = parameter.Light_554.Motion_freq[phonon_index] - 8000

            print(parameter.Light_554.Motion_freq)
            print("Main Frequency:", parameter.Light_554.Motion_freq[phonon_index])
            print("Gate Frequency:", gate_frequency)

            gate_time = 400e-6
            parameter.Light_554.Motion_freq += np.random.uniform(-1e3, 1e3, N_ions)
            tester = AMPulseDesigner((i, j),
                                    gate_time,
                                    N_ions,
                                    parameter.Light_554.Motion_freq,
                                    gate_frequency)

            #N_range = [40]
            N_range = range(20, 68, 8)
            goal = 5.0
            tester.run(N_range, goal)
            print("Qubit Pair (%d, %d) Finished\n" % (i, j))
            area_list[i, j] = tester.area
            print(tester.rabi)