
from scipy.constants import m_u, hbar
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from matplotlib.collections import LineCollection
from pulse_design_function import *

def update_phi(phi_half,*args):
    paras, = args

    # 构建完整的 phi 序列，满足 phi[k] = -phi[N-k-1]
    phi = np.zeros(paras.N)
    half_len = len(phi_half)
    phi[:half_len] = phi_half
    phi[half_len:] = -phi_half[::-1]  # 对称性约束
    paras.phi = phi

    return paras


# 定义约束(相空间面积大于8)
def constraint_area(phi_half,*args):
    area = pst_area(phi_half,update_phi,*args)
    return abs(area)- 8.0

def optimize_area(phi_half,*args):
    return -abs(pst_area(phi_half,update_phi,*args))

# 定义约束函数（dispalcement <= 0.0001）
def constraint_displacement(phi_half, *args):
    total1 = pst_displacement(phi_half,update_phi,*args)
    return 0.001 - total1  # total1 <= 0.01

# 定义约束函数（robust <= 0.1）
def constraint_robust(phi_half, *args):
    total11 = pst_robust(phi_half,update_phi,*args)
    total1 = pst_displacement(phi_half,update_phi,*args)
    return 1- total11-total1  # total1 <= 0.01


class PhaseModulator():
    def __init__(self, ion_index, gate_time, N_ions, N_modulation, phonon_freq, drive_freq):

        self.ion_index = ion_index
        self.gate_time = gate_time
        self.N_ions = N_ions

        self.N_modulation = N_modulation
        self.t_modulation = self.gate_time/self.N_modulation
        self.nu = np.array(phonon_freq) * 2 * np.pi * 1e-6
        self.mu = drive_freq * 2 * np.pi * 1e-6



        self.eta = self.lamb_dick_parameter()
        print(self.eta)
        self.paras = Parameters(
            self.nu,
            self.mu,
            self.N_modulation,
            self.gate_time * 1e6,
            np.zeros(self.N_modulation),
            np.zeros(self.N_modulation),
            self.eta,
            self.ion_index
        )

        self.phi = np.zeros(N_modulation)

        for i in range(10):
            self.optimize_phi()
            total1 = pst_displacement(self.phi[:self.N_modulation//2], update_phi, self.paras)

            total11 = pst_robust(self.phi[:self.N_modulation//2], update_phi, self.paras)
            total2 = pst_area(self.phi[:self.N_modulation//2], update_phi, self.paras)
            print("Phase space displacement :", total1)
            print("Time average displacement:", total11)
            print("Area :", total2)
            if total1 <=0.01 and total2 > 5:
                break


    def lamb_dick_parameter(self):
        N = self.N_ions
        omega_p = self.nu * 1e6
        _, x_mode = mode_x(N, 5)
        lambda_raman = 532e-9
        x0_k = np.sqrt(hbar / (2 * m_u * 171 * omega_p))
        eta_k = 2 * 2 * np.pi / lambda_raman * x0_k
        eta = x_mode * eta_k
        return eta



    def optimize_phi(self):

        paras = self.paras
        initial_phi_half = np.random.rand(self.N_modulation // 2)  # 假设 N 是偶数
        # 约束定义
        # 约束定义
        constraints = (
            {
                'type': 'ineq',  # 不等式约束 robust 条件
                'fun': constraint_robust,
                'args': (paras,)
            },
            {
                'type': 'ineq',  # 不等式约束 相空间面积
                'fun': constraint_area,
                'args': (paras,)
            },
            # {
            #     'type': 'ineq',  # 不等式约束
            #     'fun': constraint_displacement,
            #     'args': (paras,)
            # }
        )
        try:
            # 优化
            result = minimize(
                pst_displacement,  # 目标函数
                initial_phi_half,  # 初始猜测值
                args=(update_phi,paras,),  # 额外参数
                method='SLSQP',  # 支持约束优化的算法
                constraints=constraints,  # 约束条件
                options={'maxiter': 1000, 'ftol': 1e-6}  # 优化选项
            )
        except Exception as e:
            print(e)
        print(result.x)
        # 提取优化后的 phi_half
        optimized_phi_half = result.x
        # 构建完整的 phi 序列

        update_phi(optimized_phi_half,paras)

        # 更新 paras.phi
        self.phi = paras.phi

    def draw_PST(self):
        M = self.N_ions
        tau = self.gate_time
        paras = self.paras
        djm_values = np.zeros((M, 2, 1001), dtype=float)  # 存储 djm 的实部和虚部
        t_plot = np.linspace(0.001, tau * 1e6, 1001)
        for m in range(M):
            for i in range(1001):
                d_re, d_im = djmt(m, t_plot[i], paras)
                djm_values[m, 0, i] = d_re
                djm_values[m, 1, i] = d_im
            x = djm_values[m, 0,:]
            y = djm_values[m, 1,:]
            points = np.array([x, y]).T.reshape(-1, 1, 2)
            segments = np.concatenate([points[:-1], points[1:]], axis=1)

            # 创建颜色映射
            cmap = plt.get_cmap('viridis')  # 选择颜色映射
            norm = plt.Normalize(0, len(x))  # 归一化到点的编号范围

            # 创建 LineCollection
            lc = LineCollection(segments, cmap=cmap, norm=norm)
            lc.set_array(np.arange(len(x)))  # 设置颜色随点的编号变化

            # 绘图
            fig, ax = plt.subplots(figsize=(6, 6))
            plt.axhline(0, color='black', linewidth=0.5, linestyle='--')  # 实轴
            plt.axvline(0, color='black', linewidth=0.5, linestyle='--')  # 虚轴
            line = ax.add_collection(lc)
            # fig.colorbar(line, ax=ax, label='Point Index')
            lim = np.max(abs(djm_values[m,:,:]))
            ax.set_title('$d_{jm}$ in the Complex Plane')
            # 设置坐标轴范围
            ax.set_xlim(-lim, lim)
            ax.set_ylim(-lim, lim)
            plt.show()




if __name__=='__main__':
    pm = PhaseModulator((0,4),0.000233961198,5,6
                        ,
                        [
                            1981028.1564572062,
                            2135853.1687762425,
                            2258931.6770918197,
                            2352964.3634648616,
                            2417578.23828742
                        ],2258931.6770918197-8548.4)
    pm.draw_PST()