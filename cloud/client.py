import logging
import time
from typing import Optional, Dict, Any
import requests
from requests.exceptions import RequestException
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ExperimentSubmissionError(Exception):
    """实验提交异常"""

    pass


class ExperimentStatusError(Exception):
    """获取实验状态异常"""

    pass


class ExperimentResultError(Exception):
    """获取实验结果异常"""

    pass


class ExperimentChipConfigError(Exception):
    """获取芯片配置信息异常"""

    pass


class QuantumClient:
    """量子计算实验客户端"""

    def __init__(self, base_url: str = "http://************:8001"):
        self.base_url = base_url.rstrip("/")
        self.version = "1.0"
        self.session = requests.Session()  # 使用会话对象

    def submit_experiment(
        self, circuit: str, ion_index: tuple, repeats: int, task_id: str
    ) -> Dict[str, Any]:
        """提交量子实验任务

        Parameters
        ----------
        circuit : str
            量子线路描述
        ion_index : tuple
            使用的离子索引
        repeats : int
            重复次数
        task_id : str
            任务唯一标识

        Returns
        -------
        Dict[str, Any]
            提交响应，包含以下字段：
            - submitted_at : str
                任务提交时间戳(ISO格式)
            - task_id : str
                唯一任务标识
            - status_code : int
                状态码(200表示成功)
            - status_info : str
                状态描述
            - version : str
                API版本
            - rid : int
                实验运行ID

        Raises
        ------
        ExperimentSubmissionError
            当实验提交失败时抛出，包含具体错误信息
        """
        payload = {
            "version": self.version,
            "task_id": task_id,
            "arguments": {
                "circuit": circuit,
                "ion_index": str(ion_index),
                "repeats": repeats,
            },
        }

        try:
            response = self.session.post(
                f"{self.base_url}/v1/experiment/submit", json=payload
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            error_detail = ""
            try:
                error_response = response.json()
                error_detail = error_response.get('detail', str(e))
            except Exception:
                error_detail = str(e)

            logger.error(f"提交实验请求失败: HTTP {response.status_code}")
            logger.error(f"请求数据: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            logger.error(f"错误详情: {error_detail}")
            raise ExperimentSubmissionError(f"实验提交失败 (HTTP {response.status_code}): {error_detail}")
        except requests.exceptions.RequestException as e:
            logger.error(f"提交实验请求失败: {e}")
            logger.error(f"请求数据: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            raise ExperimentSubmissionError(f"实验提交失败: {str(e)}")

    def get_experiment_status(self) -> Dict[str, Any]:
        """获取所有实验的状态

        Returns
        -------
        Dict[str, Any]
            实验状态信息，包含以下字段：
            - status_code : int
                状态码(200表示成功)
            - status_info : str
                状态描述
            - experiments : Dict[str, Dict[str, str]]
                实验状态列表，格式为：
                {
                    "实验ID": {
                        "status": "状态描述"  # 可能的状态值:
                            # pending (0): 等待中
                            # flushing (1): 刷新中
                            # preparing (2): 准备中
                            # prepare_done (3): 准备完成
                            # running (4): 运行中
                            # run_done (5): 运行完成
                            # analyzing (6): 分析中
                            # deleting (7): 删除中
                            # paused (8): 已暂停
                    }
                }

        Example
        -------
        # >>> client = QuantumClient()
        # >>> status = client.get_experiment_status()
        # >>> print(status)
        {
            'status_code': 200,
            'status_info': '获取实验状态成功',
            'experiments': {
                '123': {'status': 'running'},
                '124': {'status': 'preparing'},
                '125': {'status': 'deleting'}
            }
        }

        Raises
        ------
        ExperimentStatusError
            当获取状态请求失败时抛出，包含具体错误信息
        """
        try:
            response = self.session.get(f"{self.base_url}/v1/experiment/status")
            response.raise_for_status()
            return response.json()
        except RequestException as e:
            logger.error(f"获取实验状态请求失败: {e}")
            raise ExperimentStatusError(f"获取实验状态失败: {str(e)}")

    def get_experiment_result(
        self, target_id: str, root_dir: str = "results"
    ) -> Dict[str, Any]:
        """获取实验结果

        Parameters
        ----------
        target_id : str
            实验ID
        root_dir : str, optional
            结果目录，默认为 "results"

        Returns
        -------
        Dict[str, Any]
            实验结果数据

        Raises
        ------
        ExperimentResultError
            当获取结果请求失败时抛出，包含具体错误信息
        """
        params = {"target_id": target_id, "root_dir": root_dir}
        try:
            response = self.session.get(
                f"{self.base_url}/v1/experiment/get_exp_result", params=params
            )
            response.raise_for_status()
            return response.json()
        except RequestException as e:
            logger.error(f"获取实验结果请求失败: {e}")
            raise ExperimentResultError(f"获取实验结果失败: {str(e)}")

    def wait_for_experiment(
        self, rid: int, timeout: int = 300, interval: float = 1.0
    ) -> Optional[Dict[str, Any]]:
        """等待实验完成

        Parameters
        ----------
        rid : int
            实验运行 ID
        timeout : int, optional
            超时时间（秒）, 默认为 300
        interval : float, optional
            查询间隔（秒）, 默认为 1.0

        Returns
        -------
        Optional[Dict[str, Any]]
            实验状态信息，如果成功完成返回最终状态，如果超时或出错返回 None
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            status_response = self.get_experiment_status()
            if not status_response:
                logger.error("无法获取实验状态，等待中止")
                return None

            experiments = status_response.get("experiments", {})
            exp_status_info = experiments.get(str(rid))

            if not exp_status_info:
                logger.info(f"实验 {rid} 已完成")
                return {"status": "completed"}

            exp_status = exp_status_info.get("status", "")
            logger.info(f"实验 {rid} 当前状态: {exp_status}")

            if exp_status == "error":
                logger.error(f"实验 {rid} 发生错误")
                return None
            elif exp_status == "deleting":
                logger.info(f"实验 {rid} 已完成")
                return {"status": "completed"}

            time.sleep(interval)

        logger.warning(f"实验 {rid} 在 {timeout} 秒后超时")
        return None  # 超时

    def get_chip_config(self) -> Dict[str, Any]:
        """获取芯片配置信息

        Returns
        -------
        Dict[str, Any]
            芯片配置信息，包含以下字段：
            - chip : str
                芯片名称
            - qubit_count : int
                量子比特的总数
            - max_shots : int
                最大测量次数
            - last_update_time : int
                最后更新时间戳
            - adj_matrix : Dict[str, list]
                保真度矩阵
            - qubit_params : Dict[str, Dict]
                量子比特参数
            - available_qubits : list[int]
                可用比特
            - basic_q_gate : list[str]
                支持的基础逻辑门

        Example
        -------
        # >>> client = QuantumClient()
        # >>> config = client.get_chip_config()
        # >>> print(config)
        {
            "Chip": "Matrix2",
            "QubitCount": 2,
            "AdjMatrix": [
            [0.0, 0.993],
            [0.993, 0.0]
            ],
            "QubitParams": {
            "0": {
                "ReadoutFidelity": 0.9500,
                "FidelityMat": [
                [0.9700, 0.0300],
                [0.0500, 0.9500]
                ],
                "SingleGateFidelity": 0.9980
            },
            "1": {
                "ReadoutFidelity": 0.9480,
                "FidelityMat": [
                [0.9650, 0.0400],
                [0.0350, 0.9600]
                ],
                "SingleGateFidelity": 0.9970
            }
            },
            "AvailableQubits": [0, 1],
            "BasicQGate": ["RPhi", "MS"],
            "LastUpdateTime": 1747200000000,
            "MaxShots": 1000,
            "MaxCircuitLayer": 20
        }

        Raises
        ------
        ExperimentChipConfigError
            当获取状态请求失败时抛出，包含具体错误信息
        """
        try:
            response = self.session.get(
                f"{self.base_url}/v1/experiment/get_chip_config"
            )
            response.raise_for_status()
            return response.json()
        except RequestException as e:
            logger.error(f"获取芯片配置信息失败: {e}")
            raise ExperimentChipConfigError(f"获取芯片配置信息失败: {str(e)}")


# 使用示例
if __name__ == "__main__":
    import uuid
    import json
    from pprint import pprint

    # 初始化客户端
    # client = QuantumClient(base_url="http://************:8001")
    client = QuantumClient(base_url="http://localhost:18001")

    # 获取芯片参数
    chip_config = client.get_chip_config()
    logger.info(json.dumps(chip_config, indent=2, ensure_ascii=False))

    # 准备实验参数
    c_list = [
        [{"gate": "X", "qubit_index": (0,)}],
        # [{"gate": "Y", "qubit_index": (1,)}],
        # [{"gate": "X", "qubit_index": (1,)}],
        # [{"gate": "Y", "qubit_index": (1,)}],
    ]
    task_id = str(uuid.uuid4())

    # 提交实验
    logger.info("正在提交实验...")
    try:
        result = client.submit_experiment(
                circuit=str(c_list),
                ion_index=(0,1,2,3),
                repeats=200,
                task_id=task_id
            )
        logger.info("提交成功，响应数据:")
        logger.info(json.dumps(result, indent=2, ensure_ascii=False))
        rid = result["rid"]
    except ExperimentSubmissionError as e:
        logger.error(f"实验提交失败: {e}")
        exit(1)

    # 等待实验完成
    logger.info("等待实验完成...")
    status = client.wait_for_experiment(rid)
    if status:
        logger.info("实验最终状态:")
        pprint(status)

    # 获取结果
    exp_result = client.get_experiment_result(str(rid))
    if exp_result:
        logger.info("实验结果:")
        pprint(exp_result)
    else:
        logger.error("获取实验结果失败")
