# 关于 IonCtrl 3.0 的改动

1. Raman 实验提供一个公共的基类, 定义一个 raman 实验的所有关键行为. 子实验只需要继承该基类并补充完相应函数即可;
2. 公共基类中将实验时序分为四个层级:
   1. level_1, repeat 层: 循环 repeat 次实验, 是最基本的层级;
   2. level_2, wave_compute & data_process 层: 计算当前扫描点的波形, 调用 level_1 跑 repeat 次实验;
   3. level_3, scan_point 层: 该层可以有多种设计, 以应对不同的需求. 当前的设计是用来遍历扫描点, 并加入离子丢失检测和救回的逻辑;
   4. level_4, task_num 层: 用于应对那些需要多次执行实验的类型.
3. 现在实验执行后的数据读取不再是异步 RPC, 每次读数完毕会检测离子是否丢失, 如果丢失则重新做本次循环;
4. 公共实验基类中提供的基本 arguement;
   1. AWG_mode: AWG  or DDS
   2. qubit_index: str(tuple)
   3. task_num: int
   4. side_choice: 单双边 raman, 默认双边
5. 一个新的实验文件要改哪些东西?
   1. build: 主要的参数设置组件
   2. prepare_dataset: 根据扫频率还是扫幅度修改单位;
   3. dadta_process: 选用期待的数据处理函数, 默认 x_scan_check_ion
   4. wave_compute: 核心代码, 构造属于该实验的波形
   5. operation: 同上