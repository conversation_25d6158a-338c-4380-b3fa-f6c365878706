#%% md
# 多离子载波 Rabi 调试
1. 获取数据,
2. 编写拟合函数,
3. 合适的绘图

当前的主要问题: 拟合函数效果很差, 拟合的完全不对, 需要排查问题
#%%
# 项目路径调整
import os

current_path =  os.path.abspath("")
print(current_path)

project_root = os.path.abspath(os.path.join(current_path, "../.."))
os.chdir(project_root)
print(os.path.abspath(""))

#%%
import numpy as np

from cloud.artiq_manager import get_result
from modules.config import LOADED_PARAMETER
from auto_calitulation.fit_model import aod_ratio_fit, rabi_sin_fit
from auto_calitulation.tools_for_calibration import get_result_x_y, change_dir
from pprint import pprint

#%%
data = get_result("15415")
pprint(data["datasets"])

#%%
x, y = get_result_x_y(15415, qubit_index=(0, 2))
print(x)
print(y)
x = x * 1e-6

#%%
# 绘制 xy
import matplotlib.pyplot as plt
plt.plot(x, y)
plt.show()

#%%
