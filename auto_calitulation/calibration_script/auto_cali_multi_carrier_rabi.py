"""
多离子载波 Rabi 频率校准
"""
import time

import numpy as np

from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.fit_model import aod_ratio_fit
from auto_calitulation.tools_for_calibration import cali_logger, get_result_x_y, change_dir

# 0. 初始化一个日志模块
logger = cali_logger(__name__)
# 1. 切换到项目根目录
change_dir()
from cloud.fit_func import (
    Rabi_fit,
    Cross_fit,
)

# 2. 实例化 artiq_manager 和 LOADER_PARAMETER
manager = ARTIQManager()
parameter = LOADED_PARAMETER()

# 3. 提取关键参数
ion_nums = len(parameter.PMT.Select_channels)  # 需要从实验文件中提取, 此处模拟提取
rabi_fit_error = 0.01  # rabi 拟合允许的误差

# 初始化 AOD Ratio 矩阵， 改粒子数时使用
AOD_ratio = np.ones([ion_nums, ion_nums])  # 每个元素代表打双边 Rabi 时, 两个离子所对应 AOD 频率之比
parameter.Light_554.AOD_AWG_ratio = AOD_ratio

rabi_fre_matrix = np.ones([ion_nums, ion_nums])
# parameter.update_config_json()

rabi_time_matrix = np.ones([ion_nums, ion_nums])

# 2. 定义两个关键的逻辑: 两离子 Carrier Raman Rabi 实验, 目的是拟合两个离子的 Rabi 频率
# 参数:
#   1. 两离子索引;
#   2. Rabi 时间, 建议保证 3 个周期以上, 那么从 pi/2 的矩阵中提取当前参数, 跑 15 * (pi/2) 的时间;


def run_carrier_rabi_two_ions(scan_start=1e-6, scan_stop=30e-6, n_scan_point=21, qubit_index="(0, 1)"):
    """
    提交并监控一个两离子 Rabi 实验
    :param scan_start: 起始点
    :param scan_stop: 中止点
    :param n_scan_point: 扫描点数
    :param qubit_index: 选择两离子
    :return:
    """
    Rabi554 = {'arguments': {'X_scan_range':
                                 dict(ty='RangeScan', start=scan_start, stop=scan_stop, npoints=n_scan_point),
                             'rabi_choice': 'Carrier',
                             'side_choice': 'All',
                             'qubit_index': qubit_index
                             },
               'class_name': 'RamanRabi',
               'file': 'repository\\Raman_exp\\Raman_Rabi.py',
               'log_level': 30}
    rid = manager.submit(Rabi554)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid


# 3. 定义 AOD_ratio 测试函数
def run_aod_ratio_scan(scan_start=0.95, scan_stop=1.05, n_scan_point=16, operation_time=40e-6, qubit_index="(0, 1)"):
    """
    执行 AOD_Ratio_scan 实验并且监控, 直至实验结束
    :param repeat:
    :param scan_start: 开始点
    :param scan_stop:  结束点
    :param n_scan_point:  扫描点数
    :param operation_time:  操作时间
    :param qubit_index:  比特索引
    :return:
    """
    logger.info("提交 AOD Ratio Scan 实验")
    AODratio = {'arguments': {'X_scan_range':
                                  dict(ty='RangeScan', start=scan_start, stop=scan_stop, npoints=n_scan_point),
                              'operation_time': operation_time,
                              'qubit_index': str(qubit_index)
                              },
                'class_name': 'RamanAODRatioScan',
                'file': 'repository\\Raman_exp\\Raman_AOD_ratio_scan.py',
                'log_level': 30}

    rid = manager.submit(AODratio)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid


def aod_ratio_fit_cross(x, Y):
    data = Cross_fit(x, Y)
    return data[0]


def rabi_sin_fit(x, Y, ion_index=(0, 1)):
    # 拟合拉比,
    rabi_0, t_fit, y_fit, _ = Rabi_fit(x, Y[:, ion_index[0]])
    rabi_1, t_fit, y_fit, _ = Rabi_fit(x, Y[:, ion_index[1]])
    return rabi_0, rabi_1


def main():
    """校准主程序"""
    logger.info("========================================================")
    logger.info("===== 自动化校准：多离子载波 Rabi 的 AOD 比率校准 启动 =====")
    logger.info("========================================================")

    for i in range(ion_nums):
        for j in range(i + 1, ion_nums):
            # logger.info("******************************************")
            logger.info(f" ******** 选择离子对：({i}, {j}) *********")
            # logger.info("******************************************")
            cali_point = 0
            t_init = 50e-6

            while True:
                # 1. 提交对于离子 i, j 的双边载波 Raman 实验;
                repeat = 100
                parameter.Experiment.Repeat = repeat
                parameter.update_config_json()
                logger.info(f"更新 repeat 参数为： {parameter.Experiment.Repeat}.")
                logger.info(f"启动双离子 Raman Rabi 实验...")
                rid = run_carrier_rabi_two_ions(scan_start=1e-6, scan_stop=t_init, n_scan_point=24,
                                                qubit_index=f"{i, j}")
                logger.info(f"Rabi 实验结果文件 ID {rid}")

                # 2. 获取数据并拟合二者的 Rabi 频率;
                X_Rabi, Y_Rabi = get_result_x_y(rid, qubit_index=(i,j))
                t_i, t_j = rabi_sin_fit(X_Rabi, Y_Rabi)
                logger.info(f"拟合两离子 Rabi 2 pi 时间： t_{i}={t_i:.8f}, t_{j}={t_j:.8f}")
                diff_ratio = abs(t_i - t_j) / max(t_i, t_j)
                logger.info(f"Rabi 时间差为： ={diff_ratio:.8f} vs. 误差阈值为：={rabi_fit_error}")
                t_init = t_i * 4
                # 3. 判断二者的频率差是否大于某个容错阈值,
                # 如果大于该阈值, 则启动面向该两个离子的 AOD 校准程序

                if diff_ratio > rabi_fit_error:
                    logger.info(f"误差大于阈值, 启动两离子 AOD Ratio 校准：{i} {j}")

                    # 3.1 找到小的那个 rabi 频率得到 pi/2 时间
                    operation_time = (t_i + t_j)/2
                    # operation_time = min(t_i, t_j)
                    logger.info(f"设置 AOD Ratio operation_time 为：{operation_time:.6e}")

                    # 3.2 提交 AOD Ratio 扫描实验
                    # 将 AOD scan 实验 repeat 次数调到 300，以提高拟合精度
                    parameter.Experiment.Repeat = 300
                    logger.info(f"更新 repeat 参数为： {parameter.Experiment.Repeat}.")
                    parameter.update_config_json()
                    if cali_point == 0:
                        # 第一次扫描, 使用 t_j/t_i 的平方作为初始值
                        aod_ratio_now = (t_j/t_i)**2
                    else:
                        # 后续扫描, 使用上一次的 AOD Ratio 
                        aod_ratio_now = parameter.Light_554.AOD_AWG_ratio[i][j]
                    logger.info(f"AOD ratio 扫描区间：（ {aod_ratio_now - 0.2/(1+cali_point)},{aod_ratio_now + 0.2/(1+cali_point)}）")
                    rid_aod = run_aod_ratio_scan(scan_start=aod_ratio_now - 0.3/(1+2 * cali_point),
                                                 scan_stop=aod_ratio_now + 0.3/(1+2 * cali_point),
                                                 n_scan_point=11,
                                                 operation_time=operation_time * (1 + 4 * cali_point) / 4,
                                                 qubit_index=f"{i, j}")
                    logger.info(f"AOD Ratio 实验结果文件 ID {rid}")

                    X_AOD, Y_AOD = get_result_x_y(rid_aod, qubit_index=(i, j))
                    if cali_point == 0:
                        try:
                            # 如果第一次拟合找不到交点, 就使用线性拟合来构造交点
                            fit_ratio = aod_ratio_fit_cross(X_AOD, Y_AOD)
                        except IndexError:
                            logger.info("未发现交叉点, 使用线性拟合构造交叉点")
                            fit_ratio = aod_ratio_fit(X_AOD, Y_AOD)
                    else:
                        fit_ratio = aod_ratio_fit(X_AOD, Y_AOD)

                    logger.info(f"拟合 AOD Ratio： {fit_ratio:.5f}")
                    parameter.Light_554.AOD_AWG_ratio[i][j] = fit_ratio
                    parameter.Light_554.AOD_AWG_ratio[j][i] = 1 / fit_ratio
                    parameter.update_config_json()
                    # 3.4 更新到参数表
                    logger.info(f"AOD Ratio 参数更新 -> AOD_ratio[{i}][{j}]={fit_ratio:.5f}")
                    cali_point += 1

                #  退回到第一步, 重新跑该两离子双边 Raman 拟合频率
                else:
                    logger.info(f"离子对 ({i}, {j}) Rabi 误差小于阈值，本参数校准完成")
                    rabi_time_matrix[i][j] = (t_i + t_j) / 2
                    break

    logger.info("===== All calibration tasks finished. =====")


if __name__ == '__main__':
    start_time = time.time()
    main()
    logger.info(f"校准时间花费： {time.time() - start_time} s")
