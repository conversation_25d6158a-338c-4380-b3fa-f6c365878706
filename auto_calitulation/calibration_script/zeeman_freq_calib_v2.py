import os, sys
sys.path.append(r"C:\Users\<USER>\PycharmProjects\ionctrl_develop_new\ionctrl_develop")

from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from cloud.fit_func import Rabi_fit
from scipy.optimize import curve_fit
import numpy as np
from scipy.signal import savgol_filter
from auto_calitulation.tools_for_calibration import cali_logger
from ionctrl_pkg.utils.reporter import report

logger = cali_logger(__name__)

manager = ARTIQManager()
parameter = LOADED_PARAMETER()

result_dir = 'C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\results'

def mw_rabi(figure_show=0, zeeman_choice="0"):
    scan_start = 0
    scan_stop = 40e-6
    n_scan_point = 41

    RabiMW = {'arguments': {'X_scan_range': dict(ty='RangeScan',
                                                 start=scan_start, stop=scan_stop, npoints=n_scan_point),
                            'zeeman_choice': zeeman_choice,
                            'task_num': '1'
                            },
              'class_name': 'RabiMWQCMOS',
              'file': 'repository\\QCMOS_package\\mw_rabi.py',
              'log_level': 30}
    rid = manager.submit(RabiMW)
    if isinstance(rid, str):
        print(rid)
    else:
        status = manager.monitor_experiment(rid)
        print(status["status"])
        rid = status["rid"]

    result = get_result(f"{rid}", root_dir=result_dir)
    x = np.array(result["datasets"]["x_points"])
    y = np.array(result["datasets"]["y_probability"])
    print(y)

    # 拟合拉比, 
    mw_rabi, t_fit, y_fit, _ = Rabi_fit(x, y[:, 0])
    print(f"rabi_time = {mw_rabi * 1e6} us")

    #更新参数
    pi2_time = mw_rabi/4
    # pi2_time = 4e-6
    if zeeman_choice == "0":
        parameter.Signal_MW.mw_pi_2.zero = pi2_time
        parameter.update_config_json()
    elif zeeman_choice == "+":
        parameter.Signal_MW.mw_pi_2.positive = pi2_time
        parameter.update_config_json()
    elif zeeman_choice == "-":
        parameter.Signal_MW.mw_pi_2.negative = pi2_time
        parameter.update_config_json()

#
# def mv_ramsy(scan_stop=20000e-6, num_scan=41):  # 单位s):
#     scan_start = 0
#
#     RamseyMW = {'arguments': {'X_scan_range':
#                                   dict(ty='RangeScan', start=scan_start, stop=scan_stop, npoints=num_scan),
#                               'zeeman_choice': "0"},
#                 'class_name': 'RamseyMW',
#                 'file': 'repository\\QCMOS_package\\mw_ramsey.py',
#                 'log_level': 30}
#     rid = manager.submit(RamseyMW)
#     if isinstance(rid, str):
#         print(rid)
#     else:
#         status = manager.monitor_experiment(rid)
#         print(status["status"])
#         rid = status["rid"]
#
#     # 实验结果提取
#     result = get_result(f"{rid}", root_dir=result_dir)
#     x = np.array(result["datasets"]["x_points"])
#     y = np.array(result["datasets"]["y_probability"])[:, 0]
#     print(y)
#
#     #拟合Ramsey
#     # detuning, _, y_fit = Ramsey_fit(x, y[:,0])
#     return x, y


def Sin(x, freq, phise, amp):
    return amp * np.sin(2 * np.pi * freq * x + phise) + 0.5


def Ramsey_fit(t, y):
    if np.all(abs(np.diff(y)) < 0.1):
        freq = 1 / (t[-1] - t[0]) * (y[0] - y[-1]) / 2
        print(r'freq初值:{:.2f}'.format(freq))
        y = savgol_filter(y, 3, 1)
    else:
        data_fft = abs(np.fft.fft(y))
        pos = max(enumerate(data_fft[1:int(len(t) / 2)]), key=lambda x: x[1])[0]
        xmin = t[0]
        xmax = t[-1]
        xscale = xmax - xmin
        freq = (pos + 1) / xscale
        print(r'freq初值:{:.2f}'.format(freq))

    try:
        popt, pcov = curve_fit(Sin, t, y, p0=[freq, np.pi / 2, 0.5], bounds=([0, 0, 0.2], [5e5, np.pi, 0.6]))
        y_fit = Sin(t, popt[0], popt[1], popt[2])
        detuning = abs(round(popt[0], 2))
    # print(f'deturning:{detuning}')
    except:
        print("fitting error")
        y_fit = y
        detuning = np.inf

    return detuning, y_fit


def mv_ramsy(scan_stop=20000e-6, num_scan=41, zeeman_choice="0", qubit_index=0):  # 单位s):0, + ,-
    scan_start = 0

    RamseyMW = {'arguments': {'X_scan_range':
                                  dict(ty='RangeScan', start=scan_start, stop=scan_stop, npoints=num_scan),
                              'zeeman_choice': zeeman_choice},
                'class_name': 'RamseyMW',
                'file': 'repository\\QCMOS_package\\mw_ramsey.py',
                'log_level': 30}
    rid = manager.submit(RamseyMW)
    if isinstance(rid, str):
        print(rid)
    else:
        status = manager.monitor_experiment(rid)
        print(status["status"])
        rid = status["rid"]

    # 实验结果提取
    result = get_result(f"{rid}", root_dir=result_dir)
    x = np.array(result["datasets"]["x_points"]) * 1e-6
    y = np.array(result["datasets"]["y_probability"])[:, qubit_index]
    return x, y


def detuning_calcu(scan_time, num_sample=11, zeeman_choice="0", qubit_index=0):
    ramsey_exp = np.zeros(num_sample)
    ramsey_fit = np.zeros(num_sample)
    time = np.zeros(num_sample)

    sample_rate = (num_sample - 1) / scan_time

    time, _ramsey_exp = mv_ramsy(scan_stop=scan_time, num_scan=num_sample, zeeman_choice=zeeman_choice, qubit_index=qubit_index)
    # ramsey_exp[i] = savgol_filter(_ramsey_exp, 3, 1)
    ramsey_exp = _ramsey_exp
    freq_detuning, ramsey_fit = Ramsey_fit(time, ramsey_exp)

    return time, ramsey_exp, ramsey_fit, freq_detuning


def zeeman_calib(zeeman_choice="0", qubit_index=0):
    mw_rabi(figure_show=0, zeeman_choice=zeeman_choice)

    num_sample = 41
    set_detuning = [2000, 300]
    scan_time = np.array([1e-3, 1e-2])
    time = np.zeros([3, num_sample])
    ramsey_exp = np.zeros([3, num_sample])
    ramsey_fit = np.zeros([3, num_sample])
    freq_detuning = np.zeros(3)

    z_state_fre = parameter.Signal_MW.dds_for_mw_fre
    p_state_fre = parameter.Signal_MW.zeeman_p
    n_state_fre = parameter.Signal_MW.zeeman_n

    for i in range(len(scan_time)):
        print(f'i = {i}, set_detuning = {set_detuning[i]}')
        if zeeman_choice == "0":
            parameter.Signal_MW.dds_for_mw_fre -= set_detuning[i]
        elif zeeman_choice == "+":
            parameter.Signal_MW.zeeman_p -= set_detuning[i]
        elif zeeman_choice == "-":
            parameter.Signal_MW.zeeman_n -= set_detuning[i]

        parameter.update_config_json()

        time[i], ramsey_exp[i], ramsey_fit[i], freq_detuning[i] = detuning_calcu(scan_time[i], num_sample=num_sample,
                                                                                 zeeman_choice=zeeman_choice, qubit_index= qubit_index)

        detuning = freq_detuning[i]

        if zeeman_choice == "0":
            parameter.Signal_MW.dds_for_mw_fre += detuning
            print(parameter.Signal_MW.dds_for_mw_fre)
        elif zeeman_choice == "+":
            parameter.Signal_MW.zeeman_p += detuning
            print(parameter.Signal_MW.zeeman_p)
        elif zeeman_choice == "-":
            parameter.Signal_MW.zeeman_n += detuning
            print(parameter.Signal_MW.zeeman_n)

    parameter.update_config_json()

    # if zeeman_choice == "0":
    #     delta = abs(parameter.Signal_MW.dds_for_mw_fre - parameter.ref_parameter.dds_for_mw_fre)
    #     if delta < 50:
    #         logger.info(f"校准状态正常,微波中心频率为{parameter.Signal_MW.dds_for_mw_fre}")
    #         parameter.update_config_json()
    #         return "Normal",parameter.Signal_MW.dds_for_mw_fre
    #     elif delta < 1e3:
    #         logger.info(f"偏差较大,微波中心频率为{parameter.Signal_MW.dds_for_mw_fre}")
    #         parameter.update_config_json()
    #         return "Attention",parameter.Signal_MW.dds_for_mw_fre
    #     else:
    #         parameter.Signal_MW.dds_for_mw_fre = z_state_fre
    #         logger.info(f"偏差值超过预警值,参数未更新,需人工介入")
    #         parameter.update_config_json()
    #         return "Warning",parameter.Signal_MW.dds_for_mw_fre
    # elif zeeman_choice == "+":
    #     delta = abs(parameter.Signal_MW.zeeman_p - parameter.ref_parameter.zeeman_p)
    #     if delta < 500:
    #         logger.info(f"校准状态正常,+态Zeeman劈裂为{parameter.Signal_MW.zeeman_p}")
    #         parameter.update_config_json()
    #         return "Normal",parameter.Signal_MW.zeeman_p
    #     elif delta < 1e4:
    #         logger.info(f"偏差较大,+态Zeeman劈裂为{parameter.Signal_MW.zeeman_p}")
    #         parameter.update_config_json()
    #         return "Attention",parameter.Signal_MW.zeeman_p
    #     else:
    #         parameter.Signal_MW.zeeman_p = p_state_fre
    #         logger.info(f"偏差值超过预警值,参数未更新,需人工介入")
    #         parameter.update_config_json()
    #         return "Warning",parameter.Signal_MW.zeeman_p
    # elif zeeman_choice == "-":
    #     delta = abs(parameter.Signal_MW.zeeman_n - parameter.ref_parameter.zeeman_n)
    #     if delta < 500:
    #         logger.info(f"校准状态正常,-态Zeeman劈裂为{parameter.Signal_MW.zeeman_n}")
    #         parameter.update_config_json()
    #         return "Normal",parameter.Signal_MW.zeeman_n
    #     elif delta < 1e4:
    #         logger.info(f"偏差较大,-态Zeeman劈裂为{parameter.Signal_MW.zeeman_n}")
    #         parameter.update_config_json()
    #         return "Attention",parameter.Signal_MW.zeeman_n
    #     else:
    #         parameter.Signal_MW.zeeman_n = n_state_fre
    #         logger.info(f"偏差值超过预警值,参数未更新,需人工介入")
    #         parameter.update_config_json()
    #         return "Warning",parameter.Signal_MW.zeeman_n

    return None

def main():
    zeeman_list=["0","+","-"]
    # metrics = {}
    # global_status = "Normal"
    qubit_index = 6

    for zeeman_choice in zeeman_list:
        zeeman_calib(zeeman_choice, qubit_index=qubit_index)
    #     status,zeeman_data = zeeman_calib(zeeman_choice,qubit_index)
    #     if status == "Warning":
    #         global_status = "Warning"
    #     elif status == "Attention" and global_status != "Warning":
    #         global_status = "Attention"
    #     metrics[f"level_{zeeman_choice}"] = zeeman_data
    #
    # report(status=global_status, metrics=metrics)

if __name__ == '__main__':
    main()