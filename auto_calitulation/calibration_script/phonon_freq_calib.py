"""

自动声子频率校准(使用蓝边带Ramsey)

"""

import time
import sys

sys.path.append(r"C:\Users\<USER>\PycharmProjects\ionctrl_develop_new\ionctrl_develop")
import numpy as np
from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.tools_for_calibration import cali_logger, get_result_x_y
from cloud.fit_func import Ramsey_fit,Rabi_fit
from ms_package.ms_parameter import lamb_dicke_parameter

logger = cali_logger(__name__)

manager = ARTIQManager()
parameter = LOADED_PARAMETER()

# def rabi_submit(qubit_index):
#     CarrierRabi = {'arguments': {'X_scan_range':
#                                           dict(ty='RangeScan', start=0, stop=200e-6, npoints=21),
#                                       'rabi_choice': 'Blue',
#                                       'side_choice': 'All',
#                                       'qubit_index': qubit_index
#                                       },
#                         'class_name': 'RamanRabi',
#                         'file': 'repository\\QCMOS_package\\Raman_Rabi.py',
#                         'log_level': 30}
#     rid = manager.submit(CarrierRabi)
#     if isinstance(rid, str):
#         logger.info(f"rid:", rid)
#     else:
#         status = manager.monitor_experiment(rid)
#         logger.info(status["status"])
#         rid = status["rid"]
#     return rid
#
# def pi_2_time_calib(qubit_index):
#     rid = rabi_submit(f"({qubit_index},)")
#     x, y = get_result_x_y(rid, (qubit_index,))
#     y = y.flatten()
#     Rabi_time = Rabi_fit(x,y)[0]
#     pi_2_time = Rabi_time/4.0
#     parameter.Light_554.pi_2.Blue = pi_2_time
#     logger.info(f"蓝边带 rabi 时间为 {pi_2_time}")
#     return None

def exp_submit(scan_time,num_sampling,phonon_index,qubit_index):
    BlueSidebandRamsey = {'arguments': {'X_scan_range':
                                 dict(ty='RangeScan', start=0, stop=scan_time, npoints=num_sampling),
                             'ramsey_choice': 'Blue',
                             'qubit_index': qubit_index,
                             'phonon_index':phonon_index
                             },
                        'class_name': 'RamanRamsey',
                        'file': 'repository\\QCMOS_package\\Raman_Ramsey.py',
                        'log_level': 30}
    rid = manager.submit(BlueSidebandRamsey)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid

def get_ramsey_detuning(scan_time,num_sampling,phonon_index,qubit_index):
    qubit_index_str = f"({qubit_index},)"
    rid = exp_submit(scan_time,num_sampling,phonon_index,qubit_index_str)
    x, y = get_result_x_y(rid, qubit_index)
    y = y.flatten()
    detuning = Ramsey_fit(x,y)
    return detuning

def phonon_calib(phonon_index, qubit_index):
    num_sampling = 24 # 蓝边带Ramsey采样点数
    set_detuning = [2000] # 每次实验人为添加的失谐量(Hz)，原则上来说，每次声子自动校准的漂移量不能够大于第一个值
    scan_time = np.array([1.5e-3]) # 每次实验的扫描时间终止值
    # phonon_fre_original = parameter.Light_554.Motion_freq[phonon_index]

    for i in range(len(scan_time)):
        parameter.Light_554.Motion_freq[phonon_index] -= set_detuning[i] # 人为设置一定的失谐量
        parameter.update_config_json()

        logger.info(f"提交蓝边带Ramsey实验，设置失谐量为{set_detuning[i]}")
        detuning = get_ramsey_detuning(scan_time=scan_time[i], num_sampling=num_sampling, phonon_index=phonon_index, qubit_index=qubit_index)[0] # 提交实验，得到失谐量
        logger.info(f"失谐量为{detuning-set_detuning[i]}")

        parameter.Light_554.Motion_freq[phonon_index] += detuning # 更新声子频率
        parameter.update_config_json()

    # delta = abs(parameter.Light_554.Motion_freq[phonon_index] - parameter.ref_parameter.Motion_freq[phonon_index])
    # if delta < 1e4 :
    #     logger.info(f"校准状态正常,声子频率为{parameter.Light_554.Motion_freq[phonon_index]}")
    #     return "Normal",parameter.Light_554.Motion_freq[phonon_index]
    # elif delta < 1e5 :
    #     logger.info(f"声子频率偏差较大,声子频率为{parameter.Light_554.Motion_freq[phonon_index]}")
    #     return "Attention",parameter.Light_554.Motion_freq[phonon_index]
    # else :
    #     parameter.Light_554.Motion_freq[phonon_index] = phonon_fre_original
    #     parameter.update_config_json()
    #     logger.info(f"偏差值超过预警值,参数未更新,需人工介入")
    #     return "Warning",parameter.Light_554.Motion_freq[phonon_index]

def main():
    n = 12
    start_time = time.time()
    phonon_list = range(n)
    #phonon_list = [0]
    freq_old = parameter.Light_554.Motion_freq
    eta_b = lamb_dicke_parameter(n, freq_old)
    temp = parameter.Light_554.AOM_AWG_amp
    parameter.Light_554.AOM_AWG_amp = 0.3
    parameter.update_config_json()
    # metrics = {}
    # global_status = "Normal"

    # pi_2_time_calib(3)

    for phonon_index in phonon_list:
        logger.info(f"第 {phonon_index} 声子模式校准开始")
        recommend_ion = np.argmax(abs(eta_b[:,phonon_index]))

        logger.info(f"作用离子为 离子{recommend_ion}")
        phonon_calib(phonon_index,recommend_ion)

        # status,phonon_fre = phonon_calib(phonon_index,recommend_ion)
        # if status == "Warning":
        #     global_status = "Warning"
        # elif status == "Attention" and global_status != "Warning":
        #     global_status = "Attention"
        # metrics[f"phonon_{phonon_index}"] = phonon_fre

    logger.info(f"校准时间花费： {time.time() - start_time} s")
    # report(status=global_status, metrics=metrics)

    parameter.Light_554.AOM_AWG_amp = temp
    parameter.update_config_json()

if __name__ == '__main__':
    main()