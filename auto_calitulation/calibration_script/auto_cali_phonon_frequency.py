import time

import numpy as np
from Jupyter.jupyter_function import read_parameter
from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.fit_model import aod_ratio_fit
from auto_calitulation.tools_for_calibration import cali_logger, get_result_x_y, change_dir

# 0. 初始化一个日志模块
logger = cali_logger(__name__)
# 1. 切换到项目根目录
change_dir()
from cloud.fit_func import (
    Rabi_fit,
    Cross_fit,
    Ramsey_fit
)

# 2. 实例化 artiq_manager 和 LOADER_PARAMETER
manager = ARTIQManager()


operation_time = [
    80e-6,
    80e-6,
    80e-6,
    110e-6,
    80e-6,
    80e-6,
    120e-6,
]

operation_ion = [
    2,2,1,1,0,0,2
]

def phonon_frequency_cali(phonon_index):
    parameter = LOADED_PARAMETER()

    parameter = read_parameter()
    logger.info(f"第 {phonon_index} 声子模式校准开始")

    logger.info("修改声子频率设置")
    parameter.Light_554.Motion_freq[phonon_index] -= 2000
    parameter.Light_554.pi_2.Blue = operation_time[phonon_index]
    parameter.update_config_json()

    logger.info("进行蓝边带Ramsey")
    scan_start = 1e-6
    scan_stop = 1001e-6
    n_scan_point = 21
    # 定义expid
    RamanRamseyBlue = {'arguments': {'X_scan_range':
                                         dict(ty='RangeScan', start=scan_start, stop=scan_stop, npoints=n_scan_point),
                                     'Ramsey_choice': 'Blue',
                                     'qubit_index': f'({operation_ion[phonon_index]},)',
                                     'phonon_index': phonon_index,
                                     'AWG_Mode': 'AWG'
                                     },
                       'class_name': 'RamanRamsey',
                       'file': 'repository\\Raman_exp\\Raman_Ramsey.py',
                       'log_level': 30}

    # 提交实验
    rid = manager.submit(RamanRamseyBlue)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
    logger.info("实验完成")
    x, y = get_result_x_y(rid, qubit_index=operation_ion[phonon_index])
    x = x[0:len(y)]

    detuning, coherent_time, t_fit, y_fit = Ramsey_fit(x, y)
    logger.info(f"拟合得到失谐为{detuning-2000}Hz")
    if detuning < 5000:
        parameter.Light_554.Motion_freq[phonon_index] += detuning
        logger.info(f"新的声子频率为{parameter.Light_554.Motion_freq[phonon_index]}Hz")
    else:
        parameter.Light_554.Motion_freq[phonon_index] += 2000
        logger.warning(f"拟合失谐过大，未完成声子{phonon_index}的频率校准")
    parameter.update_config_json()

if __name__ == '__main__':

    for i_phonon in range(7):
        start_time = time.time()
        phonon_frequency_cali(i_phonon)
        logger.info(f"校准时间花费： {time.time() - start_time} s")





