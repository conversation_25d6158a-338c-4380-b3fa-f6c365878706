#%% md
# 
#%% md
# Raman 光对准实验测试

#%% md
## 二次函数拟合
#%% md

#%%
import numpy as np
import matplotlib.pyplot as plt

# 输入 X 和 Y 数据
X = np.array([1, 4, 10, 20])  # 例如移动的步长
Y = np.array([0.1, 0.3, 0.8, 0.9])  # 例如离子在 0 态和 1 态的概率

# 二次拟合
coefficients = np.polyfit(X, Y, 2)  # 2 表示二次多项式拟合

# 获取拟合后的函数表达式
a, b, c = coefficients

# 打印拟合的二次函数系数
print(f"拟合结果：y = {a:.4f}x^2 + {b:.4f}x + {c:.4f}")

# 生成拟合曲线
X_fit = np.linspace(min(X), max(X), 100)
Y_fit = np.polyval(coefficients, X_fit)

# 绘制数据点和拟合曲线
plt.scatter(X, Y, color='red', label='Data points')  # 原始数据点
plt.plot(X_fit, Y_fit, label='Fitted curve')  # 拟合的曲线
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.show()

#%%
import numpy as np
import matplotlib.pyplot as plt

# 输入 X 和 Y 数据
X = np.array([1, 4, 10, 20])  # 例如移动的步长
Y = np.array([0.1, 0.3, 0.8, 0.9])  # 例如离子在 0 态和 1 态的概率

# 二次拟合
coefficients = np.polyfit(X, Y, 2)  # 2 表示二次多项式拟合

# 获取拟合后的函数表达式
a, b, c = coefficients

# 打印拟合的二次函数系数
print(f"拟合结果：y = {a:.4f}x^2 + {b:.4f}x + {c:.4f}")

# 计算最后一个点的导数
x_last = X[-1]
derivative_last_point = 2 * a * x_last + b
print(f"最后一个点的导数：f'({x_last}) = {derivative_last_point:.4f}")

# 判断导数是否大于 0
if derivative_last_point > 0:
    print("导数大于 0，表示仍然在上升区间")
elif derivative_last_point < 0:
    print("导数小于 0，表示已经进入下降区间")
else:
    print("导数等于 0，表示接近最大值")

# 生成拟合曲线
X_fit = np.linspace(min(X), max(X), 100)
Y_fit = np.polyval(coefficients, X_fit)

# 绘制数据点和拟合曲线
plt.scatter(X, Y, color='red', label='Data points')  # 原始数据点
plt.plot(X_fit, Y_fit, label='Fitted curve')  # 拟合的曲线
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.show()

#%%
def fit_poly_2(x, y):
    """
    二次函数拟合, numpy 风格注释
    Parameters
    ----------
    x : array_like
        自变量
    y : array_like
        因变量

    Returns
    -------
    coefficients : array_like
        二次函数拟合系数
    """
    coefficients = np.polyfit(x, y, 2)  # 2 表示二次多项式拟合
    return coefficients

def calculate_derivative(coefficients, x):
    """
    计算二次函数在 x 处的导数
    Parameters
    ----------
    coefficients : array_like
        二次函数拟合系数
    x : array_like
        自变量
    Returns
    -------
    derivative : array_like
        二次函数在 x 处的导数
    """
    a, b, c = coefficients
    return 2 * a * x + b

#%%
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit

# 输入 X 和 Y 数据
X = np.array([1, 4, 10, 20])  # 例如移动的步长
Y = np.array([0.1, 0.3, 0.8, 0.9])  # 例如离子在 0 态和 1 态的概率

# 定义高斯函数
def gaussian(x, A, mu, sigma):
    return A * np.exp(-(x - mu)**2 / (2 * sigma**2))

# 使用 curve_fit 来进行高斯拟合
popt, pcov = curve_fit(gaussian, X, Y, p0=[1, 10, 5])  # 初始猜测 A=1, mu=10, sigma=5

# 拟合结果
A, mu, sigma = popt
print(f"拟合结果：A = {A:.4f}, mu = {mu:.4f}, sigma = {sigma:.4f}")

# 生成拟合曲线
X_fit = np.linspace(min(X), max(X), 100)
Y_fit = gaussian(X_fit, *popt)

# 绘制数据点和拟合曲线
plt.scatter(X, Y, color='red', label='Data points')  # 原始数据点
plt.plot(X_fit, Y_fit, label='Gaussian fit')  # 拟合的高斯曲线
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.show()

#%%
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit

# 输入 X 和 Y 数据
X = np.array([1, 4, 10])  # 例如移动的步长
Y = np.array([0.1, 0.3, 0.8])  # 例如离子在 0 态和 1 态的概率

# 定义高斯函数
def gaussian(x, A, mu, sigma):
    return A * np.exp(-(x - mu)**2 / (2 * sigma**2))

# 高斯函数的导数
def gaussian_derivative(x, A, mu, sigma):
    return -(x - mu) / (sigma ** 2) * A * np.exp(-(x - mu) ** 2 / (2 * sigma ** 2))

# 使用 curve_fit 来进行高斯拟合
popt, pcov = curve_fit(gaussian, X, Y, p0=[1, 10, 5])  # 初始猜测 A=1, mu=10, sigma=5

# 拟合结果
A, mu, sigma = popt
print(f"拟合结果：A = {A:.4f}, mu = {mu:.4f}, sigma = {sigma:.4f}")

# 计算最后一个点的导数
x_last = X[-1]
derivative_last_point = gaussian_derivative(x_last, A, mu, sigma)
print(f"最后一个点的导数：f'({x_last}) = {derivative_last_point:.4f}")

# 判断导数是否小于 0，表示在下降区间
if derivative_last_point < 0:
    print("导数小于 0，表示已经进入下降区间")
else:
    print("导数大于或等于 0，表示在上升区间")

# 生成拟合曲线
X_fit = np.linspace(min(X), max(X), 100)
Y_fit = gaussian(X_fit, *popt)

# 绘制数据点和拟合曲线
plt.scatter(X, Y, color='red', label='Data points')  # 原始数据点
plt.plot(X_fit, Y_fit, label='Gaussian fit')  # 拟合的高斯曲线
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.show()

#%%
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit

# 输入 X 和 Y 数据
X = np.array([1, 4, 10, 20])  # 例如移动的步长
Y = np.array([0.1, 0.3, 0.8, 0.9])  # 例如离子在 0 态和 1 态的概率

# 定义高斯函数
def gaussian(x, A, mu, sigma):
    return A * np.exp(-(x - mu)**2 / (2 * sigma**2))

# 使用 curve_fit 来进行高斯拟合
popt, pcov = curve_fit(gaussian, X, Y, p0=[1, 10, 5])  # 初始猜测 A=1, mu=10, sigma=5

# 拟合结果
A, mu, sigma = popt
print(f"拟合结果：A = {A:.4f}, mu = {mu:.4f}, sigma = {sigma:.4f}")

# 计算最后一个点与最高点的距离
x_last = X[-1]
distance_to_max = abs(x_last - mu)
print(f"最后一个点距离最高点的距离：{distance_to_max:.4f}")

# 根据距离调整步长
min_step = 1
max_step = 20

# 计算步长：距离越远，步长越大，距离越近，步长越小
new_step = min(max_step, max(min_step, 20 / (1 + distance_to_max)))

print(f"根据当前位置计算的步长为：{new_step:.4f}")

# 生成拟合曲线
X_fit = np.linspace(min(X), max(X), 100)
Y_fit = gaussian(X_fit, *popt)

# 绘制数据点和拟合曲线
plt.scatter(X, Y, color='red', label='Data points')  # 原始数据点
plt.plot(X_fit, Y_fit, label='Gaussian fit')  # 拟合的高斯曲线
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.show()

#%%
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit

# 输入 X 和 Y 数据
X = np.array([-20, -10, 0])  # 例如移动的步长
Y = np.array([0.09, 0.1, 0.101])  # 例如离子在 0 态和 1 态的概率

# 定义高斯函数
def gaussian(x, A, mu, sigma):
    return A * np.exp(-(x - mu)**2 / (2 * sigma**2))

# 使用 curve_fit 来进行高斯拟合
popt, pcov = curve_fit(gaussian, X, Y, p0=[1, 10, 5])  # 初始猜测 A=1, mu=10, sigma=5

# 拟合结果
A, mu, sigma = popt
print(f"拟合结果：A = {A:.4f}, mu = {mu:.4f}, sigma = {sigma:.4f}")

# 计算最后一个点与高斯中心的距离
x_last = X[-1]
distance_to_center = abs(x_last - mu)
print(f"最后一个点距离高斯中心的距离：{distance_to_center:.4f}")

# 生成拟合曲线
X_fit = np.linspace(min(X), max(X), 100)
Y_fit = gaussian(X_fit, *popt)

# 绘制数据点和拟合曲线
plt.scatter(X, Y, color='red', label='Data points')  # 原始数据点
plt.plot(X_fit, Y_fit, label='Gaussian fit')  # 拟合的高斯曲线
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.show()

#%%
def gaussian(x, A, mu, sigma):
    """高斯函数模型"""
    return A * np.exp(-(x - mu)**2 / (2 * sigma**2))

def gausssion_derivative(x, A, mu, sigma):
    """高斯函数的导数"""
    return -(x - mu) / (sigma ** 2) * A * np.exp(-(x - mu) ** 2 / (2 * sigma ** 2))

def gauss_init_guess(x, y):
    """
    高斯拟合的初始猜测
    Parameters
    ----------
    x : array_like
        自变量
    y : array_like
        因变量
    Returns
    -------
    p0 : array_like
        高斯拟合系数(A: 振幅, mu: 中心, sigma: 标准差)
    """
    A = 1
    mu = (x[-1] - x[0]) / 2
    sigma = 20
    return [A, mu, sigma]

def fit_gaussian(x, y):
    """
    高斯拟合
    Parameters
    ----------
    x : array_like
        自变量
    y : array_like
        因变量
    Returns
    -------
    popt : array_like
        高斯拟合系数
    """
    # 构造初始猜测
    p0 = gauss_init_guess(x, y)
    popt, pcov = curve_fit(gaussian, x, y, p0=p0)
    return popt

def process_logic(x, y, direction):
    """
    处理逻辑:
    1. 将 y 轴数据归一化, 最大值设置为 1 
    2. 如果 direction 为 "+", 什么都不做, 如果 direction 为 "-", 则将 x 轴数据反索引
    3. 拟合 x, y 数据
    4. 计算最后一个点的导数
    5. 判断导数是否小于 0, 如果小于 0, 则认为已经进入下降区间, 返回 -1

    """
    # 1. 将 y 轴数据归一化, 最大值设置为 1 
    y = y / np.max(y)
    # 2. 如果 direction 为 "-", 则将 x 轴数据取反
    if direction == "-":
        x = x[::-1]
    # 3. 拟合 x, y 数据, 拟合失败返回 0 , 继续循环
    try:
        popt = fit_gaussian(x, y)
    except Exception as e:
        return 0
    # 4. 计算最后一个点的导数
    derivative_last_point = gausssion_derivative(x[-1], *popt)
    # 5. 判断导数是否小于 0, 如果小于 0, 则认为已经进入下降区间, 返回 -1
    if derivative_last_point < 0:
        return -1
    else:
        return 1


#%%
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit

# 高斯函数
def gaussian(x, amplitude, mean, stddev):
    return amplitude * np.exp(-(x - mean)**2 / (2 * stddev**2))

# 生成模拟数据
np.random.seed(0)
x_data_increasing = np.linspace(-4, 10, 100)  # 递增 x
x_data_decreasing = x_data_increasing[::-1]   # 递减 x
y_data = gaussian(x_data_increasing, amplitude=1, mean=0, stddev=1) + 0.1 * np.random.normal(size=x_data_increasing.size)

# 使用递增的 x 值进行拟合
params_increasing, _ = curve_fit(gaussian, x_data_increasing, y_data, p0=[1, 0, 1])

# 使用递减的 x 值进行拟合
params_decreasing, _ = curve_fit(gaussian, x_data_decreasing, y_data, p0=[1, 0, 1])

# 绘制结果
x_fit = np.linspace(-5, 5, 500)
y_fit_increasing = gaussian(x_fit, *params_increasing)
y_fit_decreasing = gaussian(x_fit, *params_decreasing)

plt.figure(figsize=(10, 6))

# 绘制递增和递减 x 的拟合曲线
plt.plot(x_data_increasing, y_data, 'o', label="Data (Increasing x)", markersize=5)
plt.plot(x_fit, y_fit_increasing, label="Fit (Increasing x)", linestyle='--')
plt.plot(x_data_decreasing, y_data, 'o', label="Data (Decreasing x)", markersize=5)
plt.plot(x_fit, y_fit_decreasing, label="Fit (Decreasing x)", linestyle='-.')

plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.title('Effect of X Order on Gaussian Fit')
plt.show()

# 打印拟合参数
print(f"Fit parameters (increasing x): amplitude={params_increasing[0]}, mean={params_increasing[1]}, stddev={params_increasing[2]}")
print(f"Fit parameters (decreasing x): amplitude={params_decreasing[0]}, mean={params_decreasing[1]}, stddev={params_decreasing[2]}")

#%%
x = 0.9
Y = int(x)
Y
#%%
