"""

自动声子相干时间测量(使用蓝边带Ramsey)

"""

import time

from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.tools_for_calibration import cali_logger, get_result_x_y
from cloud.fit_func import <PERSON><PERSON>_<PERSON>,Ramsey_fit

logger = cali_logger(__name__)

manager = ARTIQManager()
parameter = LOADED_PARAMETER()

def rabi_submit(phonon_index,qubit_index):
    BlueSidebandRabi = {'arguments': {'X_scan_range':
                                 dict(ty='RangeScan', start=0, stop=200e-6, npoints=41),
                             'rabi_choice': 'Blue',
                             'side_choice': 'All',
                             'qubit_index': str(qubit_index),
                             'phonon_index': phonon_index
                             },
                        'class_name': 'RamanRabi',
                        'file': 'repository\\QCMOS_package\\Raman_Rabi.py',
                        'log_level': 30}
    rid = manager.submit(BlueSidebandRabi)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid
    
    
def ramsey_submit(scan_time,num_sampling,phonon_index,qubit_index):
    BlueSidebandRamsey = {'arguments': {'X_scan_range':
                                 dict(ty='RangeScan', start=0, stop=scan_time, npoints=num_sampling),
                             'ramsey_choice': 'Blue',
                             'qubit_index': str(qubit_index),
                             'phonon_index': phonon_index
                             },
                        'class_name': 'RamanRamsey',
                        'file': 'repository\\QCMOS_package\\Raman_Ramsey.py',
                        'log_level': 30}
    rid = manager.submit(BlueSidebandRamsey)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid

def get_pi_2_time(phonon_index,qubit_index):
    rid = rabi_submit(phonon_index,qubit_index)
    x, y = get_result_x_y(rid, (qubit_index[0],))
    y = y.flatten()
    Rabi_time = Rabi_fit(x,y)[0]
    pi_2_time = Rabi_time/4.0
    return pi_2_time

def get_coherence_time(scan_time,num_sampling,phonon_index,qubit_index):
    rid = ramsey_submit(scan_time,num_sampling,phonon_index,qubit_index)
    x, y = get_result_x_y(rid, (qubit_index[0],))
    y = y.flatten()
    coherence_time = Ramsey_fit(x,y)[1]
    return coherence_time

def main(phonon_index,qubit_index):
    start_time = time.time()
    num_sampling = 2 # 蓝边带Ramsey采样点数
    scan_time = 20e-6 # 蓝边带Ramsey扫描时间

    temp = parameter.Light_554.AOM_AWG_amp
    parameter.Light_554.AOM_AWG_amp = 0.1
    parameter.Light_554.Motion_freq[phonon_index] += 200
    parameter.update_config_json()

    logger.info(f"提交蓝边带Rabi实验，校准pi_2时间")
    pi_2_time = get_pi_2_time(phonon_index,qubit_index)
    logger.info(f"蓝边带Rabi的pi_2时间为 {pi_2_time} s")
    parameter.Light_554.pi_2.Blue = pi_2_time
    parameter.update_config_json()

    logger.info(f"提交蓝边带Ramsey实验，测量声子相干时间")

    coherence_time = get_coherence_time(scan_time,num_sampling,phonon_index,qubit_index)

    parameter.Light_554.Motion_freq[phonon_index] -= 200
    parameter.Light_554.AOM_AWG_amp = temp
    parameter.update_config_json()
    logger.info(f"校准时间花费： {time.time() - start_time} s")


    if coherence_time > 5e-3:
        logger.info(f"校准状态正常,声子相干时间为{coherence_time}")
        # return "Normal"
    else:
        logger.info(f"声子相干时间为{coherence_time},需引起注意")
        # return "Attention"

if __name__ == '__main__':
    main(phonon_index=0, qubit_index=(3,))