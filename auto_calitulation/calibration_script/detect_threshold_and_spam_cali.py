# %%
import os,sys

new_dir = "C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop"
os.chdir(new_dir)

sys.path.append(r"C:\Users\<USER>\PycharmProjects\ionctrl_develop_new\ionctrl_develop")

from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.fit_model import *
from ionctrl_pkg.utils.log import get_logger
from Jupyter.jupyter_function import read_parameter
from ionctrl_pkg.utils.reporter import report

manager = ARTIQManager()
logger = get_logger(__name__)

def run_detect_threshold_cali(re_repeat=30):
    detect_threshold_cali = {'arguments': {"re_repeat": re_repeat},
                             'class_name': 'HistogramMWReRepeat3',
                             'file': 'repository\\QCMOS_package\\detect_threshold_calibration.py',
                             'log_level': 30}
    rid = manager.submit(detect_threshold_cali)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid

def main():
    # 1. 执行校准程序
    rid = run_detect_threshold_cali(re_repeat=20)
    metrics = {}

    # 2. 提取数据
    spam = get_result(target_id=f"{rid}")["datasets"]["SPAM"]
    detect_threshold = get_result(target_id=f"{rid}")["datasets"]["Detect_threshold"]
    Errors_Dark_to_Bright = get_result(target_id=f"{rid}")["datasets"]["Errors_Dark_to_Bright"]
    Errors_Bright_to_Dark = get_result(target_id=f"{rid}")["datasets"]["Errors_Bright_to_Dark"]

    # 3. 更新数据
    if any(x > 0.2 for x in spam):
        logger.error("Detect_threshold calibration error, too high spam, please check the system!!!")
        report(status="Warning", metrics={})
    else:
        PARAMETER = read_parameter()
        PARAMETER.QCMOS.Detect_threshold = detect_threshold
        PARAMETER.QCMOS.Errors.SPAM = spam
        PARAMETER.QCMOS.Errors.Dark_to_Bright = Errors_Dark_to_Bright
        PARAMETER.QCMOS.Errors.Bright_to_Dark = Errors_Bright_to_Dark
        PARAMETER.update_config_json()
        logger.info(f" Detect_threshold is update to: {PARAMETER.QCMOS.Detect_threshold}")
        logger.info(f" SPAM  is update to: {PARAMETER.QCMOS.Errors.SPAM}")
        logger.info(f" Dark_to_Bright  is update to: {PARAMETER.QCMOS.Errors.Dark_to_Bright}")
        logger.info(f" Bright_to_Dark  is update to: {PARAMETER.QCMOS.Errors.Bright_to_Dark}")
        for x in range(len(spam)):
            metrics[f"ion_{x}"] = spam[x]
        if any(x > 0.06 for x in spam):
            status = "Attention"
        else:
            status = "Normal"
        report(status,metrics)

if __name__ == "__main__":
    main()

