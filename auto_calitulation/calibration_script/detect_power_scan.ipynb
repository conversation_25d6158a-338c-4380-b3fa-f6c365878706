#%%
import os
new_dir = "C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop"
os.chdir(new_dir)

from cloud.artiq_manager import *
from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.fit_model import *
from auto_calitulation.tools_for_calibration import cali_logger
import matplotlib.pyplot as plt

manager = ARTIQManager()
logger = cali_logger(__name__)

def plot_spam_matrix(detect_scan_list, spam_matrix, title="SPAM Matrix Plot", xlabel="Detection Scan Value", ylabel="SPAM Value"):
    """
    绘制SPAM矩阵数据的函数
    
    参数:
    - detect_scan_list: x轴数据 (1D数组)
    - spam_matrix: 要绘制的数据矩阵 (2D数组，每列为一组数据)
    - title: 图表标题
    - xlabel: x轴标签
    - ylabel: y轴标签
    """
    # 将数据转换为numpy数组
    spam_matrix = np.array(spam_matrix)
    
    # 创建图形
    plt.figure(figsize=(10, 6))
    
    # 绘制每一列数据
    for col in range(spam_matrix.shape[1]):
        plt.plot(detect_scan_list, spam_matrix[:, col], 
                marker='o', linestyle='-', 
                label=f'Dataset {col+1}')
    
    # 添加图表元素
    plt.title(title)
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend()
    
    # 显示图形
    plt.tight_layout()
    plt.show()
    
def run_detect_threshold_cali(re_repeat=10, detecting_power=0.3):
    """执行 Raman 光对准 2 阶段实验"""
    detect_threshold_cali = {'arguments': {"re_repeat": re_repeat,
                                "detecting_power": detecting_power},
            'class_name': 'HistogramMWReRepeat3',
            'file': 'repository\\QCMOS_package\\detect_threshold_calibration.py',
            'log_level': 30}
    rid = manager.submit(detect_threshold_cali)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid


#%%

detect_scan_list = np.linspace(0.35, 0.7, 7)
spam_matrix = []
for i in range(len(detect_scan_list)):
    rid = run_detect_threshold_cali(re_repeat=20, detecting_power=detect_scan_list[i])
    spam = get_result(target_id=f"{rid}")["datasets"]["SPAM"]
    spam_matrix.append(spam)

spam_matrix = np.array(spam_matrix)
plot_spam_matrix(detect_scan_list, spam_matrix,xlabel="Detect Power Scan")
#%%
