"""

热率测量

"""

import time

import numpy as np
import matplotlib.pyplot as plt
from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.tools_for_calibration import cali_logger, get_result_x_y

logger = cali_logger(__name__)

manager = ARTIQManager()
parameter = LOADED_PARAMETER()


def run_sideband_rabi_with_delay(scan_start=0e-6, scan_stop=600e-6, n_scan_point=11, qubit_index="(3, )",
                                 delay_time=100e-6, rabi_choice="Red", phonon_index=0):
    """

    执行 Raman_Rabi_delay 实验并且监控，直至实验结束
    ----------
    :param scan_start: 开始点
    :param scan_stop:  结束点
    :param n_scan_point:  扫描点数
    :param qubit_index:  比特索引
    :param delay_time:  延迟时间
    :param rabi_choice:  边带选择
    :return:
    -------

    """
    logger.info("提交 Raman Rabi Delay 实验")
    RabiDelay = {'arguments': {'X_scan_range':
                                 dict(ty='RangeScan', start=scan_start, stop=scan_stop, npoints=n_scan_point),
                             'rabi_choice': rabi_choice,
                             'side_choice': 'All',
                             'qubit_index': qubit_index,
                             'delay_time': delay_time,
                             'phonon_index': phonon_index
                             },
               'class_name': 'RamanRabiDelay',
               'file': 'repository\\QCMOS_package\\Raman_Rabi_delay.py',
               'log_level': 30}
    rid = manager.submit(RabiDelay)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid


def main(qubit_index, phonon_index):
    num = 6 # 实验取点数
    times = np.zeros(num)
    results = np.zeros(num)
    stop = 3000e-6
    i = 0

    start_time = time.time()

    for delay_time in np.linspace(0, stop, num):
        logger.info(f"延迟时间为：{delay_time/1e-6:.3f}us")
        logger.info(f"启动红边带 Raman Rabi Delay 实验...")
        rid = run_sideband_rabi_with_delay(delay_time=delay_time, rabi_choice="Red", qubit_index=str(qubit_index), phonon_index=phonon_index)
        x, y = get_result_x_y(rid, qubit_index)
        red_high = max(y)
        # logger.info(red_high)
        logger.info(f"启动蓝边带 Raman Rabi Delay 实验...")
        rid = run_sideband_rabi_with_delay(delay_time=delay_time, rabi_choice="Blue", qubit_index=str(qubit_index), phonon_index=phonon_index)
        x, y = get_result_x_y(rid, qubit_index)
        blue_high = max(y)
        # logger.info(blue_high)
        NOP = red_high / (blue_high - red_high)
        NOP = NOP.item()
        times[i] = delay_time
        results[i] = NOP
        i += 1

    # logger.info(times)
    # logger.info(results)
    logger.info(f"校准时间花费： {time.time() - start_time} s")

    coefficients = np.polyfit(times, results, 1)
    poly = np.poly1d(coefficients)

    print(coefficients)

    results_fit = poly(times)

    plt.scatter(times, results, label='Data Points')

    plt.plot(times, results_fit, label='Linear Fit', color='red')

    plt.title(f'heating rate = {coefficients[0]}/s')
    plt.xlabel('delay time')
    plt.ylabel('Number of Phonon')

    plt.legend()

    plt.show()


if __name__ == '__main__':
    qubit_index = (5,)
    phonon_index = 11
    main(qubit_index,phonon_index)
    # rid = run_sideband_rabi_with_delay(delay_time=1e-6, rabi_choice="Red")
    # x, y = get_result_x_y(rid, (0,))