# %%
import os
import sys
from typing import List

sys.path.append(r"C:\Users\<USER>\PycharmProjects\ionctrl_develop_new\ionctrl_develop")

from cloud.artiq_manager import ARTIQManager, get_result
from ionctrl_pkg.utils.log import get_logger
# from utils.reporter import report
# from utils.modify_json import modify_json
from Jupyter.jupyter_function import read_parameter
import numpy as np
manager = ARTIQManager()
logger = get_logger(__name__)

def remove_padding(padded_array: np.ndarray) -> List[List[List[int]]]:
    """
    从填充了 [-1, -1] 的均匀数组中移除占位符，还原原始的不规则列表结构。

    Parameters
    ----------
    padded_array : np.ndarray
        形状为 (n_ions, max_pixels, 2) 的数组，用 [-1, -1] 填充过。

    Returns
    -------
    List[List[List[int]]]
        原始的像素标记列表，格式为 [[[x1, y1], [x2, y2], ...], ...]。
    """
    original_list = []
    for ion in padded_array:
        # 过滤掉 [-1, -1] 的占位符
        valid_pixels = [coord.tolist() for coord in ion if not np.all(coord == -1)]
        original_list.append(valid_pixels)
    return original_list

def run_roi_cali():
    """roi 校准实验"""
    detect_threshold_cali = {'arguments': {},
                             'class_name': 'IonsROIsCalibration',
                             'file': 'repository\\QCMOS_package\\ions_rois_calibration.py',
                             'log_level': 30}
    rid = manager.submit(detect_threshold_cali)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid

def get_roi(rid):
    """从数据集中提取校准后的 ROI"""
    result = get_result(f"{rid}")
    roi = result["datasets"]["new_roi"]
    return remove_padding(roi)


def main():
    # 1. 校准前读取初始参数
    # PARAMETER = read_parameter()
    # init_roi = PARAMETER.QCMOS.roi_for_ions
    # # init_ion_num =  len(init_roi)
    # init_ion_num = 7

    # 2. 校准
    # rid = run_roi_cali()
    run_roi_cali()

    # # 3. 获取校准后的 ROI 和离子数
    # roi = get_roi(rid)
    # ion_num = len(roi)

    # # 4. 校准结果判断与参数更新
    # if ion_num == init_ion_num:
    #     # 校准成功才更新参数
    #     # PARAMETER.QCMOS.roi_for_ions = roi
    #     # PARAMETER.QCMOS.bit16.background_noise = [190 * len(_roi) for _roi in PARAMETER.QCMOS.roi_for_ions]
    #
    #     logger.info(f"calibrate roi : {roi}")
    #     logger.info(f"calibrate background_noise : {PARAMETER.QCMOS.bit16.background_noise}")
    #     # PARAMETER.update_config_json()
    #
    #     # 更新芯片信息
    #     # modify_json("C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop\\chip_info.json",
    #     #             {"QuantumChipArch.QubitCount":ion_num,  # 更新芯片信息的离子数量参数
    #     #              "QuantumChipArch.AvailableQubits": list(range(ion_num))})  # 更新允许的 qubit 参数
    #     # logger.info(f"chip_info.QubitCount is update to: {ion_num}")
    #     # logger.info(f"chip_info.AvailableQubits is update to: {list(range(ion_num))}")
    #
    #     # # 返回自动化校准信息
    #     # report("normal",  {"roi":PARAMETER.QCMOS.roi_for_ions, "ion_num":ion_num}, "ROI calibration success")
    # else:
    #     # 校准失败
    #     # report("warning",   {"roi":roi, "ion_num":ion_num}, "No Ion, human check !!!")
    #     pass


if __name__ == "__main__":
    main()
    # report("warning", {"roi": None, "ion_num": None}, "No Ion, human check !!!")


