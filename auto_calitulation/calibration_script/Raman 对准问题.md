## 背景:

左右 Raman 光各经过两个反射镜打到离子上, 每个反射镜都通过一个 new_port 的步进电机控制.

## 问题:

由于温飘, 机械振动等因素的影响, Raman 光的指向容易飘, 导致光无法精准的对上离子, 并降低离子可以接收的驱动功率. 因此, 需要经常校准 Raman 光的指向, 保证光功率维持在足够良好的范围内.

这个任务之前是通过人工手动校准实现的, 现在需要通过自动化的方式来实现.

## 关键信息:

控制器参数:

- 步长;
- 方向;

一次小循环的拟合:

- 左: 数据集 x, y
- 右: 数据集 x, y
  参数空间的特征:

1. 四个偏转镜, 每个偏转镜一个自由度
2. Y 轴数据在每个自由度上, 都应当是凸的, 时一个高斯型
3. 为避免过周期问题, 扫描时间经验的选取之前有过的最大值会怎么样?
4. 遍历四个自由度做校准. 每个校准的策略是?

两个关键的条件:

1. Raman 光飘的范围大概只有几十步(第一阶段的步长可以设小一点)
2. Raman 光不会飘到完全看不到激发的位置, 一般都还在高斯峰上(这意味着第一阶段的拟合总是能确定峰的位置)

基本实验应该怎么构造?
输入:
	1. 偏转轴
	2. 偏转方向
时序伪代码:

```python
while True:
	# 停止条件: 拟合得到光斑中心在左边还是在右边的信息, 这里是需要传入一个 x 轴移动步长的. 
	# 数据集 x, y
	run_level_2() # 其数据处理的方式需要修改, 
	try:
		 fit_data()
		 # 如果拟合成功, 则判断
	 except:
	 # 如果没有拟合成功, 则继续行动
	   
```

状态参数: 往左扫 or 往又扫, 步长
最高值: 保存一个最高值参数,

基本循环, 跑一个点, 给出一个点的 probability.

```python
for i in range(300):
	run cycle
get probability
```

伪代码

```python
for i in range(4):
	# 遍历4 个轴, 对于每个轴, 跑各自的校准程序
	# 方向: +
	probability = []
	probability += run basic
	if len(probaility) > 5:
		fit 

```

二级循环

```python
while True:
	probability_list = []
	direction = "+"
	probability = run one cycle
	probability_list += probability
	if len(probability_list) > 3
		direction = fit(probability_list)
		if 
```

```
拟合函数:
in: Y data, direction
process: 
1. 根据 direction 整理 Y 轴数据, 标定方向, 使得总是从 - 往 + 扫的;
2. 使得当前位置总是Y 数据的最后一个值
3. 二次拟合, 判断最后一个值所在的位置是不是已经在最高值周围的小区间内;
4. 判断当前点所在位置的导数是不是小于 0 , 小于 0 在往低处走, 大于 0 在往高处走
out: 你在往高处走还是往低处走;
```

新的策略, 对于每个轴, 都有两个阶段:

1. 先判断最高点在什么方向: 往某个方向走 4 步(大步), 高斯(二次)拟合, 识别最高点在最后一个点的什么方位(左侧 or 右侧), 最后一个点处导数大于 0 则在右侧, 小于 0 则在左侧, 通过高斯拟合估计跟最高点的位置, 每次步长为到最高点位置的一半, 最小为 1, 停止条件是, 由于移动一个点, 导致从上升区间跑到下降区间. 末了往回移动一个点.
2. 自适应步长, 根据最后一个点的导数设置初始步长, 重新构造 x 轴和 Y 轴

```python
While True:
	# 跑一个点, 估计当前位置是否满足停止条件(处在下降区域), 满足

The End: Motor.Move(-1)
```

Tips:

1. 高斯拟合的参数有哪些, 怎么做初始猜测?
2. 高斯拟合失败怎么办? 光很偏, 导致线几乎是平的?
   1. 首先要知道偏离的极限, 如果往一个方向移动了很多步(多少步?)还是找不到光, 可能就走反了, 要及时修改方向;
   2. 如果修改方向之后还是发现跑很久没有激发, 提示人工介入.
3. 拟合要返回什么?
   1. 有没有达到停止条件;
   2. 下一次迭代的步长(同时要更新 x 轴的数据集)
4. 拟合的输入:
   1. x 轴数据集
   2. Y 轴数据集(要做归一化)
   3. 偏转镜移动方向(用于调整拟合方向)\
5. 构造步长:
   1. 要知道几个信息, 当前位置跟估计的中心位置的距离, 标准差.
   2. 最差的情形是: 扫到的最后一个点的位置已经是比较好的了, 20 点一步, 走 3-4步, 就已经能得到初始信息, 万一最后一步就已经在最高点呢?
   3. 排除上面的情形, 返回估计的当前位置离最好位置的距离, 和方向, 和标准差, 步长设置成
6. 修改数据集: 使得传入的 x 和 y 能插入到目标位置, 并在原图上更新.
7. x 轴更新的逻辑:
   1. x_dataset = [];
   2. step += a;
   3. x_dataset.append(a)
8. 自适应步长方法的核心是什么?
   1. 假设一阶段已经估计出最后一个点离极值的位置; x_0 (中心以及当前位置的差), 以及高斯峰的形状(标准差);
      1. 可能的异常: Raman 光没打上
      2. 注意平时Raman 光飘的范围, 如果往一边走没有, 那就直接往另一边打.
   2. 几个参数共同决定步长是多少: 标准差 $\sigma$, 中心离当前位置的差 $x_0$, 最小步长;
      1. if x_0 > 步长, 移动 x_0/ 2 的距离;
      2. if x_0 < 步长, 移动最小步长的距离(这一步在接近峰值的时候起作用);
   3. 什么时候开始做拟合?
      1. 做高斯拟合至少需要 3 个数据点, 因此往前移动两次再做
9. 两个关键的条件:
   1. Raman 光飘的范围大概只有几十步(第一阶段的步长可以设小一点)
   2. Raman 光不会飘到完全看不到激发的位置, 一般都还在高斯峰上(这意味着第一阶段的拟合总是能确定峰的位置)

第二阶段的伪代码逻辑

```python
# 前置条件: Raman 光是打上了

# 第一阶段逻辑: 试图确定峰值所在的位置. 往一个方向跑四个点, 每个点 50 步, 做高斯拟合, 判断最后一个点所在的位置导数大于 0 还是小于 0 . 可能的情形, 拟合失败? 反方向跑 8 个点, 每个点 50 步? 没有必要, 如果往一个方向走 200 步还没有拟合到峰, 那说明峰在反方向;
x_dataset = [0, 50, 100, 150, 200]

# 在 0 位置跑第一个点
self.run_level_2(self.operation_time)

# 跑后面四个点
for i in range(4):
    self.move_motor(50)
    self.run_level_2(self.operation_time)

# 拟合, 判断峰值位置, 并给出当前位置离高斯峰中心的距离
return: + or -, 以及 x_delta
前面抽象为一个传入 x 轴的一维扫描实验
  
# 第一阶段拟合出来的中心离当前位置的距离
init_step = x[-1] - mu
x_point_now = 0
x_dataset = [] # 初始化 x 轴的数据集
x_dataset.append(x_point_now)

# 先跑三个点, 做拟合和处理
self.run_level_2(self.operation_time)

for i in range(1):
    self.move_motor(init_step/(i+1))
    x_point_now += init_step/(i+1)
    self.run_level_2(self.operation_time)
    x_dataset.append(x_point_now)

# 数据拟合: 估计 x_0
new_x_0 = fit(x, y)

# 开启自适应校准循环
while True:
    if abs(new_x_0) > 1:
        # 更新 x 轴的数据
	self.move_motor(round(new_x_0/2))
        x_point_now += round(new_x_0/2)
        x_dataset.append(x_point_now)
        # 更新 y 轴的数据
        self.run_level_2(self.operation_time)
     else:
        self.move_motor(1)
        x_point_now += round(1)
        x_dataset.append(x_point_now)
        # 更新 y 轴的数据
        self.run_level_2(self.operation_time)
     # 拟合判断停止条件
     fit_end = gauss_fit(x, y)
     if fit_end < 0
        # 注意到最后一个点已经处在下降区间后, 往回移动一个点, 然后停止
        self.move_motor(-1)
        break

```

#### 如何得到二阶段初始四步的值?

经过一阶段, 我们得到了:

1. mu: 一阶段数据拟合到的峰值中心
2. x_last: 最后一个 x 轴数据点的位置
3. sigma: 高斯峰的标准差
4. +-: 峰在最后一个点的 + 向还是 - 向

x_delta = mu - x_last, 如果 x_delta > 0, 则接下来要往 + 走, 如果 x_delta < 0, 接下来要往负走.

构造初始四步的值: [0, x_delta/2, x_delta * 2 / 3, x_delta * 3 / 4] , 如果是正向, 会自动设置正向步长, 同样的, 如果是负向, 也会自动设置负向的值. (PS: 由于回程差的存在, 如果往反方向走, mu 值会比估计的更远一些, 正向走的话, mu 值会比较符合估计. 总之, 初始的 4 步不会迈过峰值. )

似乎不大行, 还是得把数据处理成正的.

如果 x_delta > 0, 正常不变

如果 x_delta < 0, 获取 abs(x_delta) 值, 并在 move_motor 时取反;

#### 继续自适应迭代

初始四步的数据跑完, 做拟合, 得到在二阶段估计的高斯峰 mu 值, 以及与当前 x 位置

1. mu
2. x_last

x_delta = mu - x_last,

#### 写成两个实验文件可能更解耦一些

一阶段和二阶段各一个文件, 一阶段可以使用基本的 x-scan 数据处理方式.

逻辑代码:

```python
for i in range(4):
    # 遍历四个轴, 校准他们
    # 一阶段:
    submit_exp("一阶段校准实验")
    x, y = get_result_x_y("实验结果")
    # 拟合
    (direction, mu) = alignment_process(x, y)
    # 二阶段:
    # 1. 构造初始四个点的数据
    scan_list = []
    # 2. 提交二阶段实验
    submit_exp(二阶段校准实验)
    # 二阶段就没有数据回来了, 直接结束

```
