
import time

import numpy as np

from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.fit_model import alignment_process
from auto_calitulation.tools_for_calibration import cali_logger, get_result_x_y, change_dir

# 0. 初始化一个日志模块
logger = cali_logger(__name__)

# 1. 切换到项目根目录
change_dir()

# 2. 实例化 artiq_manager 和 LOADER_PARAMETER
manager = ARTIQManager()
parameter = LOADED_PARAMETER()

# 提交一阶段校准实验
def run_raman_alignment_stage_1(scan_start=0, scan_stop=50, n_scan_point=6, operation_time=3e-6, scan_axis=0):
    """
    提交并监控一个 Raman Alignment stage 1 实验
    :param scan_start: 起始点
    :param scan_stop: 中止点
    :param n_scan_point: 扫描点数
    :param operation_time: 操作时间
    :param scan_axis: 扫描轴
    :return:
    """
    RamanAlignmentStage1 = {'arguments': {'X_scan_range':
                                 dict(ty='RangeScan', start=scan_start, stop=scan_stop, npoints=n_scan_point),
                             'operation_time': operation_time,
                             'scan_axis': scan_axis
                             },
               'class_name': 'RamanAlignmentStage1',
               'file': 'repository\\calibration\\Raman_Alignment_stage_1.py',
               'log_level': 30}
    rid = manager.submit(RamanAlignmentStage1)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid

# 2. 提交二阶段校准实验
def run_raman_alignment_stage_2(scan_list=[0, 100, 50, 25], operation_time=3e-6, scan_axis=0, direction=1):
    """
    提交并监控一个 Raman Alignment stage 2 实验
    :param scan_start: 起始点
    :param scan_stop: 中止点
    :param n_scan_point: 扫描点数
    :param operation_time: 操作时间
    :param scan_axis: 扫描轴
    :param direction: 方向, 1 表示正方向, -1 表示负方向
    :return:
    """
    RamanAlignmentStage2 = {'arguments': {'X_scan_range':
                                 dict(ty='ExplicitScan', sequence=scan_list),
                             'operation_time': operation_time,
                             'scan_axis': scan_axis,
                             'direction': direction
                             },
               'class_name': 'RamanAlignmentStage2',
               'file': 'repository\\calibration\\Raman_Alignment_stage_2.py',
               'log_level': 30}
    rid = manager.submit(RamanAlignmentStage2)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid

    
def main(): 
    """校准主程序"""
    logger.info("========================================================")
    logger.info("===== 自动化校准: Raman Alignment 启动 =====")
    logger.info("========================================================")
    for i in range(4):
        # 一阶段:
        logger.info(f"提交一阶段 Raman Alignment 实验, 扫描轴: {i}")
        rid = run_raman_alignment_stage_1(start=0, stop=50, n_scan_point=6, operation_time=3e-6, scan_axis=i)
        logger.info(f"一阶段 Raman Alignment 实验结果文件 ID {rid}")
        # 获取数据  
        x, y = get_result_x_y(rid, qubit_index=(0,))
        # 拟合
        (direction, mu) = alignment_process(x, y)
        logger.info(f"一阶段拟合结果: direction={direction}, mu={mu}")

         # 二阶段:
        # 1. 构造初始四个点的数据
        x_delta = abs(mu - x[-1])
        scan_list = [0, x_delta/2, x_delta * 2 / 3, x_delta * 3 / 4]
        scan_list = [int(x) for x in scan_list] 
        # 2. 提交二阶段实验
        rid = run_raman_alignment_stage_2(scan_list, operation_time=3e-6, scan_axis=i, direction=direction)
        logger.info(f"二阶段 Raman Alignment 实验结果文件 ID {rid}")
        # 二阶段就没有数据回来了, 直接结束

if __name__ == "__main__":
    main()