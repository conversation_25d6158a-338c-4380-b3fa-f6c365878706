#%%
import os
import numpy as np

target_directory = "C:/Users/<USER>/PycharmProjects/ionctrl_develop_new/ionctrl_develop"
os.chdir(target_directory)
#%%
from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.tools_for_calibration import cali_logger, get_result_x_y, change_dir
import matplotlib.pyplot as plt
from IPython.display import display, clear_output
from cloud.fit_func import <PERSON><PERSON>_fit, Ramsey_fit
#%%
# 实例化 artiq_manager 和 LOADER_PARAMETER
manager = ARTIQManager()
parameter = LOADED_PARAMETER()
logger = cali_logger(__name__)
#%%
import matplotlib.pyplot as plt
from pprint import pprint
import time 

def exp_submit(scan_time,num_sampling,phonon_index,qubit_index):
    BlueSidebandRamsey = {'arguments': {'X_scan_range':
                                 dict(ty='RangeScan', start=0, stop=scan_time, npoints=num_sampling),
                             'ramsey_choice': 'Blue',
                             'qubit_index': qubit_index,
                             'phonon_index':phonon_index
                             },
                        'class_name': 'Raman<PERSON><PERSON><PERSON>',
                        'file': 'repository\\QCMOS_package\\Raman_Ramsey.py',
                        'log_level': 30}
    rid = manager.submit(BlueSidebandRamsey)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid

def get_ramsey_detuning(scan_time,num_sampling,phonon_index,qubit_index):
    rid = exp_submit(scan_time,num_sampling,phonon_index,qubit_index)
    x, y = get_result_x_y(rid, (0,))
    y = y.flatten()
    detuning = Ramsey_fit(x,y)
    return detuning

def get_phonon_fre(phonon_index, qubit_index):
    num_sampling = 41 # 蓝边带Ramsey采样点数
    set_detuning = 2000 # 每次实验人为添加的失谐量(Hz)，原则上来说，每次声子自动校准的漂移量不能够大于第一个值
    scan_time = np.array([1e-3]) # 每次实验的扫描时间终止值
    temp = parameter.Light_554.AOM_AWG_amp
    parameter.Light_554.AOM_AWG_amp = 0.1
    parameter.update_config_json()


    for i in range(len(scan_time)):
        parameter.Light_554.Motion_freq[phonon_index] -= set_detuning # 人为设置一定的失谐量
        parameter.update_config_json()

        print(f"提交蓝边带Ramsey实验，设置失谐量为{set_detuning}")
        detuning = get_ramsey_detuning(scan_time=scan_time[i], num_sampling=num_sampling, phonon_index=phonon_index, qubit_index=qubit_index)[0] # 提交实验，得到失谐量
        print(f"失谐量为{detuning}")

        parameter.Light_554.Motion_freq[phonon_index] += detuning # 更新声子频率
        parameter.update_config_json()
        
    parameter.Light_554.AOM_AWG_amp = temp
    parameter.update_config_json()
    
    return parameter.Light_554.Motion_freq[phonon_index],time.time()

def plot_data(times, fit_data_values):
    """
    绘制 times 作为 x 轴，fit_data_values 作为 y 轴的图形。

    参数:
    times (list): x 轴数据，时间戳列表。
    fit_data_values (list): y 轴数据，对应的值列表。
    """
    # 将时间戳转换为以秒为单位的相对时间
    start_time = times[0]  # 第一个时间点作为基准
    relative_times = [t - start_time for t in times]  # 计算相对时间
    
    # 创建图形
    plt.figure(figsize=(10, 6))  # 设置图形大小
    plt.plot(relative_times, fit_data_values, marker='o', linestyle='-', color='b', label='Data')  # 绘制曲线

    # 添加标题和标签
    plt.title('Times vs Phonon_frequency', fontsize=16)
    plt.xlabel('Time (s)', fontsize=14)
    plt.ylabel('Phonon_frequency (MHz)', fontsize=14)


    # 添加网格
    plt.grid(True, linestyle='--', alpha=0.6)

    # 显示图例
    plt.legend()

    # 显示图形
    plt.show()


times = []
fit_data_values = []

    
# 主循环
def main_loop():
    """
    主循环
    """
    iteration = 0
    while True:
        iteration += 1
        try:
        # 获取实验结果
            fit_data, current_time = get_phonon_fre(phonon_index=6, qubit_index="(0,)")
            fit_data /= 1e6

            # 保存实验数据
            times.append(current_time)
            fit_data_values.append(fit_data)
            clear_output(wait=True)
            plot_data(times, fit_data_values)
            logger.info(f"Iteration {iteration}: Fit data = {fit_data} at time = {current_time}")
        except Exception as e:
            logger.error(f"Error in main_loop: {e}")
        # 每轮实验的延迟时间（例如 5 秒）
        time.sleep(1)
#%%
main_loop()
#%%
