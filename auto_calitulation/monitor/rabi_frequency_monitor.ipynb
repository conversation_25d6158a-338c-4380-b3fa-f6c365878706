#%%
import os


target_directory = "C:/Users/<USER>/PycharmProjects/ionctrl_develop_new/ionctrl_develop"
os.chdir(target_directory)
#%%
from cloud.artiq_manager import *
from modules.config import LOADED_PARAMETER
from auto_calitulation.tools_for_calibration import cali_logger, get_result_x_y, change_dir
import matplotlib.pyplot as plt
from IPython.display import display, clear_output
from cloud.fit_func import Rabi_fit

#%%
# 实例化 artiq_manager 和 LOADER_PARAMETER
manager = ARTIQManager()
parameter = LOADED_PARAMETER()
logger = cali_logger(__name__)
#%%
import matplotlib.pyplot as plt
from pprint import pprint
import time 
def run_exp():
    RabiFast = {'arguments': {'X_scan_range':
                                 dict(ty='RangeScan', start=7.5e-6, stop=7.5e-6, npoints=1),
                             'rabi_choice': 'Carrier',
                             'side_choice': 'All',
                             'qubit_index': "(4,)"
                             },
                        'class_name': 'RamanRabiFast',
                        'file': 'repository\\QCMOS_package\\Raman_Rabi_Fast.py',
                        'log_level': 30}
    rid = manager.submit(RabiFast)
    if isinstance(rid, str):
        logger.info(f"rid:", rid)
    else:
        status = manager.monitor_experiment(rid)
        logger.info(status["status"])
        rid = status["rid"]
    return rid

def get_y0():
    rid = run_exp()
    x, y = get_result_x_y(rid, qubit_index=(4, ))
    print(x)
    print(y)
    y = y.flatten()
    y0 = y[0]
    return y0, time.time()

def plot_data(times, fit_data_values):
    """
    绘制 times 作为 x 轴，fit_data_values 作为 y 轴的图形。

    参数:
    times (list): x 轴数据，时间戳列表。
    fit_data_values (list): y 轴数据，对应的值列表。
    """
    # 将时间戳转换为以秒为单位的相对时间
    start_time = times[0]  # 第一个时间点作为基准
    relative_times = [t - start_time for t in times]  # 计算相对时间
    
    # 创建图形
    plt.figure(figsize=(10, 6))  # 设置图形大小
    plt.plot(relative_times, fit_data_values, marker='o', linestyle='-', color='b', label='Data')  # 绘制曲线

    # 添加标题和标签
    plt.title('Times vs Rabi', fontsize=16)
    plt.xlabel('Time (s)', fontsize=14)
    plt.ylabel('P_e', fontsize=14)

    # 添加网格
    plt.grid(True, linestyle='--', alpha=0.6)

    # 显示图例
    plt.legend()

    # 显示图形
    plt.show()


times = []
fit_data_values = []

    
# 主循环
def main_loop():
    """
    主循环
    """
    iteration = 0
    while True:
        iteration += 1
        try:
        # 获取实验结果
            fit_data, current_time = get_y0()

            # 保存实验数据
            times.append(current_time)
            fit_data_values.append(fit_data)
            clear_output(wait=True)
            plot_data(times, fit_data_values)
            logger.info(f"Iteration {iteration}: Fit data = {fit_data} at time = {current_time}")
        except Exception as e:
            logger.error(f"Error in main_loop: {e}")
        # 每轮实验的延迟时间（例如 5 秒）
        time.sleep(20)
#%%
main_loop()
#%%
