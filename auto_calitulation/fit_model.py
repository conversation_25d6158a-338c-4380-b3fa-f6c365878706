"""
用于拟合的函数模型
"""
import numpy as np
from scipy.optimize import curve_fit, differential_evolution
import matplotlib.pyplot as plt

########### 1. 函数模型 #############

def sin_for_fit(x, amp, fre, phase, shift):
    return amp * np.sin(2 * np.pi * fre * x + phase) + shift

def cos_for_fit(x, amp, fre, phase, shift):
    return amp * np.cos(2 * np.pi * fre * x + phase) + shift

def gaussian(x, A, mu, sigma):
    """高斯函数模型"""
    return A * np.exp(-(x - mu)**2 / (2 * sigma**2))

def gausssion_derivative(x, A, mu, sigma):
    """高斯函数的导数"""
    return -(x - mu) / (sigma ** 2) * A * np.exp(-(x - mu) ** 2 / (2 * sigma ** 2))

########### 2. 初始猜测 #############


def sin_init_guess(x, y):
    """对正弦函数拟合进行参数初始值估计。

    Parameters
    ----------
    x : ndarray
        时间点数据, shape=(N,) 的一维数组
    y : ndarray
        测量数据, shape=(N,) 的一维数组

    Returns
    -------
    ndarray
        初始参数估计值, shape=(4,) 的一维数组
        参数顺序为: [振幅, 频率, 相位, 偏置]

    Notes
    -----
    通过数据特征估计正弦函数的初始参数, 用于提高曲线拟合的收敛性
    """
    # 进行傅里叶变换
    Y = abs(np.fft.fft(y))
    freqs = np.fft.fftfreq(len(x), d=x[1] - x[0])[1:len(x)//2] # 计算正频率部分
    peak_freq_index = np.argmax(np.abs(Y[1:])) + 1  # 找主频的索引, 并跳过零频率
    peak_freq = np.abs(freqs[peak_freq_index])  # 根据主频索引确认主频
    
    # 使用傅里叶变换结果估计初始猜测的参数
    amp = np.max(y) - np.min(y)  # 振幅估计为最大值与最小值之差
    fre = peak_freq  # 主频估计为傅里叶变换中的主频，单位为 rad/s
    phase = 0  # 初始相位猜测为0
    shift = np.mean(y)  # 偏移量（D）为信号的平均值
    return [amp, fre, phase, shift]

def gauss_init_guess(x, y):
    """
    高斯拟合的初始猜测
    Parameters
    ----------
    x : array_like
        自变量
    y : array_like
        因变量
    Returns
    -------
    p0 : array_like
        高斯拟合系数(A: 振幅, mu: 中心, sigma: 标准差)
    """
    A = 1
    mu = (x[-1] - x[0]) / 2
    sigma = 20
    return [A, mu, sigma]

########### 3. 拟合函数 #############
def rabi_sin_fit(x, y):
    """对单个或多个 Rabi 振荡曲线进行正弦函数拟合。

    Parameters
    ----------
    x : ndarray
        时间点数据, shape=(N,) 的一维数组
    y : ndarray
        测量数据, shape=(N, m) 的二维数组, 其中 N 是时间点数, m 是曲线数量

    Returns
    -------
    ndarray
        拟合参数数组, shape=(m, 4), 每行包含一条曲线的 4 个拟合参数
        参数顺序为: [振幅, 频率, 相位, 偏置]

    Notes
    -----
    使用 sin_for_fit 函数作为拟合模型, 通过 sin_init_guess 获取初始参数估计
    """
    fit_params = []
    # 对每一列（每一条曲线）进行拟合
    for i in range(y.shape[1]):
        init_guess = sin_init_guess(x, y[:, i])
        bounds = (
            (0.0, init_guess[1]/2, 0, 0.0),  # lower
            (1.0, init_guess[1]*2, 2*np.pi, 1.0)  # upper
        )
        popt, _ = curve_fit(sin_for_fit, x, y[:, i], p0=init_guess, bounds=bounds)
        fit_params.append(popt)
    
    return np.array(fit_params)

def aod_ratio_fit(x, Y):
    """
    对输入的 x (shape: [N,]) 和 Y (shape: [N,2]) 进行一次线性回归，
    并求出两条直线的交点对应的 x 值。

    参数:
    -------
    x : np.ndarray
        一维数组，形状为 [N,]，存储 N 个自变量样本点。
    Y : np.ndarray
        二维数组，形状为 [N,2]，其中:
          - Y[:, 0] 对应第一条曲线在各个 x 处的 y 值
          - Y[:, 1] 对应第二条曲线在各个 x 处的 y 值

    返回值:
    -------
    x_intersect : float 或 None
        两条直线的交点在 x 轴上的坐标。
        若两条拟合线平行或几乎平行，则返回 None。
    y_intersect : float 或 None
        两条直线的交点在 y 轴上的坐标。
        若两条拟合线平行或几乎平行，则返回 None。
    """

    # 用 numpy.polyfit 分别对两列 Y 进行一次线性回归，得到斜率和截距
    # polyfit(x, y, deg=1) 返回 [a, b]，其中:
    #   y = a * x + b
    a1, b1 = np.polyfit(x, Y[:, 0], 1)
    a2, b2 = np.polyfit(x, Y[:, 1], 1)

    # 若斜率相同或接近相同，说明两条直线平行或几乎平行，没有唯一交点
    if np.isclose(a1, a2, atol=1e-12):
        print("警告：两条拟合线平行或几乎平行，无法求唯一交点。")
        return None, None

    # 求交点 x 坐标：令 a1 * x + b1 = a2 * x + b2
    # => x * (a1 - a2) + (b1 - b2) = 0
    # => x = (b2 - b1) / (a1 - a2)
    x_intersect = (b2 - b1) / (a1 - a2)

    # 在任意一条直线上，求出对应的 y
    # y_intersect = a1 * x_intersect + b1

    return x_intersect

def fit_gaussian(x, y):
    """
    高斯拟合
    Parameters
    ----------
    x : array_like
        自变量
    y : array_like
        因变量
    Returns
    -------
    popt : array_like
        高斯拟合系数(A: 振幅, mu: 中心, sigma: 标准差)
    """
    # 构造初始猜测
    p0 = gauss_init_guess(x, y)
    popt, pcov = curve_fit(gaussian, x, y, p0=p0)
    return popt

def alignment_process(x, y):
    """处理高斯拟合数据并判断扫描方向。

    Parameters
    ----------
    x : array_like
        扫描位置数据点
    y : array_like
        对应的测量数据点

    Returns
    -------
    tuple
        (direction, mu)
        direction : int
            扫描方向指示：
            * -1 表示已进入下降区间
            * 1 表示继续当前方向扫描
            * 0 表示拟合失败
        mu : float
            高斯拟合的中心位置

    """
    try:
        popt = fit_gaussian(x, y)
        mu = popt[1]
    except Exception as e:
        return (0, 0)
    # 4. 计算最后一个点的导数
    derivative_last_point = gausssion_derivative(x[-1], *popt)
    # 5. 判断导数是否小于 0, 如果小于 0, 则认为已经进入下降区间, 返回 -1
    if derivative_last_point < 0:
        return (-1, mu)
    else:
        return (1, mu)

########### 4. 绘图函数 #############
def plot_sin_fitted_data(x, y, fit_params, model_func):
    """绘制原始数据点和拟合曲线的对比图。

    Parameters
    ----------
    x : ndarray
        时间点数据, shape=(N,) 或 (1, N) 的数组
    y : ndarray
        测量数据, shape=(N, m) 的二维数组, 其中 N 是时间点数, m 是曲线数量
    fit_params : ndarray
        拟合参数数组, shape=(m, 4) 的二维数组, 每行包含一条曲线的拟合参数
        参数顺序为: [振幅, 频率, 相位, 偏置]
    model_func : callable
        拟合模型函数, 接收参数 (x, *params) 返回拟合值

    Returns
    -------
    None
        显示包含原始数据点和拟合曲线的图像

    Notes
    -----
    - 对每条曲线, 使用相同颜色的圆点标记原始数据和实线表示拟合结果
    - 使用插值生成更平滑的拟合曲线
    - 图像包含网格线、坐标轴标签、图例和标题
    """
    x = np.squeeze(x)
    plt.figure(figsize=(10, 6))

    # 创建更密集的 x 点用于插值
    x_dense = np.linspace(x.min(), x.max(), num=1000)

    # 获取默认的颜色循环
    colors = plt.rcParams['axes.prop_cycle'].by_key()['color']

    # 绘制原始数据和拟合结果
    for i in range(y.shape[1]):
        color = colors[i % len(colors)]  # 循环使用颜色
        
        # 绘制原始数据点，使用较低的透明度
        plt.plot(x, y[:, i], 'o', color=color, alpha=0.6, 
                label=f'data {i+1}', markersize=6)
        
        # 使用密集 x 点生成平滑的拟合曲线，使用相同的基础颜色
        fitted_y = model_func(x_dense, *fit_params[i])
        plt.plot(x_dense, fitted_y, '-', color=color, 
                label=f'fitting {i+1}', linewidth=2)
    
    plt.xlabel('time (s)')
    plt.ylabel('signal (a.u.)')
    plt.legend()
    plt.title('Rabi oscillation fitting result')
    plt.grid(True, alpha=0.3)
    plt.show()