import logging
from cloud.artiq_manager import get_result
import numpy as np
import os
def cali_logger(name: str, level=logging.INFO) -> logging.Logger:
    # 创建 logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)  # 控制台输出 INFO 级别及以上日志
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s', 
                                  datefmt='%Y-%m-%d %H:%M:%S')  # 自定义日期格式
    console_handler.setFormatter(formatter)
    
    # # 创建文件处理器
    # file_handler = logging.FileHandler('app.log')
    # file_handler.setLevel(logging.DEBUG)  # 文件输出 DEBUG 级别及以上日志
    # file_formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(name)s - %(message)s')
    # file_handler.setFormatter(file_formatter)
    
    # 添加处理器
    logger.addHandler(console_handler)

    return logger


logger = cali_logger(__name__)


def get_result_x_y(rid, qubit_index=(0, 1)):
    """
    返回某个 RID 的 x 和 y
    :param qubit_index: 选择感兴趣的离子索引
    :param rid:
    :return:
    """
    root_dir = "C://Users//Administrator//PycharmProjects//ionctrl_develop_new//ionctrl_develop//results"
    result = get_result(f"{rid}", root_dir=root_dir)
    # print("result:", result)
    x = result["datasets"]["x_points"]
    y = np.array(result["datasets"]["y_probability"])[:, qubit_index]
    return x, y


def change_dir():
    current_path = os.path.abspath("")
    logger.info(f"current_path: {current_path}")

    project_root = os.path.abspath(os.path.join(current_path, "../.."))
    os.chdir(project_root)
    logger.info(f"project_root: {project_root}")