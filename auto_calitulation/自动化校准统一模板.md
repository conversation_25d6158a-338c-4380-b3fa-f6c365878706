# 自动化校准实验文件统一模板

1. 在根目录下已经新建了文件夹: `auto_calibration` ;
2. 该文件夹中有 `calibration_script` 文件夹, 用于存放所有的校准文件;
3. `toos_for_calibration.py` 模块提供多个常用函数:
   1. `cali_logger` : 便捷的配置日志;
   2. `get_result_x_y` : 获取数据集的 x 和 Y (感兴趣的离子) 部分;
4. 每个校准实验文件通常包含如下的部分:
   1. 基本校准逻辑的定义: 如定义一个提交实验并获取其 rid;
   2. 数据处理函数;
   3. 主校准循环;
5. 在每一个校准文件之中, 建议从遵循如下实践:
   1. 引用 `toos_for_calibration.py` 文件中  `cali_logger`  函数, 并在主校准程序中关键信息处插入 logger.info();
   2. 封装提交实验获取 rid 的代码;
   3. 构造主循环.
