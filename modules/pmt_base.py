"""32通道PMT管理器基类

功能：
1. 管理数据集的准备、绘图组件的提交和更新
2. 管理数据采集、数据处理逻辑
"""

from artiq.experiment import *
from modules.config import *
import numpy as np
class PMT_Base(HasEnvironment):
    """PMT基类
    功能：
    1. 管理数据集的准备、绘图组件的提交和更新
    2. 管理数据采集、数据处理逻辑
    真实模块需要具体实现 build到 read_data 函数
    """

    def build(self):
        """
        1. 连仪器
        2. 拉参数
        """
        # 拉参数
        self.parameter = Config()  # 从配置文件获取参数
        self.repeat = self.parameter.Experiment.Repeat
        self.ions_nums = len(self.parameter.PMT.Select_channels)

    @rpc(flags={})
    def connect_pmt(self):
        pass
    @rpc(flags={"async"})
    def close_pmt(self):
        """关闭 PMT"""
        pass

    @rpc(flags={"async"})
    def resetPMT(self):
        """重置 PMT"""
        pass

    @kernel()
    def pmt_start_count(self):
        """开始 pmt 计数"""
        pass

    @kernel()
    def pmt_stop_count(self):
        """停止 pmt 计数"""
        pass

    @kernel()
    def initial(self):
        pass
    @rpc(flags={"async"})
    def read_data(self):
        """模拟读取 PMT数据
        返回：shape = (repeat,32) 的数列
        """
        data_temp = np.zeros([self.repeat, 32])
        for repeat in range(self.repeat):
            data = np.random.randint(0, 255, size=32)
            data_temp[repeat] = data
        return data_temp

    @rpc(flags={"async"})
    def read_data_double(self):
        """read pmt data twice"""
        data_temp = np.zeros([self.repeat * 2, 32])

        data_temp[:self.repeat, :] = self.read_data()
        data_temp[self.repeat:, :] = self.read_data()

        return data_temp

    @rpc(flags={"async"})
    def tprint(self, data):
        """异步打印"""
        print(data)

    ######################################
    ##
    ##          Cooling Idel
    ##
    ######################################
    def prepare_for_cooling_idle(self):
        # 1. PMT 32 channels
        self.set_dataset("PMT_channels", np.arange(0, 32), broadcast=True)  # PMT 32 通道的索引 从零开始
        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)

        self.ccb.issue("create_applet", "PMT_counts_Plot",
                       "${artiq_applet}green_plot_hist_8 PMT_counts --x PMT_channels")

        # 2. PMT big_number
        self.set_dataset("Cooling_Count", 0, broadcast=True)
        self.ccb.issue("create_applet", "Cooling_Count",
                       "${artiq_applet}big_number Cooling_Count")

        # 3. PMT data history
        self.set_dataset("Cooling_data", [], broadcast=True)
        self.ccb.issue("create_applet", "Cooling_Counts_History",
                       "${artiq_applet}green_plot_single_xy_8 Cooling_data")

        self.set_dataset("stop", 0, broadcast=True, archive=False)

    @rpc(flags={"async"})
    def data_process_for_cooling_idle(self):
        data_temp = self.read_data()

        cooling_counts_32 = np.sum(data_temp, axis=0)

        cooling_count_sum = np.sum(cooling_counts_32)

        self.set_dataset("PMT_counts", cooling_counts_32, broadcast=True)
        self.set_dataset("Cooling_Count", cooling_count_sum, broadcast=True)
        self.append_to_dataset("Cooling_data", cooling_count_sum)

    #####################################################
    ##
    ##      Micro Motion Compesation
    ##
    #####################################################
    def prepare_for_mmc(self, x_points):

        self.set_dataset("x_points", x_points, broadcast=True)
        self.set_dataset("y_probability", [np.full(self.ions_nums, np.nan)], broadcast=True)
        self.set_dataset("probability_all", [np.full(self.ions_nums, np.nan)], broadcast=True)
        self.ccb.issue("create_applet", "X-Scan",
                       "${artiq_applet}green_plot_multi_xy y_probability --x x_points")  # 提交绘图组件

        self.set_dataset("PMT_channels", np.arange(0, 32), broadcast=True)

        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)

        self.ccb.issue("create_applet", "PMT_counts_Plot",
                       "${artiq_applet}plot_pmt PMT_counts --x PMT_channels")

    @rpc(flags={"async"})
    def data_process_for_x_scan_mmc(self, reset=False):
        if reset:
            self.set_dataset("y_probability", [np.full(self.ions_nums, np.nan)], broadcast=True)
        data_temp = self.read_data()

        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num
        pmt_data = np.sum(data_temp, 0)

        counts = np.sum(select_channels_counts, axis=0)  # 1 * ion_num
        self.append_to_dataset("y_probability", counts)
        self.append_to_dataset("probability_all", counts)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data)

    ####################################################
    ##
    ##    Histogram
    ##
    ####################################################

    def prepare_for_histogram(self):
        self.set_dataset("Detecting_data", [], broadcast=True)
        self.ccb.issue("create_applet", "Detecting_Counts_History",
                       "${artiq_applet}green_plot_single_xy Detecting")

        self.set_dataset("Hist_bin", np.arange(0, 30), broadcast=True)
        self.set_dataset("Histogram", [np.full(2, np.nan)], broadcast=True)
        self.ccb.issue("create_applet", "Histogram",
                       "${artiq_applet}green_plot_multi_xy_8 Histogram --x Hist_bin")

    @rpc(flags={"async"})
    def data_process_for_histogram(self):
        data_temp = self.read_data_double()

        pump_detect_data_temp = data_temp[0::2, :]
        pump_detect_data_select_channels = self._sum_multi_channels(pump_detect_data_temp,
                                                                    self.parameter.PMT.Select_channels)

        control_detect_data_temp = data_temp[1::2, :]
        control_detect_select_channels = self._sum_multi_channels(control_detect_data_temp,
                                                                  self.parameter.PMT.Select_channels)
        # self.mutate_dataset("y_probability", probability)
        n_ions = len(pump_detect_data_select_channels[0])

        for i in range(n_ions):
            hist_dark = np.histogram(pump_detect_data_select_channels[:, i], bins=np.arange(30))[0]
            hist_bright = np.histogram(control_detect_select_channels[:, i], bins=np.arange(30))[0]
            # hist_dark = np.insert(hist_dark, 0, 0)
            # hist_bright = np.insert(hist_bright, 0, 0)
            if i == 0:
                hist_to_plot = np.array([hist_bright, hist_dark]).T
            else:
                hist_to_plot = np.hstack([hist_to_plot, np.array([hist_bright, hist_dark]).T])
        self.mutate_dataset("Histogram", (0, 29, 1), hist_to_plot)

    ##########################################
    ##
    ##  Mølmer–Sørensen Gate
    ##
    ##########################################
    def prepare_for_x_scan_ms(self, x_points):
        self.set_dataset("x_points", x_points, broadcast=True)

        self.set_dataset("y_probability", np.full((len(x_points), self.ions_nums), np.nan), broadcast=True)

        self.set_dataset("ms_data", np.full((len(x_points), 3), np.nan), broadcast=True)
        self.set_dataset("correlation", np.full((len(x_points), 4), np.nan), broadcast=True)

        self.set_dataset("PMT_channels", np.arange(1, 33), broadcast=True)
        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)

        self.set_dataset("ion_lost", 0, broadcast=True)

        self.ccb.issue("create_applet", "PMT_counts_Plot",
                       "${artiq_applet}green_plot_hist_8 PMT_counts --x PMT_channels")

        self.ccb.issue("create_applet", "MSgate",
                       "${artiq_applet}green_plot_multi_xy_8 ms_data --x x_points"
                       )

    @rpc(flags={"async"})
    def data_process_for_x_scan_ms(self, x_index,ion_index = (0,1)):

        data_temp = self.read_data()

        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num

        ion0_index = ion_index[0]
        ion1_index = ion_index[1]
        detect_ion1 = select_channels_counts[:, ion0_index] > self.parameter.PMT.Detect_threshold
        detect_ion2 = select_channels_counts[:, ion1_index] > self.parameter.PMT.Detect_threshold

        threshold_data = self._threshold_sum(select_channels_counts, self.parameter.PMT.Detect_threshold)  # 1 * ion_num

        probability = np.array((threshold_data / self.parameter.Experiment.Repeat).tolist()[0])  # 1 * ion_num

        ms_11 = np.sum((detect_ion1 & detect_ion2)) / self.parameter.Experiment.Repeat
        ms_00 = 1 - np.sum((detect_ion1 | detect_ion2)) / self.parameter.Experiment.Repeat
        ms_10 = 1 - ms_11 - ms_00
        p_10 = np.sum(np.logical_and(detect_ion1 == True, detect_ion2 == False)) / self.parameter.Experiment.Repeat
        p_01 = np.sum(np.logical_and(detect_ion1 == False, detect_ion2 == True)) / self.parameter.Experiment.Repeat

        correlation = [ms_00, p_10, p_01, ms_11]
        # return probability

        ms_data = [ms_00, p_10 + p_01, ms_11]
        pmt_data = np.sum(data_temp, 0)

        self.mutate_dataset("PMT_counts", (0, 32), pmt_data)
        self.mutate_dataset("y_probability", x_index, probability)
        self.mutate_dataset("ms_data", x_index, ms_data)
        self.mutate_dataset("correlation", x_index, correlation)

    ######################################
    ##
    ##  X_scan
    ##
    ######################################
    def prepare_for_x_scan(self, x_points, ion_choice=(0,)):
        """one dimension scan"""
        # 1. probability
        # num = len(ion_choice)
        self.set_dataset("x_points", x_points, broadcast=True)
        self.set_dataset("y_probability", [], broadcast=True)
        self.ccb.issue("create_applet", "X-Scan",
                       "${artiq_applet}ionctrl_plot_multi_xy_8_color y_probability --x x_points --rid rid")  # 提交绘图组件

        self.set_dataset("computational_basis_probability", [], broadcast=True)
        self.ccb.issue("create_applet", "X-Scan-computational-basis",
                       "${artiq_applet}ionctrl_plot_multi_xy_8_color computational_basis_probability --x x_points --rid rid")
        
        # 2. PMT 32 通道数据
        self.set_dataset("PMT_channels", np.arange(0, 32), broadcast=True)
        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)
        self.ccb.issue("create_applet", "PMT_counts_Plot",
                       "${artiq_applet}green_plot_hist_8 PMT_counts --x PMT_channels")

        # 3. cooling 历史计数
        self.set_dataset("Cooling_data", [], broadcast=True)
        self.ccb.issue("create_applet", "Cooling_Counts_History",
                       "${artiq_applet}green_plot_single_xy_8 Cooling_data")

        # 4. cooling 总计数
        self.set_dataset("Cooling_Count", 0, broadcast=True)
        self.ccb.issue("create_applet", "Cooling_Count",
                       "${artiq_applet}big_number Cooling_Count")

        # 5. computational_basis 曲线
        self.set_dataset("computational_basis_histogram", [], broadcast=True)
        self.ccb.issue("create_applet", "Computational Basis Histogram",
                       "${artiq_applet}computational_basis_histogram computational_basis_histogram")

        # 6. ion_lost
        self.set_dataset("ion_lost", 0, broadcast=True)
        self.set_dataset("one_ion_dark",0,broadcast= True)
        self.set_dataset("all_ion_lost", 0, broadcast=True)
        self.set_dataset("repeat",self.repeat,broadcast=True)

    @rpc(flags={"async"})
    def data_process_for_x_scan(self, scan_point, ion_choice=(0,)):
        """data_process_for_one_dimension scan"""
        # 1. 读取数据
        data_temp = self.read_data()

        # 2. 32 通道数据
        pmt_data_32 = np.sum(data_temp, 0)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)

        # 3. 计算基计数和概率
        self.computational_basis_data_process(data_temp,scan_point,ion_choice)

        # 4. 各离子激发概率
        self.each_ion_data_process(data_temp,scan_point)

        # 5.以detect计数总和更新big number
        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num
        cooling_count_sum = np.sum(select_channels_counts)
        self.set_dataset("Cooling_Count", cooling_count_sum, broadcast=True)

    #########################################
    ##
    ##  check ion lost
    ##
    #########################################

    @rpc(flags={"async"})
    def data_process_for_x_scan_check_ion(self, scan_point, ion_choice=(0, )):
        """
        data_process_for_one_dimension scan
        with cooling count to check if the ion is lost
        """
        # 1. 读取数据
        data_temp_all = self.read_data_double()

        # 2. 处理Cooling数据，判断离子是否丢失
        data_temp_cooling = data_temp_all[0::2, :]
        # print(data_temp_cooling)
        self.cooling_count_data_process(data_temp_cooling)

        # 4. 处理Detect数据
        data_temp = data_temp_all[1::2, :]

        # 5.PMT32通道数据
        pmt_data_32 = np.sum(data_temp, 0)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)

        # 6. 计算基计数和概率计算
        self.computational_basis_data_process(data_temp,scan_point,ion_choice)

        # 7. 各离子激发概率计算
        self.each_ion_data_process(data_temp,scan_point)

    @rpc(flags={"async"})
    def data_process_for_x_scan_ms_check_ion(self, x_index, ion_index =(0,1)):
        data_temp_all = self.read_data_double()

        # 处理Cooling数据，判断离子是否丢失
        data_temp_cooling = data_temp_all[0::2, :]
        self.cooling_count_data_process(data_temp_cooling)

        # 处理Detect数据
        data_temp = data_temp_all[1::2, :]

        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num
        ion0_index = ion_index[0]
        ion1_index = ion_index[1]
        detect_ion1 = select_channels_counts[:, ion0_index] > self.parameter.PMT.Detect_threshold
        detect_ion2 = select_channels_counts[:, ion1_index] > self.parameter.PMT.Detect_threshold

        threshold_data = self._threshold_sum(select_channels_counts, self.parameter.PMT.Detect_threshold)  # 1 * ion_num

        probability = np.array((threshold_data / self.parameter.Experiment.Repeat).tolist()[0])  # 1 * ion_num

        ms_11 = np.sum((detect_ion1 & detect_ion2)) / self.parameter.Experiment.Repeat
        ms_00 = 1 - np.sum((detect_ion1 | detect_ion2)) / self.parameter.Experiment.Repeat
        ms_10 = 1 - ms_11 - ms_00
        p_10 = np.sum(np.logical_and(detect_ion1 == True, detect_ion2 == False)) / self.parameter.Experiment.Repeat
        p_01 = np.sum(np.logical_and(detect_ion1 == False, detect_ion2 == True)) / self.parameter.Experiment.Repeat

        correlation = [ms_00, p_10, p_01, ms_11]
        # return probability

        ms_data = [ms_00, p_10 + p_01, ms_11]
        pmt_data = np.sum(data_temp, 0)

        self.mutate_dataset("PMT_counts", (0, 32), pmt_data)
        self.mutate_dataset("y_probability", x_index, probability)
        self.mutate_dataset("ms_data", x_index, ms_data)
        self.mutate_dataset("correlation", x_index, correlation)

    def prepare_for_x_scan_counts(self, x_points):
        self.set_dataset("x_points", x_points, broadcast=True)
        self.set_dataset("y_probability", [np.full(self.ions_nums, np.nan)], broadcast=True)
        self.ccb.issue("create_applet", "X-Scan",
                       "${artiq_applet}green_plot_multi_xy y_probability --x x_points")  # 提交绘图组件

        self.set_dataset("PMT_channels", np.arange(0, 32), broadcast=True)
        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)
        self.ccb.issue("create_applet", "PMT_counts_Plot",
                       "${artiq_applet}green_plot_hist PMT_counts --x PMT_channels")

        # self.set_dataset("Cooling_data", [], broadcast=True)
        # self.ccb.issue("create_applet", "Cooling_Counts_History",
        #                "${artiq_applet}green_plot_single_xy Cooling_data")

        # self.set_dataset("Cooling_Count", 0, broadcast=True)
        # self.ccb.issue("create_applet", "Cooling_Count",
        #                "${artiq_applet}big_number Cooling_Count")

    def prepare_for_x_scan_inf(self):
        # 设置数据集 probability，值为 ions_nums 个 np.nan
        self.set_dataset("y_probability", [np.full(self.ions_nums, np.nan)], broadcast=True)
        # 提交绘图组件，创建 X-Scan 绘图组件
        self.ccb.issue("create_applet", "X-Scan",
                       "${artiq_applet}green_plot_multi_xy y_probability")  # 提交绘图组件

        # 设置数据集 PMT_channels，值为 0 到 31
        self.set_dataset("PMT_channels", np.arange(0, 32), broadcast=True)
        # 设置数据集 PMT_counts，值为 32 个 np.nan
        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)
        # 提交绘图组件，创建 PMT_counts_Plot 绘图组件
        self.ccb.issue("create_applet", "PMT_counts_Plot",
                       "${artiq_applet}green_plot_hist PMT_counts --x PMT_channels")

    def prepare_cooling_counts(self):

        # 设置 Cooling_counts_real_time 数据集，初始值为 0
        self.set_dataset("Cooling_counts_real_time", 0, broadcast=True)
        # 设置 x_scale 数据集，初始值为 1
        self.set_dataset("x_scale", 1, broadcast=True)
        # 创建 Cooling_counts_realtime applet
        self.ccb.issue("create_applet", "Cooling_counts_realtime",
                       "${artiq_applet}big_number Cooling_counts_real_time")

        # 获取 PMT.Select_channels 的长度，即离子数
        self.ions_nums = len(self.parameter.PMT.Select_channels)
        # 初始化 PMT_counts_temp数组，长度为离子数，初始值为0
        self.PMT_counts_temp = np.zeros(self.ions_nums)
        # 设置 Cooling_counts_history 数据集，初始值为 0
        self.set_dataset("Cooling_counts_history", np.zeros([1, self.ions_nums]).tolist(), broadcast=True, persist=True)
        # 创建 Cooling_counts_histroy applet
        self.ccb.issue("create_applet", "Cooling_counts_histroy",
                       "${artiq_applet}plot_cooling_counts Cooling_counts_history --x_scale x_scale")

    def prepare_pmt_hist(self):
        self.set_dataset("PMT_channels", np.arange(0, 32), broadcast=True)
        self.set_dataset("Histotram_number", np.arange(1, 257), broadcast=True)

        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)
        self.set_dataset("PMT_histogram", np.full([self.ions_nums, 256], np.nan), broadcast=True)

        self.ccb.issue("create_applet", "PMT_counts_Plot",
                       "${artiq_applet}plot_pmt PMT_counts --x PMT_channels")
        self.ccb.issue("create_applet", "Select_channel_Histogram",
                       "${artiq_applet}plot_histogram PMT_histogram --x Histotram_number")

    @rpc(flags={"async"})
    def data_process_for_x_scan_inf(self):
        data_temp = self.read_data()

        pmt_data_32 = np.sum(data_temp, 0)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)

        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMTController.Select_channels)  # repeats * ion_num
        threshold_data = self._threshold_sum(select_channels_counts,
                                             self.parameter.PMTController.Detect_threshold)  # 1 * ion_num
        probability = (threshold_data / self.parameter.Experiment.Repeat).tolist()[0]  #

        self.append_to_dataset("y_probability", probability)

    @rpc(flags={"async"})
    def data_process_for_x_scan_counts(self, scan_point):

        data_temp = self.read_data()

        pmt_data_32 = np.sum(data_temp, 0)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data_32)

        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num

        sum_select_channels_counts = np.sum(select_channels_counts, axis=0)
        sum_all = np.sum(pmt_data_32)
        data_len = len(self.get_dataset("y_probability"))
        if data_len < scan_point + 2:
            self.append_to_dataset("y_probability", sum_select_channels_counts)
        else:
            self.mutate_dataset("y_probability", scan_point + 1, sum_select_channels_counts)

    @rpc(flags={})
    def data_process_for_x_scan_with_cooling_counts(self, scan_point):
        """x scan 数据处理
        在 cooling 阶段计数，并去除 cooling 阶段离子比较暗的情形
        """
        # 1. 读取数据
        data_temp = self.read_data_double()

        # 2. cooling 数据提取和处理
        cooling_data_temp = data_temp[0::2, :]
        cooling_data_select_channels = self._sum_multi_channels(cooling_data_temp,
                                                                self.parameter.PMT.Select_channels)
        # 获取比较坏的数据的索引
        bad_cooling_index = np.any(cooling_data_select_channels < self.parameter.PMT.Cooling_threshold,
                                   axis=1)

        # 删除这部分坏数据
        delete_indices = np.where(bad_cooling_index)[0]
        cooling_data_temp = np.delete(cooling_data_temp, delete_indices, axis=0)

        # 3. detecting 数据的处理
        detecting_data_temp = data_temp[1::2, :]
        detecting_data_temp = np.delete(detecting_data_temp, delete_indices, axis=0)

        # 4. cooling 总计数的处理
        cooling_data = np.sum(cooling_data_temp, axis=0)
        sum_cooling_data = np.sum(cooling_data[self.parameter.PMT.Select_channels[0]])

        self.append_to_dataset("Cooling_data", sum_cooling_data)  # 历史 cooling 计数
        self.set_dataset("Cooling_Count", sum_cooling_data, broadcast=True)  # 实时 cooling 的计数

        # detecting PMT 32 通道数据的处理和显示
        detect_data_32 = np.sum(detecting_data_temp, 0)
        self.mutate_dataset("PMT_counts", (0, 32), detect_data_32)

        # 5. detecting 数据的概率计算
        select_channels_counts = self._sum_multi_channels(detecting_data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num
        threshold_data = self._threshold_sum(select_channels_counts,
                                             self.parameter.PMT.Detect_threshold)  # 1 * ion_num
        correct_repeat = self.repeat - len(delete_indices)

        probability = (threshold_data / correct_repeat).tolist()[0]  #

        data_len = len(self.get_dataset("y_probability"))
        if data_len < scan_point + 2:
            self.append_to_dataset("y_probability", probability)
        else:
            self.mutate_dataset("y_probability", scan_point + 1, probability)

    @rpc(flags={"async"})
    def _sum_multi_channels(self, data_matrix, channels_list):

        return np.hstack([np.sum(data_matrix[:, index], axis=1, keepdims=True) for index in channels_list])

    @rpc(flags={"async"})
    def _threshold_sum(self, data_summed, threshold):

        threshold_data = np.sum(data_summed > threshold, axis=0)

        return threshold_data.reshape(1, -1)

    @rpc(flags={"async"})
    def data_process_probability(self, times=1):
        sum_counts = 0
        pmt_data = np.zeros(32)
        ion_hist = np.zeros([self.ions_nums, 256])
        ion_pro_temp = np.zeros(self.ions_nums)
        for repeat in range(times):
            count_temp = self.read_data()
            sum_counts += np.sum(count_temp)
            pmt_data += count_temp
            ion_pro_temp += self.data_process_threshold(count_temp)
            ion_hist += self.data_process_histogram(count_temp)
        self.set_dataset("Cooling_counts_real_time", sum_counts, broadcast=True)

        # self.cooling_counts_process(pmt_data)
        self.append_to_dataset("Cooling_counts_history", ion_pro_temp / times)
        self.mutate_dataset("PMT_counts", (0, 32), pmt_data)
        self.mutate_dataset("PMT_histogram", (0, 256), ion_hist)

    @staticmethod
    def _compute_histogram(data, bins=25):
        """
        用于将做完通道选择的数据转变成 histogram 数据

        Parameters
        ----------
        data: N * 2 的矩阵,
        bins: 分类区间

        Returns
        -------
        np.array: N * 2 的矩阵, 两个数据做完 histogram.
        """
        hist_data_0, bin_edges_0 = np.histogram(
            data[:, 0], bins=bins, range=(0, bins-1)
        )
        hist_data_10, bin_edges_10 = np.histogram(
            data[:, 1], bins=bins, range=(0, bins-1)
        )
        print(data[:,1])
        print(hist_data_10)
        return np.column_stack((hist_data_0, hist_data_10))

    @staticmethod
    def computational_basis_analysis(data):
        """
        将 M * N 的矩阵数据做 single shot 处理.

        Parameters
        ----------
        data: 经过**通道选择**和**阈值处理**的 M * N 的 numpy array, M 是 repeat 次数, N 是离子个数,

        Returns:
        -------
        result:
        L * 2 的 numpy array, 其中第一列是非 0 计数的 single shot 基转换到十进制, 第二列是该基上的计数.
        这意味着, 后续展示时要转换成 2 进制.
        """
        state_ids = (data @ (1 << np.arange(data.shape[1] - 1, -1, -1))).astype(int)
        unique, counts = np.unique(state_ids, return_counts=True)
        result = np.stack((unique, counts), axis=1)
        return result
    
    @staticmethod
    def computational_basis_analysis_all(data):
        """
        返回所有基的 single-shot 数据处理

        Parameters
        ----------
        data: 经过**通道选择**和**阈值处理**的 M * N 的 numpy array, M 是 repeat 次数, N 是离子个数,

        Returns
        -------
        result:
        """
        # print(f"data{data}")
        # 计算每一行对应的基的十进制表示
        state_ids = (data @ (1 << np.arange(data.shape[1] - 1, -1, -1))).astype(int)

        # 获取所有可能的基索引范围（从 0 到 2^N - 1）
        total_states = 1 << data.shape[1]

        # 使用 np.bincount 来高效统计基的出现次数
        counts = np.bincount(state_ids, minlength=total_states)

        # # 将结果构造为 2 * N 的列表
        # result = [list(range(total_states)), counts.tolist()]
        # print("counts", counts)
        return counts

    def cooling_count_data_process(self,data_temp_cooling):
        '''
        处理cooling时的计数并更新数据集
        Parameters
        ----------
        data_temp_cooling:M*32 PMT 读数，M为repeat次数

        '''
        select_channels_cooling_counts = self._sum_multi_channels(data_temp_cooling,
                                                                  self.parameter.PMT.Select_channels)
        notlost_probability = (self._threshold_sum(select_channels_cooling_counts,
                                                   self.parameter.PMT.Cooling_threshold) / self.parameter.Experiment.Repeat).tolist()[
            0]
        cooling_count_sum = np.sum(select_channels_cooling_counts)
        # 3. 判断离子是否丢失
        for p in notlost_probability:
            if p < 0.9:
                self.set_dataset("ion_lost", 1, broadcast=True)
        if all(p < 0.9 for p in notlost_probability) and cooling_count_sum*100/self.parameter.Experiment.Repeat < 1000:
            self.set_dataset("all_ion_lost",1,broadcast=True)
        if len([p for p in notlost_probability if p < 0.9]) == 1:
            self.set_dataset("one_ion_dark",1,broadcast=True)

        self.set_dataset("Cooling_Count", cooling_count_sum, broadcast=True)

    def computational_basis_data_process(self,data_temp,scan_point,ion_choice):
        '''
        计算各计算基的频次和概率并更新到数据集
        Parameters
        ----------
        data_temp:  M*32 PMT 读数，M为repeat次数
        scan_point: 更新数据点的序号
        ion_choice: 感兴趣的离子序号，元组
        '''
        # 0. 选择离子通道数据
        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num
        # 1. 选择离子通道数据
        if all(0 <= idx < select_channels_counts.shape[1] for idx in ion_choice):
            select_channels_counts = select_channels_counts[:, ion_choice]
        else:
            print("Qubit Index Not Exist. ")

        data_1 = select_channels_counts > self.parameter.PMT.Detect_threshold

        # 2. computational basis process
        data = self.computational_basis_analysis(data_1)
        self.set_dataset("computational_basis_histogram", data, broadcast=True)
        # 3. 得到 computational_basis 概率数据
        computational_basis_probability = (
                    self.computational_basis_analysis_all(data_1) / self.parameter.Experiment.Repeat).tolist()
        data_len_ = len(self.get_dataset("computational_basis_probability"))
        if data_len_ <= scan_point:
            self.append_to_dataset("computational_basis_probability", computational_basis_probability)
        else:
            self.mutate_dataset("computational_basis_probability", scan_point, computational_basis_probability)

    def each_ion_data_process(self,data_temp,scan_point):
        '''
        计算各离子的激发概率并更新到数据集
        Parameters
        ----------
        data_temp:  M*32 PMT 读数，M为repeat次数
        scan_point: 更新数据点的序号

        '''
        select_channels_counts = self._sum_multi_channels(data_temp,
                                                          self.parameter.PMT.Select_channels)  # repeats * ion_num
        # print(data_temp)
        threshold_data = self._threshold_sum(select_channels_counts, self.parameter.PMT.Detect_threshold)  # 1 * ion_num
        probability = (threshold_data / self.parameter.Experiment.Repeat).tolist()[0]  #

        data_len = len(self.get_dataset("y_probability"))
        if data_len <= scan_point:
            self.append_to_dataset("y_probability", probability)
        else:
            self.mutate_dataset("y_probability", scan_point, probability)

        return probability


    def process_for_x_scan_re_repeat(self,a):
        pass
    def qcmos_start_count(self):
        pass
    def qcmos_stop_count(self):
        pass