from modules.light_369 import *
from modules.light_554_awg import Light554 as Light554AWG
from modules.light_554_dds import Light554 as Light554DDS
from modules.pmt import PMT as PMTBase
from modules.config import LOADED_PARAMETER
from modules.servo_motor import MotorServer
from artiq.experiment import *
import xmlrpc.client
import time
from modules.signal_mw import SignalMW
class PMT(PMTBase):
    """用于自定义数据处理逻辑"""

    pass


class RamanBaseExp(HasEnvironment):

    def build(self):
        # 1. 参数获取
        self.parameter = LOADED_PARAMETER()
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.cooling2_time = self.parameter.Experiment.Cooling2_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.repeat = self.parameter.Experiment.Repeat
        self.idle_time = self.parameter.Light_554.AOD_time_before_AOM
        self.eit_time = self.parameter.Experiment.EIT_Cooling_Time
        self.eit2_time = self.parameter.Experiment.EIT_Cooling2_Time
        self.pi_2time = self.parameter.Signal_MW.mw_pi_2.zero
        # 2. 通道连接
        self.setattr_device("core")
        self.setattr_device("scheduler")
        self.setattr_argument(
            "AWG_Mode",
            EnumerationValue(["AWG", "DDS"], default="AWG"),
            tooltip="选择 DDS 模式 or AWG 模式",
            group="Experiment",
        )
        self.setattr_argument(
            "qubit_index", StringValue("(0,)"), tooltip="离子索引", group="Experiment"
        )

        self.setattr_argument(
            "ion_choice", StringValue("(0,)"), tooltip="选择展示的离子", group="Experiment"
        )

        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="扫描任务的重复次数",
            group="Experiment",
        )

        self.setattr_argument(
            "enable_SBC", BooleanValue(default=False),
            tooltip="是否使用边带冷却",
            group="Experiment")

        self.setattr_argument(
            "loop", BooleanValue(default=False),
            tooltip="是否循环执行",
            group="Experiment"
        )

        self.setattr_argument(
            "Adaptive", BooleanValue(default=False),
            tooltip="是否自适应步长",
            group="Experiment"
        )
        
        if self.AWG_Mode is not None:
            if self.AWG_Mode == "AWG":
                self.l554 = Light554AWG(self)
            elif self.AWG_Mode == "DDS":
                self.l554 = Light554DDS(self)
            else:
                raise "Unknown AWG_Mode"

        self.l369 = Light369(self)
        self.pmt = PMT(self)
        self.wm = SignalMW(self)
    def prepare(self):
        self.qubit_index = eval(self.qubit_index)  # 获取离子索引
        self.motor = MotorServer()
        self.scan_parameter = self.X_scan_range.sequence
        self.scan_point = 0

        # 用于记录连续救离子次数
        self.save_ions_time = 0
        self.max_save_time = 100

        #如果勾选loop，则会循环1万次
        if self.loop:
            self.task_num = 10000

        self.proxy_RF = xmlrpc.client.ServerProxy("http://***************:8000",allow_none=True)

    @rpc(flags={""})
    def set_rid(self):
        # 读取当前实验rid
        rid = int(list(self.scheduler.get_status().keys())[0])
        self.set_dataset('rid',rid,broadcast=True)

    def analyse(self):
        pass
    @rpc(flags={"async"})
    def tprint(self, data):
        """用于 artiq 环境中的异步数据打印"""
        print(data)
    @rpc(flags={""})
    def prepare_dataset(self):
        pass

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        pass

    @rpc(flags={""})
    def data_process(self, scan_point):
        """数据处理的逻辑"""
        self.pmt.data_process_for_x_scan_check_ion(scan_point, eval(self.ion_choice))

    @rpc(flags={""})
    def save_ions(self):
        """救离子的逻辑"""
        # 目前只加入开离子化光救离子的代码
        # self.motor.open_ionization(5)
        print("ion lost at ",time.time())
        rid = int(list(self.scheduler.get_status().keys())[0])
        print("rid",rid)
        with open("lost_time", "a") as file:
            file.write(f"{time.time()} - {rid}\n")

        all_ion_lost = self.get_dataset("all_ion_lost")
        one_ion_dark = self.get_dataset("one_ion_dark")
        if all_ion_lost == 1:
            # 如果全暗，认为是雾化，启动救离子
            self.motor.open_channel(self.motor.ionization_channel)
            time.sleep(3)
            self.proxy_RF.shake()
            time.sleep(2)
            self.motor.close_channel(self.motor.ionization_channel)
            time.sleep(10)
            self.set_dataset("all_ion_lost", 0, broadcast=True)
        else:
            # 否则，暂停实验5秒等760发力
            time.sleep(5)
        # else:
        #
        #     self.schedule.request_termination(self.get_dataset("rid"))

        self.set_dataset("ion_lost", 0, broadcast=True)

    @rpc(flags={})
    def check_lost(self) -> TBool:
        """检查离子是否丢失"""
        # return False #取消自动救离子
        lost = self.get_dataset("ion_lost")
        if lost == 0 :
            self.save_ions_time = 0
        if lost == 1:
            self.save_ions_time += 1
        print(f"Ion_State: {lost}")
        if self.save_ions_time > self.max_save_time:
            self.scheduler.request_termination(self.get_dataset("rid"))
            print("save ion time reach maximum")
        return lost == 1

    @kernel()
    def run_level_1(self, operation_time):
        """单次 repeat 中的时序"""
        for _ in range(self.repeat):
            self.l369.switch_to_cooling2()
            self.pmt.pmt_start_count()
            delay(self.cooling2_time)

            self.l369.switch_to_cooling1()
            delay(self.cooling_time)
            self.pmt.pmt_stop_count()


            self.l369.switch_to_eit()
            delay(self.eit_time)
            self.l369.switch_to_eit2()
            delay(self.eit2_time)

            self.l369.switch_to_pump()
            delay(self.pumping_time)

            # with parallel:
            self.l369.switch_to_control()
            #
            # self.wm.mw_on()
            # delay(self.pi_2time*2)
            # self.wm.mw_off()

            delay(0.0 * ms)
            self.l554.AWG_on()

            with parallel:
                self.l369.sbc_cooling(self.enable_SBC)
                delay(operation_time)

            # delay(2*ms)


            with parallel:
                self.l554.AWG_off()
                self.pmt.pmt_start_count()
                self.l369.switch_to_detect()

            delay(self.detecting_time)

            with parallel:
                self.pmt.pmt_stop_count()
                self.l369.switch_to_cool()

    @kernel()
    def run_level_2(self, scan_parameter):
        """加入一个扫描点的波形计算和数据处理"""
        while True:
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter)

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1(operation_time)

            self.core.wait_until_mu(now_mu())

            # 3. 数据处理
            self.data_process(self.scan_point)

            # 4. 离子状态检查
            # delay(10*s)
            if self.check_lost():
                self.save_ions()  # 封装救离子的逻辑
            else:
                self.scan_point += 1
                self.set_dataset("scan_point",self.scan_point,broadcast=True)
                break
            # 添加优雅中止
            if self.scheduler.check_termination():
                break

    @kernel()
    def run_level_3(self, scan_list):
        """N 个扫描点"""
        self.scan_point = 0
        while self.scan_point < len(scan_list):
            self.run_level_2(scan_list[self.scan_point])
            # 添加优雅中止

            scan_list = self.update_scan_list(scan_list)
            if self.scheduler.check_termination():
                break
    @rpc(flags={""})
    def update_scan_list(self,scan_list)->TList(TFloat):
        new_step = self.get_new_step()
        scan_point = self.get_dataset("scan_point")
        if self.add_point_to_list():
            try:
                if scan_list[scan_point]-scan_list[scan_point-1]>new_step*1.1:
                    print(11)
                    scan_list.insert(scan_point, scan_list[scan_point-1]+new_step)
                    print(scan_list)

                    self.update_x(scan_list)
            except:
                pass
        else:
            pass
        return scan_list
    def get_new_step(self):
        return (self.scan_parameter[1]-self.scan_parameter[0])/4
    def update_x(self,scan_list):
        x_points = (np.array(scan_list))
        self.set_dataset("x_points", x_points, broadcast=True)
    def add_point_to_list(self):
        return False
    @kernel()
    def run_task_num(self, task_num, scan_list):
        """多次任务"""
        for _ in range(task_num):
            self.run_level_3(scan_list)
            if self.scheduler.check_termination():
                break

    @kernel()
    def exp_initial(self):
        """exp 初始化"""
        self.set_rid()
        self.prepare_dataset()
        self.core.break_realtime()
        self.core.reset()
        self.pmt.initial()
        self.l554.initial()
        self.l369.initial()
        self.core.break_realtime()

    @kernel
    def run(self):
        self.exp_initial()
        self.run_task_num(self.task_num, self.scan_parameter)
        