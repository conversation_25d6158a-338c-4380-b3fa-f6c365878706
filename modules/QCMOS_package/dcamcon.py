"""DCAM 控制台操作模块.

本模块实现了在控制台上控制 DCAM 的功能函数。主要用于:
1. 当检测到多个设备时,选择要使用的设备
2. 设置指定属性的值
3. 提供交互式的 DCAM 控制界面

Notes
-----
本模块依赖于 dcam.py 模块提供的底层 DCAM API 接口

Examples
--------
>>> from .dcamcon import *
>>> dcamcon_init()  # 初始化 DCAM
>>> my = dcamcon_choose_and_open()  # 选择并打开设备
>>> my.allocbuffer(10)  # 分配 10 帧缓冲区
>>> my.startcapture()  # 开始采集
"""

__copyright__ = 'Copyright (C) 2024 Hamamatsu Photonics K.K.'

from .dcam import *
# for control DCAM functions

# 标记 dcamapi_init() 是否已被调用且成功
called_dcamapi_init = False

# Dcamcon 实例数组,在 dcamcon_init() 中初始化
dcamcon_list = []

class PromptRestrictMode(IntEnum):
    """prompt_propvalue() 的限制标志.
    
    用于在 prompt_propvalue() 中限制数据选择类型的标志。
    
    Attributes
    ----------
    No : int
        无限制
    ModeFilter : int
        通过指定列表过滤
    ClipMinimum : int
        通过指定值限制最小值
    """
    No = 0
    ModeFilter = 1
    ClipMinimum = 2



class Dcamcon:
    """DCAM-API 控制台交互类.
    
    提供了易于使用的交互式 DCAM-API 控制功能。
    主要用于相机功能的示例演示。
    
    Attributes
    ----------
    deviceindex : int
        设备索引号
    dcam : Dcam
        Dcam 实例
    device_title : str
        设备标题,由 MODEL + CAMERAID + BUS 组成
        用于 OpenCV 窗口标题和相机列表显示
    """

    def __init__(self):
        """初始化 Dcamcon 实例."""
        self.deviceindex = -1
        self.dcam = None
        self.device_title = None
        self.__number_of_frames = 10

    def close(self):
        """关闭 Dcam 设备.
        
        调用 Dcam.close() 并将 self.dcam 设为 None
        
        Returns
        -------
        bool
            操作结果
        """
        if self.dcam is None:
            return True
        
        if not self.dcam.dev_close():
            print('-NG: Dcam.dev_close() failed with error {}'.format(self.dcam.lasterr().name))
            return False
        
        self.dcam = None
        return True
        
    def allocbuffer(self, number_of_frames):
        """分配帧缓冲区.
        
        使用 Dcam.buf_alloc() 分配缓冲区。
        如果成功,分配的帧数将保存在 self.__number_of_frames 中。
        
        Parameters
        ----------
        number_of_frames : int
            要分配的帧缓冲区数量
        
        Returns
        -------
        bool
            操作结果
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False

        if not self.dcam.buf_alloc(number_of_frames):
            print('-NG: Dcam.buf_alloc({}) failed with error {}'.format(number_of_frames, self.dcam.lasterr().name))
            return False
        
        self.__number_of_frames = number_of_frames
        return True
    
    def releasebuffer(self):
        """释放已分配的帧缓冲区.
        
        使用 Dcam.buf_release() 释放已分配的缓冲区。
        
        Returns
        -------
        bool
            操作结果
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        if not self.dcam.buf_release():
            print('-NG: Dcam.buf_release() failed with error {}'.format(self.dcam.lasterr().name))
            return False
        
        return True
    
    def startcapture(self, is_sequence = True):
        """开始图像采集.
        
        使用 Dcam.cap_start() 开始采集。
        如果失败,将显示错误信息。
        
        Parameters
        ----------
        is_sequence : bool, optional
            True 表示连续采集,False 表示单次采集,默认为 True
        
        Returns
        -------
        bool
            操作结果
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        if not self.dcam.cap_start(is_sequence):
            print('-NG: Dcam.cap_start() failed with error {}'.format(self.dcam.lasterr().name))
            return False
        
        return True
    
    def stopcapture(self):
        """停止图像采集.
        
        使用 Dcam.cap_stop() 停止采集。
        如果失败,将显示错误信息。
        
        Returns
        -------
        bool
            操作结果
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        if not self.dcam.cap_stop():
            print('-NG: Dcam.cap_stop() failed with error {}'. format(self.dcam.lasterr().name))
            return False
        
        return True
    
    def is_capstaus_ready(self):
        """检查 DCAMCAP_STATUS 是否为 READY 状态.
        
        调用 Dcam.cap_status() 并检查返回值是否为 READY
        
        Returns
        -------
        bool
            如果状态为 READY 返回 True,否则返回 False
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        capstatus = self.dcam.cap_status()
        if capstatus is False:
            print('-NG: Dcam.cap_status() failed with error {}'.format(self.dcam.lasterr().name))
            return False
        
        return capstatus == DCAMCAP_STATUS.READY
    
    def firetrigger(self):
        """触发软件触发.
        
        当触发源为 SOFTWARE 时,发送软件触发信号。
        
        Returns
        -------
        bool
            操作结果
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        if not self.dcam.cap_firetrigger():
            print('-NG: Dcam.cap_firetrigger() failed with error {}'.format(self.dcam.lasterr().name))
            return False
        
        return True
    
    def wait_capevent_frameready(self, timeout_millisec):
        """等待帧就绪事件.
        
        等待指定时间内的帧就绪事件。
        如果在超时时间内收到帧就绪事件,返回 True。
        否则返回 DCAMERR。
        
        Parameters
        ----------
        timeout_millisec : int
            等待超时时间,单位为毫秒
        
        Returns
        -------
        bool or DCAMERR
            成功时返回 True,失败时返回 DCAMERR
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        if not self.dcam.wait_capevent_frameready(timeout_millisec):
            return self.dcam.lasterr()
        
        return True
    
    def get_lastframedata(self):
        """获取最后一帧图像数据.
        
        使用 Dcam.buf_getlastframedata() 获取最后一帧图像。
        如果成功,返回包含图像数据的 NumPy ndarray。
        
        Returns
        -------
        numpy.ndarray or bool
            成功时返回包含图像数据的 NumPy ndarray,失败时返回 False
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        ret = self.dcam.buf_getlastframedata()
        # print("ret:", ret)
        if ret is False:
            print('-NG: Dcam.buf_getlastframedata() failed with error {}'.format(self.dcam.lasterr().name))
            return False
        
        return ret
    
    def get_transferinfo(self):
        """获取传输状态信息.
        
        获取已采集的总帧数和最后一帧的索引。
        
        Returns
        -------
        tuple(int, int) or bool
            成功时返回元组 (最后一帧索引, 已采集帧数),失败时返回 False
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        captransferinfo = self.dcam.cap_transferinfo()
        if captransferinfo is False:
            print('-NG: Dcam.cap_transferinfo() failed with error {}'.format(self.dcam.lasterr().name))
            return False

        if captransferinfo.nFrameCount < 1:
            print('-NG: There are no images retrieved.')
            return False
        
        return (captransferinfo.nNewestFrameIndex, captransferinfo.nFrameCount)

    def save_rawimages(self, prefix):
        """保存已采集的图像为原始数据格式.
        
        将已采集并保留的图像保存为原始数据格式。
        输出文件名格式为 "{prefix} - {frameindex}.raw"
        其中 frameindex 从 1 开始,按图像从旧到新的顺序编号。
        
        Parameters
        ----------
        prefix : str
            输出文件名的前缀
        
        Returns
        -------
        bool
            操作结果
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        captransferinfo = self.dcam.cap_transferinfo()
        if captransferinfo is False:
            print('-NG: Dcam.cap_transferinfo() failed with error {}'.format(self.dcam.lasterr().name))
            return False
        
        if captransferinfo.nFrameCount < 1:
            print('-NG: There are no images retrieved.')
            return False
        
        if captransferinfo.nFrameCount > self.__number_of_frames:
            number_of_images = self.__number_of_frames
            start_frameindex = (captransferinfo.nNewestFrameIndex + 1) % self.__number_of_frames
        else:
            number_of_images = captransferinfo.nFrameCount
            start_frameindex = 0
        
        for i in range(0, number_of_images, 1):
            index = (start_frameindex + i) % self.__number_of_frames
            datai = self.dcam.buf_getframedata(index)
            filename = '{} - {}.raw'.format(prefix, i+1)
            datai.tofile(filename)
        
        return True
    
    def get_propertyvalue(self, propid:IntEnum, showerrmsg=True):
        """获取属性值.
        
        使用 Dcam.prop_getvalue() 获取属性值。
        当 showerrmsg 为 True 时,如果 Dcam.prop_getvalue() 返回 False,
        将显示错误信息。showerrmsg 默认为 True。
        当错误是预期的情况时,可以将 showerrmsg 设为 False。
        
        Parameters
        ----------
        propid : IntEnum
            DCAM_IDPROP IntEnum 类型的属性 ID
        showerrmsg : bool, optional
            是否显示错误信息,默认为 True
        
        Returns
        -------
        float or bool
            成功时返回获取的值,失败时返回 False
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        propvalue = self.dcam.prop_getvalue(propid.value)
        if propvalue is False:
            if showerrmsg:
                print('-NG: Dcam.prop_getvalue({}) failed with error {}'.format(propid.name, self.dcam.lasterr().name))
            return False
        
        return propvalue
    
    def set_propertyvalue(self, propid:IntEnum, val):
        """设置属性值.
        
        使用 Dcam.prop_setvalue() 设置属性值。
        如果 Dcam.prop_setvalue() 返回 False,将显示错误信息。
        
        Parameters
        ----------
        propid : IntEnum
            DCAM_IDPROP IntEnum 类型的属性 ID
        val : float
            要设置的值
        
        Returns
        -------
        bool
            操作结果
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        if not self.dcam.prop_setvalue(propid, val):
            print('-NG: Dcam.prop_setvalue({}, {}) failed with error {}'.format(propid.name, val, self.dcam.lasterr().name))
            return False
        
        return True
    
    def setget_propertyvalue(self, propid:IntEnum, val):
        """设置并获取属性值.
        
        使用 Dcam.prop_setgetvalue() 设置并获取属性值。
        如果成功,返回获取的值。如果失败,返回 False。
        
        Parameters
        ----------
        propid : IntEnum
            DCAM_IDPROP IntEnum 类型的属性 ID
        val : float
            要设置的值
        
        Returns
        -------
        float or bool
            成功时返回获取的值,失败时返回 False
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        res = self.dcam.prop_setgetvalue(propid, val)
        if res is False:
            print('-NG: Dcam.prop_setgetvalue({}, {}) failed with error {}'.format(propid.name, val, self.dcam.lasterr().name))
            return False
        
        return res

    def prompt_propvalue(self, propid, restrictmode=PromptRestrictMode.No, restrictval=None):
        """在提示符下设置属性值.
        
        在提示符下设置由 propid 指定的属性。
        如果成功,返回设置的值。
        如果失败,返回 False。
        
        Parameters
        ----------
        propid : IntEnum
            DCAM_IDPROP IntEnum 类型的属性 ID
        restrictmode : PromptRestrictMode, optional
            限制模式,默认为 PromptRestrictMode.No
        restrictval : float or list, optional
            限制值,默认为 None
        
        Returns
        -------
        float or None or bool
            - 如果属性不可用,返回 None
            - 如果属性可用,成功时返回设置的值,失败时返回 False
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        val = None
        propattr = self.dcam.prop_getattr(propid)
        if propattr is False:
            # error happened
            return None
        
        min = propattr.valuemin
        max = propattr.valuemax
        step = propattr.valuestep
        default = propattr.valuedefault

        if (propattr.attribute & DCAM_PROP.ATTR.EFFECTIVE and
            propattr.attribute & DCAM_PROP.ATTR.WRITABLE and
            min != max):
            proptype = propattr.attribute & DCAM_PROP.TYPE.MASK
            if proptype == DCAM_PROP.TYPE.MODE:    # change value to text
                def gettextvaluelist(propid):
                    """Return supported values.
                    Returns an array of the supported values for text.
                    
                    Args:
                        propid (int): Property ID
                    
                    Returns: value list
                    """
                    currentvalue = min
                    valuelist = []
                    if (restrictmode != PromptRestrictMode.ModeFilter or
                        currentvalue in restrictval):
                        valuelist.append(int(currentvalue))
                    while currentvalue != max:
                        currentvalue = self.dcam.prop_queryvalue(propid, currentvalue, DCAMPROP_OPTION.NEXT)
                        if (restrictmode != PromptRestrictMode.ModeFilter or
                            currentvalue in restrictval):
                            valuelist.append(int(currentvalue))
                    return valuelist
                
                valuelist = gettextvaluelist(propid)
                value_is_good = False
                while not value_is_good:
                    print()
                    prompt = '\nEnter a [value] for ' + str(self.dcam.prop_getname(propid)) + ' between:\n'
                    for textval in valuelist:
                        valuetext = self.dcam.prop_getvaluetext(propid, textval)
                        prompt += '[{}]'.format(int(textval)) + valuetext + '\n'

                    valuetext = self.dcam.prop_getvaluetext(propid, default)
                    prompt += '\n[default] ' + valuetext
                    prompt += '\n\n>'
                    try:
                        instr = input(prompt)
                        val = int(instr)
                    except ValueError:
                        val = int(default)
                        break
                    value_is_good = (val in valuelist)
            elif proptype == DCAM_PROP.TYPE.LONG:
                if (restrictmode == PromptRestrictMode.ClipMinimum and min < restrictval):
                    min = restrictval
                while True:
                    print()
                    prompt = '\nEnter a value for ' + str(self.dcam.prop_getname(propid))
                    prompt += ' between ' + str(int(min))
                    prompt += ' and ' + str(int(max))
                    prompt += ' in steps of ' + str(int(step))
                    prompt += ' [default is ' + str(int(default)) + ']'
                    prompt += '\n\n> '
                    try:
                        instr = input(prompt)
                        val = int(instr)
                    except ValueError:
                        val = int(default)
                        break

                    if (val % int(step) == 0 and
                        val >= int(min) and
                        val <= int(max)):
                        break
            elif proptype == DCAM_PROP.TYPE.REAL:
                if (restrictmode == PromptRestrictMode.ClipMinimum and min < restrictval):
                    min = restrictval
                while True:
                    print()

                    def get_units(unitid):
                        unitlist = {
                            DCAMPROP_UNIT.SECOND: 's',
                            DCAMPROP_UNIT.CELSIUS: '°C',
                            DCAMPROP_UNIT.KELVIN: 'K',
                            DCAMPROP_UNIT.METERPERSECOND: 'm/s',
                            DCAMPROP_UNIT.PERSECOND: '/s',
                            DCAMPROP_UNIT.DEGREE: '°',
                            DCAMPROP_UNIT.MICROMETER: 'µm',
                        }
                        unitstr = unitlist.get(unitid, '')
                        return unitstr
                    
                    unitname = get_units(propattr.iUnit)
                    minstr = '{:.6f}'.format(min).rstrip('0')
                    if minstr[-1] == '.':
                       minstr += '0'
                    maxstr = '{:.6f}'.format(max).rstrip('0')
                    if maxstr[-1] == '.':
                        maxstr += '0'
                    stepstr = '{:.6f}'.format(step).rstrip('0')
                    if stepstr[-1] == '.':
                        stepstr += '0'
                    defstr = '{:.6f}'.format(default).rstrip('0')
                    if defstr[-1] == '.':
                        defstr += '0'
                    prompt = '\nEnter a value for ' + str(self.dcam.prop_getname(propid))
                    prompt += ' between ' + minstr + unitname
                    prompt += ' and ' + maxstr + unitname
                    prompt += ' in steps of ' + stepstr + unitname
                    prompt += ' [default is ' + defstr + unitname + ']'
                    prompt += '\n\n> '
                    try:
                        instr = input(prompt)
                        val = float(instr)
                    except ValueError:
                        val = default
                        break
                    if (val >= min and
                        val <= max):
                        # ignore step for REAL check due to float precision possible problems.
                        # nomally the property has AUTOROUNDING
                        break
        
        if val is not None:
            val = self.set_propertyvalue(propid, val)

        return val
    
    def _prompt_longpropvalue_stack(self, propid, clipmax=False):
        """Get to set value of long property.
        Prompt to input setting value, but not set to DCAM.
        Input value is returned if success

        Args:
            propid (DCAM_IDPROP): property id
            clipmax (bool or int): clip maximum of range if clipmax is not False

        Returns:
            None: attribute does not have EFFECTIVE or(and) WRITABLE
            int: input value
            bool: False if failure. 
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        val = None
        propattr = self.dcam.prop_getattr(propid)
        if propattr is False:
            # error happened
            return False
        
        attribute = propattr.attribute
        proptype = attribute & DCAM_PROP.TYPE.MASK
        if proptype != DCAM_PROP.TYPE.LONG:
            # not support
            return False
        
        min = propattr.valuemin
        max = propattr.valuemax
        if clipmax is not False:
            max = clipmax
        step = propattr.valuestep
        default = propattr.valuedefault

        if (attribute & DCAM_PROP.ATTR.EFFECTIVE and
            attribute & DCAM_PROP.ATTR.WRITABLE and
            min != max):
            while True:
                print()
                prompt = '\nEnter a value for ' + str(self.dcam.prop_getname(propid))
                prompt += ' between ' + str(int(min))
                prompt += ' and ' + str(int(max))
                prompt += ' in steps of ' + str(int(step))
                prompt += ' [default is ' + str(int(default)) + ']'
                prompt += '\n\n> '
                try:
                    instr = input(prompt)
                    val = int(instr)
                except ValueError:
                    val = int(default)
                    break

                if (val % int(step) == 0 and
                    val >= int(min) and
                    val <= int(max)):
                    break
            
        return val
    
    def prompt_propvalue_subarray(self):
        """Set property value related subarray at the prompt.
        Set following properties at the prompt.
        DCAM_IDPROP.SUBARRAYMODE,
        DCAM_IDPROP.SUBARRAYHPOS, DCAM_IDPROP.SUBARRAYHSIZE,
        DCAM_IDPROP.SUBARRAYVPOS, DCAM_IDPROP.SUBARRAYVSIZE,
        Control offset and size combinations

        Returns:
            bool: result
        """
        if self.dcam is None:
            print('-NG: Dcamcon is not opened')
            return False
        
        res = self.prompt_propvalue(DCAM_IDPROP.SUBARRAYMODE)
        if res is None:
            # not available
            return True
        elif res is False:
            # error happened
            return False

        res = True
        subarraymode = self.get_propertyvalue(DCAM_IDPROP.SUBARRAYMODE)
        if subarraymode is False:
            # error happened
            return False
        elif subarraymode == DCAMPROP.MODE.OFF:
            # not need setting subarray parameters
            res = True
        else:
            # subarraymode == DCAMPROP.MODE.ON
            def prompt_offset_and_size(offsetid, sizeid):
                """Set subarray offset and size.
                Set subarray offset and size.

                Args:
                    offsetid (DCAM_IDPROP): SUBARRAYHPOS or SUBARRAYVPOS
                    sizeid (DCAM_IDPROP): SUBARRAYHSIZE or SUBARRAYVSIZE
                
                Returns:
                    None: if property is not available.
                """
                propattr_offset = self.dcam.prop_getattr(offsetid)
                if propattr_offset is False:
                    # error happen
                    return False
                
                propattr_size = self.dcam.prop_getattr(sizeid)
                if propattr_size is False:
                    # error happen
                    return False
                
                is_offset_available = (propattr_offset.attribute & DCAM_PROP.ATTR.EFFECTIVE and
                                       propattr_offset.attribute & DCAM_PROP.ATTR.WRITABLE and
                                       propattr_offset.valuemin != propattr_offset.valuemax)
                
                is_size_available = (propattr_size.attribute & DCAM_PROP.ATTR.EFFECTIVE and
                                     propattr_size.attribute & DCAM_PROP.ATTR.WRITABLE and
                                     propattr_size.valuemin != propattr_size.valuemax)
                
                if (is_offset_available and
                    is_size_available):
                    # set both offset and size
                    offsetval = self._prompt_longpropvalue_stack(offsetid)
                    if offsetval is False:
                        # error happened
                        res = False
                    elif offsetval is None:
                        # not need to set offset. prompt to set size
                        res = self.prompt_propvalue(sizeid)
                    else:
                        # offsetval is int value. temporarily suspend setting
                        # clip the maximum of size by subtracting offsetval
                        clipmax = propattr_size.valuemax - offsetval
                        sizeval = self._prompt_longpropvalue_stack(sizeid, clipmax)
                        if sizeval is False:
                            res = False
                        elif sizeval is None:
                            # not need to set size. set offset value
                            res = self.set_propertyvalue(offsetid, offsetval)
                        else:
                            # sizeval is int value. need to consider setting order
                            cursize = self.get_propertyvalue(sizeid)
                            if cursize is False:
                                res = False
                            else:
                                if sizeval < cursize:
                                    if (self.set_propertyvalue(sizeid, sizeval) and
                                        self.set_propertyvalue(offsetid, offsetval)):
                                        res = True
                                    else:
                                        res = False
                                else:
                                    if (self.set_propertyvalue(offsetid, offsetval) and
                                        self.set_propertyvalue(sizeid, sizeval)):
                                        res = True
                                    else:
                                        res = False
                elif is_offset_available:
                    # prompt to set offset only
                    res = self.prompt_propvalue(offsetid)
                elif is_size_available:
                    # prompt to set size only
                    res = self.prompt_propvalue(sizeid)
                else:
                    # nothing to configure
                    res = True
                
                return res
            
            # horizontal
            if prompt_offset_and_size(DCAM_IDPROP.SUBARRAYHPOS, DCAM_IDPROP.SUBARRAYHSIZE) is False:
                return False

            # vertical
            if prompt_offset_and_size(DCAM_IDPROP.SUBARRAYVPOS, DCAM_IDPROP.SUBARRAYVSIZE) is False:
                return False
            
            res = True
        
        return res

def dcamcon_init():
    """初始化 DCAM 并创建设备列表.
    
    主要功能：
    1. 初始化 DCAM-API 系统
    2. 检测并统计可用的相机设备
    3. 获取每个相机的详细信息
    4. 创建相机控制实例列表
    
    工作流程：
    1. 检查是否已初始化
    2. 调用 DCAM-API 初始化
    3. 获取设备数量
    4. 遍历设备并收集信息
    5. 创建设备控制实例
    
    Returns
    -------
    bool
        初始化成功返回 True，失败返回 False
    """
    # 使用全局变量标记初始化状态
    global called_dcamapi_init
    if called_dcamapi_init:
        return True  # 如果已经初始化过，直接返回成功
    
    # 首次初始化 DCAM-API
    print('Calling Dcamapi.init()')
    if not Dcamapi.init():
        print('-NG: Dcamapi.init() failed with error {}'.format(Dcamapi.lasterr().name))
        Dcamapi.uninit()  # 初始化失败，清理资源
        return False
    
    # 标记初始化成功
    called_dcamapi_init = True

    # 获取系统中可用的相机数量
    cameracount = Dcamapi.get_devicecount()
    if cameracount <= 0:
        print('-NG: Dcamapi.init() succeeded but not device is available.')
        return False  # 没有检测到相机设备
    
    # 更新设备列表
    global dcamcon_list
    dcamcon_list = []  # 清空现有设备列表
    
    # 遍历每个检测到的相机设备
    for icamera in range(cameracount):
        # 创建相机实例
        dcam = Dcam(icamera)
        device_title = '设备 #{}: '.format(icamera)  # 设备标题起始部分

        # 获取相机型号信息
        model = dcam.dev_getstring(DCAM_IDSTR.MODEL)
        text = ''
        if model is False:
            text = '型号：未知'  # 无法获取型号
        else:
            text = '型号：{}'.format(model)  # 记录型号信息
        device_title += text

        # 获取相机 ID 信息
        cameraid = dcam.dev_getstring(DCAM_IDSTR.CAMERAID)
        text = ''
        if cameraid is False:
            text = ' | 序列号：未知'  # 无法获取相机 ID
        else:
            text = ' | 序列号：{}'.format(cameraid)  # 记录相机 ID
        device_title += text

        # 获取相机总线（接口）信息
        bus = dcam.dev_getstring(DCAM_IDSTR.BUS)
        text = ''
        if bus is False:
            text = ' | 接口：未知'  # 无法获取总线信息
        else:
            text = ' | 接口：{}'.format(bus)  # 记录总线信息
        device_title += text

        # 创建并初始化相机控制实例
        my = Dcamcon()
        my.iCamera = icamera  # 设置相机索引
        my.device_title = device_title  # 设置设备标题（包含所有收集到的信息）
        my.dcam = None  # 初始化时不打开设备

        # 将相机实例添加到设备列表
        dcamcon_list.append(my)
    
    return True  # 初始化成功

def dcamcon_uninit():
    """清除设备列表并反初始化 DCAM.
    
    清除设备列表并反初始化 DCAM-API。
    会执行以下操作:
    1. 关闭所有打开的设备
    2. 停止所有正在进行的采集
    3. 释放所有已分配的缓冲区
    4. 清空设备列表
    5. 反初始化 DCAM-API
    """
    # close device
    global dcamcon_list
    for my in dcamcon_list:
        if my.dcam is None:
            # not opened or closed
            continue
        
        my.stopcapture()    # Stop capturing. No effect if already stopped

        if my.is_capstatus_ready():
            my.releasebuffer()

        my.close()
        my.dcam = None
    
    # clear device list
    dcamcon_list.clear()

    # uninitialize DCAM-API
    global called_dcamapi_init
    if called_dcamapi_init:
        Dcamapi.uninit()
        called_dcamapi_init = False

def dcamcon_choose_and_open():
    """选择并打开设备.
    
    从 dcamcon_list 中选择 DCAM 设备并打开。
    如果只有一个设备,则直接打开该设备。
    如果有多个设备,则提示用户选择要打开的设备。
    
    Returns
    -------
    Dcamcon or None
        成功时返回已打开设备的 Dcamcon 实例,失败时返回 None
    """
    global dcamcon_list
    devicecount = len(dcamcon_list)
    if devicecount <= 0:
        # print('-NG: No device is available.')
        return None
    
    idevice = 0
    if devicecount == 1:
        # print(dcamcon_list[0].device_title)
        pass
    else:
        # mean devicecount > 1

        # print device list
        devicelist = ''
        for dcamcon in dcamcon_list:
            devicelist += devicelist + dcamcon.device_title + '\n'
        
        print(devicelist)

        # choose device index
        fmt = '\n# Choose device index between 0 - {}. [default] is 0\n '
        prompt = fmt.format(devicecount - 1)
        while True:
            instr = input(prompt)
            if instr == '':
                # default index
                idevice = 0
                break
            
            try:
                idevice = int(instr)
            except ValueError:
                idevice = -1
            
            if (idevice >= 0 and
                idevice < devicecount):
                break
        
    dcam = Dcam(idevice)
    if not dcam.dev_open():
        # print('-NG: Dcam.dev_open() failed with error {}'.format(dcam.lasterr().name))
        return None
    
    dcamcon_list[idevice].dcam = dcam
    return dcamcon_list[idevice]

