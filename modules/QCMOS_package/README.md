# README

本项目提供控制 QCMOS 项目的 API, 主要包含四个文件:

- dcamapi4: SDK 的 python 封装, 0 层;
- dcam: 相机控制类级别的封装, 1 层;
- dcamcon: 也是相机控制类的封装, 1 层;
- qcomos_driver: 可直接调用的便捷接口, 2 层;

## 使用方式:

1. 安装驱动: 访问: https://www.hamamatsu.com.cn/cn/zh-cn/product/cameras/software/driver-software.html, 下载最新的相机驱动, 安装之.
2. 下载 SDK: 访问 https://www.hamamatsu.com.cn/cn/zh-cn/product/cameras/software/driver-software/dcam-sdk4.html, 下载最新的 SDK
3. SDK 中有大量不同语言的使用示例, 包括 python 的, 直接拷贝到项目目录里, 一个一个运行即可调试;

## 问题:

1. 初始化参数是什么?
   1. BIN:  1 X 1
   2. 色深:
2. 如何配置成 CXP 模式, 是自动的吗?
3. 一些主要的寄存器:
   1. 如何切换成帧束模式?
   2. 如何设置帧束数量?
   3. 如何配制成外触发模式?
   4. 分配 bundle 模式的内存, 有单独的函数, `dcammisc_alloc_ndarray()` 返回的图像是所有图像的纵向堆叠.
   5. 如何理解 bundle 模式的返回? 一张图片, 纵向堆叠所有帧. bundle 是将最近的 N 张图片打包吗?
   6. 该怎么测试 bundle: 设置一个小点的 roi, 设置少数几张 bundle, 在用 artiq 触发之后, getlastframe 将得到所有照片的纵向排列(理想情况下), 到这一步, 应该就说明调试成功了. 写个 demo.buf_getframebuf_getframe. 对偏移量的考虑

## 读出模式

![1741079117043](image/README/1741079117043.png)

1. Readout Mode:
   1. Normal area mode: 正常区域模式
   2. Photon number resolving mode: 光子数分辨模式(该模式是我们需要的, 但在 yf 的 demo 里没有体现)
   3. Lightsheet readout mode: 通过将相机行扫描速度与光束扫描速度同步，并将曝光行宽度与光束宽度对齐，可以在去除焦外平面荧光光的情况下获得高对比度图像。
2. Scan Mode:
   1. Standard scan mode: 在标准扫描模式下，ORCA®-Quest 可以以全帧 9.4 兆像素的速度实现每秒 120 帧的快速帧率，相当于每秒 1.13 吉像素的像素率。即使在快速帧率和高像素率下，读出噪声为 0.5 电子rms，这比 CMOS 摄像机提供的水平更高。
   2. Ultra quiet scan mode: 在超静音扫描模式下，ORCA®-Quest 可以实现最低读出噪声，从而实现光子数分辨。在这种模式下，帧率为 5 fps，全帧为 9.4 兆像素。
3. Operation Mode: 作为一个成像系统的一部分，相机与其他仪器一起使用。例如，与光源、快门、x-y 台等仪器一起使用时，有时需要相机同步。ORCA®-Quest 有三种触发操作模式。
   1. Free running mode (internal trigger mode): 在自由运行模式下，有时称为内部模式，摄像机的操作时机与摄像机内部生成的触发器同步。如果需要同步，其他仪器可以使用摄像机的输出触发器进行同步。在这种同步中，摄像机是主设备，其他仪器是从设备。ORCA®-Quest有许多输出触发器，稍后会描述。
   2. Start trigger mode: 在启动触发模式下，相机操作会与来自其他仪器的一个脉冲（一个触发）同步启动。相机的第一个曝光与外部触发的输入同步，但第二个及之后的曝光则由相机的内部定时同步。***适用于一次触发拍照多张的情形.***
   3. External trigger mode: 在外部触发模式下，相机等待来自其他仪器的输入触发。一旦触发输入，相机就会开始一帧的曝光，需要更多的触发来获取更多的帧。ORCA®-Quest 有五种外部触发模式：边缘触发、全局复位边缘触发、电平触发、全局复位电平触发和同步读出触发。所有这些模式都可以在正常区域和光子数解析模式下选择。仅在激光片读出模式下可以选择边缘触发模式。最佳的外部触发模式取决于具体的应用。各种定时图在“各种定时图”章节中描述。这些模式的详细定时细节也在 ORCA®-Quest 相机手册中描述，我们全球的销售和技术团队可以提供定制支持。

## 同步:

1. Use the camera as a slave instrument:
   1. 外部触发模式是当ORCA®-Quest作为从仪器使用时的操作模式。相机有一个触发输入接口，并与其它仪器的脉冲同步。
2. 触发输入:
   1. 为了使相机与其他仪器同步，相机通过触发输入连接器使用来自其他仪器的触发输入信号。信号电平需要是 ***TTL*** 或 ***3.3 V*** LVCMOS，并且可以选择正极性和负极性。输入触发可以使用可编程延迟时间（0 μs到10 s，步长为1 μs）进行延迟。触发时间是一个功能，允许通过时间数量的比例减少启用的触发次数。例如，如果触发时间是四，则会忽略三个触发输入，并启用下一个触发输入。
   2. 三个特性: 触发输入电平(TTL), 触发延迟(在收到触发信号后多长时间启动拍照, 延迟 0 us-10s,步长 1us), 触发次数(多次触发时, 忽略前面几次触发, 只将最后一次作为触发)

## 行同步问题

![1741080767626](image/README/1741080767626.png)

由于行读出问题(不同行的读出时间不同), 读数时如果数据没有写完毕, 就会出现数据不全或者拖影问题, 使用全局读出模式, 在曝光完全结束之后读取数据, 避免该问题.

## 最大帧率

通过估计最大帧率来设置最小积分时间

关键结论:

1. 对于 USB 模式, 要达到 1000帧的速率, 需要限制 ROI 在 512 * 256;

### 标准模式

![1741056932277](image/README/1741056932277.png)

### ROI 模式

![1741057275816](image/README/1741057275816.png)

### BIN 模式

![1741057328849](image/README/1741057328849.png)

## 特性:

1. 支持 USB 3.1 接口和 CXP 接口;
