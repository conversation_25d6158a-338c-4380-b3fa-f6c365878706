"""QCMOS 相机高层驱动模块.

本模块在 DCAM 基础控制层之上提供了更高级的相机控制功能，主要实现了以下特性：

架构设计：
--------
1. 用户接口层
   - 提供简单直观的相机控制接口
   - 自动化的资源管理和错误处理
   - 集成图像显示和保存功能

2. 功能集成层
   - 整合 OpenCV 显示功能
   - 处理用户交互和事件
   - 管理相机配置和状态

3. 安全控制层
   - 信号处理和优雅退出
   - 异常处理和恢复机制
   - 资源自动释放

主要功能：
--------
1. 图像显示
   - 实时预览
   - 自动窗口管理
   - 图像尺寸自适应

2. 相机控制
   - 触发模式配置
   - ROI 设置
   - 参数管理

3. 用户交互
   - 键盘事件处理
   - 窗口事件响应
   - 状态反馈

4. 资源管理
   - 自动缓冲区管理
   - 窗口生命周期控制
   - 信号处理

技术特点：
--------
1. 集成 OpenCV 实现实时显示
2. 支持多种触发模式配置
3. 智能的窗口管理机制
4. 完善的错误处理和恢复机制

Notes
-----
本模块依赖于：
- OpenCV：用于图像显示和处理
- screeninfo：用于获取显示器信息
- dcamcon：提供底层 DCAM 控制接口
- signal：用于信号处理

Examples
--------
>>> from modules.QCMOS_package.qcmos_driver import *
>>> # 配置相机
>>> dcamcon = Dcamcon()
>>> setup_properties(dcamcon, width=2048, height=2048)
>>> # 显示实时图像
>>> show_live_captured_images(dcamcon)
"""

import os

# for getting file name form __file__

import signal

# for handiling pressing Ctrl+C
try:
    import cv2
except:
    pass
# pip install opencv-python
# for disply image
# tested with (********) which used numpy (1.20.2)

from screeninfo import get_monitors

# pip install screeninfo
# for getting monitor information

from modules.QCMOS_package.dcamcon import *

cv_window_status = 0

signaled_sigint = False  # True means Ctrl+C was pressed

save_at_finish_live = False


def dcamtest_show_framedata(data, windowtitle, windowstatus):
    """显示图像数据.

    使用 OpenCV 将 NumPy 数组显示为图像。本函数实现了智能的窗口管理，
    可以检测窗口关闭事件并相应处理。

    工作流程：
    1. 检查窗口状态
    2. 处理图像数据（如有必要进行数据转换）
    3. 显示图像

    Parameters
    ----------
    data : numpy.ndarray
        包含图像数据的 NumPy 数组
    windowtitle : str
        窗口标题
    windowstatus : int
        上一次调用返回的窗口状态。首次调用时应指定为 0

    Returns
    -------
    int
        窗口状态：
        1：窗口正常显示
        -1：窗口已关闭或发生错误

    Notes
    -----
    对于 16 位图像，函数会自动进行亮度调整以优化显示效果
    """
    if (
            windowstatus > 0
            and cv2.getWindowProperty(windowtitle, cv2.WND_PROP_VISIBLE) == 0
    ):
        return -1
        # Window has been closed.
    if windowstatus < 0:
        return -1
        # Window is already closed.

    if data.dtype == np.uint16:
        imax = np.amax(data)
        if imax > 0:
            imul = int(65535 / imax)
            # print('Multiple %s' % imul)
            data = data * imul

        cv2.imshow(windowtitle, data)
        return 1
    else:
        print("-NG: dcamtest_show_image(data) only support Numpy.uint16 data")
        return -1


def dcamtest_thread_live(dcam: Dcam):
    """Show live images.
    Capture and show live images.

    Args:
        dcam (Dcam): Dcam instance.

    Returns:
        Nothing.
    """
    if dcam.cap_start() is not False:

        timeout_milisec = 100
        iWindowStatus = 0
        while iWindowStatus >= 0:
            if dcam.wait_capevent_frameready(timeout_milisec) is not False:
                data = dcam.buf_getlastframedata()
                iWindowStatus = dcamtest_show_framedata(data, "test", iWindowStatus)
            else:
                dcamerr = dcam.lasterr()
                if dcamerr.is_timeout():
                    print("===: timeout")
                else:
                    print("-NG: Dcam.wait_event() fails with error {}".format(dcamerr))
                    break

            key = cv2.waitKey(1)
            if key == ord("q") or key == ord(
                    "Q"
            ):  # if 'q' was pressed with the live window, close it
                break

        dcam.cap_stop()
    else:
        print("-NG: Dcam.cap_start() fails with error {}".format(dcam.lasterr()))


def setup_properties(
        dcamcon: Dcamcon,
        x_pos=0,
        y_pos=0,
        width=4096,
        height=2304,
        tirg_mode=0,
        bundle_mode=1,
        bundle_number=100,
):
    """配置相机属性.

    提供全面的相机参数配置功能，包括触发模式、ROI 设置和缓冲区配置。

    工作流程：
    1. 配置触发模式
    2. 设置 ROI 参数
    3. 配置分辨率
    4. 分配缓冲区

    Parameters
    ----------
    dcamcon : Dcamcon
        相机控制实例
    width : int, optional
        水平尺寸，默认为 4096
    height : int, optional
        垂直尺寸，默认为 2304
    x_pos : int, optional
        水平偏移，默认为 0
    y_pos : int, optional
        垂直偏移，默认为 0
    tirg_mode : int, optional
        触发模式：
        0：外部高电平触发
        1：外部上升沿触发

    Returns
    -------
    bool
        配置成功返回 True，失败返回 False

    Notes
    -----
    触发模式配置包括：
    - 触发源设置
    - 触发极性配置
    - 触发响应方式设置
    """
    # 设置触发源
    # if(dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGERSOURCE, DCAMPROP.TRIGGERSOURCE.EXTERNAL) is False or
    #    dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGER_MODE, DCAMPROP.TRIGGER_MODE.NORMAL) is False or
    #    dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGERACTIVE, DCAMPROP.TRIGGERACTIVE.EDGE) is False):
    #     return False
    # if dcamcon.set_propertyvalue(DCAM_IDPROP.SENSORMODE, DCAMPROP.SENSORMODE.PHOTONNUMBERRESOLVING) is False:
    #    return False

    # if dcamcon.set_propertyvalue(DCAM_IDPROP.READOUTSPEED, DCAMPROP.READOUTSPEED.SLOWEST) is False:
    #     return False

    if tirg_mode == 0:  # 外部高电平触发
        if (dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGERSOURCE, DCAMPROP.TRIGGERSOURCE.EXTERNAL) is False
                or dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGER_MODE, DCAMPROP.TRIGGER_MODE.NORMAL) is False
                or dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGERACTIVE, DCAMPROP.TRIGGERACTIVE.LEVEL) is False
                or dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGERPOLARITY, DCAMPROP.TRIGGERPOLARITY.POSITIVE) is False
                or dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGER_GLOBALEXPOSURE, DCAMPROP.TRIGGER_GLOBALEXPOSURE.GLOBALRESET) is False):
            return False

    elif tirg_mode == 1:  # 外部上升沿触发
        if (dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGERSOURCE, DCAMPROP.TRIGGERSOURCE.EXTERNAL) is False
                or dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGER_MODE, DCAMPROP.TRIGGER_MODE.NORMAL) is False
                or dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGERPOLARITY, DCAMPROP.TRIGGERPOLARITY.POSITIVE) is False
                or dcamcon.set_propertyvalue(DCAM_IDPROP.TRIGGERACTIVE, DCAMPROP.TRIGGERACTIVE.EDGE) is False):
            return False

    # 设置ROI区域和位置
    if (
            dcamcon.set_propertyvalue(DCAM_IDPROP.SUBARRAYMODE, DCAMPROP.MODE.OFF) is False
            or dcamcon.set_propertyvalue(DCAM_IDPROP.SUBARRAYHSIZE, width) is False
            or dcamcon.set_propertyvalue(DCAM_IDPROP.SUBARRAYVSIZE, height) is False
            or dcamcon.set_propertyvalue(DCAM_IDPROP.SUBARRAYVPOS, y_pos) is False
            or dcamcon.set_propertyvalue(DCAM_IDPROP.SUBARRAYHPOS, x_pos) is False
            or dcamcon.set_propertyvalue(DCAM_IDPROP.SUBARRAYMODE, DCAMPROP.MODE.ON)
            is False
    ):
        return False

    # 设置binning大小
    if dcamcon.set_propertyvalue(DCAM_IDPROP.BINNING, DCAMPROP.BINNING._1) is False:
        return False

    # 设置色深大小
    if dcamcon.set_propertyvalue(DCAM_IDPROP.IMAGE_PIXELTYPE, DCAM_PIXELTYPE.MONO16) is False:
        return False



    if bundle_mode == 1:
        # 设置 bundle 模式
        if (dcamcon.set_propertyvalue(DCAM_IDPROP.FRAMEBUNDLE_MODE, DCAMPROP.MODE.ON) is False):
            return False

        number_of_frames = bundle_number

        # 设置 bundle number
        if dcamcon.set_propertyvalue(DCAM_IDPROP.FRAMEBUNDLE_NUMBER, number_of_frames) is False:
            return False

        # prepare buffer
        if not dcamcon.allocbuffer(number_of_frames):
            return
    else:
        if not dcamcon.allocbuffer(1):
            return

    # start live
    if not dcamcon.startcapture():
        dcamcon.releasebuffer()
        return

    return True


def show_framedata(camera_title, data):
    """显示图像数据.

    创建并管理 OpenCV 窗口，实现图像的实时显示。本函数提供了智能的
    窗口管理机制，包括自动布局、大小调整和事件处理。

    工作流程：
    1. 检查信号状态
    2. 管理窗口生命周期
    3. 配置窗口属性
    4. 显示图像数据

    Parameters
    ----------
    camera_title : str
        相机窗口标题
    data : numpy.ndarray
        图像数据数组

    Notes
    -----
    窗口特性：
    - 支持自动缩放以适应屏幕
    - 保持图像比例
    - 智能的窗口位置计算
    - 键盘事件响应（'q' 键退出）
    """
    global signaled_sigint
    if signaled_sigint:
        return

    global cv_window_status
    if cv_window_status > 0:  # was the window created and open?
        cv_window_status = cv2.getWindowProperty(camera_title, 0)
        if cv_window_status == 0:  # if it is still open
            cv_window_status = 1  # mark it as still open again

    if (
            cv_window_status >= 0
    ):  # see if the window is not created yet or created and open
        # maxval = np.amax(data)
        # if data.dtype == np.uint16: # 最大值归一化
        #     if maxval > 0:
        #         imul = int(65535 / maxval)
        #         data = data * imul

        if cv_window_status == 0:  # 创建cv2窗口
            # OpenCV window is not created yet
            cv2.namedWindow(
                camera_title,
                cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO | cv2.WINDOW_GUI_NORMAL,
            )

            # resize display window
            data_width = data.shape[1]
            data_height = data.shape[0]

            window_pos_left = 156
            window_pos_top = 48

            screeninfos = get_monitors()  # 获得显示器信息

            max_width = screeninfos[0].width - (window_pos_left * 2)
            max_height = screeninfos[0].height - (window_pos_top * 2)

            if data_width > max_width:
                scale_X100 = int(100 * max_width / data_width)
            else:
                scale_X100 = 100

            if data_height > max_height:
                scale_Y100 = int(100 * max_height / data_height)
            else:
                scale_Y100 = 100

            if scale_X100 < scale_Y100:
                scale_100 = scale_X100
            else:
                scale_100 = scale_Y100

            # 设置显示窗口缩放比例
            disp_width = int(data_width * scale_100 * 0.01)
            disp_height = int(data_height * scale_100 * 0.01)

            cv2.resizeWindow(camera_title, disp_width, disp_height)
            # end of resize

            cv2.moveWindow(camera_title, window_pos_left, window_pos_top)
            cv_window_status = 1

        cv2.imshow(camera_title, data)
        key = cv2.waitKey(1)
        if key == ord("q") or key == ord(
                "Q"
        ):  # if 'q' or 'Q' was pressed with the live window, close it
            cv_window_status = -1


def show_live_captured_images(dcamcon: Dcamcon):
    """显示实时图像.

    实现相机图像的实时采集和显示。本函数提供了完整的图像采集流程控制，
    包括参数配置、缓冲区管理、超时处理和用户交互。

    工作流程：
    1. 获取相机参数
    2. 配置缓冲区
    3. 计算超时时间
    4. 启动图像采集
    5. 循环处理图像

    Parameters
    ----------
    dcamcon : Dcamcon
        相机控制实例

    Notes
    -----
    功能特点：
    - 智能的超时计算
    - 自动的触发控制
    - 完善的错误处理
    - 支持用户中断
    - 资源自动释放
    """
    # get property value used
    exposuretime = dcamcon.get_propertyvalue(DCAM_IDPROP.EXPOSURETIME)
    if exposuretime is False:
        # should be able to get the value
        return

    triggersource = dcamcon.get_propertyvalue(DCAM_IDPROP.TRIGGERSOURCE)
    if triggersource is False:
        # should be able to get the value
        return

    trigger_mode = dcamcon.get_propertyvalue(DCAM_IDPROP.TRIGGER_MODE)
    print(f"trigger mode:{trigger_mode}")
    if trigger_mode is False:
        # shoulf be able to get the value
        return

    number_of_frames = 10
    # prepare buffer
    if not dcamcon.allocbuffer(number_of_frames):
        return

    # calculate timeout time
    timeout_millisec = 2

    frameinterval = dcamcon.get_propertyvalue(DCAM_IDPROP.INTERNAL_FRAMEINTERVAL, False)
    if frameinterval is not False:
        # set timeout waiting for a frame to arrive to exposure time + internal frame interval + 500 ms
        timeout_millisec = int((exposuretime + frameinterval) * 1000.0) + 500
    else:
        # set timeout waiting for a frame to arrive to exposure time + 1 second
        timeout_millisec = int(exposuretime * 1000.0) + 1000

    # let's use 2ms minimum timeout
    if timeout_millisec < 2:
        timeout_millisec = 2

    # start live
    if not dcamcon.startcapture():
        # dcamcon.allocbuffer() should have succeeded
        dcamcon.releasebuffer()
        return

    triggersource = dcamcon.get_propertyvalue(DCAM_IDPROP.TRIGGERSOURCE)

    firetrigger_cycle = 0
    framecount_till_firetrigger = 0
    if triggersource == DCAMPROP.TRIGGERSOURCE.SOFTWARE:
        if trigger_mode == DCAMPROP.TRIGGER_MODE.START:
            # Software Start requires only one firetrigger at beginning
            firetrigger_cycle = 0
        elif trigger_mode == DCAMPROP.TRIGGER_MODE.PIV:
            # PIV require firetrigger for 2 frames
            firetrigger_cycle = 2
        else:
            # standard software trigger requires one firetrigger for one frame
            firetrigger_cycle = 1

        # we'll fire a trigger to initiate capturing for this sample
        dcamcon.firetrigger()
        framecount_till_firetrigger = firetrigger_cycle

    timeout_happened = 0

    global cv_window_status
    global signaled_sigint
    while cv_window_status >= 0:
        if signaled_sigint:
            break

        res = dcamcon.wait_capevent_frameready(timeout_millisec)  # 等待数据采集完成
        if res is not True:
            # frame does not come
            if res != DCAMERR.TIMEOUT:
                print("-NG: Dcam.wait_event() failed with error {}".format(res))
                break

            # TIMEOUT error happens
            timeout_happened += 1
            if timeout_happened == 1:
                print("Waiting for a frame to arrive.", end="")
                if triggersource == DCAMPROP.TRIGGERSOURCE.EXTERNAL:
                    print(" Check your trigger source.", end="")
                else:
                    print(
                        " Check your <timeout_millisec> calculation in the code.",
                        end="",
                    )
                print(" Press Ctrl+C to abort.")
            else:
                print(".")
                if timeout_happened > 5:
                    timeout_happened = 0

            continue

        # wait_capevent_frameready() succeeded
        lastdata = dcamcon.get_lastframedata()  # 获得采集的最后一帧图像
        if lastdata is not False:
            show_framedata(dcamcon.device_title, lastdata)
            # print(r"frame shape:{}".format(lastdata.shape))

        if framecount_till_firetrigger > 0:
            framecount_till_firetrigger -= 1
            if framecount_till_firetrigger == 0:
                dcamcon.firetrigger()
                framecount_till_firetrigger = firetrigger_cycle

        timeout_happened = 0

    # End live
    cv2.destroyAllWindows()

    dcamcon.stopcapture()

    if save_at_finish_live:
        dcamcon.save_rawimages("LastGoodImage")

    dcamcon.releasebuffer()


def sigint_handler(signum, frame):
    """处理中断信号.

    实现对 Ctrl+C 信号的处理，确保程序可以优雅退出。

    Parameters
    ----------
    signum : int
        信号编号
    frame : frame
        当前栈帧

    Notes
    -----
    本函数设置全局标志 signaled_sigint，用于通知其他组件程序即将退出
    """
    global signaled_sigint
    signaled_sigint = True


# run handler (which does cleanup) if Ctrl+C is pressed in Python console.
signal.signal(signal.SIGINT, sigint_handler)

if __name__ == "__main__":
    ownname = os.path.basename(__file__)
    print("Start {}".format(ownname))

    # initialize DCAM-API
    if dcamcon_init():
        # choose camera and get Dcamcon instance
        dcamcon = dcamcon_choose_and_open()
        if dcamcon is not None:
            res = True
            # set basic properties
            if not signaled_sigint and res:
                res = setup_properties(dcamcon)

            # show live image
            if not signaled_sigint and res:
                show_live_captured_images(dcamcon)

            # close dcam
            dcamcon.close()

    # cleanup dcamcon
    dcamcon_uninit()

    print("End {}".format(ownname))
