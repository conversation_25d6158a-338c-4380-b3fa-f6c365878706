"""DCAM 相机控制系统核心模块.

本模块实现了一个优雅的分层架构来控制 DCAM 相机系统，主要包含以下几个层次：

1. 底层接口层
   - 通过 dcamapi4 模块提供与硬件的直接交互
   - 处理底层的 C 接口调用和内存管理

2. 中间抽象层
   - dcammisc 工具函数：提供帧设置和内存分配等基础功能
   - Dcamapi 类：管理 DCAM-API 的全局状态和初始化

3. 高层控制层
   - Dcam 类：提供完整的相机控制接口
   - 将底层操作封装为易用的 Python 方法

主要功能模块划分：
---------------
1. 设备控制模块
   - 设备的打开、关闭
   - 设备信息的获取

2. 属性管理模块
   - 相机参数的获取和设置
   - 属性值的查询和转换

3. 缓冲区管理模块
   - 图像缓冲区的分配和释放
   - 帧数据的获取和处理

4. 图像采集模块
   - 单帧和连续采集控制
   - 触发控制和状态监控

5. 事件处理模块
   - 异步事件的等待和处理
   - 超时管理

技术特点：
--------
1. 采用面向对象设计，将相机操作封装为类
2. 使用 NumPy 进行高效的图像数据处理
3. 实现了优雅的错误处理机制
4. 提供了同步和异步操作接口

Notes
-----
本模块依赖于：
- dcamapi4：提供 DCAM-API v4 接口
- numpy：用于图像数据处理和缓冲区管理

Examples
--------
>>> from .dcam import *
>>> # 初始化相机系统
>>> Dcamapi.init()
>>> # 创建相机实例
>>> dcam = Dcam(0)
>>> # 打开相机
>>> dcam.dev_open()
>>> # 配置和使用相机
>>> dcam.buf_alloc(10)  # 分配 10 帧缓冲区
>>> dcam.cap_start()    # 开始采集
"""

__date__ = '2021-06-30'
__copyright__ = 'Copyright (C) 2021-2024 Hamamatsu Photonics K.K.'

from .dcamapi4 import *
# DCAM-API v4 module

import numpy as np
# pip install numpy
# allocated to receive the image data


# ==== DCAM 辅助函数 ====

def dcammisc_setupframe(hdcam, bufframe: DCAMBUF_FRAME):
    """配置帧缓冲区结构体.
    
    此函数是中间抽象层的核心组件之一，负责从相机硬件获取成像参数并正确配置帧缓冲区结构体。
    它处理了所有必要的底层属性查询，确保帧缓冲区与相机设置匹配。
    
    工作流程：
    1. 获取像素格式
    2. 获取图像尺寸（宽度和高度）
    3. 处理帧束模式（如果启用）
    4. 配置行字节数
    
    Parameters
    ----------
    hdcam : c_void_p
        DCAM 设备句柄，用于访问相机硬件
    bufframe : DCAMBUF_FRAME
        帧缓冲区结构体，将被配置为匹配相机设置
    
    Returns
    -------
    DCAMERR
        操作结果的错误码：
        - 成功：返回 DCAMERR.SUCCESS
        - 失败：返回对应的错误码
    """
    fValue = c_double()
    idprop = DCAM_IDPROP.IMAGE_PIXELTYPE
    err = dcamprop_getvalue(hdcam, idprop, byref(fValue))
    if not err.is_failed():
        bufframe.type = int(fValue.value)

        idprop = DCAM_IDPROP.IMAGE_WIDTH
        err = dcamprop_getvalue(hdcam, idprop, byref(fValue))
        if not err.is_failed():
            bufframe.width = int(fValue.value)
            
            idprop = DCAM_IDPROP.IMAGE_HEIGHT
            err = dcamprop_getvalue(hdcam, idprop, byref(fValue))
            if not err.is_failed():
                bufframe.height = int(fValue.value)
                
                idprop = DCAM_IDPROP.FRAMEBUNDLE_MODE
                err = dcamprop_getvalue(hdcam, idprop, byref(fValue))
                if not err.is_failed() and int(fValue.value) == DCAMPROP.MODE.ON:
                    idprop = DCAM_IDPROP.FRAMEBUNDLE_ROWBYTES
                    err = dcamprop_getvalue(hdcam, idprop, byref(fValue))
                    if not err.is_failed():
                        bufframe.rowbytes = int(fValue.value)
                else:
                    idprop = DCAM_IDPROP.IMAGE_ROWBYTES
                    err = dcamprop_getvalue(hdcam, idprop, byref(fValue))
                    if not err.is_failed():
                        bufframe.rowbytes = int(fValue.value)

    return err


def dcammisc_alloc_ndarray(frame: DCAMBUF_FRAME, framebundlenum=1):
    """创建图像数据 NumPy 数组.
    
    此函数是缓冲区管理模块的关键组件，负责创建适合存储相机图像数据的 NumPy 数组。
    它根据相机的像素格式和图像尺寸，分配正确类型和大小的内存空间。
    
    支持的像素格式：
    - MONO16：16 位单色图像
    - MONO8：8 位单色图像
    
    Parameters
    ----------
    frame : DCAMBUF_FRAME
        包含图像参数的帧结构体
    framebundlenum : int, optional
        帧束模式下的帧数，默认为 1
    
    Returns
    -------
    numpy.ndarray or bool
        成功：返回配置好的 NumPy 数组，数组类型取决于像素格式
        失败：返回 False
    
    Notes
    -----
    数组的形状为 (height * framebundlenum, width)，其中：
    - height：图像高度
    - width：图像宽度
    - framebundlenum：帧束模式下的帧数
    """
    height = frame.height * framebundlenum
        
    if frame.type == DCAM_PIXELTYPE.MONO16:
        return np.zeros((height, frame.width), dtype='uint16')

    if frame.type == DCAM_PIXELTYPE.MONO8:
        return np.zeros((height, frame.width), dtype='uint8')

    return False


# ==== Dcamapi 类定义 ====


class Dcamapi:
    """DCAM-API 全局状态管理类.
    
    此类是中间抽象层的核心组件，负责管理 DCAM-API 的全局状态和生命周期。
    它实现了单例模式的思想，通过类方法和类变量维护全局状态，确保 DCAM-API 的正确初始化和清理。
    
    设计特点：
    ----------
    1. 全局状态管理
       - 维护 DCAM-API 的初始化状态
       - 跟踪已连接的设备数量
       - 记录最近的错误信息
    
    2. 错误处理机制
       - 提供统一的错误码管理
       - 支持错误状态查询
    
    3. 资源管理
       - 确保 DCAM-API 资源的正确分配和释放
       - 防止重复初始化
    
    主要职责：
    ----------
    1. API 生命周期管理
       - 初始化 DCAM-API
       - 清理和关闭 DCAM-API
    
    2. 设备管理
       - 获取已连接设备的数量
       - 维护设备状态信息
    
    3. 错误处理
       - 记录操作结果
       - 提供错误查询接口
    
    Attributes
    ----------
    __lasterr : DCAMERR
        记录最近一次操作的错误码
    __bInitialized : bool
        标记 DCAM-API 是否已初始化
    __devicecount : int
        记录检测到的设备数量
    
    Notes
    -----
    此类的所有方法都是类方法，不需要实例化即可使用。
    推荐使用 with 语句管理 DCAM-API 的生命周期。
    
    Examples
    --------
    >>> # 基本使用方式
    >>> Dcamapi.init()
    >>> try:
    ...     # 使用 DCAM-API
    ...     device_count = Dcamapi.get_devicecount()
    ... finally:
    ...     Dcamapi.uninit()
    """
    
    __lasterr = DCAMERR.SUCCESS
    __bInitialized = False
    __devicecount = 0

    @classmethod
    def __result(cls, errvalue):
        """保存最后的错误码.
        
        内部使用,保存最后一次操作的错误码。
        
        Parameters
        ----------
        errvalue : int
            错误码
        
        Returns
        -------
        bool
            如果 errvalue < 0 返回 False,否则返回 True
        """
        if errvalue < 0:
            cls.__lasterr = errvalue
            return False

        return True

    @classmethod
    def lasterr(cls):
        """返回最后的错误码.
        
        返回 Dcamapi 成员函数的最后一次错误码。
        
        Returns
        -------
        DCAMERR
            最后一次操作的错误码
        """
        return cls.__lasterr

    @classmethod
    def init(cls, *initparams):
        """初始化 DCAM-API.
        
        初始化 DCAM-API。
        当存在 Dcam 对象时不要调用此函数,因为 Dcam 构造函数会执行初始化。
        调用 close() 后,如果需要恢复测量,请再次调用此函数。
        
        Returns
        -------
        bool
            如果初始化成功返回 True,如果 dcamapi_init() 返回 DCAMERR 错误则返回 False。
            可以通过 lasterr() 获取具体的 DCAMERR 错误码。
        """
        if cls.__bInitialized:
            return cls.__result(DCAMERR.ALREADYINITIALIZED)

        paraminit = DCAMAPI_INIT()
        err = dcamapi_init(byref(paraminit))
        cls.__bInitialized = True
        if cls.__result(err) is False:
            return False

        cls.__devicecount = paraminit.iDeviceCount
        return True

    @classmethod
    def uninit(cls):
        """反初始化 DCAM-API.
        
        反初始化 DCAM-API。
        使用完 DCAM-API 后,调用此函数释放所有资源。
        
        Returns
        -------
        bool
            反初始化成功返回 True
        """
        if cls.__bInitialized:
            dcamapi_uninit()
            cls.__lasterr = DCAMERR.SUCCESS
            cls.__bInitialized = False
            cls.__devicecount = 0

        return True

    @classmethod
    def get_devicecount(cls):
        """返回已连接的相机数量.
        
        Returns
        -------
        int or bool
            如果已初始化,返回已连接的相机数量
            如果未初始化,返回 False
        """
        if not cls.__bInitialized:
            return False

        return cls.__devicecount

# ==== Dcam class ====


class Dcam:
    """DCAM 设备高层控制接口.
    
    此类是高层控制层的核心组件，为单个 DCAM 设备提供完整的控制接口。
    它封装了底层的 DCAM-API 调用，提供了直观的 Python 风格接口。
    
    设计理念：
    ----------
    1. 面向对象封装
       - 每个实例对应一个物理设备
       - 维护设备的完整生命周期
       - 提供直观的方法命名和参数设计
    
    2. 功能模块化
       - 设备控制：打开、关闭、获取信息
       - 参数配置：获取和设置各类属性
       - 图像采集：单帧、连续采集、触发控制
       - 缓冲区管理：内存分配、数据访问
       - 事件处理：异步等待、状态监控
    
    3. 错误处理
       - 每个操作都有清晰的成功/失败指示
       - 详细的错误信息记录
       - 支持错误恢复机制
    
    4. 资源管理
       - 自动管理 DCAM 句柄
       - 自动管理等待句柄
       - 确保资源正确释放
    
    主要功能组：
    -----------
    1. 设备控制
       - dev_open()：打开设备
       - dev_close()：关闭设备
       - dev_getstring()：获取设备信息
    
    2. 属性管理
       - prop_getvalue()：读取属性值
       - prop_setvalue()：设置属性值
       - prop_getattr()：获取属性特性
    
    3. 缓冲区操作
       - buf_alloc()：分配缓冲区
       - buf_release()：释放缓冲区
       - buf_getframe()：获取帧数据
    
    4. 采集控制
       - cap_start()：开始采集
       - cap_stop()：停止采集
       - cap_status()：获取状态
    
    5. 事件处理
       - wait_event()：等待事件
       - wait_capevent_frameready()：等待帧就绪
    
    Attributes
    ----------
    __lasterr : DCAMERR
        记录最近一次操作的错误码
    __iDevice : int
        设备索引号
    __hdcam : int
        DCAM 设备句柄
    __hdcamwait : int
        事件等待句柄
    __bufframe : DCAMBUF_FRAME
        帧缓冲区信息结构体
    
    Notes
    -----
    推荐使用 with 语句管理设备的生命周期。
    所有可能失败的操作都会返回 bool 类型的成功/失败指示。
    
    Examples
    --------
    >>> # 基本使用流程
    >>> dcam = Dcam(0)  # 创建第一个设备的控制实例
    >>> if dcam.dev_open():  # 打开设备
    ...     try:
    ...         # 配置相机参数
    ...         dcam.prop_setvalue(DCAM_IDPROP.EXPOSURE_TIME, 0.1)
    ...         # 分配缓冲区
    ...         dcam.buf_alloc(10)
    ...         # 开始采集
    ...         dcam.cap_start()
    ...         # 获取图像数据
    ...         frame = dcam.buf_getframe(0)
    ...     finally:
    ...         # 确保设备正确关闭
    ...         dcam.dev_close()
    """
    
    def __init__(self, iDevice=0):
        """初始化 Dcam 实例.
        
        Parameters
        ----------
        iDevice : int, optional
            设备索引号,默认为 0
        """
        self.__lasterr = DCAMERR.SUCCESS
        self.__iDevice = iDevice
        self.__hdcam = 0
        self.__hdcamwait = 0
        self.__bufframe = DCAMBUF_FRAME()

    def __repr__(self):
        return 'Dcam()'

    def __result(self, errvalue):
        """保存最后的错误码.
        
        内部使用,保存最后一次操作的错误码。
        
        Parameters
        ----------
        errvalue : int
            错误码
        
        Returns
        -------
        bool
            如果 errvalue < 0 返回 False,否则返回 True
        """
        if errvalue < 0:
            self.__lasterr = errvalue
            return False

        return True

    def lasterr(self):
        """返回最后的错误码.
        
        返回 Dcam 成员函数的最后一次错误码。
        
        Returns
        -------
        DCAMERR
            最后一次操作的错误码
        """
        return self.__lasterr

    def is_opened(self):
        """检查 DCAM 句柄是否已打开.
        
        Returns
        -------
        bool
            如果 DCAM 句柄已打开返回 True,否则返回 False
        """
        if self.__hdcam == 0:
            return False
        else:
            return True

    def dev_open(self, index=-1):
        """获取 DCAM 句柄.
        
        获取用于控制相机的 DCAM 句柄。
        调用 close() 后,如果需要恢复测量,请再次调用此函数。
        
        Parameters
        ----------
        index : int, optional
            设备索引号,默认为 -1,表示使用构造函数中指定的索引
        
        Returns
        -------
        bool
            如果获取句柄成功返回 True,如果 dcamdev_open() 返回 DCAMERR 错误则返回 False。
            可以通过 lasterr() 获取具体的 DCAMERR 错误码。
        """
        if self.is_opened():
            return self.__result(DCAMERR.ALREADYOPENED)

        paramopen = DCAMDEV_OPEN()
        if index >= 0:
            paramopen.index = index
        else:
            paramopen.index = self.__iDevice

        ret = self.__result(dcamdev_open(byref(paramopen)))
        if ret is False:
            return False

        self.__hdcam = paramopen.hdcam
        return True

    def dev_close(self):
        """关闭 DCAM 句柄.
        
        如果需要关闭当前设备,请调用此函数。
        
        Returns
        -------
        bool
            关闭句柄成功返回 True
        """
        if self.is_opened():
            self.__close_hdcamwait()
            dcamdev_close(self.__hdcam)
            self.__lasterr = DCAMERR.SUCCESS
            self.__hdcam = 0

        return True

    def dev_getstring(self, idstr: DCAM_IDSTR):
        """获取设备字符串信息.
        
        Parameters
        ----------
        idstr : DCAM_IDSTR
            字符串 ID
        
        Returns
        -------
        str or bool
            成功时返回由 DCAM_IDSTR 指定的设备信息字符串
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if self.is_opened():
            hdcam = self.__hdcam
        else:
            hdcam = self.__iDevice

        paramdevstr = DCAMDEV_STRING()
        paramdevstr.iString = idstr
        paramdevstr.alloctext(256)

        ret = self.__result(dcamdev_getstring(hdcam, byref(paramdevstr)))
        if ret is False:
            return False

        return paramdevstr.text.decode()

    # ==== 属性相关函数 ====

    def prop_getattr(self, idprop: DCAM_IDPROP):
        """获取属性特性.
        
        Parameters
        ----------
        idprop : DCAM_IDPROP
            属性 ID
        
        Returns
        -------
        DCAMPROP_ATTR or bool
            成功时返回属性的特性信息
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        propattr = DCAMPROP_ATTR()
        propattr.iProp = idprop
        ret = self.__result(dcamprop_getattr(self.__hdcam, byref(propattr)))
        if ret is False:
            return False

        return propattr

    def prop_getvalue(self, idprop: DCAM_IDPROP):
        """获取属性值.
        
        Parameters
        ----------
        idprop : DCAM_IDPROP
            属性 ID
        
        Returns
        -------
        float or bool
            成功时返回属性值
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        cDouble = c_double()
        ret = self.__result(dcamprop_getvalue(self.__hdcam, idprop, byref(cDouble)))
        if ret is False:
            return False

        return cDouble.value

    def prop_setvalue(self, idprop: DCAM_IDPROP, fValue):
        """设置属性值.
        
        Parameters
        ----------
        idprop : DCAM_IDPROP
            属性 ID
        fValue : float
            要设置的值
        
        Returns
        -------
        bool
            设置成功返回 True,失败返回 False
            失败时可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        ret = self.__result(dcamprop_setvalue(self.__hdcam, idprop, fValue))
        if ret is False:
            return False

        return True

    def prop_setgetvalue(self, idprop: DCAM_IDPROP, fValue, option=0):
        """设置并获取属性值.
        
        设置属性值后立即读回实际设置的值。
        
        Parameters
        ----------
        idprop : DCAM_IDPROP
            属性 ID
        fValue : float
            要设置的值
        option : int, optional
            选项,默认为 0
        
        Returns
        -------
        float or bool
            成功时返回设备中实际设置的值
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        cDouble = c_double(fValue)
        cOption = c_int32(option)
        ret = self.__result(dcamprop_setgetvalue(self.__hdcam, idprop, byref(cDouble), cOption))
        if ret is False:
            return False

        return cDouble.value

    def prop_queryvalue(self, idprop: DCAM_IDPROP, fValue, option=0):
        """查询属性值.
        
        Parameters
        ----------
        idprop : DCAM_IDPROP
            属性 ID
        fValue : float
            属性值
        option : int, optional
            选项,默认为 0
        
        Returns
        -------
        float or bool
            成功时返回由 option 指定的属性值
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        cDouble = c_double(fValue)
        cOption = c_int32(option)
        ret = self.__result(dcamprop_queryvalue(self.__hdcam, idprop, byref(cDouble), cOption))
        if ret is False:
            return False

        return cDouble.value

    def prop_getnextid(self, idprop: DCAM_IDPROP):
        """获取下一个属性 ID.
        
        Parameters
        ----------
        idprop : DCAM_IDPROP
            属性 ID
        
        Returns
        -------
        DCAM_IDPROP or bool
            成功时返回下一个属性 ID
            如果没有更多属性或发生错误,返回 False
            错误时可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        cIdprop = c_int32(idprop)
        cOption = c_int32(0)  # 搜索下一个 ID

        ret = self.__result(dcamprop_getnextid(self.__hdcam, byref(cIdprop), cOption))
        if ret is False:
            return False

        return cIdprop.value

    def prop_getname(self, idprop: DCAM_IDPROP):
        """获取属性名称.
        
        Parameters
        ----------
        idprop : DCAM_IDPROP
            属性 ID
        
        Returns
        -------
        str or bool
            成功时返回属性 ID 对应的名称字符串
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        textbuf = create_string_buffer(256)
        ret = self.__result(dcamprop_getname(self.__hdcam, idprop, textbuf, sizeof(textbuf)))
        if ret is False:
            return False

        return textbuf.value.decode()

    def prop_getvaluetext(self, idprop: DCAM_IDPROP, fValue):
        """获取属性值的文本描述.
        
        Parameters
        ----------
        idprop : DCAM_IDPROP
            属性 ID
        fValue : float
            属性值
        
        Returns
        -------
        str or bool
            成功时返回属性值对应的文本描述
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        paramvaluetext = DCAMPROP_VALUETEXT()
        paramvaluetext.iProp = idprop
        paramvaluetext.value = fValue
        paramvaluetext.alloctext(256)

        ret = self.__result(dcamprop_getvaluetext(self.__hdcam, byref(paramvaluetext)))
        if ret is False:
            return False

        return paramvaluetext.text.decode()

    # ==== 缓冲区相关函数 ====

    def buf_alloc(self, nFrame):
        """分配 DCAM 内部缓冲区.
        
        Parameters
        ----------
        nFrame : int
            要分配的帧数
        
        Returns
        -------
        bool
            如果缓冲区准备就绪返回 True
            如果缓冲区未准备好返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        cFrame = c_int32(nFrame)
        ret = self.__result(dcambuf_alloc(self.__hdcam, cFrame))
        if ret is False:
            return False

        return self.__result(dcammisc_setupframe(self.__hdcam, self.__bufframe))

    def buf_release(self):
        """释放 DCAM 内部缓冲区.
        
        Returns
        -------
        bool
            释放成功返回 True
            释放过程中发生错误返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        cOption = c_int32(0)
        return self.__result(dcambuf_release(self.__hdcam, cOption))

    def buf_getframe(self, iFrame):
        """获取指定帧的图像数据.
        
        Parameters
        ----------
        iFrame : int
            目标帧的索引
        
        Returns
        -------
        tuple(DCAMBUF_FRAME, numpy.ndarray) or bool
            成功时返回元组 (帧信息结构体, NumPy 缓冲区)
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)
        
        framebundlenum = 1

        fValue = c_double()
        # 判断当前是否处于 bundle 模式
        err = dcamprop_getvalue(self.__hdcam, DCAM_IDPROP.FRAMEBUNDLE_MODE, byref(fValue))
        if not err.is_failed() and int(fValue.value) == DCAMPROP.MODE.ON:
            # 获取 bundle 模式下的帧数
            err = dcamprop_getvalue(self.__hdcam, DCAM_IDPROP.FRAMEBUNDLE_NUMBER, byref(fValue))
            if not err.is_failed():
                framebundlenum = int(fValue.value)
            else:
                return False
        # 生成 NumPy 缓冲区
        npBuf = dcammisc_alloc_ndarray(self.__bufframe, framebundlenum)
        if npBuf is False:
            return self.__result(DCAMERR.INVALIDPIXELTYPE)
        # 创建帧信息结构体
        aFrame = DCAMBUF_FRAME()
        aFrame.iFrame = iFrame
        # 设置帧信息结构体的缓冲区
        aFrame.buf = npBuf.ctypes.data_as(c_void_p)
        aFrame.rowbytes = self.__bufframe.rowbytes
        aFrame.type = self.__bufframe.type
        aFrame.width = self.__bufframe.width
        aFrame.height = self.__bufframe.height

        ret = self.__result(dcambuf_copyframe(self.__hdcam, byref(aFrame)))
        if ret is False:
            return False

        return (aFrame, npBuf)

    def buf_getframedata(self, iFrame):
        """获取指定帧的图像数据.
        
        Parameters
        ----------
        iFrame : int
            目标帧的索引
        
        Returns
        -------
        numpy.ndarray or bool
            成功时返回包含图像数据的 NumPy 缓冲区
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        ret = self.buf_getframe(iFrame)
        if ret is False:
            return False

        return ret[1]

    def buf_getlastframedata(self):
        """获取最后更新的帧的图像数据.
        
        Returns
        -------
        numpy.ndarray or bool
            成功时返回包含最后更新帧图像数据的 NumPy 缓冲区
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        return self.buf_getframedata(-1)

    # ==== 采集相关函数 ====

    def cap_start(self, bSequence=True):
        """开始采集.
        
        Parameters
        ----------
        bSequence : bool, optional
            True 表示连续采集,False 表示单次采集,默认为 True
        
        Returns
        -------
        bool
            开始采集成功返回 True
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        if bSequence:
            mode = DCAMCAP_START.SEQUENCE
        else:
            mode = DCAMCAP_START.SNAP

        return self.__result(dcamcap_start(self.__hdcam, mode))

    def cap_snapshot(self):
        """采集快照.
        
        采集在 buf_alloc() 中指定的帧数。
        
        Returns
        -------
        bool
            开始快照采集成功返回 True
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        return self.cap_start(False)

    def cap_stop(self):
        """停止采集.
        
        Returns
        -------
        bool
            停止采集成功返回 True
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        return self.__result(dcamcap_stop(self.__hdcam))

    def cap_status(self):
        """获取采集状态.
        
        Returns
        -------
        DCAMCAP_STATUS or bool
            成功时返回当前的采集状态
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        cStatus = c_int32()
        ret = self.__result(dcamcap_status(self.__hdcam, byref(cStatus)))
        if ret is False:
            return False

        return cStatus.value

    def cap_transferinfo(self):
        """获取传输信息.
        
        Returns
        -------
        DCAMCAP_TRANSFERINFO or bool
            成功时返回当前的图像传输状态
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        paramtransferinfo = DCAMCAP_TRANSFERINFO()
        ret = self.__result(dcamcap_transferinfo(self.__hdcam, byref(paramtransferinfo)))
        if ret is False:
            return False

        return paramtransferinfo

    def cap_firetrigger(self):
        """触发软件触发.
        
        Returns
        -------
        bool
            触发成功返回 True
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.is_opened():
            return self.__result(DCAMERR.INVALIDHANDLE)

        cOption = c_int32(0)
        ret = self.__result(dcamcap_firetrigger(self.__hdcam, cOption))
        if ret is False:
            return False

        return True

    # ==== 等待相关函数 ====

    def __open_hdcamwait(self):
        """获取 DCAMWAIT 句柄.
        
        Returns
        -------
        bool
            获取句柄成功返回 True
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if not self.__hdcamwait == 0:
            return True

        paramwaitopen = DCAMWAIT_OPEN()
        paramwaitopen.hdcam = self.__hdcam
        ret = self.__result(dcamwait_open(byref(paramwaitopen)))
        if ret is False:
            return False

        if paramwaitopen.hwait == 0:
            return self.__result(DCAMERR.INVALIDWAITHANDLE)

        self.__hdcamwait = paramwaitopen.hwait
        return True

    def __close_hdcamwait(self):
        """关闭 DCAMWAIT 句柄.
        
        Returns
        -------
        bool
            关闭句柄成功返回 True
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        if self.__hdcamwait == 0:
            return True

        ret = self.__result(dcamwait_close(self.__hdcamwait))
        if ret is False:
            return False

        self.__hdcamwait = 0
        return True

    def wait_event(self, eventmask: DCAMWAIT_CAPEVENT, timeout_millisec):
        """等待指定事件.
        
        Parameters
        ----------
        eventmask : DCAMWAIT_CAPEVENT
            要等待的事件掩码
        timeout_millisec : int
            超时时间,单位为毫秒
        
        Returns
        -------
        DCAMWAIT_CAPEVENT or bool
            成功时返回发生的事件
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        ret = self.__open_hdcamwait()
        if ret is False:
            return False

        paramwaitstart = DCAMWAIT_START()
        paramwaitstart.eventmask = eventmask
        paramwaitstart.timeout = timeout_millisec
        ret = self.__result(dcamwait_start(self.__hdcamwait, byref(paramwaitstart)))
        if ret is False:
            return False

        return paramwaitstart.eventhappened

    def wait_capevent_frameready(self, timeout_millisec):
        """等待帧就绪事件.
        
        等待 DCAMWAIT_CAPEVENT.FRAMEREADY 事件。
        
        Parameters
        ----------
        timeout_millisec : int
            超时时间,单位为毫秒
        
        Returns
        -------
        bool
            等待成功返回 True
            失败时返回 False,可以通过 lasterr() 获取错误码
        """
        ret = self.wait_event(DCAMWAIT_CAPEVENT.FRAMEREADY, timeout_millisec)
        if ret is False:
            return False

        # ret 是 DCAMWAIT_CAPEVENT.FRAMEREADY

        return True


