# -*- coding: utf-8 -*-

import numpy as np
# load registers for easier access
from Spectrum_py_header.spcerr import *
from Spectrum_py_header.pyspcm import *
from Spectrum_py_header.regs import *


class AWG_Card:
    def __init__(self, address, hCardindex=0):
        self.hCardindex = hCardindex
        # hCard = b'TCPIP::*************::inst%d::INSTR'%hCardindex
        Card_ID_String = b"TCPIP::" + address + b'::inst%d::INSTR' % hCardindex
        device = create_string_buffer(Card_ID_String)
        hCard = spcm_hOpen(device)
        if hCard is None:
            sys.stdout.write("no card found\n")
            return
        self.address = address
        self.hCard = hCard

    def close(self):
        spcm_vClose(self.hCard)

    def start(self):
        dwError = uint32()
        dwError = spcm_dwSetParam_i32(
            self.hCard, SPC_M2CMD, M2CMD_CARD_START | M2CMD_CARD_ENABLETRIGGER)
        # | M2CMD_CARD_ENABLETRIGGER | M2CMD_CARD_WAITREADY
        return dwError

    def stop(self):
        return spcm_dwSetParam_i32(self.hCard, SPC_M2CMD, M2CMD_CARD_STOP)

    def checkError(self):
        szErrorTextBuffer = create_string_buffer(ERRORTEXTLEN)
        spcm_dwGetErrorInfo_i32(self.hCard, None, None, szErrorTextBuffer)
        return "{0}\n".format(szErrorTextBuffer.value)

    def setChannels(self, dats, amps, loops=0, repalymode='singlerestart'):
        '''
        Trigger:
        repalymode: singlerestart | single | autorestart
        loops: 0,1,n
        ---------------------------------------
        1, set SPC_REP_STD_SINGLERESTART, loops = N 起作用，trigger 一次播报一次波形，一共可以接收 N 个 trigger, 适合正式实验
        2，set SPC_REP_STD_SINGLE，需要再设置loops，适合调试
            如果loops=0,则一次trigger后循环播报，且不再接受trigger，直至再次start，才可以接受下一次trigger
            如果loops>=1,则一次trigger播报loops次，且不再接受trigger，直至再次start，才可以接受下一次trigger
        3, set SPC_TMASK_SOFTWARE,loops起作用，无需外部触发，start即播报，loops=0则持续播报
        Params:
        ----------------------------------------
        amps = (amp1,amp2)，两个通道的放大器幅度
        amp1,amp2: a scalar float in (0,1] 
        amp[0], or amp[0] shoule be in [-2.5, 2.5] V, unit is Voltage 
        
        dats = (dat1,dat2)
        dat1,dat2 are 1-d array，其中dat1的1.0表示幅度取1.0*amp1，0.5表示幅度取0.5*amp1
        '''
        SPC_TMASK_mode = SPC_TMASK_EXT0
        if repalymode == 'single':
            SPC_REP_STD_mode = SPC_REP_STD_SINGLE
        elif repalymode == 'singlerestart':
            SPC_REP_STD_mode = SPC_REP_STD_SINGLERESTART
        elif repalymode == 'autorestart':
            SPC_TMASK_mode = SPC_TMASK_SOFTWARE
            SPC_REP_STD_mode = SPC_REP_STD_SINGLE
        else:
            print('repalymode should be one of {single,singlerestart,autorestart}')

        self.SPC_TMASK_mode = SPC_TMASK_mode
        self.SPC_REP_STD_mode = SPC_REP_STD_mode

        # settings done to external clock like shown above.
        # if (spcm_dwSetParam_i32 (self.hCard, SPC_M2CMD, M2CMD_CARD_START | M2CMD_CARD_ENABLETRIGGER) == ERR_CLOCKNOTLOCKED):
        #     print("External clock not locked. Please check connection\n")

        dat1, dat2 = dats
        amp1, amp2 = amps
        # print(len(dat1),len(dat2))
        if len(dat1) != len(dat2):
            raise IndexError('array lenght error: dat1 and dat2 is not equal')

        # set Data length to 32 times
        N0 = 32 - len(dat1) % 32
        dat1 = np.concatenate((dat1, np.zeros(N0)))
        dat2 = np.concatenate((dat2, np.zeros(N0)))
        llMemSamples = int64(len(dat1))
        # print('llMemSamples', llMemSamples)

        # setup the mode
        llLoops = int64(loops)  # loop continuously
        spcm_dwSetParam_i32(self.hCard, SPC_CARDMODE, SPC_REP_STD_mode)
        qwChEnable = uint64(CHANNEL0 | CHANNEL1)  # enabble two channel
        spcm_dwSetParam_i64(self.hCard, SPC_CHENABLE, qwChEnable)
        spcm_dwSetParam_i64(self.hCard, SPC_MEMSIZE, llMemSamples)
        spcm_dwSetParam_i64(self.hCard, SPC_LOOPS, llLoops)
        #  enable output!
        spcm_dwSetParam_i64(self.hCard, SPC_ENABLEOUT0, uint64(1))
        spcm_dwSetParam_i64(self.hCard, SPC_ENABLEOUT1, uint64(1))

        # set channel amplitude
        lChannel = int32(0)  # channel 0
        amp = int32(int(1000 * amps[0]))
        address = SPC_AMP0 + lChannel.value * (SPC_AMP1 - SPC_AMP0)
        spcm_dwSetParam_i32(self.hCard, address, amp)
        lChannel = int32(1)  # channel 1
        address = SPC_AMP0 + lChannel.value * (SPC_AMP1 - SPC_AMP0)
        amp = int32(int(1000 * amps[1]))
        spcm_dwSetParam_i32(self.hCard, address, amp)

        # write data to AFG memory
        data = np.vstack([dat1, dat2]).T.flatten()
        data = data * (2 ** 15 - 1)
        data = data.astype(np.int16)
        pvBuffer = data.ctypes.data_as(POINTER(int16))
        qwBufferSize = uint64(len(data) * 2)

        # sys.stdout.write("Startin the DMA transfer and waiting until data is in board memory\n")
        spcm_dwDefTransfer_i64(self.hCard, SPCM_BUF_DATA,
                               SPCM_DIR_PCTOCARD, int32(0),
                               pvBuffer, uint64(0), qwBufferSize)
        spcm_dwSetParam_i32(
            self.hCard, SPC_M2CMD, M2CMD_DATA_STARTDMA | M2CMD_DATA_WAITDMA)
        # sys.stdout.write("... data has been transferred to board memory\n")
        lUserLen = int32(0)
        spcm_dwGetParam_i32(self.hCard,SPC_DATA_AVAIL_USER_LEN,byref(lUserLen))
        # print(lUserLen)


if __name__ == '__main__':
    ### Test
    # MasterhCard = 1  # there are 1-4 cards in spectrum, but only 1 is master, the others are the slaves, here the inst1 is the master for the 4 channel instrument
    address = 'TCPIP::**************'
    AWG0 = AWG_Card(address, hCardindex=0)
    AWG1 = AWG_Card(address, hCardindex=1)  # b'TCPIP::*************::inst1::INSTR'

    listhCards = [AWG0.hCard, AWG1.hCard]
    # open handle for star-hub
    hSync = spcm_hOpen(b'sync0')
    print(hSync)
    # if hSync == None:
    #     sys.stdout.write("Could not open star-hub...\n")
    #     for hCard in listhCards:
    #         spcm_vClose (hCard)
    #     exit (1)
    # # setup star-hub
    nCardCount = len(listhCards)
    spcm_dwSetParam_i32(hSync, SPC_SYNC_ENABLEMASK, (1 << nCardCount) - 1)  # // all 2 cards are masked

    spcm_dwSetParam_i32(AWG1.hCard, SPC_CLOCKMODE, SPC_CM_EXTREFCLOCK)  # Set to reference clock mode
    spcm_dwSetParam_i32(AWG1.hCard, SPC_REFERENCECLOCK, 10000000)  # Reference clock that is fed in is 10 MHz
    # set all the slaves to run synchronously with  1 GHz (M4i,M4x)
    spcm_dwSetParam_i64(AWG1.hCard, SPC_SAMPLERATE, uint64(MEGA(1000)))  # uint64(MEGA(10))
    spcm_dwSetParam_i64(AWG0.hCard, SPC_SAMPLERATE, uint64(MEGA(1000)))  # uint64(MEGA(10))

    freqchannel1 = 200.00e6  # - 8.945e6 +500 -1200 +1000
    freqchannel2 = 200.00e6
    freqchannel3 = 0
    wavetime = 1000e-6  # t= 0~1 ms
    samples = int(wavetime * 1e9) + 1
    ts = np.linspace(0, wavetime, samples)

    y2 = 1.0 * np.sin(2 * np.pi * freqchannel1 * ts) + 1.0 * np.sin(2 * np.pi * freqchannel2 * ts)
    y2 = y2 / np.max(y2)
    y1 = y2

    dats = (y1, y2)
    amps = (1.0, 1.0)

    AWG1.setChannels(dats, amps, loops=0, repalymode='autorestart')
    AWG0.setChannels(dats, amps, loops=0, repalymode='autorestart')
    print(AWG0.checkError())
    print(AWG1.checkError())

    # setup the trigger mode
    # spcm_dwSetParam_i32(self.hCard, SPC_TRIG_ORMASK, SPC_TMASK_SOFTWARE)
    spcm_dwSetParam_i32(AWG1.hCard, SPC_TRIG_EXT0_MODE, SPC_TM_POS)
    spcm_dwSetParam_i32(AWG1.hCard, SPC_TRIG_ORMASK, AWG1.SPC_TMASK_mode)
    spcm_dwSetParam_i32(AWG1.hCard, SPC_TRIG_ANDMASK, 0)
    spcm_dwSetParam_i32(AWG0.hCard, SPC_TRIG_ORMASK, SPC_TM_NONE)

    # AWG1.start()
    # AWG0.start()

    spcm_dwSetParam_i32(hSync, SPC_TIMEOUT, 5000)
    spcm_dwSetParam_i32(hSync, SPC_M2CMD, M2CMD_CARD_START | M2CMD_CARD_ENABLETRIGGER)
    if (spcm_dwSetParam_i32(hSync, SPC_M2CMD, M2CMD_CARD_WAITREADY) == ERR_TIMEOUT):
        print("Timeout occured - no trigger received within time\n")

    time.sleep(10)

    # spcm_vClose (hSync)
    # AWG1.stop()
    AWG1.close()
    # AWG0.stop()
    AWG0.close()
