# -*- coding: utf-8 -*-

from Spectrum_AWG_Card import AWG_Card
# load registers for easier access
from Spectrum_py_header.spcerr import *
from Spectrum_py_header.pyspcm import *
from Spectrum_py_header.regs import *

__all__ = ['AWG_2_Cards']


class AWG_2_Cards():

    def __init__(self, address='**************'):
        # 检查address是否为字节字符串，如果不是，则转换为字节字符串
        if not isinstance(address, bytes):
            address = address.encode('utf-8')

        self.address = address
        self.Card0 = AWG_Card(address, hCardindex=0)
        self.Card1 = AWG_Card(address, hCardindex=1)
        # open handle for star-hub
        self.hSync = spcm_hOpen(b'sync0')
    
    def init_clock(self):
        # the following code should be 1 second, otherwise AWG will not response to Trigger
        # setup star-hub and set clock
        nCardCount = 2
        spcm_dwSetParam_i32(self.hSync, SPC_SYNC_ENABLEMASK, (1 << nCardCount) - 1)  # // all 2 cards are masked
        spcm_dwSetParam_i32(self.Card1.hCard, SPC_CLOCKMODE, SPC_CM_EXTREFCLOCK)  # Set to reference clock mode
        spcm_dwSetParam_i32(self.Card1.hCard, SPC_REFERENCECLOCK, 10000000)  # Reference clock that is fed in is 10 MHz
        # set all the slaves to run synchronously with  1 GHz (M4i,M4x)
        spcm_dwSetParam_i64(self.Card1.hCard, SPC_SAMPLERATE, uint64(MEGA(1000)))  # uint64(MEGA(10))
        spcm_dwSetParam_i64(self.Card0.hCard, SPC_SAMPLERATE, uint64(MEGA(1000)))  # uint64(MEGA(10))

    def start(self):
        # setup the trigger mode
        # spcm_dwSetParam_i32(self.hCard, SPC_TRIG_ORMASK, SPC_TMASK_SOFTWARE)
        spcm_dwSetParam_i32(self.Card1.hCard, SPC_TRIG_EXT0_MODE, SPC_TM_POS)
        spcm_dwSetParam_i32(self.Card1.hCard, SPC_TRIG_ORMASK, self.Card1.SPC_TMASK_mode)
        spcm_dwSetParam_i32(self.Card1.hCard, SPC_TRIG_ANDMASK, 0)
        spcm_dwSetParam_i32(self.Card0.hCard, SPC_TRIG_ORMASK, SPC_TM_NONE)

        spcm_dwSetParam_i32(self.hSync, SPC_TIMEOUT, 10)
        spcm_dwSetParam_i32(self.hSync, SPC_M2CMD, M2CMD_CARD_START | M2CMD_CARD_ENABLETRIGGER)
        # if (spcm_dwSetParam_i32 (self.hSync, SPC_M2CMD, M2CMD_CARD_WAITREADY) == ERR_TIMEOUT):
        #     print ("Timeout occured - no trigger received within time\n")

    def close(self):
        self.Card0.close()
        self.Card1.close()

    def stop(self):
        # self.AWG.stop()
        print('AWG connection stop')
