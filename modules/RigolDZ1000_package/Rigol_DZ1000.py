import pyvisa as visa
import math

class RigolDZ1000():
    '''
    可以独立地控制Rigol DZ1000的两个通道
    更多的函数请自己添加
    建立实例时，两个通道分别建立和控制：
    CH1 = DZ1000(CH=1)
    CH2 = DZ1000(CH=2)
    '''
    def __init__(self,CH=1):
        self.CH=CH
        self.RS = visa.ResourceManager()
        self.DZ1000 = self.RS.open_resource(u'USB0::0x1AB1::0x0642::DG1ZA253905109::INSTR')
        self.InitialDZ1000()

    def Vpp2dBm(self,value):
        '''将V转换为dbm'''
        dBm = 10+20*math.log10(0.5*value)
        return(dBm)
    def dBm2Vpp(self,power):
        '''将dbm转换为V'''
        #dBm = 10+20*log10(0.5*Vpp);
        Amplitude = 2*10**((power-10)/20)
        return(Amplitude)

    def query(self):
        '''询问设备当前参数'''
        print("???")
        freq_c =float(self.DZ1000.query(":SOUR%s:FREQ?"%(self.CH)))
        print("????")
        ampl_c = float(self.DZ1000.query(":SOUR%s:VOLT?"%self.CH))
        print("ampl_c")
        print(self.CH)
        print(freq_c)
        print(ampl_c)
        # print ("CH=%s, Freq:%.2f Hz，Amp:%.2f V"%(self.CH,freq_c,ampl_c))

    def InitialDZ1000(self):
        '''
        初始化为10MHz的10mV的正弦波输出，阻抗为50欧姆，关闭幅度调制
        如需关闭其他调制请自行添加
        '''
        
        self.DZ1000.write(':OUTP%s:LOAD 50'%self.CH)
        self.DZ1000.write(':SOUR%s:AM:STAT OFF'%self.CH)
        self.DZ1000.write(':SOUR%s:FUNC SIN'%self.CH)
        self.DZ1000.write(':SOUR%s:FREQ 10000000'%(self.CH))
        self.DZ1000.write(':SOUR%s:VOLT 0.01' %(self.CH))

        self.DZ1000.write(':SOUR%s:VOLT:OFFS 0'%self.CH)
        self.DZ1000.write(':SOUR%s:PHAS 0'%self.CH)
        self.DZ1000.write(':OUTP%s ON'%self.CH)

    def setAmp(self,value):
        '''
        设置当前通道的幅度，单位为V
        可以使用amp = dBm2Vpp(value)将其转换为dbm
        '''
        #     amp = dBm2Vpp(value)
        amp = value
        print("setamp")
        if (amp<0.01) | (amp >4):
            print (amp, 'is out of range(0.01-4) V')
        print("?")
        self.DZ1000.write(':SOUR%s:VOLT %f' %(self.CH,amp))
        print("??")
        self.query()
        # self.DZ1000.write(':SOUR%s:VOLT %f' %(self.CH,amp))
        # self.query()
        print("self.query()")
        return 0

    def setFreq(self,value):
        '''设置当前通道的频率，单位为Hz'''
        value = value
        if value>59*1e6:
            print (value,'Maximal frequency is 60 MHz!')
        self.DZ1000.write(':SOUR%s:FREQ %f' %(self.CH,value))
        self.query()
        
    def stopScan(self):
        print ('Stopping scan by turning off tickling and initializing the DC')
        self.setFreq(1*1e6)
        self.setAmp(0.00)
        self.DZ1000.write(':SOUR%s:SWE:STAT OFF'%(self.CH))
        self.DZ1000.write('OUTP%s OFF'%(self.CH))
