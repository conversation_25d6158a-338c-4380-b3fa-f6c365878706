import time

from modules.light_369 import *
from modules.light_554_awg import Light554 as Light554AWG
from modules.light_554_dds import Light554 as Light554DDS
from modules.qcmos import *
from modules.config import LOADED_PARAMETER
from artiq.experiment import *
from artiq.language import now_mu, delay, parallel, sequential

from modules.RPC_Client.servo_motor import MotorServer
from modules.RPC_Client.rf_ctrl import  RFCtrl
from modules.RPC_Client.ablator import  Ablator

@rpc(flags={"async"})
def tprint(*data):
    """用于 artiq 环境中的异步数据打印"""
    print(data)


class QCMOSForHistogram(QCMOS):
    @rpc(flags={})
    def connect_qcmos(self, bundle_mode=1):
        if dcamcon_init():
            self.dcamcon = dcamcon_choose_and_open()
            if self.dcamcon is not None:
                # 设置相机参数
                res = setup_properties(
                    self.dcamcon,
                    x_pos=self.init_roi[0],
                    y_pos=self.init_roi[1],
                    width=self.init_roi[2],
                    height=self.init_roi[3],
                    tirg_mode=0,
                    bundle_mode=bundle_mode,
                    bundle_number=self.parameter.QCMOS.bundle_num * 2,
                )
                if res is False:
                    print("Parameter Configure False")
                else:
                    print("Parameter Configure Success")
            else:
                print("Device Open Failure")
        else:
            dcamcon_uninit()
            print("DCAM Initial Failure")

class RamanBaseExp(HasEnvironment):

    def build(self):
        # 1. 参数获取
        self.parameter = LOADED_PARAMETER()
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        self.idle_time = self.parameter.Light_554.AOD_time_before_AOM
        self.eit_time = self.parameter.Experiment.EIT_Cooling_Time
        self.ion_num = len(self.parameter.QCMOS.roi_for_ions)

        self.pre_cooling_time = self.parameter.Experiment.Pre_Cooling_Time
        self.setattr_argument("repeat", NumberValue(default=self.parameter.Experiment.Repeat, min=1, step=1, precision=0),
                              tooltip="执行次数", group="Experiment")
        if self.repeat is not None:
            self.parameter.Experiment.Repeat = self.repeat
            self.bundle_num = self.parameter.QCMOS.bundle_num
            if self.repeat % self.bundle_num != 0:
                raise ValueError(
                    f"QCMOS Repeat value error {self.repeat}, must be a multiple of 100"
                )
            self.re_repeat = int(self.repeat / self.bundle_num)


        # 2. 通道连接
        self.setattr_device("core")
        self.setattr_device("scheduler")


        self.setattr_argument(
            "AWG_Mode",
            EnumerationValue(["AWG", "DDS"], default="AWG"),
            tooltip="选择 DDS 模式 or AWG 模式",
            group="Experiment",
        )
        self.setattr_argument(
            "qubit_index", StringValue("(0,)"), tooltip="离子索引", group="Experiment"
        )

        self.setattr_argument(
            "ion_choice",
            StringValue(f"{tuple(range(self.ion_num))}"),
            tooltip="选择展示的离子",
            group="Experiment",
        )

        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="扫描任务的重复次数",
            group="Experiment",
        )

        self.setattr_argument(
            "enable_SBC",
            BooleanValue(default=False),
            tooltip="是否使用边带冷却",
            group="Experiment",
        )

        self.setattr_argument(
            "loop",
            BooleanValue(default=False),
            tooltip="是否循环执行",
            group="Experiment",
        )

        if self.AWG_Mode is not None:
            if self.AWG_Mode == "AWG":
                self.l554 = Light554AWG(self)
            elif self.AWG_Mode == "DDS":
                self.l554 = Light554DDS(self)
            else:
                raise "Unknown AWG_Mode"

        self.l369 = Light369(self)
        self.qcmos = QCMOSForHistogram(self)

    def prepare(self):
        self.qubit_index = eval(self.qubit_index)  # 获取离子索引
        self.ion_choice = eval(self.ion_choice)
        self.scan_parameter = self.X_scan_range.sequence
        self.scan_point = 0
        self.motor = MotorServer(sever_ip=self.parameter.Motor.ip, ionization_channel=1)
        self.rf_ctrl = RFCtrl(ip=self.parameter.RFCtrl.ip)
        self.ablator = Ablator(ip=self.parameter.Ablator.ip)
        self.save_ions_time = 0  # 当前救离子次数
        self.max_save_time = 100  # 最大救离子次数
        if self.loop:
            self.task_num = 10000

    def analyse(self):
        pass

    @rpc(flags={""})
    def prepare_dataset(self):
        pass

    @rpc(flags={""})
    def wave_compute(self, scan_parameter) -> TFloat:
        """传入参数计算波形"""
        pass

    @rpc(flags={""})
    def data_process(self, scan_point):
        """数据处理的逻辑"""
        self.qcmos.process_for_x_scan_re_repeat_check_ion_lost(scan_point, ion_choice=self.ion_choice)

    @rpc(flags={""})
    def save_ions(self):
        """救离子的逻辑"""
        # 目前只加入开离子化光救离子的代码
        self.motor.open_channel(self.motor.ionization_channel)
        self.rf_ctrl.shake(self.parameter.RFCtrl.minus, self.parameter.RFCtrl.delay_time)
        time.sleep(5)
        self.motor.close_channel(self.motor.ionization_channel)
        time.sleep(2)
        # self.set_dataset("all_ion_lost", 0, broadcast=True)
        self.set_dataset("ion_lost", 0, broadcast=True)

    @rpc(flags={})
    def check_lost(self) -> TBool:
        """检查离子是否丢失"""
        lost = self.get_dataset("ion_lost")
        if lost == 0 :
            self.save_ions_time = 0
        if lost == 1:
            self.save_ions_time += 1
            print(f"Ion_State: {lost}, Ion lost time: {self.save_ions_time}")
        if self.save_ions_time > self.max_save_time:
            self.scheduler.request_termination(self.get_dataset("rid"))
            print("save ion time reach maximum")
        return lost == 1

    @rpc(flags={})
    def ion_check(self) -> TInt32:
        """在主时序中检查"""
        ion_status = self.get_dataset("ion_status")
        # logger.info(f"ion_status: {ion_status}")
        return ion_status

    @rpc(flags={})
    def ion_catcher(self):
        """抓离子逻辑"""
        self.motor.open_channel(self.motor.ionization_channel)
        self.motor.open_channel(2)
        time.sleep(1)
        self.ablator.ablation()
        time.sleep(2)
        self.rf_ctrl.shake(self.parameter.RFCtrl.minus, self.parameter.RFCtrl.delay_time)
        time.sleep(1)
        self.motor.close_channel(self.motor.ionization_channel)
        self.motor.close_channel(2)
        time.sleep(1)

    @rpc(flags={})
    def process_ion_atomization(self):
        """处理离子雾化情形
        1. 开离子化光;
        2. RF shake;
        """
        self.motor.open_channel(self.motor.ionization_channel)
        self.motor.open_channel(2)
        time.sleep(1)

        self.rf_ctrl.shake(self.parameter.RFCtrl.minus, self.parameter.RFCtrl.delay_time)
        time.sleep(3)
        self.motor.close_channel(self.motor.ionization_channel)
        self.motor.open_channel(2)
        time.sleep(2)
        return

    @rpc(flags={})
    def process_ion_lost(self):
        """处理少离子, 抓离子"""
        self.ion_catcher()
        return

    @rpc(flags={})
    def process_ion_dark(self):
        """处理暗离子情形"""
        self.rf_ctrl.rf_re_open()
        self.ion_catcher()
        return

    @rpc(flags={})
    def process_ion_increase(self):
        """处理多离子情形"""
        self.rf_ctrl.rf_re_open()
        self.ion_catcher()
        return

    @rpc()
    def process_ion_status(self) ->TBool:
        """离子状态检查的统一入口"""
        ion_status = self.ion_check()
        if ion_status == 0:
            self.save_ions_time = 0
            return True
        elif ion_status == 1: #
            self.process_ion_lost()
        elif ion_status == 2:
            self.process_ion_atomization()
        elif ion_status == 3:
            self.process_ion_dark()
        elif ion_status == 4:
            self.process_ion_increase()
        else:
            raise ValueError("un supported ion status")
        self.save_ions_time += 1
        logger.info(f"save_ions_time {self.save_ions_time}")
        return False

    @kernel()
    def run_level_1(self, operation_time):
        """单次 repeat 中的时序"""
        for _ in range(self.bundle_num):
            self.qcmos.qcmos_start_count()
            delay(self.pre_cooling_time)
            self.l369.switch_pre_cooling_to_cooling()
            delay(self.cooling_time)
            self.qcmos.qcmos_stop_count()
            # self.l369.switch_cool_to_pump()
            self.l369.switch_cool_to_eit()
            delay(self.eit_time)

            self.l369.switch_eit_to_pump()
            delay(self.pumping_time)

            with parallel:
                self.l369.switch_pump_to_control()
                self.l554.AWG_on()

            with parallel:
                self.l369.sbc_cooling(self.enable_SBC)
                delay(operation_time)

            with parallel:
                self.l554.AWG_off()
                self.qcmos.qcmos_start_count()
                self.l369.switch_control_to_detect()

            delay(self.detecting_time)

            self.qcmos.qcmos_stop_count()

            delay(1 * ms)

            self.l369.switch_detect_to_pre_cooling()

    @kernel()
    def run_level_1_re_repeat(self, scan_parameter):
        """re_repeat 的设计逻辑
        1. 每 bundle_num 次处理一遍数据, 仅仅将数据保存, 等待在 process_for_x_scan 中统一处理
        """
        for re_repeat_num in range(self.re_repeat):
            self.run_level_1(scan_parameter)
            delay(5 * ms)
            self.core.wait_until_mu(now_mu())
            # self.qcmos.process_for_single_bundle(re_repeat_num)
            self.qcmos.process_for_single_bundle_double(re_repeat_num)

            self.core.break_realtime()

    @kernel()
    def run_level_2(self, scan_parameter):
        """加入一个扫描点的波形计算和数据处理"""
        while True:
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(scan_parameter)

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1_re_repeat(operation_time)
            delay(10 * ms)
            self.core.wait_until_mu(now_mu())

            # 3. 数据处理
            self.data_process(self.scan_point)

            # 4. 离子状态检查
            #
            # if self.check_lost():
            #     self.save_ions()  # 封装救离子的逻辑
            # else:
            #     self.scan_point += 1
            #     break
            #
            # # 添加优雅中止
            while not self.process_ion_status():
                self.core.break_realtime()
                self.qcmos.qcmos_trigger(self.bundle_num * 2)
                self.core.wait_until_mu(now_mu())
                self.qcmos.process_for_ion_check()

                if self.scheduler.check_termination():
                    break

            self.scan_point += 1
            break
            # while  ion_status !=0:
            #     if ion_status == 1: #
            #         self.process_ion_lost()
            #     elif ion_status == 2:
            #         self.process_ion_atomization()
            #     elif ion_status == 3:
            #         self.process_ion_dark()
            #     elif ion_status == 4:
            #         self.process_ion_increase()
            #     else:
            #         raise ValueError("un supported ion status")
                # tprint("hello")
                # self.core.break_realtime()
                # delay(1 * ms)
                # self.qcmos.qcmos_trigger(self.bundle_num * 2)
                # self.core.wait_until_mu(now_mu())
                # self.qcmos.process_for_ion_check()

            # not save ions
            # self.scan_point +=1
            # break

    @kernel()
    def run_level_3(self, scan_list):
        """N 个扫描点"""
        self.scan_point = 0
        while self.scan_point < len(scan_list):
            self.run_level_2(scan_list[self.scan_point])
            # 添加优雅中止
            if self.scheduler.check_termination():
                break

    @kernel()
    def run_task_num(self, task_num, scan_list):
        """多次任务"""
        for _ in range(task_num):
            self.run_level_3(scan_list)
            if self.scheduler.check_termination():
                break

    @kernel()
    def exp_initial(self):
        """exp 初始化"""
        self.prepare_dataset()
        self.core.break_realtime()
        self.core.reset()
        self.qcmos.initial()
        self.l554.initial()
        self.l369.initial()
        self.core.break_realtime()

    @kernel
    def run(self):
        self.exp_initial()
        self.run_task_num(self.task_num, self.scan_parameter)
