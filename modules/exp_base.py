from artiq.experiment import *
from modules.config import LOADED_PARAMETER
import numpy as np

from modules.pmt import PMT
from modules.light_369 import *
from modules.light_554_awg import *
from modules.signal_mw import SignalMW
from typing import Optional

class ExperimentBase():
    @kernel
    def run_scan_time(self):
        # AWG波形只需生成一次的实验，以扫描时间的实验为代表.默认生成波形部分在主实验文件完成
        # 0. 初始化
        self.core.reset()
        self.pmt.initial()
        self.l554.initial()
        self.l369.initial()
        self.l369.switch_off_to_cool()

        # 1. 初始化
        # 主时序
        for i in range(self.task_num):
            scan_point = 0
            while scan_point< len(self.scan_parameter):
                # 1. 运行时序
                self.core.break_realtime()
                self.sequence_554_scan_time()

                # 2. 处理数据
                self.core.wait_until_mu(now_mu())
                delay(1e-6)
                self.pmt.process_for_x_scan(scan_point)

                # 3. 检查离子是否丢失
                if self.check_lost():
                    self.motor.open_ionization(5)#离子丢失则打开离子化光5s
                else:
                    scan_point +=1


    @kernel
    def run_scan_frequency(self):
        # AWG波形需要随着扫描参数更新而更新的实验，以扫描频率的实验为代表
        # 0. 初始化
        self.core.reset()
        self.pmt.initial()
        self.l554.initial()
        self.l369.initial()

        # 1. 初始化
        # 主时序
        for i in range(self.task_num):
            scan_point = 0
            while scan_point< len(self.scan_parameter):

                # 1. 计算波形
                self.wave_compute(self.scan_parameter[scan_point])  # 计算本轮循环的波形
                self.l554.load_wave_to_awg()  # 波形导入 AWG
                self.l554.start_awg()

                # 2. 运行时序
                self.core.break_realtime()
                self.sequence_554_scan_frequency()

                # 3. 处理数据
                self.core.wait_until_mu(now_mu())
                delay(1e-6)
                self.pmt.process_for_x_scan(scan_point)

                # 4. 检查离子是否丢失
                if self.check_lost():
                    self.motor.open_ionization(5)#离子丢失则打开离子化光5s
                else:
                    scan_point +=1


    @kernel
    def sequence_554_scan_time(self,scan_point):

        # AWG波形只需生成一次的实验，以扫描时间的实验为代表
        for repeat in range(self.repeat):
            # 1. 读取冷却光计数
            self.pmt.pmt_start_count()
            delay(self.cooling_time)
            self.pmt.pmt_stop_count()

            # 2. eit冷却
            self.l369.switch_cool_to_eit()
            delay(self.eit_time)

            # 3. 切换到pumping

            # 测热率临时用
            # self.l369.all_eit_off()
            # delay(100*ms)
            # self.l369.switch_control_to_pump()

            self.l369.switch_eit_to_pump()
            delay(self.pumping_time)

            # 4. pump切换到 control,打开aod和aom的射频开关
            self.l369.switch_pump_to_control()

            self.l554.AOM_dds_off()
            delay(1 * us)  # 这个delay是为了让aom的dds信号关闭
            with parallel:
                self.l554.AOM_TTL_on()
                delay_mu(np.int64(self.core.ref_multiplier))

                self.l554.AOD_on()

            self.l554.AWG_on()

            delay(self.idle_time)  # 等待 AOD 上升沿时间

            # 5. 边带冷却
            for j in range(self.SBC_num):
                delay(self.SBC_time)

                self.l369.switch_control_to_pump()

                delay(pumping_time)

                self.l369.switch_pump_to_control()
            # 6. 边带冷却后再次pump
            self.l369.switch_control_to_pump()
            delay(pumping_time)
            self.l369.switch_pump_to_control()
            delay(self.idle_time)

            # 7. 操作时间
            delay(self.scan_parameter[scan_point])  # 从扫描参数中提取一个时间

            # 8. 关闭aom和aod的射频开关
            with parallel:
                self.l554.AOM_TTL_off()

                delay_mu(np.int64(self.core.ref_multiplier))

                self.l554.AOD_off()
            delay(10 * us)
            self.l554.AOM_dds_on()

            self.l554.AWG_off()
            delay(1 * us)  # 等aod信号切换到远离离子的时候再打开aom

            # 9. 切换到 detect， 同时开启 pmt 计数
            with parallel:
                self.l554.AWG_off()
                self.pmt.pmt_start_count()
                self.l369.switch_control_to_detect()

            delay(self.detecting_time)

            # 10. 停止 pmt 计数， 同时切换到 cooling
            with parallel:
                self.pmt.pmt_stop_count()
                self.l369.switch_detect_to_cool()


    @kernel
    def sequence_554_scan_frequency(self):
        # AWG波形需要随着扫描参数更新而更新的实验，以扫描频率的实验为代表
        for repeat in range(self.repeat):
            # 1. 读取冷却光计数
            self.pmt.pmt_start_count()
            delay(self.cooling_time)
            self.pmt.pmt_stop_count()

            # 2. eit冷却
            self.l369.switch_cool_to_eit()
            delay(self.eit_time)

            # 3. 切换到pumping
            self.l369.switch_eit_to_pump()
            delay(self.pumping_time)

            # 4. pump切换到 control,打开aod和aom的射频开关
            with parallel:
                self.l369.switch_pump_to_control()
                self.l554.AWG_on()

            # 5. 边带冷却时间
            delay(self.idle_time)

            for i in range(self.SBC_num):
                delay(self.SBC_time)
                self.l369.switch_control_to_pump()
                delay(self.pumping_time)
                self.l369.switch_pump_to_control()

            delay(self.idle_time)

            delay(self.delay_total)  # delay一个固定的操作时间

            with parallel:

                self.l554.AWG_off()
                self.pmt.pmt_start_count()
                self.l369.switch_control_to_detect()

            delay(self.detecting_time)

            with parallel:
                self.pmt.pmt_stop_count()
                self.l369.switch_detect_to_cool()

    @rpc(flags={"async"})
    def tprint(self, data):
        print(data)