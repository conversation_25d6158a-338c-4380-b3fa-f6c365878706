"""
关于 PMT 模块的校准
1. AD5721 与 LTC2668 电压值的校准，使用 AWG or artiq 通道给窗口信号
"""

import ctypes
import datetime
import os
import sys
from time import sleep
import time
import numpy as np
from matplotlib import pyplot as plt
from mpl_toolkits.axes_grid1 import make_axes_locatable

# from modules.PMT_package.pmt_base import PMTController
import logging

plt.rcParams["font.family"] = "serif"
plt.rcParams["font.serif"] = ["Times New Roman"]


def setup_logger(name):
    """初始化并返回一个 logger 对象"""
    logger = logging.getLogger(name)
    handler = logging.StreamHandler()  # 或者 FileHandler，根据需求修改
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)  # 设置日志级别
    return logger


# 获取 dll 文件的路径
# current_dir_path = os.path.dirname((os.path.abspath(__file__)))
# dll_path = os.path.join(current_dir_path, "x64", "wtctlmgr.dll")
__all__ = ["PMTController"]


class PMTController:
    def __init__(self, repeat: int = 100, ad5721: int = 2030, ltc2668: int = 1800):
        """PMT控制器的初始化函数

        初始化PMT设备，包括加载DLL、连接设备、设置初始参数等。

        Parameters
        ----------
        repeat : int
            数据采集的重复次数, by default 100
        ad5721 : int
            AD5721 DAC的初始值(增益电压), by default 2030
            有效范围: 0-2040
        ltc2668 : int
            LTC2668 DAC的初始值(比较器电压), by default 1800
            有效范围: 0-4095，2047对应0V

        Notes
        -----
        初始化过程包括：
        1. 加载并初始化DLL
        2. 连接PMT设备
        3. 设置AD5721和LTC2668的初始值
        4. 重置FIFO
        5. 启用数据读取
        6. 设置读数重复次数

        Raises
        ------
        RuntimeError
            当设备初始化失败时抛出
        """
        self.logger = setup_logger(__name__)
        try:
            self.pmt_sdk = 0
            self.pmt_device_handle = ctypes.c_void_p(0)
            self._load_dll()
            self.connect()
            self.is_open()
            self.set_ad5721(ad5721)
            self.set_ltc2668(ltc2668)
            self.reset()
            self.enable_read_data(enable=0)
            self.enable_read_data(enable=1)
            self.set_repeat(repeat=repeat)
        except Exception as e:
            self.close()  # 确保资源被释放
            raise RuntimeError(f"PMT initialise failed: {str(e)}")

    def set_repeat(self, repeat: int = 100):
        """设置数据采集的重复次数并初始化相关缓存

        Parameters
        ----------
        repeat : int, optional
            数据采集的重复次数, by default 100

        Notes
        -----
        量子计算实验的一个数据点通常涉及 N 次测量, 统一读出统一处理会更高效. 本函数用于设置被打包读出的数据组数.
        """
        self.repeat = int(repeat)  # 组数
        self.data_length = self.repeat * 32  # 每次是 32 个数
        self.data_buffer_on_pc = np.zeros(self.data_length, dtype=np.uint8)  # 创建缓存
        self.data_buffer_on_pc_pointer = self.data_buffer_on_pc.ctypes.data_as(
            ctypes.POINTER(ctypes.c_uint8)
        )  # 创建指向该缓存的指针
        self.data_actual_len = ctypes.c_uint32(
            0
        )  # 初始化一个缓存空间，所读数据的实际长度
        print("set repeat num:", self.repeat)

    def _load_dll(self):
        """加载并初始化PMT设备的DLL文件

        该函数负责加载PMT设备控制所需的DLL文件，并初始化相关方法。

        Notes
        -----
        加载过程包括：
        1. 获取当前文件路径
        2. 构建DLL文件路径 (x64/wtctlmgr.dll)
        3. 验证DLL文件存在
        4. 加载DLL文件
        5. 初始化必要的DLL方法

        Attributes
        ----------
        self.pmt_sdk : ctypes.CDLL
            加载的DLL对象
        self.pmtCtlOpen : function
            从DLL中加载的设备打开方法

        Raises
        ------
        FileNotFoundError
            当DLL文件不存在时抛出
        OSError
            当DLL加载过程中发生操作系统错误时抛出
        Exception
            当发生其他未预期的错误时抛出

        Notes
        -----
        DLL文件路径结构：
        - 当前文件所在目录
          - x64/
            - wtctlmgr.dll
        """
        try:
            # 获取当前文件的绝对路径
            script_dir = os.path.dirname(os.path.abspath(__file__))

            # 构建DLL文件路径
            dll_dir = os.path.abspath(os.path.join(script_dir, "x64"))
            dll_path = os.path.join(dll_dir, "wtctlmgr.dll")

            # 检查DLL文件是否存在
            if not os.path.exists(dll_path):
                raise FileNotFoundError(
                    f"[Error]: DLL file not found at path: {dll_path}"
                )

            # 加载DLL文件
            self.pmt_sdk = ctypes.cdll.LoadLibrary(dll_path)
            print(self.pmt_sdk)
            if self.pmt_sdk is not None:
                print("DLL loaded successfully")

            # 初始化DLL方法
            self.pmtCtlOpen = self.pmt_sdk.pmtCtlOpen
            print("Initialized pmtCtlOpen method")

        except FileNotFoundError as fnf_error:
            print(fnf_error)
        except OSError as os_error:
            print(f"[Error]: OS error occurred while loading DLL: {os_error}")
        except Exception as e:
            print(f"[Error]: An unexpected error occurred: {e}")

    def is_open(self):
        """检查PMT设备是否成功打开并连接

        检查设备句柄是否有效，用于验证设备连接状态。

        Returns
        -------
        bool
            True表示设备已打开
            False表示设备未打开

        Notes
        -----
        设备句柄(pmt_device_handle)是与PMT设备通信的关键。
        该方法通过检查句柄是否为None来判断设备状态。
        """
        is_connected = self.pmt_device_handle is not None

        if is_connected:
            self.logger.debug("PMT device is connected")
        else:
            self.logger.warning("PMT device is not connected")

        return is_connected

    def connect(self, virtualMode=False):
        """连接PMT硬件设备

        尝试连接PMT设备并将设备句柄存储到实例中。

        Parameters
        ----------
        virtualMode : bool, optional
            是否使用虚拟模式, by default False
            True: 虚拟模式
            False: 实际硬件模式

        Notes
        -----
        该函数使用pmtCtlOpen方法建立与设备的连接，并将设备句柄
        存储在self.pmt_device_handle中。

        Raises
        ------
        RuntimeError
            当设备连接失败时抛出
        """
        try:
            ret = self.pmtCtlOpen(virtualMode, ctypes.pointer(self.pmt_device_handle))
            if ret != 0:
                self.logger.error("PMT connection failed")
                raise RuntimeError("PMT connection failed")

            self.logger.info("PMT connection success")

        except Exception as e:
            self.logger.error(f"PMT connection error: {str(e)}")
            raise RuntimeError(f"connection failed: {str(e)}")

    def close(self):
        """关闭PMT设备并释放资源

        执行设备关闭前的清理工作，包括重置FIFO和禁用数据读取，
        然后关闭设备连接。

        Notes
        -----
        关闭顺序：
        1. 重置FIFO
        2. 禁用数据读取
        3. 关闭设备连接

        Raises
        ------
        RuntimeError
            当设备关闭失败时抛出
        """
        try:
            self.reset()
            self.enable_read_data(enable=0)
            ret = self.pmt_sdk.pmtCtlClose(ctypes.pointer(self.pmt_device_handle))

            if ret != 0:
                raise RuntimeError("PMT device close failed")

            self.logger.info("PMT device is closed")

        except Exception as e:
            self.logger.error(f"close PMT device error: {str(e)}")
            raise RuntimeError(f"close PMT device failed: {str(e)}")

    def reset(self):
        """重置PMT设备的FIFO缓冲区

        清空FIFO缓冲区中的数据，为新的数据采集做准备。

        Notes
        -----
        该操作会清除所有未读取的数据，应在适当的时机调用。

        Raises
        ------
        RuntimeError
            当FIFO重置失败时抛出
        """
        try:
            ret = self.pmt_sdk.pmtCtlReset(self.pmt_device_handle)
            if ret != 0:
                raise RuntimeError("FIFO reset failed")

            self.logger.debug("FIFO reset success")

        except Exception as e:
            self.logger.error(f"reset fifo error: {str(e)}")
            raise RuntimeError(f"reset fifo failed: {str(e)}")

    def set_ad5721(self, dac_value):
        """设置AD5721芯片DAC值以控制高压电源输出

        Parameters
        ----------
        dac_value : int
            DAC控制值，允许范围 0 - 2040

        Notes
        -----
        PMT 探头由高压电源供电, 高压电源的输出电压由 AD5721 芯片调控.
        PMT 探头建议工作电压是 800-900V, 需要限制 AD5721 的输出.
        2米电源线情况下的参考值对照表：

        DAC值  |  高压输出(V)  |  控制电压(V)
        -----------------------------------
        100    |  43.6        |  0.1779
        400    |  174.9       |  0.7103
        600    |  262.2       |  1.0649
        1000   |  436.6       |  1.7742
        1400   |  610.5       |  2.4833
        1600   |  697.4       |  2.8384
        1800   |  784.2       |  3.1924
        1900   |  827.5       |  3.3684
        2000   |  871.0       |  3.5482
        2100   |  914.6       |  3.7239
        2200   |  958.0       |  3.9018
        2300   |  1001.3      |  4.0787

        Raises
        ------
        RuntimeError
            当设置AD5721 DAC值失败时抛出
        ValueError
            当DAC值超出有效范围时抛出
        """
        try:
            if not 0 <= dac_value <= 2040:
                raise ValueError("DAC value must be between 0 and 2040")

            ret = self.pmt_sdk.pmtCtlSetAd5721DacValue(
                self.pmt_device_handle, dac_value
            )
            if ret != 0:
                raise RuntimeError("Failed to set AD5721 DAC value")

            self.logger.info(f"Set AD5721 DAC value: {dac_value}")

        except Exception as e:
            self.logger.error(f"Error setting AD5721 DAC value: {str(e)}")
            raise

    def set_ltc2668(self, dac_value):
        """设置LTC2668比较器的参考电压值

        Parameters
        ----------
        dac_value : int
            DAC数值，范围0-4095
            对应电压范围-5V到+5V
            2047对应0V

        Notes
        -----
        该函数会同时设置两组通道：
        - 通道0-15  (0x0F00)
        - 通道16-31 (0xF000)

        Raises
        ------
        RuntimeError
            当设置任一通道组失败时抛出
        ValueError
            当DAC值超出有效范围时抛出
        """
        try:
            if not 0 <= dac_value <= 4095:
                raise ValueError("DAC value must be between 0 and 4095")

            # 设置通道0-15
            channel1 = ctypes.c_uint32(0x0F00)
            ret = self.pmt_sdk.pmtCtlSetLtc2668DacValue(
                self.pmt_device_handle, channel1, dac_value
            )
            if ret != 0:
                raise RuntimeError("Failed to set LTC2668 channels 0-15")

            # 设置通道16-31
            channel2 = ctypes.c_uint32(0xF000)
            ret = self.pmt_sdk.pmtCtlSetLtc2668DacValue(
                self.pmt_device_handle, channel2, dac_value
            )
            if ret != 0:
                raise RuntimeError("Failed to set LTC2668 channels 16-31")

            self.logger.info(f"Set LTC2668 DAC value for all channels: {dac_value}")

        except Exception as e:
            self.logger.error(f"Error setting LTC2668 DAC value: {str(e)}")
            raise

    def enable_read_data(self, enable=1):
        """启用或禁用数据读取功能

        Parameters
        ----------
        enable : int
            控制数据读取的标志
            0: 禁用数据读取
            1: 启用数据读取

        Raises
        ------
        RuntimeError
            当设置数据读取状态失败时抛出

        Notes
        -----
        该函数用于控制PMT设备的数据读取功能，
        通常在开始新的数据采集前调用。
        """
        try:
            ret = self.pmt_sdk.pmtCtlEnableReadData(self.pmt_device_handle, enable)
            if ret != 0:
                raise RuntimeError("Failed to set data reading state")

            state = "enabled" if enable else "disabled"
            self.logger.debug(f"Data reading {state}")

        except Exception as e:
            self.logger.error(f"Error setting data reading state: {str(e)}")
            raise

    def get_make_data_num(self):
        """获取自上次 reset 到当前产生的总数据组数

        Returns
        -------
        int
            自上次reset后产生的数据数量

        Raises
        ------
        RuntimeError
            当获取数据数量失败时抛出

        Notes
        -----
        该函数返回自上次调用reset()后，
        设备产生的所有数据点数量。
        """
        try:
            make_data_num = ctypes.c_uint32(0)
            ret = self.pmt_sdk.pmtCtlGetMakeNum(
                self.pmt_device_handle, ctypes.pointer(make_data_num)
            )
            if ret != 0:
                raise RuntimeError("Failed to get generated data count")

            self.logger.debug(f"Generated data count: {make_data_num.value}")
            return make_data_num.value

        except Exception as e:
            self.logger.error(f"Error getting generated data count: {str(e)}")
            raise

    def get_read_data_num(self):
        """获取已经被读取的数据总数

        Returns
        -------
        int
            当前已读取的数据点数量

        Raises
        ------
        RuntimeError
            当获取已读数据数量失败时抛出

        Notes
        -----
        该函数返回当前实验中已经被读取的
        数据点数量，用于追踪数据读取进度。
        """
        try:
            read_data_num = ctypes.c_uint32(0)
            ret = self.pmt_sdk.pmtCtlGetReadNum(
                self.pmt_device_handle, ctypes.pointer(read_data_num)
            )
            if ret != 0:
                raise RuntimeError("Failed to get read data count")

            self.logger.debug(f"Read data count: {read_data_num.value}")
            return read_data_num.value

        except Exception as e:
            self.logger.error(f"Error getting read data count: {str(e)}")
            raise

    def read_data(self):
        """从PMT设备读取数据

        从设备缓冲区读取数据并重新组织为二维数组格式。

        Returns
        -------
        numpy.ndarray
            shape为(repeat, 32)的数据数组，其中：
            - repeat: 重复测量次数
            - 32: PMT通道数
            如果没有数据，返回空数组

        Notes
        -----
        数据读取过程：
        1. 从设备缓冲区读取原始数据
        2. 检查实际读取的数据长度
        3. 将数据重组为(repeat, 32)的二维数组

        数据格式：
        - 每行代表一次测量
        - 每列代表一个PMT通道
        - 数据类型为uint8

        数据重排: PMT 通道和 FPGA 通道的顺序不同, 需要重新排列

        Raises
        ------
        RuntimeError
            当数据读取失败时抛出
        """
        try:
            ret = self.pmt_sdk.pmtCtlReadBuffer(
                self.pmt_device_handle,
                self.data_buffer_on_pc_pointer,
                self.data_length,
                ctypes.byref(self.data_actual_len),
            )

            if ret != 0:
                raise RuntimeError("Failed to read PMT data")

            if self.data_actual_len.value == 0:
                self.logger.debug("No data available")
                return np.array([])

            data = self.data_buffer_on_pc.reshape(self.repeat, 32)
            self.logger.debug(f"Read data shape: {data.shape}")
            return data

        except Exception as e:
            self.logger.error(f"Error reading PMT data: {str(e)}")
            raise

    def read_data_speed(self):
        """读数速度测试"""
        start_time = time.time()
        ret = self.pmt_sdk.pmtCtlReadBuffer(
            self.pmt_device_handle,
            self.data_buffer_on_pc_pointer,
            self.data_length,
            ctypes.byref(self.data_actual_len),
        )
        end_time = time.time()

        return end_time - start_time

    def test_speed_of_read_data(self):
        """测试读数函数的时间"""
        start_time = time.time()
        data = self.read_data()
        print(data.sum(0))
        end_time = time.time()
        return end_time - start_time

    def test_dark_counts(self):
        """测试暗计数
        返回一百次计数的平均值
        """
        data_temp = self.read_data()
        # data_sum = data_temp.mean(axis=0)
        data_sum = data_temp.sum(axis=0)
        return data_sum

    def plot_histogram(
        self,
    ):
        """绘制直方图，显示32列数据的总和
        :param data: 32列的数据
        :param title: 直方图的标题
        :param xlabel: x轴的标签
        :param ylabel: y轴的标签
        """

        data = self.read_data()
        if len(data) == 0:
            print("No data to plot")
            return

        # 计算每列的总和
        column_sums = data.sum(axis=0)

        # 绘制直方图
        plt.bar(range(1, 33), column_sums)
        plt.title("PMT channels sum")
        plt.xlabel("Channel Index")
        plt.ylabel("counts")
        plt.xticks(range(1, 33))  # 设置x轴的刻度
        plt.show()


def test_init():
    """测试 PMT 基本的打开关闭功能"""
    pmt = PMTController()
    sleep(3)
    num = pmt.get_make_data_num()
    print("make data num", num)
    # data = pmt.read_data()
    pmt.plot_histogram()
    # print(data)
    num2 = pmt.get_read_data_num()
    print("get data num", num)
    pmt.close()


def test_read_time():
    """测试 pmt 的读数速度"""
    # 0. 测试值
    test_value = [1e0, 1e1, 1e2, 1e3, 1e4, 1e5]

    repeat_num = 10

    execution_times = np.zeros([repeat_num, len(test_value)])

    # 1. PMT 初始化

    pmt = PMTController()

    # 2. 测试循环
    for i in range(len(test_value)):
        pmt.set_repeat(int(test_value[i]))
        sleep(1)
        for j in range(repeat_num):
            # execution_times[j, i] = pmt.test_speed_of_read_data()
            execution_times[j, i] = pmt.read_data_speed()

            print(execution_times)

    pmt.close()
    print(execution_times)

    # 3. 计算平均值和标准误差
    averages = execution_times.mean(axis=0)
    std_errors = execution_times.std(axis=0)

    # 4. 绘图
    plt.errorbar(
        test_value,
        averages,
        yerr=std_errors,
        fmt="-o",
        ecolor="red",
        elinewidth=2,
        capsize=5,
    )
    plt.xscale("log")  # 设置横轴为对数刻度
    # plt.yscale('log')
    plt.xlabel("Repeats")
    plt.ylabel("Time (seconds)")
    plt.title("Speed test of different repeats value")
    # plt.legend(loc='upper left', bbox_to_anchor=(1, 1))
    plt.grid(True, which="both", linestyle="--", linewidth=0.5)
    plt.show()


def test_counts(
    ad5721_value=1730, counts_window=1e-3, ltc_start=1850, ltc_stop=1750, ltc_step=-10
):
    """测试暗计数
    ad5721_value 禁止设置大于2000
    """
    # 1. PMT 初始化

    # 2. 设置测试值
    ltc_value = np.arange(start=ltc_start, stop=ltc_stop, step=ltc_step)
    # ltc_value = [2047, 2000, 1950, 1900, 1850, 1800]
    repeat_num = 3  # 每个数据点的重复次数 ，默认每个点采样 100 次， 在 pmt 的初始化函数里, 则共 10000 组

    # 初始化存储数据集(横轴为 32 通道，纵轴为 ltc_value)
    dark_count_data = np.zeros([len(ltc_value), 32])
    data_temp = np.zeros([repeat_num, 32])
    pmt = PMTController()
    pmt.set_ad5721(ad5721_value)
    sleep(0.1)

    for i in range(len(ltc_value)):
        # 遍历 ltc 电压值
        # print(int(ltc_value[i]))
        # start_time = time.time()
        pmt.set_ltc2668(dac_value=int(ltc_value[i]))
        # end_time = time.time()
        # print(end_time-start_time)
        pmt.reset()
        sleep(0.5)

        for j in range(repeat_num):
            # 遍历平均次数

            data_temp[j, :] = pmt.test_dark_counts()
            sleep(0.1)
            # print(data_temp)

        dark_count_data[i, :] = data_temp.mean(axis=0)
        print(dark_count_data)

    pmt.close()

    # 计算采样时间长度， 以确定上述暗计数是多大的时间窗口内采集到
    sample_time = counts_window * pmt.repeat  # 计算总计数时间 单位：s
    # dark_count_data = dark_count_data  # 将单位变换到每 ms 多少个暗计数
    # dark_count_data[:, 16:32] = 0
    # 绘图
    fig, ax = plt.subplots()
    cax = ax.imshow(dark_count_data / sample_time, cmap="viridis")
    divider = make_axes_locatable(ax)
    cax2 = divider.append_axes("right", size="5%", pad=0.05)
    cbar = fig.colorbar(cax, cax=cax2)
    cbar.set_label("dark counts/s")  # 添加colorbar的标签

    cbar.update_ticks()  # 更新刻度
    ax.set_xlabel("PMT_channels")
    ax.set_ylabel("ltc_value")
    ax.set_yticks(range(len(dark_count_data)), ltc_value)
    ax.set_xticks(range(len(dark_count_data[0])), np.arange(1, 33))
    ax.set_title(
        "AD5721 = {}".format(ad5721_value), fontsize=14, color="blue", fontweight="bold"
    )

    # 保存图像
    save_dir = "test_figure"
    os.makedirs(save_dir, exist_ok=True)
    current_time = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    filename = "{}.png".format(current_time)
    filepath = os.path.join(save_dir, filename)
    plt.savefig(filepath, dpi=500)
    plt.show()


def test_counts_realtime(ad5721=2030, ltc2668=1800, counts_window=0.5e-3):
    """测试实时计数"""
    pmt = PMTController(repeat=100, ad5721=ad5721, ltc2668=ltc2668)

    while True:
        data = pmt.read_data()
        # 对列求和
        data_sum = data.sum(axis=0)
        sample_time = counts_window * pmt.repeat
        print(data_sum / sample_time)
        sleep(0.2)
        pmt.reset()


if __name__ == "__main__":
    # test_read_time()
    test_counts(
        ad5721_value=2000,
        counts_window=1e-3,
        ltc_start=1850,
        ltc_stop=1750,
        ltc_step=-10,
    )
