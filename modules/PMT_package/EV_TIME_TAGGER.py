# -*- coding: utf-8 -*-
"""
Created on Mon Mar 15 15:25:18 2021

@author: Administrator
"""

from EV_FPGA import XilinxFPGA
from EV_REG_CTRL import REG_CTRL
import time

GREG_TIME_TAGGER_CTRL_ADDR = 0


class TIME_TAGGER_CTRL(REG_CTRL):
    addrType = "FpgaPcieAddr"
    baseAddr = GREG_TIME_TAGGER_CTRL_ADDR
    status = {
        "sw_rst": 0,
        "enable": 0,
        "fifo_rd": 0,
        "rd_data_count": 0,
        "wr_data_count": 0,
        "fifo_data0": 0,
        "fifo_data1": 0,
        "fifo_data2": 0,
        "fifo_data3": 0,
        "fifo_data4": 0,
        "fifo_data5": 0,
        "fifo_data6": 0,
        "fifo_data7": 0,
    }
    ctrl_dict = {
        # signal_name: [start_bit,end_bit,reg_offset,rw]

        "sw_rst": [31, 31, 0, 'rw'],
        "enable": [30, 30, 0, 'rw'],
        "fifo_rd": [29, 29, 0, 'rw'],
        "full_signal": [25, 25, 1, 'ro'],
        "empty_signal": [24, 24, 1, 'ro'],
        "wr_data_count": [23, 12, 1, 'ro'],
        "rd_data_count": [11, 0, 1, 'ro'],
        "fifo_data0": [31, 0, 2, 'ro'],
        "fifo_data1": [31, 0, 3, 'ro'],
        "fifo_data2": [31, 0, 4, 'ro'],
        "fifo_data3": [31, 0, 5, 'ro'],
        "fifo_data4": [31, 0, 6, 'ro'],
        "fifo_data5": [31, 0, 7, 'ro'],
        "fifo_data6": [31, 0, 8, 'ro'],
        "fifo_data7": [31, 0, 9, 'ro'],
    }

    def __init__(self, fpga, baseAddr=-1):
        REG_CTRL.__init__(self, fpga, baseAddr)

    def rst(self):
        self.ctrl("sw_rst", 1)
        self.ctrl("enable", 0)
        # time.sleep(0.01)
        self.ctrl("sw_rst", 0)
        self.ctrl("enable", 1)

    def status(self):
        return self.read("rd_data_count")

    def isempty(self):
        '''
        0 : data exists
        1 : no data
        '''
        return self.read("empty_signal")

    def isfull(self):
        '''
        0 : data exists
        1 : no data
        '''
        return self.read("full_signal")

    def get_all(self):
        # start_time = time.time()
        datas = self.read_all()
        # end_time = time.time()
        # print('time used: ', end_time - start_time)
        datas = bin(int(datas.decode(), 16) + 2 ** 256)
        dataslist = list(map(lambda s: int(s, base=2), [datas[k:k + 8] for k in range(3, 259, 8)]))
        dataslist.reverse()

        return dataslist

    def get_data(self):
        data = 0;
        counts = [0] * 32
        # print(counts)
        if 1:
            self.ctrl("fifo_rd", 1)
            self.ctrl("fifo_rd", 0)
            data = self.read("fifo_data0")
            counts[0] = int(data & 0xff)
            counts[1] = int(data >> 8 & 0xff)
            counts[2] = int(data >> 16 & 0xff)
            counts[3] = int(data >> 24 & 0xff)
            data = self.read("fifo_data1")
            counts[4] = int(data & 0xff)
            counts[5] = int(data >> 8 & 0xff)
            counts[6] = int(data >> 16 & 0xff)
            counts[7] = int(data >> 24 & 0xff)
            data = self.read("fifo_data2")
            counts[8] = int(data & 0xff)
            counts[9] = int(data >> 8 & 0xff)
            counts[10] = int(data >> 16 & 0xff)
            counts[11] = int(data >> 24 & 0xff)
            data = self.read("fifo_data3")
            counts[12] = int(data & 0xff)
            counts[13] = int(data >> 8 & 0xff)
            counts[14] = int(data >> 16 & 0xff)
            counts[15] = int(data >> 24 & 0xff)
            data = self.read("fifo_data4")
            counts[16] = int(data & 0xff)
            counts[17] = int(data >> 8 & 0xff)
            counts[18] = int(data >> 16 & 0xff)
            counts[19] = int(data >> 24 & 0xff)
            data = self.read("fifo_data5")
            counts[20] = int(data & 0xff)
            counts[21] = int(data >> 8 & 0xff)
            counts[22] = int(data >> 16 & 0xff)
            counts[23] = int(data >> 24 & 0xff)
            data = self.read("fifo_data6")
            counts[24] = int(data & 0xff)
            counts[25] = int(data >> 8 & 0xff)
            counts[26] = int(data >> 16 & 0xff)
            counts[27] = int(data >> 24 & 0xff)
            data = self.read("fifo_data7")
            counts[28] = int(data & 0xff)
            counts[29] = int(data >> 8 & 0xff)
            counts[30] = int(data >> 16 & 0xff)
            counts[31] = int(data >> 24 & 0xff)
        return counts


if __name__ == '__main__':
    V6 = XilinxFPGA(mode="Serial");
    V6.openDevice();

    A_status = 1;
    B_status = 0;

    cnt_status = 0;
    while (1):
        print("-----TIME_TAGGER PROCESS-------")
        time.sleep(1);
        if (A_status):
            TIME_TAGGER = TIME_TAGGER_CTRL(V6, baseAddr=10);
            TIME_TAGGER.rst();
            cnt_status = TIME_TAGGER.status();
            if cnt_status > 1:
                print(TIME_TAGGER.get_data());
            else:
                print("fifo0 is empty")

        if (B_status):
            TIME_TAGGER = TIME_TAGGER_CTRL(V6, baseAddr=13);
            TIME_TAGGER.rst();
            cnt_status = TIME_TAGGER.status();
            if cnt_status > 1:
                print(TIME_TAGGER.get_data());
            else:
                print("fifo1 is empty")
