# -*- coding: utf-8 -*-
"""
Created on Tue Jan 03 15:23:41 2021

@author: <PERSON>
c"""

# -*- coding: utf-8 -*-


from EV_FPGA import XilinxFPGA
from EV_REG_CTRL import REG_CTRL
import time

GREG_LOAD_BIT_CTRL_ADDR = 0


class SPI_TIMING_CTRL(REG_CTRL):  # AD5679 CONFIG
    addrType = "FpgaPcieAddr"
    baseAddr = GREG_LOAD_BIT_CTRL_ADDR
    status = {
        "lfb_enable": 0,
        "lfb_transfer": 0,
        "lfb_sload_reverse": 0,
        "lfb_sync_reverse": 0,
        "lfb_wdata": 0,
        "lfb_clk_div": 0,  # lfb_clk_div= sys_clk(100mhz)/(lfb_clk*2)
    }
    ctrl_dict = {
        # signal_name: [start_bit,end_bit,reg_offset,rw]

        "lfb_enable": [31, 31, 0, 'rw'],
        "lfb_transfer": [30, 30, 0, 'rw'],
        "lfb_sload_reverse": [29, 29, 0, 'rw'],
        "lfb_sync_reverse": [28, 28, 0, 'rw'],
        "lfb_wdata": [31, 0, 1, 'rw'],
        "lfb_clk_div": [15, 0, 2, 'rw'],

    }

    def __init__(self, fpga, baseAddr=-1):
        REG_CTRL.__init__(self, fpga, baseAddr)

    def SPI_config(self):
        self.ctrl("lfb_enable", 1)
        self.ctrl("lfb_clk_div", 10)  # lfb_sck =10m lfb_sck_div =sys_clk(100mhz)/(lfb_clk*2)
        self.ctrl("lfb_sload_reverse", 0)
        self.ctrl("lfb_sync_reverse", 0)

    def SPI_wdata(self, wdata):
        self.ctrl("lfb_wdata", (wdata))
        self.ctrl("lfb_transfer", 1)
        self.ctrl("lfb_transfer", 0)

    def AD5721_init(self):
        # 1: full reset
        # 2: Write to Control Register

        cmd_word = 0xf;  # software full reset
        self.SPI_wdata((cmd_word * 2 ** 16) * 2 ** 8)
        ## 1111 0000 0000 0000 0000 xxxx xxxx  (low bit unused : fpga design)
        cmd_word = 0x4;
        self.SPI_wdata((cmd_word * 2 ** 16 + 0x513) * 2 ** 8)

    def AD5721_wdata(self, wdata):
        cmd_word = 0x3;

        if (wdata > 0xffff):
            print("overrange\n")
            print("Failed,Try again")
        else:
            self.SPI_wdata((cmd_word * 2 ** 16 + wdata) * 2 ** 8);
            # print("Write Succes")

    def LTC2668_wdata(self, cmd, address, wdata):
        self.SPI_wdata((cmd * 2 ** 16 + address * 2 ** 12 + wdata) * 2 ** 12)
        # print("Write Succes")


if __name__ == '__main__':
    V6 = XilinxFPGA(mode="Serial");
    V6.openDevice();
    LTC2668_TEST = 1;
    AD5721_TEST = 1;
    # while(1):
    print("-----PMT_DAC INIT PROCESS-------")
    if (AD5721_TEST):
        DAC_LFB_TIMING = SPI_TIMING_CTRL(V6, baseAddr=1);
        # DAC0 AD5721 = 1

        DAC_LFB_TIMING.SPI_config();

        DAC_LFB_TIMING.AD5721_init();

        Value = 0x6e80;  # 0x0000 ~ 0xffff

        DAC_LFB_TIMING.AD5721_wdata(Value);
        print("-----AD5721 INIT Finished-------")
        # while(0):
    if (LTC2668_TEST):
        DAC_LFB_TIMING = SPI_TIMING_CTRL(V6, baseAddr=4);
        # LTC2668_A = 4
        # LTC2668_B = 7

        cmd_word = 0x8;  # set all channal
        address = 0xf;
        dac_value = 0x000;  # 800 = 0V

        DAC_LFB_TIMING.SPI_config();

        DAC_LFB_TIMING.LTC2668_wdata(cmd_word, address, dac_value)
        print("-----LTC2668 A DAC INTI Finshed-------")
    if (LTC2668_TEST):
        DAC_LFB_TIMING = SPI_TIMING_CTRL(V6, baseAddr=7);
        # LTC2668_A = 4
        # LTC2668_B = 7
        cmd_word = 0x8;  # set all channal
        address = 0xf;
        dac_value = 0x000;  # 800 = 0V

        DAC_LFB_TIMING.SPI_config();

        DAC_LFB_TIMING.LTC2668_wdata(cmd_word, address, dac_value)
        print("-----LTC2668 B DAC INTI Finshed-------")
