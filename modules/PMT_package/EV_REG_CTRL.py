# -*- coding: utf-8 -*-
"""
Created on Thu Dec 10 13:32:55 2015

@author: x
"""
#from GREG_MOD_C_CPP_TEMPLATE import *
from EV_FPGA import XilinxFPGA
import time
class REG_CTRL:
    addrType = "FpgaPcieAddr"
    baseAddr = 0
    init_seq = []
    status = {
        #"reset":    0
    }
    ctrl_dict = {
        # signal_name, [start_bit,end_bit, reg_offset]
        #"reset":    [31,31, 0, 'rw']
    }

    reg_dict = {
        #0: ["reset"]    
    }
    reg_val = {
        #0: 0x00000000    
    }
    debug = 0
    logfile = ""
            
    def __init__(self, fpga, baseAddr=-1):
        self.fpga = fpga
        if baseAddr>-1:
            self.baseAddr = baseAddr+self.fpga.FPGA_GREG_BASE_ADDR
        else:
            self.baseAddr = self.baseAddr + self.fpga.FPGA_GREG_BASE_ADDR

    def init(self):
        self.status2fpga()
        
    def status2fpga(self, reduce_op = 0):
        prev_reg_val = self.reg_val
        self.build_reg_dict()
        self.build_reg_val()
        for ii in self.reg_val:
            if (reduce_op):
                if (ii in prev_reg_val) and (self.reg_val[ii]==prev_reg_val[ii]):
                    continue
            self.fpga.writeHw_PCIeAddr(self.baseAddr+ii,self.reg_val[ii])

    def print_status(self):
        print('[Info]: printing fpga status:')
        for opcode in self.ctrl_dict:
            if self.ctrl_dict[opcode][3]=="wo":
                print('        '+opcode+': '+'\t'+ 'NA, Write Only')
            else:
                start_bit = self.ctrl_dict[opcode][0]
                end_bit   = self.ctrl_dict[opcode][1]
                regn      = self.ctrl_dict[opcode][2]
                print('        '+opcode+': '+'\t0x'+ '%x'%self.fpga.getRange(self.baseAddr+regn,start_bit,end_bit))

    def ctrl(self,opcode,data=0x0):
        if opcode in self.ctrl_dict:
            if self.ctrl_dict[opcode][3]=="ro":
                return -1
            start_bit = self.ctrl_dict[opcode][0]
            end_bit   = self.ctrl_dict[opcode][1]
            regn      = self.ctrl_dict[opcode][2]
            if end_bit==start_bit:
                # bit control
                if data>0:
                    self.fpga.set1(self.baseAddr+regn,start_bit)
                else:
                    self.fpga.set0(self.baseAddr+regn,start_bit)
            else:
                self.fpga.setRange(self.baseAddr+regn,start_bit,end_bit,data)
        else:
            print("[Error]: opcode not identified: "+opcode)
    def read(self,opcode):
        if opcode in self.ctrl_dict:
            if self.ctrl_dict[opcode][3]=="wo":
                return -1;
            start_bit = self.ctrl_dict[opcode][0]
            end_bit   = self.ctrl_dict[opcode][1]
            regn      = self.ctrl_dict[opcode][2]
            return self.fpga.getRange(self.baseAddr+regn,start_bit,end_bit)

    def build_reg_dict(self):        
        self.reg_dict.clear()
        for kk in self.ctrl_dict:
            regidx = self.ctrl_dict[kk][2]
            if regidx in self.reg_dict:
                self.reg_dict[regidx].append(kk)
            else:
                self.reg_dict[regidx] = [kk]
    
    def read_all(self):
        return self.fpga.rd_alldata()
        
    def build_reg_val(self):
        self.reg_val.clear()        
        for kk in self.reg_dict:
            ctrlwd = 0
            for opcode in self.reg_dict[kk]:
                start_bit = self.ctrl_dict[opcode][0]
                end_bit   = self.ctrl_dict[opcode][1]
                if end_bit>start_bit:
                    start_bit,end_bit = (end_bit, start_bit)
                ctrlmask = 2**(start_bit-end_bit+1)-1
                if self.ctrl_dict[opcode][3]=='ro':
                    valmask  = 0
                else:
                    valmask  = ctrlmask & self.status[opcode]
                ctrlmask = ctrlmask << end_bit
                valmask  =  valmask << end_bit
                ctrlwd = ctrlwd & (~ctrlmask)
                ctrlwd = ctrlwd | valmask
            self.reg_val[kk] = ctrlwd
    def setlogfile(self,fname):
        self.logfile = fname
        
    def disp(self, msg):
        if self.debug:
            print(msg)
        if len(self.logfile)>0:
            with open(self.logfile,'a+') as f:
                f.write(msg+'\n')
            
    def _gen_c_def(self):
        class_name = self.__class__.__name__
        print("#pragma once")
        print('#include "BasicPort.h"')
        print('namespace FDKV2{')
        print("//============================================================")
        print("// Start %s Register Addr Definitions"%class_name)
        print("#define %s_DEFAULT_BASE_ADDR \t(%d)"%(class_name.upper(),self.baseAddr-self.fpga.FPGA_GREG_BASE_ADDR))
        print("// Start Register offset and mask definition")
        for ctrl_wds in sorted(self.ctrl_dict.keys()):
            print("#define "+(class_name+"_"+ctrl_wds).upper()+"_REG   \t\t(0x%x)"%(4*self.ctrl_dict[ctrl_wds][2]))
            print("#define "+(class_name+"_"+ctrl_wds).upper()+"_OFFSET\t\t(%d)"%(self.ctrl_dict[ctrl_wds][1]))
            print("#define "+(class_name+"_"+ctrl_wds).upper()+"_MASK  \t\t(0x%x<<%d)"%(2**(self.ctrl_dict[ctrl_wds][0]-self.ctrl_dict[ctrl_wds][1]+1)-1,self.ctrl_dict[ctrl_wds][1]))
        print("// Start Init Value Definition")
        for ctrl_wds in self.status:
            if self.status[ctrl_wds]>0:
                print("#define "+(class_name+"_"+ctrl_wds).upper()+"_INITVAL   \t\t(0x%x)"%(self.status[ctrl_wds]))
                
    def gen_h_file(self):
        class_name = self.__class__.__name__
        this_class_name = "GREG_" + class_name
        self.build_reg_dict()
        self.build_reg_val()
        from jinja2 import Template
        sstemp = Template(GREG_MODULE_CH_TEMPLATE)
        #old
        #self._gen_c_def()
        #print(sstemp.render(thisclass=self))

        #new
        generated_f="./_generated_cpp_files/"+this_class_name+".h"
        with open(generated_f,'w') as f:
            f.write(sstemp.render(thisclass=self))

    def update_h_file(self):
        class_name = self.__class__.__name__
        this_class_name = "GREG_" + class_name
        self.build_reg_dict()
        self.build_reg_val()
        input_file = "./_tobe_updated_cpp_files/"+this_class_name+".h"
        output_file = "./_updated_cpp_files/"+this_class_name+".h"
        import cogapp
        cogcmd = cogapp.Cog()
        cogcmd.main(["cog","-G", self,"-c","-e","-o",output_file,input_file])
        
    def gen_cpp_file(self):
        class_name = self.__class__.__name__
        this_class_name = "GREG_" + class_name
        self.build_reg_dict()
        self.build_reg_val()
        from jinja2 import Template
        sstemp = Template(GREG_MODULE_CPP_TEMPLATE)
        #old
        #print(sstemp.render(thisclass=self))
        
        #new
        generated_f="./_generated_cpp_files/"+this_class_name+".cpp"
        with open(generated_f,'w') as f:
            f.write(sstemp.render(thisclass=self))
#        
    def update_cpp_file(self):
        class_name = self.__class__.__name__
        this_class_name = "GREG_" + class_name
        self.build_reg_dict()
        self.build_reg_val()
        input_file = "./_tobe_updated_cpp_files/"+this_class_name+".cpp"
        output_file = "./_updated_cpp_files/"+this_class_name+".cpp"
        import cogapp
        cogcmd = cogapp.Cog()
        cogcmd.main(["cog","-G", self,"-c","-e","-o",output_file,input_file])
    
if __name__ == '__main__':  
    #print "No Unit Test component avalible, This is a base class"
    test_reg_ctrl = REG_CTRL(0)
    #test_reg_ctrl.gen_h_file()
    test_reg_ctrl.gen_cpp_file()
