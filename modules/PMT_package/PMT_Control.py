# -*- coding: utf-8 -*-
from EV_PMT_MEM import SPI_TIMING_CTRL
from EV_TIME_TAGGER import TIME_TAGGER_CTRL
from EV_FPGA import XilinxFPGA
from EV_REG_CTRL import REG_CTRL
import time
import ast
import numpy as np


def sum_PMT(data32=[1, 2, 3], center_channel=[2], sum_channels=0):
    """
    data32: 1*32 1d-array, counts for 32 PMT
    center_channel: 1d-array pr a list, channels selected to plot
    sum_channels: int value, channels around the center channel
    """
    sum_data = []
    for ch in center_channel:
        sum_temp = np.sum(data32[ch - sum_channels:ch + sum_channels + 1])
        sum_data.append(sum_temp)
    return np.array(sum_data)


def str2dict(str0):
    """
    turn a string to a dict
    """
    str1 = str0.replace('null', 'None').replace('N/A', 'None').replace('false', 'None')
    return ast.literal_eval(str1)


class MultichannelPMT:

    def __init__(self):
        # ==========================Initialization==============================#

        self.V6 = XilinxFPGA(COMID='COM4')
        # self.V6 = XilinxFPGA(COMID='COM24')
        # print("Xilinx")
        self.V6.openDevice()
        # print("Xilinx2")
        initialization = 1
        LTC2668_SET = 1
        AD5721_SET = 1
        #####
        # print(11111)
        ######
        ref_Volt = 0x700  # test volt without PMT, from 0x000 to 0xFFF, corresponds to -1V to +1V    750

        self.channel_order = [0, 31, 8, 23, 1, 30, 9, 22, 2, 29, 10, 21, 3, 28, 11, 20, 4, 27, 12, 19, 5, 26, 13, 18, 6,
                              25, 14, 17, 7, 24, 15, 16]
        # 生成 0到32 的numpy 数列

        # self.channel_order = np.array(
        #     [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28,
        #      29, 30, 31])
        # ------------------------------------------------------------------------#
        if initialization:
            # print("-----PMT_DAC INIT PROCESS-------")# LTC2668_A = 4   LTC2668_B = 7
            if LTC2668_SET:
                DAC_LFB_TIMING = SPI_TIMING_CTRL(self.V6, baseAddr=4);
                cmd_word = 0x8;  # set all channal
                address = 0xf;
                dac_value = ref_Volt;  # 800 = 0V
                DAC_LFB_TIMING.SPI_config();
                DAC_LFB_TIMING.LTC2668_wdata(cmd_word, address, dac_value)
                # print("-----LTC2668 A DAC INTI Finshed-------")
                # ------------------------------------------------------------------------#
                DAC_LFB_TIMING = SPI_TIMING_CTRL(self.V6, baseAddr=7);
                cmd_word = 0x8;  # set all channal
                address = 0xf;
                dac_value = ref_Volt;  # 800 = 0V
                DAC_LFB_TIMING.SPI_config();
                DAC_LFB_TIMING.LTC2668_wdata(cmd_word, address, dac_value)
                # print("-----LTC2668 B DAC INTI Finshed-------")
            # -------------------------------------------------------------------------#
            # print("----- PMT Enable-------")
            if (AD5721_SET):
                DAC_LFB_TIMING = SPI_TIMING_CTRL(self.V6, baseAddr=1);  # DAC0 AD5721 = 1
                DAC_LFB_TIMING.SPI_config();
                DAC_LFB_TIMING.AD5721_init();
                Value = 0x7500;  # 0x0000 ~ 0xffff
                DAC_LFB_TIMING.AD5721_wdata(Value);
            # print("-----PMT Initialize Successfully-----")
        # ---------------------------------------------------------------------------#
        # print("-----TIME_TAGGER PROCESS-------")
        A_status = 1;
        if (A_status):
            self.TIME_TAGGER = TIME_TAGGER_CTRL(self.V6, baseAddr=10);
        #####
        # print(22222)
        ######
        self.TIME_TAGGER.rst();

    def readPMT(self):
        # cnt_status = TIME_TAGGER.status();
        # while(cnt_status == 0):
        #     cnt_status = TIME_TAGGER.status();
        # print ('cnt_status',TIME_TAGGER.status())
        # print(cnt_status)
        # go1 = time.time()
        # data_A = TIME_TAGGER.get_data();
        data_A = self.TIME_TAGGER.get_all()
        # TIME_TAGGER.rst();
        # go2 = time.time()
        # print(go2-go1,data_A) 
        #####
        # print(33333)
        ######
        data_ordered = [data_A[i] for i in self.channel_order]
        return data_ordered

    def resetPMT(self):
        #####
        # print(44444)
        ######
        self.TIME_TAGGER.rst()

    def closePMT(self):
        self.V6.closeDevice()
        print('PMT closed')


if __name__ == '__main__':

    PMT = MultichannelPMT()
    PMT.resetPMT()
    time.sleep(1)
    for i in range(60):
        print('------------------------', i)
        start_time = time.time()
        data_single = PMT.readPMT()
        end_time = time.time()
        print(start_time-end_time)
        print(data_single)
