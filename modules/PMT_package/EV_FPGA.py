# -*- coding: utf-8 -*-
"""
Created on Thu Dec 10 13:32:55 2015

@author: x
"""
import os
from ctypes import *
import serial
import time


class XilinxFPGA:
    debug_output_read_info = 0
    debug_output_write_info = 0
    mode = ''
    serialport = None
    FPGA_GREG_BASE_ADDR = 0
    # AXI4_LREG_BASE_ADDR = 0x10530
    # AXI4_LREG_BASE_ADDR = 0x106000
    AXI4_LREG_BASE_ADDR = 0x105000  # ad9517

    # AXI4_LREG_BASE_ADDR = 0x105000 # gpx2

    def barAddr2FpgaPcieAddr(self, offAddr):
        """Convert the offset ADDR in script to FPGA PCIE_ADDR (32bit Addr, Could be verified in Chipscop).
           The converter will return: offAddr/4 + 0x200
           for example, offAddr: 0x300  --> 0x300/4+0x200(FPGA PCIE ADDR).
        """
        return self.FPGA_GREG_BASE_ADDR + offAddr / 4

    def barAddr2FpgaAsiAddr(self, offAddr):
        """Convert the offset ADDR in script to FPGA ASI ADDR (AXI) (Byte Addr, Could be verified in Chipscop).
           The converter will return: offAddr+4*0x200
           for example, offAddr: 0x300  --> 0x300+4*0x200(FPGA PCIE ADDR).
        """
        return 4 * self.FPGA_GREG_BASE_ADDR + offAddr

    def FpgaPcieAddr2BarAddr(self, fpgaPcieAddr):
        """Convert the FPGA PCIE_ADDR (32bit Addr, Could be verified in Chipscop) to offset ADDR in this script (PCIE Addr Space).
           The converter will return: (fpgaPcieAddr-0x200)*4
        """
        return (fpgaPcieAddr - self.FPGA_GREG_BASE_ADDR) * 4

    def FpgaAsiAddr2BarAddr(self, fpgaAsiAddr):
        """Convert the FPGA ASI ADDR (AXI) (Byte Addr, Could be verified in Chipscop) to offset ADDR in this script (PCIE Addr Space).
           The converter will return: fpgaAsiAddr-4*0x200
        """
        return fpgaAsiAddr - 4 * self.FPGA_GREG_BASE_ADDR

    def FpgaPcieAddr2SerAddr(self, fpgaPcieAddr):
        """Convert the FPGA PCIE_ADDR (32bit Addr, Could be verified in Chipscop) to Serial Port ADDR in this script (AXI4 Addr Space).
           The converter will return: (fpgaPcieAddr-0x200)*4
        """
        return (fpgaPcieAddr - self.FPGA_GREG_BASE_ADDR) * 4 + self.AXI4_LREG_BASE_ADDR

    def __init__(self, dllfile='InterFace.dll', COMID=None):
        self.COMID = COMID
        # print(self.COMID)
        self.mode = "Serial"
        # print(self.mode)
        self.loadSerial()
        # print("self.loadSerial")

    def __del__(self):
        self.closeDevice()

    def loadDLL(self, dllpath):
        # print ' - starting init Driver DLL...'
        # load dll and get the function object
        self.dll = cdll.LoadLibrary(dllpath);
        self.OpenFPGA = self.dll.OpenXilinxFPGA;
        self.CloseFPGA = self.dll.CloseXilinxFPGA;
        self.WriteFPGA = self.dll.WriteXilinxFPGA;
        self.ReadFPGA = self.dll.ReadXilinxFPGA;
        self.isFPGAOpen = self.dll.isXilinxFPGAOpen
        # set the argtypes & return type
        self.OpenFPGA.restype = c_uint;
        self.OpenFPGA.argtypes = [c_void_p];
        self.CloseFPGA.restype = c_uint;
        self.CloseFPGA.argtypes = [c_void_p];
        self.WriteFPGA.restype = c_uint;
        self.WriteFPGA.argtypes = [c_void_p, c_uint, c_uint];
        self.ReadFPGA.restype = c_uint;
        self.ReadFPGA.argtypes = [c_void_p, c_uint];
        self.isFPGAOpen.restype = c_bool;
        self.isFPGAOpen.argtypes = [c_void_p];
        # invoke api GetStructInfo
        self.hdev = c_void_p(0);

    def loadSerial(self):
        # print("in")
        self.serialport = serial.Serial()
        # print("serial.Serial()",serial.Serial())
        self.serialport.baudrate = 921600
        self.serialport.parity = serial.PARITY_NONE
        self.serialport.stopbits = 1
        self.serialport.timeout = 0.1  # 0.1
        # print(self.serialport.timeout)
        # self.serialport.set_buffer_size(rx_size = 4096, tx_size = 0)
        # print(self.serialport)
        self.hdev = None
        COMID = self.COMID
        # print("COMID",COMID)
        COMIDRANGE = range(20)
        if COMID == None:
            for i in COMIDRANGE:
                self.serialport.port = 'COM%d' % i
                try:
                    print("    Scaning: %s" % self.serialport.port)
                    self.serialport.open()
                    if self.serialport.isOpen():
                        self.serialport.write('dw 0x0000\r'.encode())
                        rb = self.serialport.readline()
                        rb = self.serialport.readline()
                        if (len(rb) > 15) and (rb[1:13] == b"[0x00000000]"):
                            COMID = self.serialport.port
                            print('COMID is %s' % COMID)
                            break
                        self.serialport.close()
                except serial.serialutil.SerialException:
                    continue
        else:
            self.serialport.port = COMID
            # print("inin")
            self.serialport.open()
            # print("self.serialport.open()")
            if self.serialport.isOpen():
                self.serialport.write('dw 0x0000\r'.encode())
                rb = self.serialport.readline()
                rb = self.serialport.readline()
                if (len(rb) > 15) and (rb[1:13] == b"[0x00000000]"):
                    COMID = self.serialport.port
                else:
                    print('wrong COMID')
                    COMID == None
        if COMID == None:
            self.serialport = None
            print('[Error]: No FPGA Serial Port is available!')
        else:
            # print COMID
            print("[Info]: FPGA Serial Port Connection Succeed!")

    def deviceValid(self):
        return self.hdev.value != None

    def openDevice(self):
        errMsg = '[Error]: Serial - Open Xilinx FPGA Device Error!'
        if self.serialport == None:
            print(errMsg)
            print('         Error Code: Serial Port Connection Not Found')
            raise

    def closeDevice(self):
        if self.hdev:
            try:
                self.serialport.close()
                self.hdev = None
            except Exception as e:
                print('         Error Code: Unkown')
                raise e

    def isDeviceOpen(self):
        # errMsg = '[Error]: Check Xilinx FPGA Device Open/Close Error!'
        return (not (self.serialport == None))

    def writeDevice32(self, addr, data):
        if self.debug_output_write_info: print("[Info]: writing addr: 0x%x" % addr + " value: 0x%x" % data);
        errMsg = '[Error]: Serial Write Data to Xilinx FPGA Device Error!'
        try:
            inf = 'dw 0x%x = 0x%x\r' % (addr, data)
            self.serialport.write(inf.encode())
            rb = self.serialport.readline()
            rb = self.serialport.readline()
            wdstatus = len(rb) < 30
            if wdstatus > 0:
                print(errMsg)
                print('         Error Code: %x' % (wdstatus))
                raise
        except Exception as e:
            print(errMsg)
            print('         Error Code: Unkown')
            raise e

    def readDevice32(self, addr):
        if self.debug_output_read_info: print("[Info]: reading addr: 0x%x" % addr);
        errMsg = '[Error]: Serial Read Data to Xilinx FPGA Device Error!'
        try:
            inf = 'dw 0x%x\r' % addr
            self.serialport.write(inf.encode())
            rb = self.serialport.readline()
            rb = self.serialport.readline()
            wdstatus = len(rb) < 25
            if wdstatus > 0:
                print(errMsg)
                print('         Error Code: %x' % (wdstatus))
                raise
            else:
                return int(rb[17:-1], 16)
        except Exception as e:
            print(errMsg)
            print('         Error Code: Unkown')
            raise e

    def rd_alldata(self):

        inf = 'dw 0x_'
        self.serialport.write(inf.encode())
        # start_time = time.time()
        rb = self.serialport.readline()
        # end_time = time.time()
        # print('time used: ', end_time - start_time)
        rb = self.serialport.readline()

        return rb[1:65]

    def writeHw_PCIeAddr(self, pcieAddr, data):
        self.writeDevice32(self.FpgaPcieAddr2SerAddr(pcieAddr), data)

    def readHw_PCIeAddr(self, pcieAddr):
        return self.readDevice32(self.FpgaPcieAddr2SerAddr(pcieAddr))

    def set1(self, addr, bitn):
        self.set1_SerialAddr(addr, bitn)

    def set0(self, addr, bitn):
        self.set0_SerialAddr(addr, bitn)

    def set1_SerialAddr(self, pcieAddr, bitn):
        self.set1_raw(self.FpgaPcieAddr2SerAddr(pcieAddr), bitn)

    def set0_SerialAddr(self, pcieAddr, bitn):
        self.set0_raw(self.FpgaPcieAddr2SerAddr(pcieAddr), bitn)

    def set1_raw(self, addr, bitn):
        ctrlwd = self.readDevice32(addr)
        self.writeDevice32(addr, ctrlwd | (0x1 << bitn))

    def set0_raw(self, addr, bitn):
        ctrlwd = self.readDevice32(addr)
        self.writeDevice32(addr, ctrlwd & (~(0x1 << bitn)))

    def setRange(self, addr, start_bit, end_bit, data):
        self.setRange_SerialAddr(addr, start_bit, end_bit, data)

    def setRange_SerialAddr(self, pcieAddr, start_bit, end_bit, data):
        self.setRange_raw(self.FpgaPcieAddr2SerAddr(pcieAddr), start_bit, end_bit, data)

    def setRange_raw(self, addr, start_bit, end_bit, data):
        data = int(data)
        ctrlwd = self.readDevice32(addr)
        if end_bit > start_bit:
            start_bit, end_bit = (end_bit, start_bit)
        datamask = 2 ** (start_bit - end_bit + 1) - 1
        datamask = datamask << end_bit
        data = data << end_bit
        data = data & datamask
        ctrlwd = ctrlwd & (~datamask)
        ctrlwd = ctrlwd | data
        self.writeDevice32(addr, ctrlwd)

    def getRange(self, addr, start_bit, end_bit):
        return self.getRange_SerialAddr(addr, start_bit, end_bit)

    def getRange_SerialAddr(self, pcieAddr, start_bit, end_bit):
        return self.getRange_raw(self.FpgaPcieAddr2SerAddr(pcieAddr), start_bit, end_bit)

    def getRange_raw(self, addr, start_bit, end_bit):
        ctrlwd = self.readDevice32(addr)
        if end_bit > start_bit:
            start_bit, end_bit = (end_bit, start_bit)
        datamask = 2 ** (start_bit - end_bit + 1) - 1
        data = ctrlwd >> end_bit
        return data & datamask


if __name__ == '__main__':
    # Test PCIE Read Write
    #    k7 = XilinxFPGA();
    #    k7.openDevice();
    #    k7.writeDevice32(0x00,0xFF);
    #    k7.setRange(0x00,8,1,0xFF);
    #    k7.closeDevice();

    # Test Serial Read Write
    k7 = XilinxFPGA(mode='Serial');
    k7.openDevice();
    k7.writeDevice32(0x00, 0xFF);
    k7.setRange(0x00, 8, 1, 0xFF);
    k7.closeDevice();
