"""
通过单例模式创建全局的 config 配置文件, 在任何地方调用这个类, 得到的都是同一个实例
任何地方修改了, 也都会反映到这个实例上.
1. config 类维护一个字典, 用于存储参数
2. 当以访问字典属性的方式访问 config 类时, 会调用 __getattr__ 方法, 从而返回字典中的值(本质上, 依然是个字典, 只是可以以属性的方式访问, 不可以传入实验环境)
3. 当以赋值的方式访问 config 类时, 会调用 __setattr__ 方法, 从而将值存入字典中.
4. 加线程锁, 如果有多个线程都在创建 config 实例, 也只会创建一个实例.
5. 允许以访问属性的方式修改字典的值(这似乎会导致, 必须添加参数提取的过程. 如果将代理类变成 config 类的属性, 或许也可以)
6. 访问不存在的 key-value 时, 会自动创建新的.
7. 修改成嵌套属性的方式, 以便于在实验环境中使用.
8. 允许导入外置的 config.json 文件, 并生成一个新字典, 用于替代默认参数.
"""

import threading
import json
import os
import time
from pathlib import Path
from ionctrl_pkg.utils.log import get_logger
__all__ = ["Config", "LOADED_PARAMETER"]

import numpy as np

SCRIPT_DIRECTORY = Path(__file__).resolve().parent  # 获取当前脚本的路径
JSON_FILE_PATH = (
    SCRIPT_DIRECTORY / "config.json"
)  # 获取当前脚本同路径下的 json 文件路径

JSON_FILE_PATH = str(JSON_FILE_PATH)

logger = get_logger(__name__)


class SingletonMeta(type):
    """
    线程安全的单例元类。

    此元类确保任何使用它的类只会创建一个实例，即使在多线程环境中也是如此。
    它使用 _lock 来确保线程安全。
    """

    _instances = {}
    _lock = threading.Lock()

    def __call__(cls, *args, **kwargs):
        # 确保同一时间只有一个线程可以尝试创建实例。
        with cls._lock:
            if cls not in cls._instances:
                instance = super().__call__(*args, **kwargs)
                cls._instances[cls] = instance
        return cls._instances[cls]


class ConfigProxy:
    """
    代理类，用于将嵌套字典转换为属性访问形式。

    此类递归地将字典转换为属性，以便可以使用点号访问嵌套结构。
    """

    def __init__(self, data):
        # 遍历字典并将每个键值对转换为属性
        for key, value in data.items():
            if isinstance(value, dict):
                # 如果值是字典，则创建另一个 ConfigProxy 实例
                setattr(self, key, ConfigProxy(value))
            else:
                # 否则，直接设置属性
                setattr(self, key, value)

    def to_dict(self):
        """
        将 ConfigProxy 实例转换回字典形式。

        此方法用于将所有嵌套的 ConfigProxy 实例和其他属性转换回字典形式。
        """
        result = {}
        # 遍历 ConfigProxy 实例的属性
        for key in self.__dict__:
            value = getattr(self, key)
            if isinstance(value, ConfigProxy):
                result[key] = value.to_dict()
            else:
                result[key] = value
        return result


class Config(metaclass=SingletonMeta):
    """
    全局配置管理器类，使用单例模式。

    此类为应用程序中的配置提供一个全局访问点。
    它使用 ConfigProxy 类来将嵌套字典转换为可以通过属性访问的形式。
    """

    def __init__(self, param_dict=None):
        """
        初始化 Config 实例。

        :param param_dict: 可选的初始配置字典。
        """
        self._initialize_properties(param_dict or {})

    def _initialize_properties(self, param_dict):
        """
        根据提供的字典初始化 Config 实例的属性。

        :param param_dict: 包含配置的字典。
        """
        for key, value in param_dict.items():
            if isinstance(value, dict):
                # 对于字典类型，创建 ConfigProxy 实例
                setattr(self, key, ConfigProxy(value))
            else:
                # 对于其他类型，直接设置属性
                setattr(self, key, value)

    def to_dict(self):
        """
        将 Config 实例转换回字典形式。

        用于获取当前配置的字典表示，包括所有嵌套属性。
        """

        result = {}
        # 遍历 Config 类的属性
        for key in self.__dict__:
            # 获取属性值
            value = getattr(self, key)
            # 如果属性是 ConfigProxy 类型，则调用它的 to_dict 方法
            if isinstance(value, ConfigProxy):
                result[key] = value.to_dict()
            # 如果是纯粹的数值, 则直接添加到字典
            else:
                result[key] = value
        return result

    @staticmethod
    def my_serializer(obj):
        """对象序列化函数
        json 文件不支持 np.ndarray 对象, 而只支持 list 等基本对象. 需要将 np 对象转化成 list
        """
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (float, np.float32, np.float64)):
            return round(obj, 6)
        raise TypeError("不可序列化的类型" + str(type(obj)))

    def update_config_json(self):
        """将本实例的所有参数, 更新到默认路径下的 config.json 文件"""
        try:
            with open(JSON_FILE_PATH, "w") as f:
                json.dump(self.to_dict(), f, indent=4, default=self.my_serializer)
            # logger.info("update config.json ")
        except Exception as e:
            logger.info("update config.json fali")
            logger.info(f"{e}")

    def export_config_json(self):
        """导出本实例的所有参数, 到 result 路径下"""
        file_name = "{}_parameter.json".format(get_rid())  # 获取 rid
        try:

            with open(str(file_name), "w") as f:
                json.dump(self.to_dict(), f, indent=4, default=self.my_serializer)
            logger.info(str(file_name))
            logger.info("export config to file：" + file_name)
        except Exception as e:
            logger.info(str(e))


def LOADED_PARAMETER(path=JSON_FILE_PATH):
    """从 JSON 文件加载配置,  默认加载当前脚本同目录下的 config.json 文件。"""
    with open(str(path), "r") as f:
        return Config(json.load(f))


def get_rid():
    """
    读取项目根目录下的 last_rid.pyon 文件，获取实验编号
    注意：默认 config.py 文件在 modules 文件夹下，用 file_path = '../last_rid.pyon'即可索引到文件
    :return: 实验编号
    """
    file_path = SCRIPT_DIRECTORY.parent / "last_rid.pyon"  # 假设文件存储在项目根目录下
    # file_path = '../last_rid.pyon'

    if not os.path.exists(str(file_path)):
        logger.info("Not exist:" + {}.__format__(file_path))
        return

    with open(str(file_path), "r") as file:
        content = file.read()  # 读到的是一个字符串，要转换成 int

    return int(content)


if __name__ == "__main__":
    start_time = time.perf_counter()
    config = LOADED_PARAMETER()
    print(time.perf_counter() - start_time)
