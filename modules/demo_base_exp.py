import numpy as np

from modules.light_369 import *

from structure.shots_structure import standard_shot,standard_shot_qcmos
from structure.points_structure import decode_route

from modules.light_554_awg import Light554 as Light554AWG
from modules.light_554_dds import Light554 as Light554DDS
from modules.pmt import PMT as PMTBase
from modules.qcmos import *
from modules.config import LOADED_PARAMETER
from artiq.experiment import *
import xmlrpc.client
import time
from modules.signal_mw import SignalMW
from modules.operation_manager import Circuit
from modules.circuit_to_waveform import ShiftedCircuit
from copy import deepcopy
from modules.RPC_Client.servo_motor import MotorServer
from modules.RPC_Client.rf_ctrl import  RFCtrl
from ionctrl_pkg.utils.log import  get_logger
logger = get_logger(__name__)

class PMT(PMTBase):
    """用于自定义数据处理逻辑"""

    pass

class QCMOSForHistogram(QCMOS):
    @rpc(flags={})
    def connect_qcmos(self, bundle_mode=1):
        if dcamcon_init():
            self.dcamcon = dcamcon_choose_and_open()
            if self.dcamcon is not None:
                # 设置相机参数
                res = setup_properties(
                    self.dcamcon,
                    x_pos=self.init_roi[0],
                    y_pos=self.init_roi[1],
                    width=self.init_roi[2],
                    height=self.init_roi[3],
                    tirg_mode=0,
                    bundle_mode=bundle_mode,
                    bundle_number=self.parameter.QCMOS.bundle_num * 2,
                )
                if res is False:
                    logger.info("Parameter Configure False")
                else:
                    logger.info("Parameter Configure Success")
            else:
                logger.info("Device Open Failure")
        else:
            dcamcon_uninit()
            logger.info("DCAM Initial Failure")

class RamanBaseExp(HasEnvironment):

    def build(self):
        self.parameter = LOADED_PARAMETER()
        self.ion_num = self.parameter.Experiment.ion_num
        # 2. 通道连接
        self.setattr_device("core")
        self.setattr_device("scheduler")
        self.setattr_argument("repeat", NumberValue(default=self.parameter.Experiment.Repeat, min=1, step=1, precision=0),
                              tooltip="执行次数", group="Experiment")
        if self.repeat is not None:
            self.parameter.Experiment.Repeat = self.repeat
        self.setattr_argument(
            "AWG_Mode",
            EnumerationValue(["AWG", "DDS"], default="AWG"),
            tooltip="选择 DDS 模式 or AWG 模式",
            group="Experiment",
        )

        self.setattr_argument(
            "ion_choice", StringValue(f"{tuple(range(self.ion_num))}"), tooltip="选择展示的离子", group="Experiment"
        )

        self.setattr_argument(
            "task_num",
            NumberValue(default=1, min=1, step=1, precision=0),
            tooltip="扫描任务的重复次数",
            group="Experiment",
        )

        self.setattr_argument(
            "enable_SBC", BooleanValue(default=False),
            tooltip="是否使用边带冷却",
            group="Experiment")

        self.setattr_argument(
            "loop", BooleanValue(default=False),
            tooltip="是否循环执行",
            group="Experiment"
        )

        self.setattr_argument(
            "Adaptive", BooleanValue(default=True),
            tooltip="是否自适应步长",
            group="Experiment"
        )

        self.setattr_argument(
            "is_circuit", BooleanValue(default=False),
            tooltip="是否自适应步长",
            group="Experiment"
        )

        self.setattr_argument(
            "route", StringValue("[]"), tooltip="", group="Experiment"
        )

        self.setattr_argument(
            "points_info",StringValue("[]"), tooltip="", group="Experiment"
        )

        self.setattr_argument(
            "shot_structure", StringValue("standard"), tooltip="单个Shot结构", group="Experiment"
        )

        self.setattr_argument(
            "adaptive_condition",StringValue("False"),tooltip="自适应条件",group="Experiment"
        )
        
        if self.AWG_Mode is not None:
            if self.AWG_Mode == "AWG":
                self.l554 = Light554AWG(self)
            elif self.AWG_Mode == "DDS":
                self.l554 = Light554DDS(self)
            else:
                raise "Unknown AWG_Mode"

        self.l369 = Light369(self)
        self.pmt = PMT(self)
        self.qcmos = QCMOSForHistogram(self)
        self.wm = SignalMW(self)


    def prepare(self):
        # 1. 参数获取

        self.pre_cooling_time = self.parameter.Experiment.Pre_Cooling_Time
        self.cooling_time = self.parameter.Experiment.Cooling_Time
        self.cooling2_time = self.parameter.Experiment.Cooling2_Time
        self.pumping_time = self.parameter.Experiment.Pumping_Time
        self.detecting_time = self.parameter.Experiment.Detecting_Time
        # self.repeat = self.parameter.Experiment.Repeat
        self.bundle_num = self.parameter.QCMOS.bundle_num
        if self.repeat % self.bundle_num != 0:
            raise ValueError(
                f"QCMOS Repeat value error {self.repeat}, must be a multiple of 100"
            )
        self.re_repeat = int(self.repeat / self.bundle_num)
        self.idle_time = self.parameter.Light_554.AOD_time_before_AOM
        self.eit_time = self.parameter.Experiment.EIT_Cooling_Time
        self.eit2_time = self.parameter.Experiment.EIT_Cooling2_Time
        self.pi_2time = self.parameter.Signal_MW.mw_pi_2.zero

        self.motor = MotorServer(sever_ip=self.parameter.Motor.ip, ionization_channel=self.parameter.Motor.ionization_channel)
        self.point_now = 0  # 标记一维扫描时扫到哪个点了

        # 用于记录连续救离子次数
        self.save_ions_time = 0
        self.max_save_time = 100

        #如果勾选 loop，则会循环 1 万次
        if self.loop:
            self.task_num = 10000

        self.rf_ctrl = RFCtrl(ip=self.parameter.RFCtrl.ip)


        self.route_now = self.route
        # logger.info(self.route)
        self.points_list = decode_route(self.route)
        # logger.info(self.points_list)

        self.points_param_list = list(self.points_info[0]["points"])
        self.points_param_list_now = list(self.points_info[0]["points"])
        def select_axis_unit(value):
            logger.info(f"value: {value}")
            if value == 0:
                return 1
            abs_value = abs(value)
            log10 = np.log10(abs_value)
            if log10 <= -2:
                return 1e-6
            elif -2 < log10 < 3:
                return 1
            else:
                return 1e6
        self.system_name = self.parameter.Experiment.system_name
        self.default_unit =self.points_info[0].get("unit",select_axis_unit(max(abs(self.points_info[0]["points"]))))
        self.points_info_now = self.points_info
        self.adaptive_condition_now = self.adaptive_condition
        self.is_calibrating = False
        self.point_now_temp = 0
        self.iteration_layer=0 # 迭代层级, 用于向后追溯时标记当前是哪一层级
        self.ion_choice = eval(self.ion_choice)
        logger.info(f"ion_choice {self.ion_choice}")
        logger.info("prepare_done")


    @rpc(flags={""})
    def set_rid(self):
        # 读取当前实验rid
        rid = int(list(self.scheduler.get_status().keys())[0])
        self.set_dataset('rid',rid,broadcast=True)

    def analyse(self):
        pass

    @rpc(flags={"async"})
    def tprint(self, data):
        """用于 artiq 环境中的异步数据打印"""
        print(data)

    @rpc(flags={""})
    def prepare_dataset(self):
        self.x_unit = self.default_unit
        if self.parameter.Experiment.system_name == "M2":
            self.qcmos.prepare_for_x_scan(np.array(self.points_param_list)/self.x_unit)
        else:
            self.pmt.prepare_for_x_scan(np.array(self.points_param_list)/self.x_unit)

    @rpc(flags={""})
    def wave_compute(self, point_index) -> TFloat:
        circuit_list = []
        for op in self.points_list[int(point_index)]:
            circuit_list.append([op.convert_to_operation()])
        # logger.info("11")
        # print(circuit_list)
        if self.is_circuit:
            circuit = ShiftedCircuit(circuit_list)
        else:
            circuit = Circuit(circuit_list)
        # logger.info("22")

        self.l554.prepare_waveform(circuit)
        return circuit.duration()

    @rpc(flags={""})
    def data_process(self, scan_point):
        """数据处理的逻辑"""
        if self.parameter.Experiment.system_name == "M2":
            self.qcmos.process_for_x_scan_re_repeat_check_ion_lost(scan_point, self.ion_choice)

        else:
            self.pmt.data_process_for_x_scan_check_ion(scan_point, self.ion_choice)

    @rpc(flags={""})
    def save_ions(self):
        """救离子的逻辑"""
        # 目前只加入开离子化光救离子的代码
        # self.motor.open_ionization(5)
        # logger.warning(f"ion lost at {time.time()}")
        # rid = int(list(self.scheduler.get_status().keys())[0])
        # logger.warning(f"rid {rid}")
        # with open("lost_time", "a") as file:
        #     file.write(f"{time.time()} - {rid}\n")
        #
        # all_ion_lost = self.get_dataset("all_ion_lost")
        # one_ion_dark = self.get_dataset("one_ion_dark")
        # if all_ion_lost == 1:
        #     # 如果全暗，认为是雾化，启动救离子
        #     self.motor.open_channel(self.motor.ionization_channel)
        #     time.sleep(3)
        #     self.proxy_RF.shake()
        #     time.sleep(2)
        #     self.motor.close_channel(self.motor.ionization_channel)
        #     time.sleep(10)
        #     self.set_dataset("all_ion_lost", 0, broadcast=True)
        # else:
        #     # 否则，暂停实验5秒等760发力
        #     time.sleep(5)
        # # else:
        # #
        # #     self.schedule.request_termination(self.get_dataset("rid"))
        #
        # self.set_dataset("ion_lost", 0, broadcast=True)
        self.motor.open_channel(self.motor.ionization_channel)
        self.rf_ctrl.shake(self.parameter.RFCtrl.minus, self.parameter.RFCtrl.delay_time)
        time.sleep(5)
        self.motor.close_channel(self.motor.ionization_channel)
        time.sleep(2)
        # self.set_dataset("all_ion_lost", 0, broadcast=True)
        self.set_dataset("ion_lost", 0, broadcast=True)

    @rpc(flags={})
    def check_lost(self) -> TBool:
        """检查离子是否丢失"""
        lost = self.get_dataset("ion_lost")
        if lost == 0 :
            self.save_ions_time = 0
        if lost == 1:
            self.save_ions_time += 1
            logger.warning(f"Ion_State: {lost}, Ion lost time: {self.save_ions_time}")
        if self.save_ions_time > self.max_save_time:
            self.scheduler.request_termination(self.get_dataset("rid"))
            logger.info("save ion time reach maximum")
        return lost == 1

    @kernel()
    def run_level_1(self, operation_time):
        """单次 repeat 中的时序"""
        # if self.shot_structure == "standard":
        #     if self.system_name == "M2":
        #         shot = standard_shot_qcmos
        #     else:
        #       shot = standard_shot
        # else:
        #     shot = standard_shot
        #
        # self.core.break_realtime()
        for re_repeat_num in range(self.re_repeat):
            for _ in range(self.bundle_num):
                self.qcmos.qcmos_start_count()
                delay(self.pre_cooling_time)
                self.l369.switch_pre_cooling_to_cooling()
                delay(self.cooling_time)
                self.qcmos.qcmos_stop_count()
                # self.l369.switch_cool_to_pump()
                self.l369.switch_cool_to_eit()
                delay(self.eit_time)

                self.l369.switch_eit_to_pump()
                delay(self.pumping_time)

                with parallel:
                    self.l369.switch_pump_to_control()
                    self.l554.AWG_on()

                with parallel:
                    self.l369.sbc_cooling(self.enable_SBC)
                    delay(operation_time)

                with parallel:
                    self.l554.AWG_off()
                    self.qcmos.qcmos_start_count()
                    self.l369.switch_control_to_detect()

                delay(self.detecting_time)

                self.qcmos.qcmos_stop_count()

                delay(1 * ms)

                self.l369.switch_detect_to_pre_cooling()
            delay(3 * ms)
            self.core.wait_until_mu(now_mu())
            # self.qcmos.process_for_single_bundle(re_repeat_num)
            self.qcmos.process_for_single_bundle_double(re_repeat_num)

            self.core.break_realtime()

    @kernel()
    def run_level_2(self, point_index):
        """加入一个扫描点的波形计算和数据处理"""
        while True:
            # 1. 构造 AWG 波形, 计算操作时间
            operation_time = self.wave_compute(point_index)

            self.core.break_realtime()

            # 2. 跑 repeat 次实验
            self.run_level_1(operation_time)

            self.core.wait_until_mu(now_mu())

            # 3. 数据处理
            self.data_process(self.point_now)

            # 4. 离子状态检查
            # delay(10*s)
            if self.check_lost():
                self.save_ions()  # 封装救离子的逻辑
            else:
                self.point_now += 1
                self.set_dataset("scan_point", self.point_now, broadcast=True)
                break
            # 添加优雅中止
            if self.scheduler.check_termination():
                break

    @kernel()
    def run_level_3(self, scan_list):
        """N 个扫描点"""

        self.scan_point = 0
        while self.scan_point < len(scan_list):
            self.run_level_2(scan_list[self.scan_point])
            # 添加优雅中止

            scan_list = self.update_scan_list(scan_list)
            if self.scheduler.check_termination():
                break

    @rpc(flags={""})
    def update_scan_list(self,scan_list)->TList(TFloat):
        # logger.info(f"update_scan_list {scan_list}")
        new_step = self.get_new_step()
        scan_point = self.get_dataset("scan_point")
        if self.add_point_to_list():
            try:
                if scan_list[scan_point]-scan_list[scan_point-1]>new_step*1.1:
                    new_point = deepcopy(self.route_now[scan_point-1])
                    for op in new_point:
                        if "samplingdense" in op["args"]:
                            parameter_name = op["args"]["samplingdense"]
                            op["args"][parameter_name] = scan_list[scan_point-1]+new_step
                    self.route_now.insert(scan_point,new_point)

                    new_point_op = decode_route([new_point])[0]
                    self.points_list.insert(scan_point,new_point_op)

                    scan_list.insert(scan_point, scan_list[scan_point-1]+new_step)

                    self.update_x(scan_list)
            except Exception as e:
                # logger.error("____________________")
                logger.error(e)
        else:
            # logger.info(f"not add_point_to_list")
            pass

        return scan_list
    def get_new_step(self):
        if len(self.points_param_list_now)>1:
            return (self.points_param_list_now[1]-self.points_param_list_now[0])/4
        else:
            return 0.0

    def update_x(self,scan_list):
        x_points = (np.array(scan_list)/self.x_unit)
        self.set_dataset("x_points", x_points, broadcast=True)

    def add_point_to_list(self):
        if not self.Adaptive:
            return False
        probability = self.get_dataset("y_probability")[-1]
        need_add = eval(self.adaptive_condition_now)
        return need_add
    @kernel()
    def run_task_num(self, task_num, scan_list):
        """多次任务"""
        for _ in range(task_num):
            self.run_level_3(scan_list)
            if self.scheduler.check_termination():
                break

    @kernel()
    def exp_initial(self):
        """exp 初始化"""
        self.tprint("exp_initial")
        self.set_rid()
        self.prepare_dataset()
        self.core.break_realtime()
        self.core.reset()
        if self.parameter.Experiment.system_name == "M2":
            self.qcmos.initial()
        else:
            self.pmt.initial()
        self.l554.initial()
        self.l369.initial()
        self.core.break_realtime()

    @kernel
    def run(self):
        self.tprint("start_run")
        self.core.break_realtime()
        self.exp_initial()
        self.run_task_num(self.task_num, self.points_param_list)
        