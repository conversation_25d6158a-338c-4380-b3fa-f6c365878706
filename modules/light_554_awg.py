import time
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
import numpy as np
from artiq.experiment import *
from modules.config import Config
from modules.AWG_package import Spectrum_AWG_4_Channels as AWG


class Light554(HasEnvironment):

    def build(self):
        # 1. set AWG trigger
        self.devices = ["AWG_trigger", "core"]
        for item in self.devices:
            self.setattr_device(item)

        # 2. parameter
        self.parameter = Config()  # 从配置文件获取参数
        self.Voltage_AOM = self.parameter.Light_554.AOM_AWG_full_Voltage
        self.Voltage_AOD = self.parameter.Light_554.AOD_AWG_full_Voltage
        self.wave_data_for_AOM_left = None
        self.wave_data_for_AOM_right = None
        self.wave_data_for_AOD_left = None
        self.wave_data_for_AOD_right = None

        # 3. empty wave
        self.wave_AOM = ()  #
        self.Vs_AOM = ()  #

        self.wave_AOD = ()  # shape = [1, 2],  AOD wave
        self.Vs_AOD = ()  # shape = [1, 2],  AOD voltage

        # 4.
        self.operation_time = None

        self.awg_device = None

    @rpc(flags={})
    def connect_awg(self):
        """connect to AWG"""

        print("connect_awg")
        self.awg_device = AWG.AWG_2_Cards(self.parameter.Light_554.AWG_address)
        print("connected_awg")
        print(self.awg_device)

    @kernel
    def initial(self):
        """554 initial"""
        self.core.break_realtime()
        self.AWG_trigger.output()
        delay(10e-9)
        self.AWG_trigger.off()
        delay(10e-9)
        self.core.wait_until_mu(now_mu())
        self.connect_awg()
        print("awg_device_address", self.awg_device)
        self.core.break_realtime()

    @rpc(flags={})
    def start_awg(self):
        """init AWG"""
        self.awg_device.init_clock()
        self.awg_device.start()

    @rpc(flags={})
    def close_awg(self):
        """close AWG"""
        self.awg_device.close()

    @kernel
    def AWG_on(self):
        """trigger awg"""
        self.AWG_trigger.on()

    @kernel
    def AWG_off(self):
        """trigger awg off"""
        self.AWG_trigger.off()

    @kernel()
    def AOD_on(self):
        """aod信号源切换到awg"""
        # 将ttl通道打开，使得awg信号通过，同时关闭554dds
        self.AODL.on()
        self.AODR_on()
        self.dds_554_AODL.sw.off()
        self.dds_554_AODR.sw.off()

        delay(50 * ns)

    @kernel()
    def AOD_off(self):
        """aod信号源切换到dds"""
        # 将ttl通道关闭，同时打开554dds
        self.AODL.off()
        self.AODR.off()
        self.dds_554_AODR.sw.on()
        self.dds_554_AODL.sw.on()

        delay(50 * ns)

    @kernel()
    def AOM_TTL_on(self):
        """AOM射频开关切换到awg"""
        self.AOM_R1.on()
        self.AOM_R2.on()
        self.AOM_L1.on()
        self.AOM_L2.on()
        delay(50 * ns)

    @kernel()
    def AOM_TTL_off(self):
        """AOM射频开关切换到DDS"""
        self.AOM_R1.off()
        self.AOM_R2.on()
        self.AOM_L1.off()
        self.AOM_L2.on()
        delay(50 * ns)

    @kernel
    def AOM_dds_on(self):
        self.dds_554_AOML.sw.on()
        self.dds_554_AOMR.sw.on()

    @kernel
    def AOM_dds_off(self):
        self.dds_554_AOML.sw.off()
        self.dds_554_AOMR.sw.off()



    @rpc(flags={})
    def prepare_waveform(self, operation):
        """transfer Waveform Objects to awg wave data"""
        # print("set_awg_waves")
        if operation.AOM_L.duration() < 1e-3:
            start_time = time.time()
            self.wave_data_for_AOM_left = operation.AOM_L.generate_waveform(
                base_fre=self.parameter.Light_554.AOM_middle_freq
                + self.parameter.Light_554.AOM_freq_LR,
                sampling_rate=1e9,
            )
            self.wave_data_for_AOM_right = operation.AOM_R.generate_waveform(
                base_fre=self.parameter.Light_554.AOM_middle_freq
                - self.parameter.Light_554.AOM_freq_LR,
                sampling_rate=1e9,
            )
            self.wave_data_for_AOD_left = operation.AOD_L.generate_waveform(
                base_fre=self.parameter.Light_554.AOD_middle_freq, sampling_rate=1e9
            )
            self.wave_data_for_AOD_right = operation.AOD_R.generate_waveform(
                base_fre=self.parameter.Light_554.AOD_middle_freq, sampling_rate=1e9
            )
        else:

            """使用多线程并行生成AWG波形数据"""
            # print("set_awg_waves")
            start_time = time.time()

            # 准备任务参数列表 (设备对象, 基础频率)
            tasks = [
                (operation.AOM_L, self.parameter.Light_554.AOM_middle_freq + self.parameter.Light_554.AOM_freq_LR),
                (operation.AOM_R, self.parameter.Light_554.AOM_middle_freq - self.parameter.Light_554.AOM_freq_LR),
                (operation.AOD_L, self.parameter.Light_554.AOD_middle_freq),
                (operation.AOD_R, self.parameter.Light_554.AOD_middle_freq)
            ]

            # 存储结果的字典 {设备名: 波形数据}
            results = {}

            try:
                with ThreadPoolExecutor(max_workers=4) as executor:
                    # 提交所有任务到线程池
                    future_to_device = {
                        executor.submit(
                            device.generate_waveform,
                            base_fre=fre,
                            sampling_rate=1e9
                        ): device_name
                        for device_name, (device, fre) in zip(
                            ['AOM_L', 'AOM_R', 'AOD_L', 'AOD_R'],
                            tasks
                        )
                    }

                    # 获取完成的任务结果
                    for future in as_completed(future_to_device):
                        device_name = future_to_device[future]
                        try:
                            results[device_name] = future.result()
                        except Exception as e:
                            print(f"Error generating waveform for {device_name}: {str(e)}")
                            raise

                # 将结果赋值给成员变量
                self.wave_data_for_AOM_left = results['AOM_L']
                self.wave_data_for_AOM_right = results['AOM_R']
                self.wave_data_for_AOD_left = results['AOD_L']
                self.wave_data_for_AOD_right = results['AOD_R']

            except Exception as e:
                print(f"Waveform generation failed: {str(e)}")
                # 这里可以添加失败恢复逻辑
                raise

        elapsed_time = time.time() - start_time
        # print(f"Calculate waveform time: {elapsed_time:.2f}s")
        # print("set_awg_waves_done")
        # start = time.time()
        self.load_wave_to_awg()
        # stop = time.time()
        # print("waveform writing time:", stop-start)
        # time.sleep(0.01)
        self.start_awg()
        # time.sleep(0.1)

    @rpc(flags={})
    def prepare_waveform_without_load(self, operation):
        """transfer Waveform Objects to awg wave data"""
        # print("set_awg_waves")
        # print(AOM_Left.waveform_list)

        self.wave_data_for_AOM_left = operation.AOM_L.generate_waveform(
            base_fre=self.parameter.Light_554.AOM_middle_freq
                     + self.parameter.Light_554.AOM_freq_LR,
            sampling_rate=1e9,
        )
        self.wave_data_for_AOM_right = operation.AOM_R.generate_waveform(
            base_fre=self.parameter.Light_554.AOM_middle_freq
                     - self.parameter.Light_554.AOM_freq_LR,
            sampling_rate=1e9,
        )
        self.wave_data_for_AOD_left = operation.AOD_L.generate_waveform(
            base_fre=self.parameter.Light_554.AOD_middle_freq, sampling_rate=1e9
        )
        self.wave_data_for_AOD_right = operation.AOD_R.generate_waveform(
            base_fre=self.parameter.Light_554.AOD_middle_freq, sampling_rate=1e9
        )
        # print("set_awg_waves_done")

    @rpc(flags={})
    def load_wave_to_awg(self, loops=None, replay_mode="singlerestart"):
        """Load wave data to AWG"""
        # print("load_waves")
        # print(self.awg_device)
        Vs_AOM = (self.Voltage_AOM, self.Voltage_AOM)
        self.set_AOMs(
            (self.wave_data_for_AOM_left, self.wave_data_for_AOM_right),
            Vs_AOM,
            loops=loops,
            replay_mode=replay_mode,
        )
        Vs_AOD = (self.Voltage_AOD, self.Voltage_AOD)
        self.set_AODs(
            (self.wave_data_for_AOD_left, self.wave_data_for_AOD_right),
            Vs_AOD,
            loops=loops,
            replay_mode=replay_mode,
        )
        # print(self.awg_device.Card0.checkError())
        # print(self.awg_device.Card1.checkError())

    @rpc(flags={})
    def set_AODs(self, data, full_Vs, loops=None, replay_mode="singlerestart"):
        """set AOD wave
        data    : data for two awg channel, in (array, array) form, each array for a AOD respecively.
        full_Vs : full_voltages for two awg channel, in (V, V) form, each V for a channel.
        """
        # print("set_AOD")
        if loops is None:
            loops = self.parameter.Experiment.Repeat

        self.awg_device.Card1.setChannels(
            data, full_Vs, loops=loops, repalymode=replay_mode
        )

    @rpc(flags={})
    def set_AOMs(self, data, full_Vs, loops=None, replay_mode="singlerestart"):
        """set AOM wave
        data    : data for two awg channel, in (array, array) form, each array for a AOM respecively.
        full_Vs : full_voltages for two awg channel, in (V, V) form, each V for a channel.
        """
        # print("set_AOM")
        # print(self.awg_device)
        if loops is None:
            loops = self.parameter.Experiment.Repeat

        self.awg_device.Card0.setChannels(
            data, full_Vs, loops=loops, repalymode=replay_mode
        )
