import  qiskit
from qiskit import QuantumCircuit
from qiskit.compiler import transpile
import numpy as np
def circuit2shot(qc:QuantumCircuit):
    qc_trans = transpile(qc,basis_gates=["rzz","rx","ry"])
    shot = []
    for instruction in qc_trans:
        if instruction.name == "rzz":
            q0 = qc.qubits.index(instruction.qubits[0])
            q1 = qc.qubits.index(instruction.qubits[1])
            qubit_index = (q0,q1)
            gate  = {
                "name": "Rzz",
                "args":{
                    "qubit_index": qubit_index
                }
                
            }
            shot.append(gate)
        # elif instruction.name == "r":
        #     q0 = qc.qubits.index(instruction.qubits[0])
        #     qubit_index = (q0,)
        #     theta = instruction.params[0]
        #     phi = instruction.params[1]
        
        #     gate={
        #         "name": "Rphi",
        #         "args":{
        #         "qubit_index": qubit_index,
        #         "theta": theta,
        #         "phi": phi
        #         }
        #     }
        #     shot.append(gate)
        if instruction.name in ["rx","ry"]:
            q0 = qc.qubits.index(instruction.qubits[0])
            qubit_index = (q0,)
            theta  = instruction.params[0]
            phi = 0 if instruction.name == "rx" else -np.pi/2
            if theta < 0:
                theta = -theta
                phi += np.pi
            gate={
                        "name": "Rphi",
                        "args":{
                        "qubit_index": qubit_index,
                        "theta": theta,
                        "phi": phi
                        }
                    }
            shot.append(gate)
    return shot

if __name__ == '__main__':
    qc = QuantumCircuit(2,2)
    qc.h(0)
    qc.cx(0,1)

    print(circuit2shot(qc))