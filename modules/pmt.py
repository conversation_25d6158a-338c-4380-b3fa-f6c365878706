import time

import numpy as np
from artiq.experiment import *
from modules.config import LOADED_PARAMETER, Config
from modules.PMT_package_new.pmt_controller import <PERSON><PERSON>ontroller
from modules.pmt_base import PMT_Base
import csv
import os
import pandas as pd

current_file_path = os.path.abspath(__file__)


class PMT(PMT_Base):
    def build(self):

        # 1. build channels
        self.devices = [
            "ccb",
            "PMT_window",
            "core"
        ]

        for item in self.devices:
            self.setattr_device(item)

        # 2. get parameters
        self.parameter = Config()
        # self.add_parameters_to_datasets()
        self.repeat = self.parameter.Experiment.Repeat
        self.ions_nums = len(self.parameter.PMT.Select_channels)

    def add_parameters_to_datasets(self):
        parameter_dict = self.parameter.to_dict()
        for category, settings in parameter_dict.items():
            for param, value in settings.items():
                # Handle nested dictionaries (e.g., 'mw_pi_2', 'rabi_time')
                if isinstance(value, dict):
                    for sub_param, sub_value in value.items():
                        dataset_name = f"{category}_{param}_{sub_param}"
                        self.set_dataset(dataset_name, sub_value, broadcast=True, archive=True)
                else:
                    dataset_name = f"{category}_{param}"
                    self.set_dataset(dataset_name, value, broadcast=True, archive=True)


    @rpc(flags={"async"})
    def close_pmt(self):
        self.pmt_device.close()
        # print(self.pmt_device.origin_dir)
        # os.chdir(self.pmt_device.origin_dir)

    @rpc(flags={})
    def connect_pmt(self):
        try:
            print("connect_pmt")
            self.pmt_device = PMTController(repeat=self.repeat, ad5721=2030, ltc2668=500)
            self.pmt_device.reset()
            time.sleep(0.03)
            print("connected_pmt")

        except Exception as e:
            print(f"Connect PMT failed: {e}")

    @rpc(flags={"async"})
    def read_data(self):
        """read PMT data， shape = (repeat,32)
        """
        # data = np.random.randint(0, 255, size=32)
        data = self.pmt_device.read_data()
        # data[:, [8, 9, 10, 11, 12, 13, 14, 15, 0, 1, 2, 3, 4, 5, 6, 7]] = data[:,
        #                                                                   [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
        #                                                                    13, 14, 15]]
        # data[]
        data[:,
        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29,
         30, 31]] \
            = data[:,
              [8, 24, 9, 25, 10, 26, 11, 27, 12, 28, 13, 29, 14, 30, 15, 31, 0, 16, 1, 17, 2, 18, 3, 19, 4, 20, 5, 21,
               6, 22, 7, 23]]

        # data[]
        # data[:,
        # [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29,
        #  30, 31]] \
        #     = data[:,
        #       [13, 14, 30, 15, 31, 0, 16, 1, 17, 2, 18, 3,  14, 30, 15, 31, 0, 16, 1, 17, 2, 18, 3, 19, 4, 20, 5, 21,
        #        6, 22, 7, 23]]


        # print(data)
        # print(data[:,5])
        # data[:, 16:33] = 0
        return data


    @kernel()
    def pmt_start_count(self):
        self.PMT_window.on()

    @kernel()
    def pmt_stop_count(self):
        self.PMT_window.off()

    @kernel()
    def initial(self):

        self.core.wait_until_mu(now_mu())
        self.connect_pmt()
        self.core.break_realtime()

        self.PMT_window.output()
        self.PMT_window.off()
        # delay(1e-6)
        # self.PMT_window.on()
        # delay(1e-6)
        # self.PMT_window.off()
        delay(1 * us)



    