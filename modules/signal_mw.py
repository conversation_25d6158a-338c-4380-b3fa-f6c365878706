"""
554 光路模块
"""
from artiq.experiment import *
from modules.config import *


class SignalMW(HasEnvironment):
    """微波光路模块

    微波光路依赖一个 DDS 通道和一个 TTL 通道

    类目标:
    1. 封装微波信号线路的光弄功能模块;
    2. 提供方便的接口给实验层调用;

    功能一览:
    1. __init__(self): 构造函数。封装了本光路用到的所有 artiq 通道, 在实例化时直接建立连接;
    2. initial(): 通道初始化函数(目前可用于 example_1_light_check, 待确认 EOM TTL 信号编码形式后, 将初始化到预备 cooling 状态;
    3. task_changer():


    """

    def build(self):

        # 1. 添加通道连接
        self.devices = [
            "dds_for_mw"
        ]

        for item in self.devices:
            self.setattr_device(item)

        # 2. 参数提取
        self.parameter = Config()
        self.mw_fre = self.parameter.Signal_MW.dds_for_mw_fre
        self.frequency = self.mw_fre
        self.mw_amp = self.parameter.Signal_MW.dds_for_mw_amp
        self.mw_phase = self.parameter.Signal_MW.dds_for_mw_phase
        self.mw_atte = self.parameter.Signal_MW.dds_for_mw_atte
        self.zeeman_p = self.parameter.Signal_MW.zeeman_p
        self.zeeman_n = self.parameter.Signal_MW.zeeman_n

    @rpc(flags={"async"})
    def tprint(self, data):
        print(data)

    @kernel()
    def initial(self):
        """微波模块初始化"""
        self.dds_for_mw.cpld.init()
        self.dds_for_mw.init()
        delay(1 * us)

        self.dds_for_mw.set_att(self.mw_atte)
        self.dds_for_mw.set(self.mw_fre,
                            self.mw_phase,
                            self.mw_amp,
                            phase_mode=2)
        delay(1 * us)

        self.mw_off()

        delay(1 * us)

    @kernel()
    def set_mw_parameter(self, detuning, amplitude=0.0, phase=0.0):
        """设置 MW DDS 参数
        用途： 微波扫频实验"""
        frequency = self.mw_fre + detuning  # 计算微波偏移
        self.frequency = frequency
        if amplitude == 0:
            amplitude = self.mw_amp
        if phase == 0:
            phase = self.mw_phase

        self.dds_for_mw.set(float(frequency),
                            float(phase),
                            float(amplitude)
                            )
        delay(10 * us)

    @kernel()
    def mw_on(self,phase = 0.0):
        """打开微波"""
        self.dds_for_mw.set(float(self.frequency),
                            self.mw_phase+phase,
                            self.mw_amp,
                            phase_mode=2)
        self.dds_for_mw.cfg_sw(True)
        # self.dds_for_mw.sw.on()

    @kernel()
    def mw_off(self):
        """关闭微波"""

        self.dds_for_mw.cfg_sw(False)
        # self.dds_for_mw.sw.off()
        self.dds_for_mw.set(0.0,
                            self.mw_phase,
                            self.mw_amp,
                            phase_mode=2)

