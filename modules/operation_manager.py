"""
operation 类 和 circuit 类的封装
1. operation 类将有四个属性(AOM 的两个通道的 Sequence)
2. operation 类相加和相乘都将生成新的 circuit 类

需要手动更改的, 所有类中的 qubit_index 的类型注释要改成 tuple
"""

import time
from copy import deepcopy
from pprint import pprint

import numpy as np
from modules.waveform_manager_ import *
from waveform_manager import *

from modules.config import LOADED_PARAMETER
try:
    import pyqtgraph as pg
except:
    pass
from PyQt5.QtWidgets import QApplication
import sys
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)

class OperationRegister:
    operation_set = {}  # 注册的子类的集合
    operation_cache = {}  # 实例缓存

    @classmethod
    def register_operation(cls, class_obj):
        """注册一个子类"""
        cls.operation_set[class_obj.__name__] = class_obj

    @classmethod
    def create_instance(cls, class_name, *args, **kwargs):
        # 创建一个基于参数的唯一键
        key = (class_name, args, tuple(sorted(kwargs.items())))

        # # 检查实例是否已经存在于缓存中
        # if key in cls.operation_cache:
        #     # print(f"Using cached instance for {class_name} with args {args} and kwargs {kwargs}")
        #     return cls.operation_cache[key]

        if class_name in cls.operation_set:
            # 创建新的实例
            instance = cls.operation_set[class_name](*args, **kwargs)
            # 存储到缓存中
            cls.operation_cache[key] = instance
            return instance
        else:
            raise ValueError(f"Class '{class_name}' not registered.")


class Operation:
    """
    量子操作的基类
    """

    def __init__(self, qubit_index: tuple):
        """初始化需要传入一个离子索引"""
        # 比特索引
        self.qubit_index = qubit_index
        # AOM 波形
        self.AOM_L = Waveform()
        self.AOM_R = Waveform()
        # AOD 波形
        self.AOD_L = Waveform()
        self.AOD_R = Waveform()
        self.duration_time = 0
        # self.parameters = Config(DEFAULT_PARAMETER)
        self.parameters = LOADED_PARAMETER()
        # 用于量子线路的参数
        self.circuit_list = [
            {"gate": self.__class__.__name__, "qubit_index": qubit_index}
        ]

    def duration(self):
        return self.AOM_L.duration()

    def __init_subclass__(cls, **kwargs):
        """注册子类"""
        super().__init_subclass__(**kwargs)
        OperationRegister.register_operation(cls)

    def compute_AOM_wave(self, algorithm=None):
        """
        构造 AOM 波形
        """
        pass

    # def compute_AOD_wave(self):
    #     """
    #     构造 AOD 波形
    #     """
    #     wave_temp = Waveform(duration=self.duration, amplitude=0, phase=0)
    #     if len(self.qubit_index) == 1:
    #         i = self.qubit_index[0]
    #         wave_temp += Waveform(
    #             duration=self.duration,
    #             amplitude=1,
    #             phase=0,
    #             detuning=self.parameters.Light_554.AOD_address_freqs[i],
    #         )

    #     elif len(self.qubit_index) == 2:
    #         a = self.qubit_index[0]
    #         b = self.qubit_index[1]
    #         ratio = self.parameters.Light_554.AOD_AWG_ratio[a][b]
    #         amp = [1 / (1 + ratio), ratio / (1 + ratio)]
    #         # 1. 生成一个空的波形对象(方便后面用别的波形加上去),生成一个
    #         wave_temp = Waveform(duration=self.duration, amplitude=0, phase=0)
    #         # 2. 遍历离子索引, 计算 AOD 波形
    #         for i, i_ion in enumerate(self.qubit_index):
    #             wave_temp += Waveform(
    #                 duration=self.duration,
    #                 amplitude=amp[i],
    #                 phase=0,
    #                 detuning=self.parameters.Light_554.AOD_address_freqs[i_ion],
    #             )
    #     else:
    #         raise ValueError(
    #             f"qubit_index {self.qubit_index} involves more than two qubits."
    #         )

    #     # 3. 赋值到属性
    #     self.AOD_L = wave_temp
    #     self.AOD_R = wave_temp

    def compute_AOD_wave(self):
        """
        构造 AOD 波形
        """
        if len(self.qubit_index) == 1:
            temp_wave = Waveform(
                duration=self.duration_time,
                amplitude=1,
                phase=0,
                detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]],
            )

        elif len(self.qubit_index) == 2:
            # 1. 提取该离子对对应的 AOD Ratio
            ratio = self.parameters.Light_554.AOD_AWG_ratio[self.qubit_index[0]][self.qubit_index[1]]
            amp = [ratio / (1 + ratio), 1 / (1 + ratio)]
            # 2. 构造两离子的波形
            ion_i_wave = Waveform(duration=self.duration_time,
                                  amplitude=amp[0],
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]])
            ion_j_wave = Waveform(duration=self.duration_time,
                                  amplitude=amp[1],
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[1]])
            temp_wave = ion_i_wave + ion_j_wave
        else:
            # raise ValueError(
            #     f"qubit_index {self.qubit_index} involves more than two qubits."
            # )
            n_qubit = len(self.qubit_index)
            amp = 1/n_qubit
            temp_wave = Waveform(duration=self.duration_time,
                                  amplitude=amp,
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]])
            for i in range(1,n_qubit):
                temp_wave += Waveform(duration=self.duration_time,
                                  amplitude=amp,
                                  phase=0,
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[i]])

        # 3. 赋值到属性
        self.AOD_L = temp_wave
        self.AOD_R = temp_wave

    def generate_waveform_for_spectrum_dds(self):
        """调整波形的时间线，以满足 dds 仪器的需求"""
        self.AOM_L = self.AOM_L.generate_waveform_for_spectrum_dds()
        self.AOM_R = self.AOM_R.generate_waveform_for_spectrum_dds()
        self.AOD_L = self.AOD_L.generate_waveform_for_spectrum_dds()
        self.AOD_R = self.AOD_R.generate_waveform_for_spectrum_dds()

    def plot_circuit_wave(self, AOM_base_fre, AOD_base_fre, sampling_rate):
        """
        1. 调用波形序列的波形生成函数来生成波形
        2. 调用 _plot_circuit_wave 来绘图
        """
        AOM_L_wave = self.AOM_L.generate_waveform(
            base_fre=AOM_base_fre, sampling_rate=sampling_rate
        )
        AOM_R_wave = self.AOM_R.generate_waveform(
            base_fre=AOM_base_fre, sampling_rate=sampling_rate
        )
        AOD_L_wave = self.AOD_L.generate_waveform(
            base_fre=AOD_base_fre, sampling_rate=sampling_rate
        )
        AOD_R_wave = self.AOD_R.generate_waveform(
            base_fre=AOD_base_fre, sampling_rate=sampling_rate
        )

        self._plot_circuit_wave(
            AOM_L_wave, AOM_R_wave, AOD_L_wave, AOD_R_wave, x_step=1 / sampling_rate
        )

    def plot_circuit_wave_for_dds(self, AOM_base_fre, AOD_base_fre, sampling_rate):
        """
        1. 调用波形序列的波形生成函数来生成波形
        2. 调用 _plot_circuit_wave 来绘图
        """
        AOM_L_wave = self.AOM_L.gen_wave_for_dds(
            base_fre=AOM_base_fre, sampling_rate=sampling_rate
        )
        AOM_R_wave = self.AOM_R.gen_wave_for_dds(
            base_fre=AOM_base_fre, sampling_rate=sampling_rate
        )
        AOD_L_wave = self.AOD_L.gen_wave_for_dds(
            base_fre=AOD_base_fre, sampling_rate=sampling_rate
        )
        AOD_R_wave = self.AOD_R.gen_wave_for_dds(
            base_fre=AOD_base_fre, sampling_rate=sampling_rate
        )

        self._plot_circuit_wave(
            AOM_L_wave, AOM_R_wave, AOD_L_wave, AOD_R_wave, x_step=1 / sampling_rate
        )

    def add_base_fre(
        self, AOM_L_base_fre, AOM_R_base_fre, AOD_L_base_fre, AOD_R_base_fre
    ):
        """添加中频"""
        self.AOM_L = self.AOM_L.add_base_fre(AOM_L_base_fre)
        self.AOM_R = self.AOM_R.add_base_fre(AOM_R_base_fre)
        self.AOD_L = self.AOD_L.add_base_fre(AOD_L_base_fre)
        self.AOD_R = self.AOD_R.add_base_fre(AOD_R_base_fre)

    @staticmethod
    def _plot_circuit_wave(
        AOM_L_wave=None, AOM_R_wave=None, AOD_L_wave=None, AOD_R_wave=None, x_step=1
    ):
        """
        circuit 波形绘制
        :param AOM_L_wave: AOM_L波形
        :param AOM_R_wave: AOM_R波形
        :param AOD_L_wave: AOD_L波形
        :param AOD_R_wave: AOD_R波形
        :param x_step: x轴步长

        """
        app = QApplication([])

        # 创建窗口/标题
        win = pg.GraphicsLayoutWidget(show=True)
        win.setWindowTitle("Wave Plot")

        # 存储所有的绘图组件
        plots = []
        names = ["AOM_L", "AOM_R", "AOD_L", "AOD_R"]
        # 遍历所有的y轴数据集
        for i, y_data in enumerate(
            [AOM_L_wave, AOM_R_wave, AOD_L_wave, AOD_R_wave], start=1
        ):
            if y_data is not None:
                # 添加绘图组件，上下排列
                plot = win.addPlot(row=i - 1, col=0)

                # 计算x轴数据
                x_data = np.arange(len(y_data)) * x_step

                # 绘制数据
                plot.plot(x_data, y_data)

                plot.setLabel("left", names[i - 1])

                # 将绘图组件添加到列表中
                plots.append(plot)

        # 绑定所有绘图组件的横坐标尺寸
        if plots:
            for plot in plots[1:]:
                plot.setXLink(plots[0])
                plot.setYLink(plots[0])

        # 显示窗口
        win.show()

        # 运行应用（如果这是你的主应用）
        # pg._exec()
        sys.exit(QApplication.instance().exec_())


class Circuit:
    """
    量子线路的管理类
    """

    def __init__(self, circuit_list=None):
        """目前只支持传入一个 circuit_list 来初始化
        同一时刻, 只支持执行一个量子操作
        """
        if circuit_list is None:
            circuit_list = []

        self.circuit_list = circuit_list
        self.AOM_L = Waveform()
        self.AOM_R = Waveform()
        self.AOD_L = Waveform()
        self.AOD_R = Waveform()
        # self.parameters = Config(DEFAULT_PARAMETER)
        self.parameters = LOADED_PARAMETER()
        # start = time.time()
        # logger.info("111")
        self.generate_sequence()
        # logger.info("211")

        # stop = time.time()
        # print("Generate waveform list time:",stop - start)


    def duration(self):
        """返回 waveform 对象的 wave_list"""
        return self.AOD_L.duration()

    def generate_waveform_for_spectrum_dds(self):
        """调整波形的时间线，以满足 dds 仪器的需求"""
        self.AOM_L = self.AOM_L.generate_waveform_for_spectrum_dds()
        self.AOM_R = self.AOM_R.generate_waveform_for_spectrum_dds()
        self.AOD_L = self.AOD_L.generate_waveform_for_spectrum_dds()
        self.AOD_R = self.AOD_R.generate_waveform_for_spectrum_dds()

    def add_base_fre(
        self, AOM_L_base_fre, AOM_R_base_fre, AOD_L_base_fre, AOD_R_base_fre
    ):
        """添加中频"""
        self.AOM_L = self.AOM_L.add_base_fre(AOM_L_base_fre)
        self.AOM_R = self.AOM_R.add_base_fre(AOM_R_base_fre)
        self.AOD_L = self.AOD_L.add_base_fre(AOD_L_base_fre)
        self.AOD_R = self.AOD_R.add_base_fre(AOD_R_base_fre)

    def update_sequence(self):
        """
        考虑 AOD 的上升沿时间，在前后操作的 index 不一样时，自动的插入 idle

        算法：滑窗
        """
        updated_circuit_list = []

        # 1. 在所有的操作之前插入一个 idle
        if self.circuit_list:
            first_layer = self.circuit_list[0]
            first_qubits = tuple(
                sorted(sum((op["qubit_index"] for op in first_layer), start=()))
            )  # 获取第一个操作的所有量子比特
            idle_operation = {"gate": "Idle", "qubit_index": first_qubits}
            updated_circuit_list.append([idle_operation])

        for i in range(len(self.circuit_list) - 1):
            # 获取滑窗中的两个值
            current_layer = self.circuit_list[i]
            next_layer = self.circuit_list[i + 1]

            # 将当前层添加进入新的电路列表
            updated_circuit_list.append(current_layer)

            # 提取并展平当前层和下一层所有操作的 qubit_index
            current_qubits = tuple(
                sorted(sum((op["qubit_index"] for op in current_layer), ()))
            )
            next_qubits = tuple(
                sorted(sum((op["qubit_index"] for op in next_layer), ()))
            )

            # 如果两个层的 qubit_index 不同，插入 Idle 操作
            if current_qubits != next_qubits:
                # 生成一个对应的 Idle 操作
                idle_operation = {"gate": "Idle", "qubit_index": next_qubits}

                # 将 Idle 操作插入到新的电路列表中
                updated_circuit_list.append([idle_operation])

        # 将最后一层的操作添加到新的电路列表中
        updated_circuit_list.append(self.circuit_list[-1])
        # 更新后的 circuit_list
        self.circuit_list = updated_circuit_list

    def generate_sequence(self):
        """如果传入了一个 circuit_list，则调用该方法生成 sequence"""
        # 1. 遍历第一层嵌套结构：layer
        # print("update_sequence")
        # logger.info("update_sequence")
        self.update_sequence()
        # logger.info("update_sequence_1")

        # print(self.circuit_list)
        for layer in self.circuit_list:
            # 2. 遍历第二层嵌套结构：operation, 这个operation 已经是个字典了

            for operation in layer:
                # 1. 判断这个操作是否已经被注册了,
                # 2. 判断这个操作的实例是否缓存过了
                # 获取实例:
                # logger.info("update_sequence_2")
                operation_temp = OperationRegister.create_instance(
                    operation["gate"],
                    **{key: value for key, value in operation.items() if key != "gate"},
                )
                # logger.info("update_sequence_3")

                # 获取将该实例的四个字符传加到本操作的后面
                # self.AOM_L.waveform_list += operation_temp.AOM_L.waveform_list
                # self.AOM_R.waveform_list += operation_temp.AOM_R.waveform_list
                # self.AOD_L.waveform_list += operation_temp.AOD_L.waveform_list
                # self.AOD_R.waveform_list += operation_temp.AOD_R.waveform_list

                self.AOM_L *= operation_temp.AOM_L
                self.AOM_R *= operation_temp.AOM_R
                self.AOD_L *= operation_temp.AOD_L
                self.AOD_R *= operation_temp.AOD_R

        # self.AOM_L.waveform_list.pop(0)
        # self.AOM_R.waveform_list.pop(0)
        # self.AOD_L.waveform_list.pop(0)
        # self.AOD_R.waveform_list.pop(0)
        # print("11")
        # print(self.AOM_L.waveform_list)
        # print(self.AOM_R.waveform_list)
        # print(self.AOD_L.waveform_list)
        # print(self.AOD_R.waveform_list)

        self.AOM_L.waveform_list = np.delete(self.AOM_L.waveform_list, 0, axis=0)
        self.AOM_R.waveform_list = np.delete(self.AOM_R.waveform_list, 0, axis=0)
        self.AOD_L.waveform_list = np.delete(self.AOD_L.waveform_list, 0, axis=0)
        self.AOD_R.waveform_list = np.delete(self.AOD_R.waveform_list, 0, axis=0)
        # print("22")

    def plot_circuit_wave(self, AOM_base_fre, AOD_base_fre, sampling_rate):
        """
        1. 调用波形序列的波形生成函数来生成波形
        2. 调用 _plot_circuit_wave 来绘图
        """
        AOM_L_wave = self.AOM_L.generate_waveform(
            base_fre=AOM_base_fre, sampling_rate=sampling_rate
        )
        AOM_R_wave = self.AOM_R.generate_waveform(
            base_fre=AOM_base_fre, sampling_rate=sampling_rate
        )
        AOD_L_wave = self.AOD_L.generate_waveform(
            base_fre=AOD_base_fre, sampling_rate=sampling_rate
        )
        AOD_R_wave = self.AOD_R.generate_waveform(
            base_fre=AOD_base_fre, sampling_rate=sampling_rate
        )

        self._plot_circuit_wave(
            AOM_L_wave, AOM_R_wave, AOD_L_wave, AOD_R_wave, x_step=1 / sampling_rate
        )

    @staticmethod
    def _plot_circuit_wave(
        AOM_L_wave=None, AOM_R_wave=None, AOD_L_wave=None, AOD_R_wave=None, x_step=1
    ):
        """
        circuit 波形绘制
        :param AOM_L_wave: AOM_L波形
        :param AOM_R_wave: AOM_R波形
        :param AOD_L_wave: AOD_L波形
        :param AOD_R_wave: AOD_R波形
        :param x_step: x 轴步长
        """
        app = QApplication([])

        # 创建窗口/标题
        win = pg.GraphicsLayoutWidget(show=True)
        win.setWindowTitle("Wave Plot")

        # 存储所有的绘图组件
        plots = []
        names = ["AOM_L", "AOM_R", "AOD_L", "AOD_R"]
        # 遍历所有的y轴数据集
        for i, y_data in enumerate(
            [AOM_L_wave, AOM_R_wave, AOD_L_wave, AOD_R_wave], start=1
        ):
            if y_data is not None:
                # 添加绘图组件，上下排列
                plot = win.addPlot(row=i - 1, col=0)

                # 计算x轴数据
                x_data = np.arange(len(y_data)) * x_step

                # 绘制数据
                plot.plot(x_data, y_data)

                plot.setLabel("left", names[i - 1])

                # 将绘图组件添加到列表中
                plots.append(plot)

        # 绑定所有绘图组件的横坐标尺寸
        if plots:
            for plot in plots[1:]:
                plot.setXLink(plots[0])
                plot.setYLink(plots[0])

        # 显示窗口
        win.show()

        # 运行应用（如果这是你的主应用）
        # pg._exec()
        sys.exit(QApplication.instance().exec_())


class Idle(Operation):
    """
    Idle operation
    """

    def __init__(self, qubit_index: tuple, idle_time: float = None):
        super().__init__(qubit_index)
        if idle_time is None:
            self.idle_time = self.parameters.Light_554.AOD_time_before_AOM
        else:
            self.idle_time = idle_time
        self.compute_AOM_wave()
        self.compute_AOD_wave()
        self.circuit_list[0]["idle_time"] = idle_time

    def compute_AOM_wave(self, algorithm=None):
        """
        Rabi_Carrier 的 AOM 波形
        """
        # 1. 创建 AOM 波形
        idle = Waveform(duration=self.idle_time, amplitude=0, phase=0, detuning=0)

        self.AOM_L = idle
        self.AOM_R = idle

        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()

class AOD_Scan(Operation):
    """
    AOD_scan 波形构造
    """
    def __init__(self,qubit_index,operation_time = None, frequency = None):
        super().__init__(qubit_index)
        self.operation_time = operation_time
        self.frequency = frequency
        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def compute_AOM_wave(self, algorithm=None):
        if self.operation_time is None:
            self.operation_time = self.parameters.Light_554.pi_2.Carrier[0]

        idle_time = self.parameters.Light_554.AOD_time_before_AOM
        aom_amp = self.parameters.Light_554.AOM_AWG_amp

        Idle_aod = Waveform(
            duration=idle_time,
            amplitude=0.0,
            phase= 0.0,
            detuning=0.0
        )

        # 2. 左边的载波波形
        Carrier = Waveform(
            duration=self.operation_time,
            amplitude=aom_amp,
            phase= 0 ,
            detuning= 0,
        )


        self.AOM_L = Idle_aod * Carrier
        self.AOM_R = Idle_aod * Carrier

        # 5. 计算持续时间
        self.duration_time = self.AOM_L.duration()
    def compute_AOD_wave(self):
        aod_mid_freq = self.parameters.Light_554.AOD_middle_freq
        if self.frequency is None:
            self.frequency = aod_mid_freq
        AOD_wave = Waveform(
            duration=self.duration_time,
            amplitude=1,
            phase=0,
            detuning=self.frequency-aod_mid_freq,
        )
        self.AOD_L = AOD_wave
        self.AOD_R = AOD_wave

class Raman_Rabi(Operation):
    """
    Raman_Rabi 波形构造

    1. 包括载波, 红, 蓝边带 rabi, 可以选择对哪个声子的边带进行操作;
    2. 可以选择指定离子进行操作;
    3. 可以选择左侧或者右侧或者两侧的 554 打开, 注意选择单侧 Rabi 时, 由于 AOM 存在关断比, 另一侧的光会有一点影响.
    """

    def __init__(self, qubit_index: tuple,
                 rabi_time=None,theta = None,
                 phase=0.0, detuning= 0.0,phonon_index=0,
                 phonon_frequency = None,
                 aom_amp = None,
                 rabi_choice="Carrier",
                 side_choice="All",
                 eta = 0.1
                 ):

        super().__init__(qubit_index)

        if rabi_time is not None and theta is not None:
            theta = None
            print("The time and theta are specified at the same time")
        if rabi_time is None and theta is None:
            theta = np.pi/2

        self.rabi_time = rabi_time
        self.aom_amp = aom_amp
        self.phase = phase
        self.detuning = detuning
        self.theta = theta
        self.side_choice = side_choice
        self.rabi_choice = rabi_choice
        self.phonon_index = phonon_index
        self.phonon_frequency = phonon_frequency
        self.eta = eta

        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        """AOM 波形计算"""
        # 1. 选择单边还是双边
        if self.aom_amp is None:
            self.aom_amp = self.parameters.Light_554.AOM_AWG_amp
        amp_dict = {
            "Right": [0, self.aom_amp],
            "Left": [self.aom_amp, 0],
            "All": [
                self.aom_amp,
                self.aom_amp
            ],
        }
        amp_left, amp_right = amp_dict[self.side_choice]
        if self.phonon_frequency is None:
            self.phonon_frequency = self.parameters.Light_554.Motion_freq[self.phonon_index]

        detuning_dict = {
            "Carrier": self.detuning,
            "Red": self.phonon_frequency,
            "Blue": -self.phonon_frequency
        }

        ac_stark_shift = self.parameters.Light_554.q1_AC_Stark_shift

        if self.theta is not None:
            AOM_phase_delay = self.parameters.Light_554.AOM_phase_delay
            pi2_time = self.parameters.Light_554.pi_2.Carrier[self.qubit_index[0]]
            pi_2_times = {
                "Carrier": pi2_time,
                "Red": (pi2_time-AOM_phase_delay)/self.eta+AOM_phase_delay,
                "Blue": (pi2_time-AOM_phase_delay)/self.eta+AOM_phase_delay
            }


            self.rabi_time = (pi_2_times[self.rabi_choice]-AOM_phase_delay)/(np.pi/2)*self.theta+AOM_phase_delay

        # 2. 左边的载波波形
        Carrier_L = Waveform(
            duration=self.rabi_time,
            amplitude=amp_left,
            phase=self.phase,
            detuning= ac_stark_shift,
        )
        # 3. 构造右侧波形
        # 根据边带选择决定右侧失谐

        Carrier_R = Waveform(
            duration=self.rabi_time,
            amplitude=amp_right,
            phase=0,
            detuning=detuning_dict[self.rabi_choice],
        )
        # 4. 波形赋值到属性

        self.AOM_L = Carrier_L
        self.AOM_R = Carrier_R

        # 5. 计算持续时间
        self.duration_time = self.AOM_L.duration()

class SBC(Operation):
    """
    SBC 操作
    用于放在线路前进行边带冷却
    默认打在第一个离子上
    """

    def __init__(self, qubit_index: tuple):
        super().__init__(qubit_index)

        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        """
        SBC 的 AOM 波形
        在开始有一个idle
        默认冷却声子模式列表的第一个
        """
        # 1. 创建 AOM 波形

        CarrierSBC = Waveform(
            duration=(
                self.parameters.Light_554.SBC_time
                + self.parameters.Experiment.Pumping_Time
            )
            * self.parameters.Light_554.SBC_num,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        RedSBC = Waveform(
            duration=(
                self.parameters.Light_554.SBC_time
                + self.parameters.Experiment.Pumping_Time
            )
            * self.parameters.Light_554.SBC_num,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=self.parameters.Light_554.Motion_freq[0],
        )
        IdlePump= Waveform(
            duration= self.parameters.Experiment.Pumping_Time,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        # 2. 波形赋值到属性

        self.AOM_L = CarrierSBC * IdlePump
        self.AOM_R = RedSBC * IdlePump
        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()
    def compute_AOD_wave(self):
        """
        构造 AOD 波形
        """

        temp_wave = Waveform(
            duration=self.duration_time,
            amplitude=1,
            phase=0,
            detuning=self.parameters.Light_554.AOD_address_freqs[0],
        )

        # 3. 赋值到属性
        self.AOD_L = temp_wave
        self.AOD_R = temp_wave


class X(Operation):
    """
    X 门的pi/2波形
    """

    def __init__(self, qubit_index: tuple):
        super().__init__(qubit_index)
        """初始化需要传入一个离子索引"""
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        """
        X 门的 AOM 波形, pi/2 波形
        duration: 单比特门是载波拉比的 pi/2 时间
        """
        # Idle = Waveform(
        #     duration=self.parameters.Light_554.AOD_time_before_AOM,
        #     amplitude=0,
        #     phase=0,
        #     detuning=0,
        # )

        Carrier_pi_2_L = Waveform(
            duration=self.parameters.Light_554.pi_2.Carrier[0],
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        Carrier_pi_2_R = Waveform(
            duration=self.parameters.Light_554.pi_2.Carrier[0],
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        self.AOM_L = Carrier_pi_2_L
        self.AOM_R = Carrier_pi_2_R

        self.duration_time = self.AOM_R.duration()



class Y(Operation):
    """
    Y 门
    """

    def __init__(self, qubit_index: tuple):
        super().__init__(qubit_index)
        """初始化需要传入一个离子索引"""
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        """
        X 门的 AOM 波形, pi/2 波形
        duration: 单比特门是载波拉比的 pi/2 时间
        """
        # Idle = Waveform(
        #     duration=self.parameters.Light_554.AOD_time_before_AOM,
        #     amplitude=0,
        #     phase=0,
        #     detuning=0,
        # )
        Carrier_pi_2_L = Waveform(
            duration=self.parameters.Light_554.pi_2.Carrier[0],
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        Carrier_pi_2_R = Waveform(
            duration=self.parameters.Light_554.pi_2.Carrier[0],
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=np.pi / 2,
            detuning=0,
        )
        self.AOM_L = Carrier_pi_2_L
        self.AOM_R = Carrier_pi_2_R
        self.dutation_time = self.AOM_R.duration()




class Xc(Operation):
    """
    反向 X  pi/2 门
    """

    def __init__(self, qubit_index: tuple):
        super().__init__(qubit_index)
        """初始化需要传入一个离子索引"""
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        """
        X 门的 AOM 波形, pi/2 波形
        duration: 单比特门是载波拉比的 pi/2 时间
        """
        # Idle = Waveform(
        #     duration=self.parameters.Light_554.AOD_time_before_AOM,
        #     amplitude=0,
        #     phase=0,
        #     detuning=0,
        # )
        Carrier_pi_2_L = Waveform(
            duration=self.parameters.Light_554.pi_2.Carrier,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        Carrier_pi_2_R = Waveform(
            duration=self.parameters.Light_554.pi_2.Carrier,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=np.pi,
            detuning=0,
        )
        self.AOM_L = Carrier_pi_2_L
        self.AOM_R = Carrier_pi_2_R
        self.dutation_time = self.AOM_R.duration()




class Yc(Operation):
    """
    反向 Y  pi/2 门
    """

    def __init__(self, qubit_index: tuple):
        super().__init__(qubit_index)
        """初始化需要传入一个离子索引"""
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        """
        X 门的 AOM 波形, pi/2 波形
        duration: 单比特门是载波拉比的 pi/2 时间
        """
        # Idle = Waveform(
        #     duration=self.parameters.Light_554.AOD_time_before_AOM,
        #     amplitude=0,
        #     phase=0,
        #     detuning=0,
        # )
        Carrier_pi_2_L = Waveform(
            duration=self.parameters.Light_554.pi_2.Carrier,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=0,
            detuning=0,
        )
        Carrier_pi_2_R = Waveform(
            duration=self.parameters.Light_554.pi_2.Carrier,
            amplitude=self.parameters.Light_554.AOM_AWG_amp,
            phase=3 * np.pi / 2,
            detuning=0,
        )
        self.AOM_L = Carrier_pi_2_L
        self.AOM_R = Carrier_pi_2_R
        self.dutation_time = self.AOM_R.duration()




class Rphi(Operation):
    """
    R 门
    """
    def __init__(self, qubit_index: tuple, theta=None, phi=None):
        super().__init__(qubit_index)
        """初始化需要传入一个离子索引"""
        if theta is None:
            theta = np.pi/2
        if phi is None:
            phi = 0.0
        self.theta = theta
        self.phi = phi
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):

        pi2_time = self.parameters.Light_554.pi_2.Carrier[self.qubit_index[0]]
        ac_stark_shift = self.parameters.Light_554.q1_AC_Stark_shift
        aom_phase_delay = self.parameters.Light_554.AOM_phase_delay
        if self.theta > 0:
            phi = 0
        else:
            phi = np.pi
        gate_time =  (pi2_time-aom_phase_delay)* (2 * abs(self.theta) / np.pi) + aom_phase_delay
        Carrier_pi_2_L = Waveform(
            duration= gate_time,
            amplitude= self.parameters.Light_554.AOM_AWG_amp,
            phase=self.phi + phi,
            detuning=ac_stark_shift,
        )
        Carrier_pi_2_R = Waveform(
            duration= gate_time,
            amplitude= self.parameters.Light_554.AOM_AWG_amp,
            phase= 0 ,
            detuning=0,
        )
        self.AOM_L = Carrier_pi_2_L
        self.AOM_R = Carrier_pi_2_R
        self.duration_time = self.AOM_R.duration()


class MSOperation(Operation):
    def __init__(
        self,
        qubit_index: tuple,rabi_choice='Carrier',
        **kwargs
    ):
        super().__init__(qubit_index)
        # logger.info("MS operation")

        # 1.读取实验参数
        operation_time = self.parameters.Light_554.MS_time[qubit_index[0]][qubit_index[1]]
        aom_amp = self.parameters.Light_554.AOM_AWG_amp_ms[qubit_index[0]][qubit_index[1]]
        detuning = self.parameters.Light_554.freq_detuning[qubit_index[0]][qubit_index[1]]
        aom_ratio = self.parameters.Light_554.ratio_AOM
        phonon_index = self.parameters.Light_554.MS_phonon_index[qubit_index[0]][qubit_index[1]]
        ac_stark_shift = self.parameters.Light_554.MS_AC_Stark_shift[qubit_index[0]][qubit_index[1]]
        phase = self.parameters.Light_554.MS_phase[qubit_index[0]][qubit_index[1]]

        # 如果有传入，则用传入值，否则用默认值
        self.operation_time = kwargs.get('operation_time',operation_time)
        self.aom_amp = kwargs.get('aom_amp',aom_amp)
        self.detuning = kwargs.get('detuning',detuning)
        self.aom_ratio = kwargs.get('aom_ratio',aom_ratio)
        self.rabi_choice = rabi_choice
        self.phonon_index = kwargs.get('phonon_index',phonon_index)
        self.ac_stark_shift = kwargs.get('ac_stark_shift',ac_stark_shift)
        self.phase = kwargs.get('phase',0)+phase
        # logger.info("MS operation_2")

        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        phonon_freq = self.parameters.Light_554.Motion_freq[int(self.phonon_index)] + self.detuning
        if self.rabi_choice == "Blue":
            ms_choice = -1
        elif self.rabi_choice == "Red":
            ms_choice = 1
        else:
            ms_choice = 0
        ac_stark_shift = self.ac_stark_shift
        # 1. 构造波形片段
        X_L = Waveform(
            duration=self.operation_time,
            amplitude=self.aom_amp,
            phase=self.phase,
            detuning=ms_choice * phonon_freq + ac_stark_shift,
        )
        Red_R = Waveform(
            duration=self.operation_time,
            amplitude=(
                self.aom_ratio
                / (self.aom_ratio + 1)
            ),
            phase=0,
            detuning= phonon_freq,
        )
        Blue_R = Waveform(
            duration=self.operation_time,
            amplitude=(1 / (self.aom_ratio + 1)),
            phase=0,
            detuning=-phonon_freq,
        )

        # 2. 波形赋值到属性
        self.AOM_L = X_L
        self.AOM_R = (Red_R+Blue_R)

        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()

MODULATOR_PATH = 'C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\cloud\\pulsedesign\\AM_modulator_list.pkl'

import dill
class MS_AM(Operation):
    def __init__(
        self,
        qubit_index: tuple,
        rabi_choice='Carrier',
        **kwargs
    ):
        # logger.info("MS AM")

        super().__init__(qubit_index)
        # logger.info("MS operation_2")
        # logger.info("MS AM1.0")

        with open(MODULATOR_PATH,'rb') as f:
            self.modulator = dill.load(f)[self.qubit_index[0]][self.qubit_index[1]]
        # logger.info("MS AM1.1")

        operation_time = self.parameters.Light_554.MS_time[qubit_index[0]][qubit_index[1]]
        # logger.info("MS AM1.2")
        aom_amp = self.parameters.Light_554.AOM_AWG_amp_ms[qubit_index[0]][qubit_index[1]]
        # detuning = self.parameters.Light_554.freq_detuning[qubit_index[0]][qubit_index[1]]
        # logger.info("MS AM1.3")
        aom_ratio = self.parameters.Light_554.ratio_AOM
        phonon_index = self.parameters.Light_554.MS_phonon_index[qubit_index[0]][qubit_index[1]]
        # logger.info("MS AM1.4")
        ac_stark_shift = self.parameters.Light_554.MS_AC_Stark_shift[qubit_index[0]][qubit_index[1]]
        # logger.info("MS AM1.5")
        phase = self.parameters.Light_554.MS_phase[qubit_index[0]][qubit_index[1]]

        # 如果有传入，则用传入值，否则用默认值
        self.operation_time = kwargs.get('operation_time',operation_time)
        self.aom_amp = kwargs.get('aom_amp',aom_amp)
        self.aom_ratio = kwargs.get('aom_ratio',aom_ratio)
        self.rabi_choice = rabi_choice
        self.phonon_index = kwargs.get('phonon_index',phonon_index)
        self.ac_stark_shift = kwargs.get('ac_stark_shift',ac_stark_shift)
        self.phase = kwargs.get('phase',0)+phase
        # logger.info("MS AM1.6")

        detuning = self.modulator.mu / 2 / np.pi - self.parameters.Light_554.Motion_freq[int(self.phonon_index)]
        self.detuning = kwargs.get('detuning',detuning)
        # logger.info("MS AM2")

        self.compute_AOM_wave()
        self.compute_AOD_wave()
    def amp_to_aom_amp(self,amp):
        sin_coeff = self.parameters.Light_554.AOM_sin_coeff
        aom_amp = np.arcsin(np.sin(sin_coeff)*amp)/sin_coeff
        return aom_amp
    def compute_AOM_wave(self, algorithm=None):

        dt = self.modulator.t_modulation
        N_len = int(np.floor(self.operation_time/dt))
        N = self.modulator.paras.N
        t_remain = self.operation_time- N_len * dt
        # 设置一个极小的阈值
        epsilon = 1e-10
        # 如果 t_remain 的绝对值小于 epsilon，则返回 0
        if abs(t_remain) < epsilon:
            t_remain = 0

        aom_amp = self.aom_amp
        ratio = self.aom_ratio

        ac_stark_shift = self.ac_stark_shift

        detuning  = self.detuning + self.parameters.Light_554.Motion_freq[int(self.phonon_index)]

        # 1. 构造波形片段
        Idle = Waveform(
            duration= 0 ,
            amplitude=0,
            phase=0,
            detuning=0,
        )

        self.AOM_L = Idle
        self.AOM_R = Idle


        for i in range(N_len):
            i_index = i % N
            # print(i_index)
            amp = self.amp_to_aom_amp(abs(aom_amp*self.modulator.rabi[i_index]))
            if self.modulator.rabi[i_index] < 0:
                phase = np.pi +self.phase
            else:
                phase = 0 +self.phase
            carrier = Waveform(
                duration=dt,
                amplitude= amp,
                phase= phase,
                detuning=ac_stark_shift,
            )
            red = Waveform(
                duration=dt,
                amplitude=ratio/(ratio + 1),
                phase=0,
                detuning=detuning
            )
            blue = Waveform(
                duration=dt,
                amplitude=1/(ratio + 1),
                phase = 0,
                detuning = -detuning
            )
            self.AOM_L = self.AOM_L * carrier
            self.AOM_R = self.AOM_R * (red + blue)
        i_index = N_len % N
        # print(N)
        # print(i_index)
        amp = self.amp_to_aom_amp(abs(aom_amp * self.modulator.rabi[i_index]))


        if self.modulator.rabi[i_index] < 0:
            phase = np.pi + self.phase
        else:
            phase = 0 + self.phase

        carrier_end = Waveform(
            duration=t_remain,
            amplitude=amp,
            phase=phase,
            detuning=ac_stark_shift,
        )
        red = Waveform(
            duration=t_remain,
            amplitude=ratio/(ratio + 1),
            phase=0,
            detuning=detuning
        )
        blue = Waveform(
            duration=t_remain,
            amplitude=1/(ratio + 1),
            phase=0,
            detuning=-detuning
        )

        self.AOM_L = self.AOM_L * carrier_end
        self.AOM_R = self.AOM_R * (red + blue)
        # 3. 计算持续时间
        self.duration_time = self.AOM_R.duration()
class AOD_Test(Operation):
    def __init__(self,qubit_index:tuple,operation_time:float,amp:tuple,phase:tuple):
        super().__init__(qubit_index)
        self.operation_time = operation_time
        self.amp = amp
        self.phase = phase
        self.compute_AOM_wave()
        self.compute_AOD_wave()

    def compute_AOM_wave(self, algorithm=None):
        idle_time = self.parameters.Light_554.AOD_time_before_AOM

        Idle_aod = Waveform(
            duration=idle_time,
            amplitude=0.0,
            phase= 0.0,
            detuning=0.0
        )
        Carrier = Waveform(
            duration=self.operation_time,
            amplitude=1.0,
            detuning=0.0,
            phase=0.0
        )

        self.AOM_L = Idle_aod*Carrier
        self.AOM_R = Idle_aod*Carrier
        self.duration_time = self.AOM_L.duration()
    def compute_AOD_wave(self):
        n_qubit = len(self.qubit_index)
        temp_wave = Waveform(duration=self.duration_time,
                             amplitude=self.amp[0],
                             phase=self.phase[0],
                             detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[0]])
        for i in range(1,n_qubit):
            temp_wave += Waveform(duration=self.duration_time,
                                  amplitude=self.amp[i],
                                  phase=self.phase[i],
                                  detuning=self.parameters.Light_554.AOD_address_freqs[self.qubit_index[i]])

        # 3. 赋值到属性
        self.AOD_L = temp_wave
        self.AOD_R = temp_wave

if __name__ == "__main__":
    print(OperationRegister.operation_set)
    c_list = [
        [{"gate": "X", "qubit_index": (1,)}],
        [{"gate": "Y", "qubit_index": (0,)}],
        [{"gate": "X", "qubit_index": (1,)}],
        [{"gate": "Y", "qubit_index": (0,)}],
        [{"gate": "X", "qubit_index": (0,)}],
    ]
    stat_time = time.time()
    circuit = Circuit(circuit_list=c_list)
    end_time = time.time()
    print(f"Time: {end_time - stat_time}")
    pprint(circuit.AOM_R.waveform_list)
    pprint(circuit.circuit_list)
    circuit.plot_circuit_wave(AOM_base_fre=1e6, AOD_base_fre=1e6, sampling_rate=1e9)
