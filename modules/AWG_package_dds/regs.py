# def KILO(k): return (  1000 * (k))
# 
# def MEGA(m): return (  1000 * 1000 * (m))
# 
# def GIGA(g): return (  1000 * 1000 * 1000 * (g))
# 
# def KILO_B(k): return (  1024 * (k))
# KIBI = KILO_B
# 
# def MEGA_B(m): return (  1024 * 1024 * (m))
# MEBI = MEGA_B
# 
# def GIGA_B(g): return (  1024 * 1024 * 1024 * (g))
# GIBI = GIGA_B


TYP_PCIDEVICEID = 0x00000000
TYP_EVAL = 0x00000010
TYP_RSDLGA = 0x00000014
TYP_GMG = 0x00000018
TYP_VAN8 = 0x00000020
TYP_VAC = 0x00000028
TYP_PCIAUTOINSTALL = 0x000000FF
TYP_DAP116 = 0x00000100
TYP_PAD82 = 0x00000200
TYP_PAD82a = 0x00000210
TYP_PAD82b = 0x00000220
TYP_PCI212 = 0x00000300
TYP_PAD1232a = 0x00000400
TYP_PAD1232b = 0x00000410
TYP_PAD1232c = 0x00000420
TYP_PAD1616a = 0x00000500
TYP_PAD1616b = 0x00000510
TYP_PAD1616c = 0x00000520
TYP_PAD1616d = 0x00000530
TYP_PAD52 = 0x00000600
TYP_PAD242 = 0x00000700
TYP_PCK400 = 0x00000800
TYP_PAD164_2M = 0x00000900
TYP_PAD164_5M = 0x00000910
TYP_PCI208 = 0x00001000
TYP_CPCI208 = 0x00001001
TYP_PCI412 = 0x00001100
TYP_PCIDIO32 = 0x00001200
TYP_PCI248 = 0x00001300
TYP_PADCO = 0x00001400
TYP_TRS582 = 0x00001500
TYP_PCI258 = 0x00001600
TYP_SERIESMASK = 0x00FF0000
TYP_VERSIONMASK = 0x0000FFFF
TYP_FAMILYMASK = 0x0000FF00
TYP_TYPEMASK = 0x000000FF
TYP_SPEEDMASK = 0x000000F0
TYP_CHMASK = 0x0000000F
TYP_MISERIES = 0x00000000
TYP_MCSERIES = 0x00010000
TYP_MXSERIES = 0x00020000
TYP_M2ISERIES = 0x00030000
TYP_M2IEXPSERIES = 0x00040000
TYP_M3ISERIES = 0x00050000
TYP_M3IEXPSERIES = 0x00060000
TYP_M4IEXPSERIES = 0x00070000
TYP_M4XEXPSERIES = 0x00080000
TYP_M2PEXPSERIES = 0x00090000
TYP_M5IEXPSERIES = 0x000A0000
TYP_MI2020 = 0x00002020
TYP_MI2021 = 0x00002021
TYP_MI2025 = 0x00002025
TYP_MI2030 = 0x00002030
TYP_MI2031 = 0x00002031
TYP_M2I2020 = 0x00032020
TYP_M2I2021 = 0x00032021
TYP_M2I2025 = 0x00032025
TYP_M2I2030 = 0x00032030
TYP_M2I2031 = 0x00032031
TYP_M2I2020EXP = 0x00042020
TYP_M2I2021EXP = 0x00042021
TYP_M2I2025EXP = 0x00042025
TYP_M2I2030EXP = 0x00042030
TYP_M2I2031EXP = 0x00042031
TYP_MC2020 = 0x00012020
TYP_MC2021 = 0x00012021
TYP_MC2025 = 0x00012025
TYP_MC2030 = 0x00012030
TYP_MC2031 = 0x00012031
TYP_MX2020 = 0x00022020
TYP_MX2025 = 0x00022025
TYP_MX2030 = 0x00022030
TYP_M3I2120 = 0x00052120
TYP_M3I2122 = 0x00052122
TYP_M3I2130 = 0x00052130
TYP_M3I2132 = 0x00052132
TYP_M3I2120EXP = 0x00062120
TYP_M3I2122EXP = 0x00062122
TYP_M3I2130EXP = 0x00062130
TYP_M3I2132EXP = 0x00062132
TYP_M4I22XX_X8 = 0x00072200
TYP_M4I2210_X8 = 0x00072210
TYP_M4I2211_X8 = 0x00072211
TYP_M4I2212_X8 = 0x00072212
TYP_M4I2220_X8 = 0x00072220
TYP_M4I2221_X8 = 0x00072221
TYP_M4I2223_X8 = 0x00072223
TYP_M4I2230_X8 = 0x00072230
TYP_M4I2233_X8 = 0x00072233
TYP_M4I2234_X8 = 0x00072234
TYP_M4I2280_X8 = 0x00072280
TYP_M4I2281_X8 = 0x00072281
TYP_M4I2283_X8 = 0x00072283
TYP_M4I2290_X8 = 0x00072290
TYP_M4I2293_X8 = 0x00072293
TYP_M4I2294_X8 = 0x00072294
TYP_M4X22XX_X4 = 0x00082200
TYP_M4X2210_X4 = 0x00082210
TYP_M4X2211_X4 = 0x00082211
TYP_M4X2212_X4 = 0x00082212
TYP_M4X2220_X4 = 0x00082220
TYP_M4X2221_X4 = 0x00082221
TYP_M4X2223_X4 = 0x00082223
TYP_M4X2230_X4 = 0x00082230
TYP_M4X2233_X4 = 0x00082233
TYP_M4X2234_X4 = 0x00082234
TYP_M4I23XX_X8 = 0x00072300
TYP_M4I2320_X8 = 0x00072320
TYP_M4I2321_X8 = 0x00072321
TYP_M4I2323_X8 = 0x00072323
TYP_M4I2330_X8 = 0x00072330
TYP_M4I2333_X8 = 0x00072333
TYP_M4I2334_X8 = 0x00072334
TYP_MI3010 = 0x00003010
TYP_MI3011 = 0x00003011
TYP_MI3012 = 0x00003012
TYP_MI3013 = 0x00003013
TYP_MI3014 = 0x00003014
TYP_MI3015 = 0x00003015
TYP_MI3016 = 0x00003016
TYP_MI3020 = 0x00003020
TYP_MI3021 = 0x00003021
TYP_MI3022 = 0x00003022
TYP_MI3023 = 0x00003023
TYP_MI3024 = 0x00003024
TYP_MI3025 = 0x00003025
TYP_MI3026 = 0x00003026
TYP_MI3027 = 0x00003027
TYP_MI3031 = 0x00003031
TYP_MI3033 = 0x00003033
TYP_M2I3010 = 0x00033010
TYP_M2I3011 = 0x00033011
TYP_M2I3012 = 0x00033012
TYP_M2I3013 = 0x00033013
TYP_M2I3014 = 0x00033014
TYP_M2I3015 = 0x00033015
TYP_M2I3016 = 0x00033016
TYP_M2I3020 = 0x00033020
TYP_M2I3021 = 0x00033021
TYP_M2I3022 = 0x00033022
TYP_M2I3023 = 0x00033023
TYP_M2I3024 = 0x00033024
TYP_M2I3025 = 0x00033025
TYP_M2I3026 = 0x00033026
TYP_M2I3027 = 0x00033027
TYP_M2I3031 = 0x00033031
TYP_M2I3033 = 0x00033033
TYP_M2I3010EXP = 0x00043010
TYP_M2I3011EXP = 0x00043011
TYP_M2I3012EXP = 0x00043012
TYP_M2I3013EXP = 0x00043013
TYP_M2I3014EXP = 0x00043014
TYP_M2I3015EXP = 0x00043015
TYP_M2I3016EXP = 0x00043016
TYP_M2I3020EXP = 0x00043020
TYP_M2I3021EXP = 0x00043021
TYP_M2I3022EXP = 0x00043022
TYP_M2I3023EXP = 0x00043023
TYP_M2I3024EXP = 0x00043024
TYP_M2I3025EXP = 0x00043025
TYP_M2I3026EXP = 0x00043026
TYP_M2I3027EXP = 0x00043027
TYP_M2I3031EXP = 0x00043031
TYP_M2I3033EXP = 0x00043033
TYP_MC3010 = 0x00013010
TYP_MC3011 = 0x00013011
TYP_MC3012 = 0x00013012
TYP_MC3013 = 0x00013013
TYP_MC3014 = 0x00013014
TYP_MC3015 = 0x00013015
TYP_MC3016 = 0x00013016
TYP_MC3020 = 0x00013020
TYP_MC3021 = 0x00013021
TYP_MC3022 = 0x00013022
TYP_MC3023 = 0x00013023
TYP_MC3024 = 0x00013024
TYP_MC3025 = 0x00013025
TYP_MC3026 = 0x00013026
TYP_MC3027 = 0x00013027
TYP_MC3031 = 0x00013031
TYP_MC3033 = 0x00013033
TYP_MX3010 = 0x00023010
TYP_MX3011 = 0x00023011
TYP_MX3012 = 0x00023012
TYP_MX3020 = 0x00023020
TYP_MX3021 = 0x00023021
TYP_MX3022 = 0x00023022
TYP_MX3031 = 0x00023031
TYP_MI3110 = 0x00003110
TYP_MI3111 = 0x00003111
TYP_MI3112 = 0x00003112
TYP_MI3120 = 0x00003120
TYP_MI3121 = 0x00003121
TYP_MI3122 = 0x00003122
TYP_MI3130 = 0x00003130
TYP_MI3131 = 0x00003131
TYP_MI3132 = 0x00003132
TYP_MI3140 = 0x00003140
TYP_M2I3110 = 0x00033110
TYP_M2I3111 = 0x00033111
TYP_M2I3112 = 0x00033112
TYP_M2I3120 = 0x00033120
TYP_M2I3121 = 0x00033121
TYP_M2I3122 = 0x00033122
TYP_M2I3130 = 0x00033130
TYP_M2I3131 = 0x00033131
TYP_M2I3132 = 0x00033132
TYP_M2I3110EXP = 0x00043110
TYP_M2I3111EXP = 0x00043111
TYP_M2I3112EXP = 0x00043112
TYP_M2I3120EXP = 0x00043120
TYP_M2I3121EXP = 0x00043121
TYP_M2I3122EXP = 0x00043122
TYP_M2I3130EXP = 0x00043130
TYP_M2I3131EXP = 0x00043131
TYP_M2I3132EXP = 0x00043132
TYP_MC3110 = 0x00013110
TYP_MC3111 = 0x00013111
TYP_MC3112 = 0x00013112
TYP_MC3120 = 0x00013120
TYP_MC3121 = 0x00013121
TYP_MC3122 = 0x00013122
TYP_MC3130 = 0x00013130
TYP_MC3131 = 0x00013131
TYP_MC3132 = 0x00013132
TYP_MX3110 = 0x00023110
TYP_MX3111 = 0x00023111
TYP_MX3120 = 0x00023120
TYP_MX3121 = 0x00023121
TYP_MX3130 = 0x00023130
TYP_MX3131 = 0x00023131
TYP_M3I3220 = 0x00053220
TYP_M3I3221 = 0x00053221
TYP_M3I3240 = 0x00053240
TYP_M3I3242 = 0x00053242
TYP_M3I3220EXP = 0x00063220
TYP_M3I3221EXP = 0x00063221
TYP_M3I3240EXP = 0x00063240
TYP_M3I3242EXP = 0x00063242
TYP_M5I33XX_X16 = 0x000A3300
TYP_M5I3321_X16 = 0x000A3321
TYP_M5I3330_X16 = 0x000A3330
TYP_M5I3337_X16 = 0x000A3337
TYP_M5I3350_X16 = 0x000A3350
TYP_M5I3357_X16 = 0x000A3357
TYP_M5I3360_X16 = 0x000A3360
TYP_M5I3367_X16 = 0x000A3367
TYP_MI4020 = 0x00004020
TYP_MI4021 = 0x00004021
TYP_MI4022 = 0x00004022
TYP_MI4030 = 0x00004030
TYP_MI4031 = 0x00004031
TYP_MI4032 = 0x00004032
TYP_M2I4020 = 0x00034020
TYP_M2I4021 = 0x00034021
TYP_M2I4022 = 0x00034022
TYP_M2I4028 = 0x00034028
TYP_M2I4030 = 0x00034030
TYP_M2I4031 = 0x00034031
TYP_M2I4032 = 0x00034032
TYP_M2I4038 = 0x00034038
TYP_M2I4020EXP = 0x00044020
TYP_M2I4021EXP = 0x00044021
TYP_M2I4022EXP = 0x00044022
TYP_M2I4028EXP = 0x00044028
TYP_M2I4030EXP = 0x00044030
TYP_M2I4031EXP = 0x00044031
TYP_M2I4032EXP = 0x00044032
TYP_M2I4038EXP = 0x00044038
TYP_MC4020 = 0x00014020
TYP_MC4021 = 0x00014021
TYP_MC4022 = 0x00014022
TYP_MC4030 = 0x00014030
TYP_MC4031 = 0x00014031
TYP_MC4032 = 0x00014032
TYP_MX4020 = 0x00024020
TYP_MX4021 = 0x00024021
TYP_MX4030 = 0x00024030
TYP_MX4031 = 0x00024031
TYP_M3I4110 = 0x00054110
TYP_M3I4111 = 0x00054111
TYP_M3I4120 = 0x00054120
TYP_M3I4121 = 0x00054121
TYP_M3I4140 = 0x00054140
TYP_M3I4142 = 0x00054142
TYP_M3I4110EXP = 0x00064110
TYP_M3I4111EXP = 0x00064111
TYP_M3I4120EXP = 0x00064120
TYP_M3I4121EXP = 0x00064121
TYP_M3I4140EXP = 0x00064140
TYP_M3I4142EXP = 0x00064142
TYP_M4I44XX_X8 = 0x00074400
TYP_M4I4410_X8 = 0x00074410
TYP_M4I4411_X8 = 0x00074411
TYP_M4I4420_X8 = 0x00074420
TYP_M4I4421_X8 = 0x00074421
TYP_M4I4450_X8 = 0x00074450
TYP_M4I4451_X8 = 0x00074451
TYP_M4I4470_X8 = 0x00074470
TYP_M4I4471_X8 = 0x00074471
TYP_M4I4480_X8 = 0x00074480
TYP_M4I4481_X8 = 0x00074481
TYP_M4X44XX_X4 = 0x00084400
TYP_M4X4410_X4 = 0x00084410
TYP_M4X4411_X4 = 0x00084411
TYP_M4X4420_X4 = 0x00084420
TYP_M4X4421_X4 = 0x00084421
TYP_M4X4450_X4 = 0x00084450
TYP_M4X4451_X4 = 0x00084451
TYP_M4X4470_X4 = 0x00084470
TYP_M4X4471_X4 = 0x00084471
TYP_M4X4480_X4 = 0x00084480
TYP_M4X4481_X4 = 0x00084481
TYP_MI4520 = 0x00004520
TYP_MI4521 = 0x00004521
TYP_MI4530 = 0x00004530
TYP_MI4531 = 0x00004531
TYP_MI4540 = 0x00004540
TYP_MI4541 = 0x00004541
TYP_M2I4520 = 0x00034520
TYP_M2I4521 = 0x00034521
TYP_M2I4530 = 0x00034530
TYP_M2I4531 = 0x00034531
TYP_M2I4540 = 0x00034540
TYP_M2I4541 = 0x00034541
TYP_MC4520 = 0x00014520
TYP_MC4521 = 0x00014521
TYP_MC4530 = 0x00014530
TYP_MC4531 = 0x00014531
TYP_MC4540 = 0x00014540
TYP_MC4541 = 0x00014541
TYP_MX4520 = 0x00024520
TYP_MX4530 = 0x00024530
TYP_MX4540 = 0x00024540
TYP_MI4620 = 0x00004620
TYP_MI4621 = 0x00004621
TYP_MI4622 = 0x00004622
TYP_MI4630 = 0x00004630
TYP_MI4631 = 0x00004631
TYP_MI4632 = 0x00004632
TYP_MI4640 = 0x00004640
TYP_MI4641 = 0x00004641
TYP_MI4642 = 0x00004642
TYP_MI4650 = 0x00004650
TYP_MI4651 = 0x00004651
TYP_MI4652 = 0x00004652
TYP_M2I4620 = 0x00034620
TYP_M2I4621 = 0x00034621
TYP_M2I4622 = 0x00034622
TYP_M2I4630 = 0x00034630
TYP_M2I4631 = 0x00034631
TYP_M2I4632 = 0x00034632
TYP_M2I4640 = 0x00034640
TYP_M2I4641 = 0x00034641
TYP_M2I4642 = 0x00034642
TYP_M2I4650 = 0x00034650
TYP_M2I4651 = 0x00034651
TYP_M2I4652 = 0x00034652
TYP_M2I4620EXP = 0x00044620
TYP_M2I4621EXP = 0x00044621
TYP_M2I4622EXP = 0x00044622
TYP_M2I4630EXP = 0x00044630
TYP_M2I4631EXP = 0x00044631
TYP_M2I4632EXP = 0x00044632
TYP_M2I4640EXP = 0x00044640
TYP_M2I4641EXP = 0x00044641
TYP_M2I4642EXP = 0x00044642
TYP_M2I4650EXP = 0x00044650
TYP_M2I4651EXP = 0x00044651
TYP_M2I4652EXP = 0x00044652
TYP_MC4620 = 0x00014620
TYP_MC4621 = 0x00014621
TYP_MC4622 = 0x00014622
TYP_MC4630 = 0x00014630
TYP_MC4631 = 0x00014631
TYP_MC4632 = 0x00014632
TYP_MC4640 = 0x00014640
TYP_MC4641 = 0x00014641
TYP_MC4642 = 0x00014642
TYP_MC4650 = 0x00014650
TYP_MC4651 = 0x00014651
TYP_MC4652 = 0x00014652
TYP_MX4620 = 0x00024620
TYP_MX4621 = 0x00024621
TYP_MX4630 = 0x00024630
TYP_MX4631 = 0x00024631
TYP_MX4640 = 0x00024640
TYP_MX4641 = 0x00024641
TYP_MX4650 = 0x00024650
TYP_MX4651 = 0x00024651
TYP_MI4710 = 0x00004710
TYP_MI4711 = 0x00004711
TYP_MI4720 = 0x00004720
TYP_MI4721 = 0x00004721
TYP_MI4730 = 0x00004730
TYP_MI4731 = 0x00004731
TYP_MI4740 = 0x00004740
TYP_MI4741 = 0x00004741
TYP_M2I4710 = 0x00034710
TYP_M2I4711 = 0x00034711
TYP_M2I4720 = 0x00034720
TYP_M2I4721 = 0x00034721
TYP_M2I4730 = 0x00034730
TYP_M2I4731 = 0x00034731
TYP_M2I4740 = 0x00034740
TYP_M2I4741 = 0x00034741
TYP_M2I4710EXP = 0x00044710
TYP_M2I4711EXP = 0x00044711
TYP_M2I4720EXP = 0x00044720
TYP_M2I4721EXP = 0x00044721
TYP_M2I4730EXP = 0x00044730
TYP_M2I4731EXP = 0x00044731
TYP_M2I4740EXP = 0x00044740
TYP_M2I4741EXP = 0x00044741
TYP_MC4710 = 0x00014710
TYP_MC4711 = 0x00014711
TYP_MC4720 = 0x00014720
TYP_MC4721 = 0x00014721
TYP_MC4730 = 0x00014730
TYP_MC4731 = 0x00014731
TYP_MX4710 = 0x00024710
TYP_MX4720 = 0x00024720
TYP_MX4730 = 0x00024730
TYP_M3I4830 = 0x00054830
TYP_M3I4831 = 0x00054831
TYP_M3I4840 = 0x00054840
TYP_M3I4841 = 0x00054841
TYP_M3I4860 = 0x00054860
TYP_M3I4861 = 0x00054861
TYP_M3I4830EXP = 0x00064830
TYP_M3I4831EXP = 0x00064831
TYP_M3I4840EXP = 0x00064840
TYP_M3I4841EXP = 0x00064841
TYP_M3I4860EXP = 0x00064860
TYP_M3I4861EXP = 0x00064861
TYP_MI4911 = 0x00004911
TYP_MI4912 = 0x00004912
TYP_MI4931 = 0x00004931
TYP_MI4932 = 0x00004932
TYP_MI4960 = 0x00004960
TYP_MI4961 = 0x00004961
TYP_MI4963 = 0x00004963
TYP_MI4964 = 0x00004964
TYP_MC4911 = 0x00014911
TYP_MC4912 = 0x00014912
TYP_MC4931 = 0x00014931
TYP_MC4932 = 0x00014932
TYP_MC4960 = 0x00014960
TYP_MC4961 = 0x00014961
TYP_MC4963 = 0x00014963
TYP_MC4964 = 0x00014964
TYP_MX4911 = 0x00024911
TYP_MX4931 = 0x00024931
TYP_MX4960 = 0x00024960
TYP_MX4963 = 0x00024963
TYP_M2I4911 = 0x00034911
TYP_M2I4912 = 0x00034912
TYP_M2I4931 = 0x00034931
TYP_M2I4932 = 0x00034932
TYP_M2I4960 = 0x00034960
TYP_M2I4961 = 0x00034961
TYP_M2I4963 = 0x00034963
TYP_M2I4964 = 0x00034964
TYP_M2I4911EXP = 0x00044911
TYP_M2I4912EXP = 0x00044912
TYP_M2I4931EXP = 0x00044931
TYP_M2I4932EXP = 0x00044932
TYP_M2I4960EXP = 0x00044960
TYP_M2I4961EXP = 0x00044961
TYP_M2I4963EXP = 0x00044963
TYP_M2I4964EXP = 0x00044964
TYP_M2P59XX_X4 = 0x00095900
TYP_M2P5911_X4 = 0x00095911
TYP_M2P5912_X4 = 0x00095912
TYP_M2P5913_X4 = 0x00095913
TYP_M2P5916_X4 = 0x00095916
TYP_M2P5920_X4 = 0x00095920
TYP_M2P5921_X4 = 0x00095921
TYP_M2P5922_X4 = 0x00095922
TYP_M2P5923_X4 = 0x00095923
TYP_M2P5926_X4 = 0x00095926
TYP_M2P5930_X4 = 0x00095930
TYP_M2P5931_X4 = 0x00095931
TYP_M2P5932_X4 = 0x00095932
TYP_M2P5933_X4 = 0x00095933
TYP_M2P5936_X4 = 0x00095936
TYP_M2P5940_X4 = 0x00095940
TYP_M2P5941_X4 = 0x00095941
TYP_M2P5942_X4 = 0x00095942
TYP_M2P5943_X4 = 0x00095943
TYP_M2P5946_X4 = 0x00095946
TYP_M2P5960_X4 = 0x00095960
TYP_M2P5961_X4 = 0x00095961
TYP_M2P5962_X4 = 0x00095962
TYP_M2P5966_X4 = 0x00095966
TYP_M2P5968_X4 = 0x00095968
TYP_MI6010 = 0x00006010
TYP_MI6011 = 0x00006011
TYP_MI6012 = 0x00006012
TYP_MI6021 = 0x00006021
TYP_MI6022 = 0x00006022
TYP_MI6030 = 0x00006030
TYP_MI6031 = 0x00006031
TYP_MI6033 = 0x00006033
TYP_MI6034 = 0x00006034
TYP_M2I6010 = 0x00036010
TYP_M2I6011 = 0x00036011
TYP_M2I6012 = 0x00036012
TYP_M2I6021 = 0x00036021
TYP_M2I6022 = 0x00036022
TYP_M2I6030 = 0x00036030
TYP_M2I6031 = 0x00036031
TYP_M2I6033 = 0x00036033
TYP_M2I6034 = 0x00036034
TYP_M2I6010EXP = 0x00046010
TYP_M2I6011EXP = 0x00046011
TYP_M2I6012EXP = 0x00046012
TYP_M2I6021EXP = 0x00046021
TYP_M2I6022EXP = 0x00046022
TYP_M2I6030EXP = 0x00046030
TYP_M2I6031EXP = 0x00046031
TYP_M2I6033EXP = 0x00046033
TYP_M2I6034EXP = 0x00046034
TYP_MC6010 = 0x00016010
TYP_MC6011 = 0x00016011
TYP_MC6012 = 0x00016012
TYP_MC6021 = 0x00016021
TYP_MC6022 = 0x00016022
TYP_MC6030 = 0x00016030
TYP_MC6031 = 0x00016031
TYP_MC6033 = 0x00016033
TYP_MC6034 = 0x00016034
TYP_MX6010 = 0x00026010
TYP_MX6011 = 0x00026011
TYP_MX6021 = 0x00026021
TYP_MX6030 = 0x00026030
TYP_MX6033 = 0x00026033
TYP_MI6105 = 0x00006105
TYP_MI6110 = 0x00006110
TYP_MI6111 = 0x00006111
TYP_M2I6105 = 0x00036105
TYP_M2I6110 = 0x00036110
TYP_M2I6111 = 0x00036111
TYP_M2I6105EXP = 0x00046105
TYP_M2I6110EXP = 0x00046110
TYP_M2I6111EXP = 0x00046111
TYP_MC6110 = 0x00016110
TYP_MC6111 = 0x00016111
TYP_MX6110 = 0x00026110
TYP_M2P65XX_X4 = 0x00096500
TYP_M2P6522_X4 = 0x00096522
TYP_M2P6523_X4 = 0x00096523
TYP_M2P6530_X4 = 0x00096530
TYP_M2P6531_X4 = 0x00096531
TYP_M2P6532_X4 = 0x00096532
TYP_M2P6536_X4 = 0x00096536
TYP_M2P6533_X4 = 0x00096533
TYP_M2P6540_X4 = 0x00096540
TYP_M2P6541_X4 = 0x00096541
TYP_M2P6546_X4 = 0x00096546
TYP_M2P6560_X4 = 0x00096560
TYP_M2P6561_X4 = 0x00096561
TYP_M2P6562_X4 = 0x00096562
TYP_M2P6566_X4 = 0x00096566
TYP_M2P6568_X4 = 0x00096568
TYP_M2P6570_X4 = 0x00096570
TYP_M2P6571_X4 = 0x00096571
TYP_M2P6576_X4 = 0x00096576
TYP_M4I66XX_X8 = 0x00076600
TYP_M4I6620_X8 = 0x00076620
TYP_M4I6621_X8 = 0x00076621
TYP_M4I6622_X8 = 0x00076622
TYP_M4I6630_X8 = 0x00076630
TYP_M4I6631_X8 = 0x00076631
TYP_M4X66XX_X4 = 0x00086600
TYP_M4X6620_X4 = 0x00086620
TYP_M4X6621_X4 = 0x00086621
TYP_M4X6622_X4 = 0x00086622
TYP_M4X6630_X4 = 0x00086630
TYP_M4X6631_X4 = 0x00086631
TYP_MI7005 = 0x00007005
TYP_MI7010 = 0x00007010
TYP_MI7011 = 0x00007011
TYP_MI7020 = 0x00007020
TYP_MI7021 = 0x00007021
TYP_M2I7005 = 0x00037005
TYP_M2I7010 = 0x00037010
TYP_M2I7011 = 0x00037011
TYP_M2I7020 = 0x00037020
TYP_M2I7021 = 0x00037021
TYP_M2I7005EXP = 0x00047005
TYP_M2I7010EXP = 0x00047010
TYP_M2I7011EXP = 0x00047011
TYP_M2I7020EXP = 0x00047020
TYP_M2I7021EXP = 0x00047021
TYP_MC7005 = 0x00017005
TYP_MC7010 = 0x00017010
TYP_MC7011 = 0x00017011
TYP_MC7020 = 0x00017020
TYP_MC7021 = 0x00017021
TYP_MX7005 = 0x00027005
TYP_MX7010 = 0x00027010
TYP_MX7011 = 0x00027011
TYP_MI7210 = 0x00007210
TYP_MI7211 = 0x00007211
TYP_MI7220 = 0x00007220
TYP_MI7221 = 0x00007221
TYP_M2I7210 = 0x00037210
TYP_M2I7211 = 0x00037211
TYP_M2I7220 = 0x00037220
TYP_M2I7221 = 0x00037221
TYP_M2I7210EXP = 0x00047210
TYP_M2I7211EXP = 0x00047211
TYP_M2I7220EXP = 0x00047220
TYP_M2I7221EXP = 0x00047221
TYP_MC7210 = 0x00017210
TYP_MC7211 = 0x00017211
TYP_MC7220 = 0x00017220
TYP_MC7221 = 0x00017221
TYP_MX7210 = 0x00027210
TYP_MX7220 = 0x00027220
TYP_M2P75XX_X4 = 0x00097500
TYP_M2P7515_X4 = 0x00097515
TYP_M4I77XX_X8 = 0x00077700
TYP_M4I7710_X8 = 0x00077710
TYP_M4I7720_X8 = 0x00077720
TYP_M4I7730_X8 = 0x00077730
TYP_M4I7725_X8 = 0x00077725
TYP_M4I7735_X8 = 0x00077735
TYP_M4X77XX_X4 = 0x00087700
TYP_M4X7710_X4 = 0x00087710
TYP_M4X7720_X4 = 0x00087720
TYP_M4X7730_X4 = 0x00087730
TYP_M4X7725_X4 = 0x00087725
TYP_M4X7735_X4 = 0x00087735
TYP_MX9010 = 0x00029010
PCIBIT_MULTI = 0x00000001
PCIBIT_DIGITAL = 0x00000002
PCIBIT_CH0DIGI = 0x00000004
PCIBIT_EXTSAM = 0x00000008
PCIBIT_3CHANNEL = 0x00000010
PCIBIT_GATE = 0x00000020
PCIBIT_SLAVE = 0x00000040
PCIBIT_MASTER = 0x00000080
PCIBIT_DOUBLEMEM = 0x00000100
PCIBIT_SYNC = 0x00000200
PCIBIT_TIMESTAMP = 0x00000400
PCIBIT_STARHUB = 0x00000800
PCIBIT_CA = 0x00001000
PCIBIT_XIO = 0x00002000
PCIBIT_AMPLIFIER = 0x00004000
PCIBIT_DIFFMODE = 0x00008000
PCIBIT_ELISA = 0x10000000
SPCM_FEAT_MULTI = 0x00000001
SPCM_FEAT_GATE = 0x00000002
SPCM_FEAT_DIGITAL = 0x00000004
SPCM_FEAT_TIMESTAMP = 0x00000008
SPCM_FEAT_STARHUB5 = 0x00000020
SPCM_FEAT_STARHUB4 = 0x00000020
SPCM_FEAT_STARHUB6_EXTM = 0x00000020
SPCM_FEAT_STARHUB8_EXTM = 0x00000020
SPCM_FEAT_STARHUB16 = 0x00000040
SPCM_FEAT_STARHUB16_EXTM = 0x00000040
SPCM_FEAT_STARHUB8 = 0x00000040
SPCM_FEAT_STARHUBXX_MASK = 0x00000060
SPCM_FEAT_ABA = 0x00000080
SPCM_FEAT_BASEXIO = 0x00000100
SPCM_FEAT_AMPLIFIER_10V = 0x00000200
SPCM_FEAT_STARHUBSYSMASTER = 0x00000400
SPCM_FEAT_DIFFMODE = 0x00000800
SPCM_FEAT_SEQUENCE = 0x00001000
SPCM_FEAT_AMPMODULE_10V = 0x00002000
SPCM_FEAT_STARHUBSYSSLAVE = 0x00004000
SPCM_FEAT_NETBOX = 0x00008000
SPCM_FEAT_REMOTESERVER = 0x00010000
SPCM_FEAT_SCAPP = 0x00020000
SPCM_FEAT_DIG16_SMB = 0x00040000
SPCM_FEAT_DIG8_SMA = 0x00040000
SPCM_FEAT_DIG16_FX2 = 0x00080000
SPCM_FEAT_DIGITALBWFILTER = 0x00100000
SPCM_FEAT_CUSTOMMOD_MASK = 0xF0000000
SPCM_FEAT_EXTFW_SEGSTAT = 0x00000001
SPCM_FEAT_EXTFW_SEGAVERAGE = 0x00000002
SPCM_FEAT_EXTFW_BOXCAR = 0x00000004
SPCM_FEAT_EXTFW_PULSEGEN = 0x00000008
SPCM_FEAT_EXTFW_DDS = 0x00000010
SPCM_FEAT_EXTFW_EVALUATION = 0x80000000
ERRORTEXTLEN = 200
SPC_LASTERRORTEXT = 999996
SPC_LASTERRORVALUE = 999997
SPC_LASTERRORREG = 999998
SPC_LASTERRORCODE = 999999
COUPLING_DC = 0
COUPLING_AC = 1
SPC_COMMAND = 0
SPC_RESET = 0
SPC_SOFTRESET = 1
SPC_WRITESETUP = 2
SPC_START = 10
SPC_STARTANDWAIT = 11
SPC_FIFOSTART = 12
SPC_FIFOWAIT = 13
SPC_FIFOSTARTNOWAIT = 14
SPC_FORCETRIGGER = 16
SPC_STOP = 20
SPC_FLUSHFIFOBUFFER = 21
SPC_POWERDOWN = 30
SPC_SYNCMASTER = 100
SPC_SYNCTRIGGERMASTER = 101
SPC_SYNCMASTERFIFO = 102
SPC_SYNCSLAVE = 110
SPC_SYNCTRIGGERSLAVE = 111
SPC_SYNCSLAVEFIFO = 112
SPC_NOSYNC = 120
SPC_SYNCSTART = 130
SPC_SYNCCALCMASTER = 140
SPC_SYNCCALCMASTERFIFO = 141
SPC_PXIDIVIDERRESET = 150
SPC_RELAISON = 200
SPC_RELAISOFF = 210
SPC_ADJUSTSTART = 300
SPC_FIFO_BUFREADY0 = 400
SPC_FIFO_BUFREADY1 = 401
SPC_FIFO_BUFREADY2 = 402
SPC_FIFO_BUFREADY3 = 403
SPC_FIFO_BUFREADY4 = 404
SPC_FIFO_BUFREADY5 = 405
SPC_FIFO_BUFREADY6 = 406
SPC_FIFO_BUFREADY7 = 407
SPC_FIFO_BUFREADY8 = 408
SPC_FIFO_BUFREADY9 = 409
SPC_FIFO_BUFREADY10 = 410
SPC_FIFO_BUFREADY11 = 411
SPC_FIFO_BUFREADY12 = 412
SPC_FIFO_BUFREADY13 = 413
SPC_FIFO_BUFREADY14 = 414
SPC_FIFO_BUFREADY15 = 415
SPC_FIFO_AUTOBUFSTART = 500
SPC_FIFO_AUTOBUFEND = 510
SPC_STATUS = 10
SPC_RUN = 0
SPC_TRIGGER = 10
SPC_READY = 20
SPC_M2CMD = 100
M2CMD_CARD_RESET = 0x00000001
M2CMD_CARD_WRITESETUP = 0x00000002
M2CMD_CARD_START = 0x00000004
M2CMD_CARD_ENABLETRIGGER = 0x00000008
M2CMD_CARD_FORCETRIGGER = 0x00000010
M2CMD_CARD_DISABLETRIGGER = 0x00000020
M2CMD_CARD_STOP = 0x00000040
M2CMD_CARD_FLUSHFIFO = 0x00000080
M2CMD_CARD_INVALIDATEDATA = 0x00000100
M2CMD_CARD_INTERNALRESET = 0x00000200
M2CMD_ALL_STOP = 0x00440060
M2CMD_CARD_WAITPREFULL = 0x00001000
M2CMD_CARD_WAITTRIGGER = 0x00002000
M2CMD_CARD_WAITREADY = 0x00004000
M2CMD_DATA_STARTDMA = 0x00010000
M2CMD_DATA_WAITDMA = 0x00020000
M2CMD_DATA_STOPDMA = 0x00040000
M2CMD_DATA_POLL = 0x00080000
M2CMD_EXTRA_STARTDMA = 0x00100000
M2CMD_EXTRA_WAITDMA = 0x00200000
M2CMD_EXTRA_STOPDMA = 0x00400000
M2CMD_EXTRA_POLL = 0x00800000
M2CMD_DATA_SGFLUSH = 0x01000000
SPC_M2STATUS = 110
M2STAT_NONE = 0x00000000
M2STAT_CARD_PRETRIGGER = 0x00000001
M2STAT_CARD_TRIGGER = 0x00000002
M2STAT_CARD_READY = 0x00000004
M2STAT_CARD_SEGMENT_PRETRG = 0x00000008
M2STAT_DATA_BLOCKREADY = 0x00000100
M2STAT_DATA_END = 0x00000200
M2STAT_DATA_OVERRUN = 0x00000400
M2STAT_DATA_ERROR = 0x00000800
M2STAT_EXTRA_BLOCKREADY = 0x00001000
M2STAT_EXTRA_END = 0x00002000
M2STAT_EXTRA_OVERRUN = 0x00004000
M2STAT_EXTRA_ERROR = 0x00008000
M2STAT_TSCNT_OVERRUN = 0x00010000
M2STAT_INTERNALMASK = 0xff000000
M2STAT_INTERNAL_SYSLOCK = 0x02000000
SPC_REGISTER_LIST = 120
SPC_WRITE_TO_LOG = 121
SPC_DATA_AVAIL_USER_LEN = 200
SPC_DATA_AVAIL_USER_POS = 201
SPC_DATA_AVAIL_CARD_LEN = 202
SPC_DATA_OUTBUFSIZE = 209
SPC_ABA_AVAIL_USER_LEN = 210
SPC_ABA_AVAIL_USER_POS = 211
SPC_ABA_AVAIL_CARD_LEN = 212
SPC_TS_AVAIL_USER_LEN = 220
SPC_TS_AVAIL_USER_POS = 221
SPC_TS_AVAIL_CARD_LEN = 222
SPC_VERSION = 1000
SPC_ISAADR = 1010
SPC_INSTMEM = 1020
SPC_INSTSAMPLERATE = 1030
SPC_BRDTYP = 1040
SPC_MIINST_MODULES = 1100
SPC_MIINST_CHPERMODULE = 1110
SPC_MIINST_BYTESPERSAMPLE = 1120
SPC_MIINST_BITSPERSAMPLE = 1125
SPC_MIINST_MAXADCVALUE = 1126
SPC_MIINST_MINADCLOCK = 1130
SPC_MIINST_MAXADCLOCK = 1140
SPC_MIINST_MINEXTCLOCK = 1145
SPC_MIINST_MAXEXTCLOCK = 1146
SPC_MIINST_MINSYNCCLOCK = 1147
SPC_MIINST_MINEXTREFCLOCK = 1148
SPC_MIINST_MAXEXTREFCLOCK = 1149
SPC_MIINST_QUARZ = 1150
SPC_MIINST_QUARZ2 = 1151
SPC_MIINST_MINEXTCLOCK1 = 1152
SPC_MIINST_FLAGS = 1160
SPC_MIINST_FIFOSUPPORT = 1170
SPC_MIINST_ISDEMOCARD = 1175
SPC_GETDRVVERSION = 1200
SPC_GETKERNELVERSION = 1210
SPC_GETDRVTYPE = 1220
DRVTYP_DOS = 0
DRVTYP_LINUX32 = 1
DRVTYP_VXD = 2
DRVTYP_NTLEGACY = 3
DRVTYP_WDM32 = 4
DRVTYP_WDM64 = 5
DRVTYP_WOW64 = 6
DRVTYP_LINUX64 = 7
DRVTYP_QNX32 = 8
DRVTYP_QNX64 = 9
SPC_GETCOMPATIBILITYVERSION = 1230
SPC_GETMINDRVVERSION = 1240
SPC_PCITYP = 2000
SPC_FNCTYPE = 2001
SPCM_TYPE_AI = 0x01
SPCM_TYPE_AO = 0x02
SPCM_TYPE_DI = 0x04
SPCM_TYPE_DO = 0x08
SPCM_TYPE_DIO = 0x10
SPC_PCIVERSION = 2010
SPC_PCIEXTVERSION = 2011
SPC_PCIMODULEVERSION = 2012
SPC_PCIMODULEBVERSION = 2013
SPC_BASEPCBVERSION = 2014
SPC_MODULEPCBVERSION = 2015
SPC_MODULEAPCBVERSION = 2015
SPC_MODULEBPCBVERSION = 2016
SPC_EXTPCBVERSION = 2017
SPC_PCIDIGVERSION = 2018
SPC_DIGPCBVERSION = 2019
SPC_PCIDATE = 2020
SPC_CALIBDATE = 2025
SPC_CALIBDATEONBOARD = 2026
SPC_PCISERIALNR = 2030
SPC_PCISERIALNO = 2030
SPC_PCIHWBUSNO = 2040
SPC_PCIHWDEVNO = 2041
SPC_PCIHWFNCNO = 2042
SPC_PCIHWSLOTNO = 2043
SPC_PCIEXPGENERATION = 2050
SPC_PCIEXPLANES = 2051
SPC_PCIEXPPAYLOAD = 2052
SPC_PCIEXPREADREQUESTSIZE = 2053
SPC_PCIEXPREADCOMPLBOUNDARY = 2054
SPC_PXIHWSLOTNO = 2055
SPC_PCISAMPLERATE = 2100
SPC_PCIMEMSIZE = 2110
SPC_PCIFEATURES = 2120
SPC_PCIEXTFEATURES = 2121
SPC_PCIINFOADR = 2200
SPC_PCIINTERRUPT = 2300
SPC_PCIBASEADR0 = 2400
SPC_PCIBASEADR1 = 2401
SPC_PCIREGION0 = 2410
SPC_PCIREGION1 = 2411
SPC_READTRGLVLCOUNT = 2500
SPC_READIRCOUNT = 3000
SPC_READUNIPOLAR0 = 3010
SPC_READUNIPOLAR1 = 3020
SPC_READUNIPOLAR2 = 3030
SPC_READUNIPOLAR3 = 3040
SPC_READMAXOFFSET = 3100
SPC_READAIFEATURES = 3101
SPCM_AI_TERM = 0x00000001
SPCM_AI_SE = 0x00000002
SPCM_AI_DIFF = 0x00000004
SPCM_AI_OFFSPERCENT = 0x00000008
SPCM_AI_OFFSMV = 0x00000010
SPCM_AI_OVERRANGEDETECT = 0x00000020
SPCM_AI_DCCOUPLING = 0x00000040
SPCM_AI_ACCOUPLING = 0x00000080
SPCM_AI_LOWPASS = 0x00000100
SPCM_AI_ACDC_OFFS_COMP = 0x00000200
SPCM_AI_DIFFMUX = 0x00000400
SPCM_AI_GLOBALLOWPASS = 0x00000800
SPCM_AI_AUTOCALOFFS = 0x00001000
SPCM_AI_AUTOCALGAIN = 0x00002000
SPCM_AI_AUTOCALOFFSNOIN = 0x00004000
SPCM_AI_HIGHIMP = 0x00008000
SPCM_AI_LOWIMP = 0x00010000
SPCM_AI_DIGITALLOWPASS = 0x00020000
SPCM_AI_INDIVPULSEWIDTH = 0x00100000
SPC_READAOFEATURES = 3102
SPCM_AO_SE = 0x00000002
SPCM_AO_DIFF = 0x00000004
SPCM_AO_PROGFILTER = 0x00000008
SPCM_AO_PROGOFFSET = 0x00000010
SPCM_AO_PROGGAIN = 0x00000020
SPCM_AO_PROGSTOPLEVEL = 0x00000040
SPCM_AO_DOUBLEOUT = 0x00000080
SPCM_AO_ENABLEOUT = 0x00000100
SPC_READDIFEATURES = 3103
SPCM_DI_TERM = 0x00000001
SPCM_DI_SE = 0x00000002
SPCM_DI_DIFF = 0x00000004
SPCM_DI_PROGTHRESHOLD = 0x00000008
SPCM_DI_HIGHIMP = 0x00000010
SPCM_DI_LOWIMP = 0x00000020
SPCM_DI_INDIVPULSEWIDTH = 0x00100000
SPCM_DI_IOCHANNEL = 0x00200000
SPC_READDOFEATURES = 3104
SPCM_DO_SE = 0x00000002
SPCM_DO_DIFF = 0x00000004
SPCM_DO_PROGSTOPLEVEL = 0x00000008
SPCM_DO_PROGOUTLEVELS = 0x00000010
SPCM_DO_ENABLEMASK = 0x00000020
SPCM_DO_IOCHANNEL = 0x00200000
SPC_READCHGROUPING = 3110
SPC_READAIPATHCOUNT = 3120
SPC_READAIPATH = 3121
SPCM_CUSTOMMOD = 3130
SPCM_CUSTOMMOD_BASE_MASK = 0x000000FF
SPCM_CUSTOMMOD_MODULE_MASK = 0x0000FF00
SPCM_CUSTOMMOD_STARHUB_MASK = 0x00FF0000
SPC_READRANGECH0_0 = 3200
SPC_READRANGECH0_1 = 3201
SPC_READRANGECH0_2 = 3202
SPC_READRANGECH0_3 = 3203
SPC_READRANGECH0_4 = 3204
SPC_READRANGECH0_5 = 3205
SPC_READRANGECH0_6 = 3206
SPC_READRANGECH0_7 = 3207
SPC_READRANGECH0_8 = 3208
SPC_READRANGECH0_9 = 3209
SPC_READRANGECH1_0 = 3300
SPC_READRANGECH1_1 = 3301
SPC_READRANGECH1_2 = 3302
SPC_READRANGECH1_3 = 3303
SPC_READRANGECH1_4 = 3304
SPC_READRANGECH1_5 = 3305
SPC_READRANGECH1_6 = 3306
SPC_READRANGECH1_7 = 3307
SPC_READRANGECH1_8 = 3308
SPC_READRANGECH1_9 = 3309
SPC_READRANGECH2_0 = 3400
SPC_READRANGECH2_1 = 3401
SPC_READRANGECH2_2 = 3402
SPC_READRANGECH2_3 = 3403
SPC_READRANGECH3_0 = 3500
SPC_READRANGECH3_1 = 3501
SPC_READRANGECH3_2 = 3502
SPC_READRANGECH3_3 = 3503
SPC_READRANGEMIN0 = 4000
SPC_READRANGEMIN99 = 4099
SPC_READRANGEMAX0 = 4100
SPC_READRANGEMAX99 = 4199
SPC_READOFFSMIN0 = 4200
SPC_READOFFSMIN99 = 4299
SPC_READOFFSMAX0 = 4300
SPC_READOFFSMAX99 = 4399
SPC_PCICOUNTER = 9000
SPC_BUFFERPOS = 9010
SPC_READAOGAINMIN = 9100
SPC_READAOGAINMAX = 9110
SPC_READAOOFFSETMIN = 9120
SPC_READAOOFFSETMAX = 9130
SPC_CARDMODE = 9500
SPC_AVAILCARDMODES = 9501
SPC_REC_STD_SINGLE = 0x00000001
SPC_REC_STD_MULTI = 0x00000002
SPC_REC_STD_GATE = 0x00000004
SPC_REC_STD_ABA = 0x00000008
SPC_REC_STD_SEGSTATS = 0x00010000
SPC_REC_STD_AVERAGE = 0x00020000
SPC_REC_STD_AVERAGE_16BIT = 0x00080000
SPC_REC_STD_BOXCAR = 0x00800000
SPC_REC_FIFO_SINGLE = 0x00000010
SPC_REC_FIFO_MULTI = 0x00000020
SPC_REC_FIFO_GATE = 0x00000040
SPC_REC_FIFO_ABA = 0x00000080
SPC_REC_FIFO_SEGSTATS = 0x00100000
SPC_REC_FIFO_AVERAGE = 0x00200000
SPC_REC_FIFO_AVERAGE_16BIT = 0x00400000
SPC_REC_FIFO_BOXCAR = 0x01000000
SPC_REC_FIFO_SINGLE_MONITOR = 0x02000000
SPC_REP_STD_SINGLE = 0x00000100
SPC_REP_STD_MULTI = 0x00000200
SPC_REP_STD_GATE = 0x00000400
SPC_REP_FIFO_SINGLE = 0x00000800
SPC_REP_FIFO_MULTI = 0x00001000
SPC_REP_FIFO_GATE = 0x00002000
SPC_REP_STD_CONTINUOUS = 0x00004000
SPC_REP_STD_SINGLERESTART = 0x00008000
SPC_REP_STD_SEQUENCE = 0x00040000
SPC_REP_STD_DDS = 0x04000000
SPC_DEMOWAVEFORM = 9600
SPC_AVAILDEMOWAVEFORMS = 9601
SPCM_DEMOWAVEFORM_SINE = 0x00000001
SPCM_DEMOWAVEFORM_RECT = 0x00000002
SPCM_DEMOWAVEFORM_TRIANGLE = 0x00000004
SPC_MEMSIZE = 10000
SPC_SEGMENTSIZE = 10010
SPC_LOOPS = 10020
SPC_PRETRIGGER = 10030
SPC_ABADIVIDER = 10040
SPC_AVERAGES = 10050
SPC_BOX_AVERAGES = 10060
SPC_SEGSPLIT_START = 10070
SPC_SEGSPLIT_PAUSE = 10071
SPC_POSTTRIGGER = 10100
SPC_STARTOFFSET = 10200
SPC_AVAILMEMSIZE_MIN = 10201
SPC_AVAILMEMSIZE_MAX = 10202
SPC_AVAILMEMSIZE_STEP = 10203
SPC_AVAILPOSTTRIGGER_MIN = 10204
SPC_AVAILPOSTTRIGGER_MAX = 10205
SPC_AVAILPOSTTRIGGER_STEP = 10206
SPC_AVAILABADIVIDER_MIN = 10207
SPC_AVAILABADIVIDER_MAX = 10208
SPC_AVAILABADIVIDER_STEP = 10209
SPC_AVAILLOOPS_MIN = 10210
SPC_AVAILLOOPS_MAX = 10211
SPC_AVAILLOOPS_STEP = 10212
SPC_AVAILAVERAGES_MIN = 10220
SPC_AVAILAVERAGES_MAX = 10221
SPC_AVAILAVERAGES_STEP = 10222
SPC_AVAILAVRGSEGSIZE_MIN = 10223
SPC_AVAILAVRGSEGSIZE_MAX = 10224
SPC_AVAILAVRGSEGSIZE_STEP = 10225
SPC_AVAILAVERAGES16BIT_MIN = 10226
SPC_AVAILAVERAGES16BIT_MAX = 10227
SPC_AVAILAVERAGES16BIT_STEP = 10228
SPC_AVAILAVRG16BITSEGSIZE_MIN = 10229
SPC_AVAILAVRG16BITSEGSIZE_MAX = 10230
SPC_AVAILAVRG16BITSEGSIZE_STEP = 10231
SPC_AVAILBOXCARAVERAGES_MIN = 10232
SPC_AVAILBOXCARAVERAGES_MAX = 10233
SPC_AVAILBOXCARAVERAGES_STEPFACTOR = 10234
SPC_CHENABLE = 11000
SPC_CHCOUNT = 11001
SPC_CHMODACOUNT = 11100
SPC_CHMODBCOUNT = 11101
CHANNEL0 = 0x00000001
CHANNEL1 = 0x00000002
CHANNEL2 = 0x00000004
CHANNEL3 = 0x00000008
CHANNEL4 = 0x00000010
CHANNEL5 = 0x00000020
CHANNEL6 = 0x00000040
CHANNEL7 = 0x00000080
CHANNEL8 = 0x00000100
CHANNEL9 = 0x00000200
CHANNEL10 = 0x00000400
CHANNEL11 = 0x00000800
CHANNEL12 = 0x00001000
CHANNEL13 = 0x00002000
CHANNEL14 = 0x00004000
CHANNEL15 = 0x00008000
CHANNEL16 = 0x00010000
CHANNEL17 = 0x00020000
CHANNEL18 = 0x00040000
CHANNEL19 = 0x00080000
CHANNEL20 = 0x00100000
CHANNEL21 = 0x00200000
CHANNEL22 = 0x00400000
CHANNEL23 = 0x00800000
CHANNEL24 = 0x01000000
CHANNEL25 = 0x02000000
CHANNEL26 = 0x04000000
CHANNEL27 = 0x08000000
CHANNEL28 = 0x10000000
CHANNEL29 = 0x20000000
CHANNEL30 = 0x40000000
CHANNEL31 = 0x80000000
CH0_8BITMODE = 65536
CH0_16BIT = 1
CH0_32BIT = 3
CH1_16BIT = 4
CH1_32BIT = 12
MOD0_8BIT = 1
MOD0_16BIT = 3
MOD0_32BIT = 15
MOD1_8BIT = 16
MOD1_16BIT = 48
MOD1_32BIT = 240
SPC_CHROUTE0 = 11010
SPC_CHROUTE1 = 11020
SPC_BITENABLE = 11030
SPC_SAMPLERATE = 20000
SPC_SYNCCLOCK = 20005
SPC_SAMPLERATE2 = 20010
SPC_SR2 = 20020
SPC_PLL_ENABLE = 20030
SPC_PLL_ISLOCKED = 20031
SPC_CLOCKDIV = 20040
SPC_INTCLOCKDIV = 20041
SPC_PXICLOCKDIV = 20042
SPC_PLL_R = 20060
SPC_PLL_F = 20061
SPC_PLL_S = 20062
SPC_PLL_DIV = 20063
SPC_PXI_CLK_OUT = 20090
SPC_EXTERNALCLOCK = 20100
SPC_EXTERNOUT = 20110
SPC_CLOCKOUT = 20110
SPC_CLOCKOUTFREQUENCY = 20111
SPC_CLOCK50OHM = 20120
SPC_CLOCK110OHM = 20120
SPC_CLOCK75OHM = 20120
SPC_STROBE75OHM = 20121
SPC_EXTERNRANGE = 20130
SPC_EXTRANGESHDIRECT = 20131
EXRANGE_NONE = 0
EXRANGE_NOPLL = 1
EXRANGE_SINGLE = 2
EXRANGE_BURST_S = 4
EXRANGE_BURST_M = 8
EXRANGE_BURST_L = 16
EXRANGE_BURST_XL = 32
EXRANGE_LOW = 64
EXRANGE_HIGH = 128
EXRANGE_LOW_DPS = 256
SPC_REFERENCECLOCK = 20140
REFCLOCK_PXI = -1
SPC_CLOCKMODE = 20200
SPC_AVAILCLOCKMODES = 20201
SPC_CM_INTPLL = 0x00000001
SPC_CM_QUARTZ1 = 0x00000002
SPC_CM_QUARTZ2 = 0x00000004
SPC_CM_EXTERNAL = 0x00000008
SPC_CM_EXTERNAL0 = 0x00000008
SPC_CM_EXTDIVIDER = 0x00000010
SPC_CM_EXTREFCLOCK = 0x00000020
SPC_CM_PXIREFCLOCK = 0x00000040
SPC_CM_SHDIRECT = 0x00000080
SPC_CM_QUARTZ2_DIRSYNC = 0x00000100
SPC_CM_QUARTZ1_DIRSYNC = 0x00000200
SPC_CM_EXTERNAL1 = 0x00000400
SPC_CM_SYNCINT = 0x01000000
SPC_CM_SYNCEXT = 0x02000000
SPC_CLOCK_READFEATURES = 20205
SPC_CLOCK_READFEATURES0 = 20205
SPC_CLOCK_READFEATURES1 = 20206
SPCM_CKFEAT_TERM = 0x00000001
SPCM_CKFEAT_HIGHIMP = 0x00000002
SPCM_CKFEAT_DCCOUPLING = 0x00000004
SPCM_CKFEAT_ACCOUPLING = 0x00000008
SPCM_CKFEAT_SE = 0x00000010
SPCM_CKFEAT_DIFF = 0x00000020
SPCM_CKFEAT_PROGEDGE = 0x00000040
SPCM_CKFEAT_LEVELPROG = 0x00000100
SPCM_CKFEAT_PROGTHRESHOLD = 0x00000200
SPCM_CKFEAT_PROGDELAY = 0x00000400
SPC_BURSTSYSCLOCKMODE = 20210
SPC_SYNCMASTERSYSCLOCKMODE = 20211
SPC_CLOCK_SETUP_CHANGED = 20212
SPC_CLOCK_AVAILDELAY_MIN = 20220
SPC_CLOCK_AVAILDELAY_MAX = 20221
SPC_CLOCK_AVAILDELAY_STEP = 20222
SPC_CLOCK_DELAY = 20223
SPC_AVAILCLOCKEDGES = 20224
SPCM_EDGE_FALLING = 0x00000001
SPCM_EDGE_RISING = 0x00000002
SPCM_BOTH_EDGES = 0x00000004
SPCM_EDGES_BOTH = 0x00000004
SPC_CLOCK_EDGE = 20225
SPC_CHANNELMUXINFO = 20300
SPCM_MUX_NONE = 0x00000000
SPCM_MUX_MUXONMOD = 0x00000001
SPCM_MUX_INVERTCLKONMOD = 0x00000002
SPCM_MUX_DLY = 0x00000003
SPCM_MUX_DLYANDMUXONMOD = 0x00000004
SPCM_MUX_MUXBETWEENMODS = 0x00000005
SPCM_MUX_MUXONMOD2CH = 0x00000006
SPCM_MUX_MAX4CH = 0x00000007
SPC_OFFS0 = 30000
SPC_AMP0 = 30010
SPC_ACDC0 = 30020
SPC_ACDC_OFFS_COMPENSATION0 = 30021
SPC_50OHM0 = 30030
SPC_DIFF0 = 30040
SPC_DOUBLEOUT0 = 30041
SPC_DIGITAL0 = 30050
SPC_110OHM0 = 30060
SPC_110OHM0L = 30060
SPC_75OHM0 = 30060
SPC_INOUT0 = 30070
SPC_FILTER0 = 30080
SPC_BANKSWITCH0 = 30081
SPC_PATH0 = 30090
SPC_ENABLEOUT0 = 30091
SPC_OFFS1 = 30100
SPC_AMP1 = 30110
SPC_ACDC1 = 30120
SPC_ACDC_OFFS_COMPENSATION1 = 30121
SPC_50OHM1 = 30130
SPC_DIFF1 = 30140
SPC_DOUBLEOUT1 = 30141
SPC_DIGITAL1 = 30150
SPC_110OHM1 = 30160
SPC_110OHM0H = 30160
SPC_75OHM1 = 30160
SPC_INOUT1 = 30170
SPC_FILTER1 = 30180
SPC_BANKSWITCH1 = 30181
SPC_PATH1 = 30190
SPC_ENABLEOUT1 = 30191
SPC_OFFS2 = 30200
SPC_AMP2 = 30210
SPC_ACDC2 = 30220
SPC_ACDC_OFFS_COMPENSATION2 = 30221
SPC_50OHM2 = 30230
SPC_DIFF2 = 30240
SPC_DOUBLEOUT2 = 30241
SPC_110OHM2 = 30260
SPC_110OHM1L = 30260
SPC_75OHM2 = 30260
SPC_INOUT2 = 30270
SPC_FILTER2 = 30280
SPC_BANKSWITCH2 = 30281
SPC_PATH2 = 30290
SPC_ENABLEOUT2 = 30291
SPC_OFFS3 = 30300
SPC_AMP3 = 30310
SPC_ACDC3 = 30320
SPC_ACDC_OFFS_COMPENSATION3 = 30321
SPC_50OHM3 = 30330
SPC_DIFF3 = 30340
SPC_DOUBLEOUT3 = 30341
SPC_110OHM3 = 30360
SPC_110OHM1H = 30360
SPC_75OHM3 = 30360
SPC_INOUT3 = 30370
SPC_FILTER3 = 30380
SPC_BANKSWITCH3 = 30381
SPC_PATH3 = 30390
SPC_ENABLEOUT3 = 30391
SPC_OFFS4 = 30400
SPC_AMP4 = 30410
SPC_ACDC4 = 30420
SPC_50OHM4 = 30430
SPC_DIFF4 = 30440
SPC_DOUBLEOUT4 = 30441
SPC_FILTER4 = 30480
SPC_ENABLEOUT4 = 30491
SPC_PATH4 = 30490
SPC_OFFS5 = 30500
SPC_AMP5 = 30510
SPC_ACDC5 = 30520
SPC_50OHM5 = 30530
SPC_DIFF5 = 30540
SPC_DOUBLEOUT5 = 30541
SPC_FILTER5 = 30580
SPC_ENABLEOUT5 = 30591
SPC_PATH5 = 30590
SPC_OFFS6 = 30600
SPC_AMP6 = 30610
SPC_ACDC6 = 30620
SPC_50OHM6 = 30630
SPC_DIFF6 = 30640
SPC_DOUBLEOUT6 = 30641
SPC_FILTER6 = 30680
SPC_ENABLEOUT6 = 30691
SPC_PATH6 = 30690
SPC_OFFS7 = 30700
SPC_AMP7 = 30710
SPC_ACDC7 = 30720
SPC_50OHM7 = 30730
SPC_DIFF7 = 30740
SPC_DOUBLEOUT7 = 30741
SPC_FILTER7 = 30780
SPC_ENABLEOUT7 = 30791
SPC_PATH7 = 30790
SPC_OFFS8 = 30800
SPC_AMP8 = 30810
SPC_ACDC8 = 30820
SPC_50OHM8 = 30830
SPC_DIFF8 = 30840
SPC_PATH8 = 30890
SPC_OFFS9 = 30900
SPC_AMP9 = 30910
SPC_ACDC9 = 30920
SPC_50OHM9 = 30930
SPC_DIFF9 = 30940
SPC_PATH9 = 30990
SPC_OFFS10 = 31000
SPC_AMP10 = 31010
SPC_ACDC10 = 31020
SPC_50OHM10 = 31030
SPC_DIFF10 = 31040
SPC_PATH10 = 31090
SPC_OFFS11 = 31100
SPC_AMP11 = 31110
SPC_ACDC11 = 31120
SPC_50OHM11 = 31130
SPC_DIFF11 = 31140
SPC_PATH11 = 31190
SPC_OFFS12 = 31200
SPC_AMP12 = 31210
SPC_ACDC12 = 31220
SPC_50OHM12 = 31230
SPC_DIFF12 = 31240
SPC_PATH12 = 31290
SPC_OFFS13 = 31300
SPC_AMP13 = 31310
SPC_ACDC13 = 31320
SPC_50OHM13 = 31330
SPC_DIFF13 = 31340
SPC_PATH13 = 31390
SPC_OFFS14 = 31400
SPC_AMP14 = 31410
SPC_ACDC14 = 31420
SPC_50OHM14 = 31430
SPC_DIFF14 = 31440
SPC_PATH14 = 31490
SPC_OFFS15 = 31500
SPC_AMP15 = 31510
SPC_ACDC15 = 31520
SPC_50OHM15 = 31530
SPC_DIFF15 = 31540
SPC_PATH15 = 31590
SPC_110OHMTRIGGER = 30400
SPC_110OHMCLOCK = 30410
AMP_BI200 = 200
AMP_BI500 = 500
AMP_BI1000 = 1000
AMP_BI2000 = 2000
AMP_BI2500 = 2500
AMP_BI4000 = 4000
AMP_BI5000 = 5000
AMP_BI10000 = 10000
AMP_UNI400 = 100400
AMP_UNI1000 = 101000
AMP_UNI2000 = 102000
SPC_TRIGGERMODE = 40000
SPC_TRIG_OUTPUT = 40100
SPC_TRIGGEROUT = 40100
SPC_TRIG_TERM = 40110
SPC_TRIG_TERM0 = 40110
SPC_TRIGGER50OHM = 40110
SPC_TRIGGER110OHM0 = 40110
SPC_TRIGGER75OHM0 = 40110
SPC_TRIG_TERM1 = 40111
SPC_TRIGGER110OHM1 = 40111
SPC_TRIG_EXT0_ACDC = 40120
SPC_TRIG_EXT1_ACDC = 40121
SPC_TRIG_EXT2_ACDC = 40122
SPC_TRIGGERMODE0 = 40200
SPC_TRIGGERMODE1 = 40201
SPC_TRIGGERMODE2 = 40202
SPC_TRIGGERMODE3 = 40203
SPC_TRIGGERMODE4 = 40204
SPC_TRIGGERMODE5 = 40205
SPC_TRIGGERMODE6 = 40206
SPC_TRIGGERMODE7 = 40207
SPC_TRIGGERMODE8 = 40208
SPC_TRIGGERMODE9 = 40209
SPC_TRIGGERMODE10 = 40210
SPC_TRIGGERMODE11 = 40211
SPC_TRIGGERMODE12 = 40212
SPC_TRIGGERMODE13 = 40213
SPC_TRIGGERMODE14 = 40214
SPC_TRIGGERMODE15 = 40215
TM_SOFTWARE = 0
TM_NOTRIGGER = 10
TM_CHXPOS = 10000
TM_CHXPOS_LP = 10001
TM_CHXPOS_SP = 10002
TM_CHXPOS_GS = 10003
TM_CHXPOS_SS = 10004
TM_CHXNEG = 10010
TM_CHXNEG_LP = 10011
TM_CHXNEG_SP = 10012
TM_CHXNEG_GS = 10013
TM_CHXNEG_SS = 10014
TM_CHXOFF = 10020
TM_CHXBOTH = 10030
TM_CHXWINENTER = 10040
TM_CHXWINENTER_LP = 10041
TM_CHXWINENTER_SP = 10042
TM_CHXWINLEAVE = 10050
TM_CHXWINLEAVE_LP = 10051
TM_CHXWINLEAVE_SP = 10052
TM_CHXLOW = 10060
TM_CHXHIGH = 10061
TM_CHXINWIN = 10062
TM_CHXOUTWIN = 10063
TM_CHXSPIKE = 10064
TM_CH0POS = 10000
TM_CH0NEG = 10010
TM_CH0OFF = 10020
TM_CH0BOTH = 10030
TM_CH1POS = 10100
TM_CH1NEG = 10110
TM_CH1OFF = 10120
TM_CH1BOTH = 10130
TM_CH2POS = 10200
TM_CH2NEG = 10210
TM_CH2OFF = 10220
TM_CH2BOTH = 10230
TM_CH3POS = 10300
TM_CH3NEG = 10310
TM_CH3OFF = 10320
TM_CH3BOTH = 10330
TM_TTLPOS = 20000
TM_TTLHIGH_LP = 20001
TM_TTLHIGH_SP = 20002
TM_TTLNEG = 20010
TM_TTLLOW_LP = 20011
TM_TTLLOW_SP = 20012
TM_TTL = 20020
TM_TTLBOTH = 20030
TM_TTLBOTH_LP = 20031
TM_TTLBOTH_SP = 20032
TM_CHANNEL = 20040
TM_TTLHIGH = 20050
TM_TTLLOW = 20051
TM_PATTERN = 21000
TM_PATTERN_LP = 21001
TM_PATTERN_SP = 21002
TM_PATTERNANDEDGE = 22000
TM_PATTERNANDEDGE_LP = 22001
TM_PATTERNANDEDGE_SP = 22002
TM_GATELOW = 30000
TM_GATEHIGH = 30010
TM_GATEPATTERN = 30020
TM_CHOR = 35000
TM_CHAND = 35010
TM_CHORTTLPOS = 35020
TM_CHORTTLNEG = 35021
SPC_PXITRGOUT = 40300
PTO_OFF = 0
PTO_LINE0 = 1
PTO_LINE1 = 2
PTO_LINE2 = 3
PTO_LINE3 = 4
PTO_LINE4 = 5
PTO_LINE5 = 6
PTO_LINE6 = 7
PTO_LINE7 = 8
PTO_LINESTAR = 9
SPC_PXITRGOUT_AVAILABLE = 40301
SPC_PXISTARTRG_DIVRST_OUT = 40302
SPC_PXISTARTRG_DIVRST_OUT_AVAILABLE = 40303
SPC_PXISTARTRG_OUT = 40304
PSTO_LINESTAR0 = 0x00000001
PSTO_LINESTAR1 = 0x00000002
PSTO_LINESTAR2 = 0x00000004
PSTO_LINESTAR3 = 0x00000008
PSTO_LINESTAR4 = 0x00000010
PSTO_LINESTAR5 = 0x00000020
PSTO_LINESTAR6 = 0x00000040
PSTO_LINESTAR7 = 0x00000080
PSTO_LINESTAR8 = 0x00000100
PSTO_LINESTAR9 = 0x00000200
PSTO_LINESTAR10 = 0x00000400
PSTO_LINESTAR11 = 0x00000800
PSTO_LINESTAR12 = 0x00001000
PSTO_LINE0 = 0x00010000
PSTO_LINE1 = 0x00020000
PSTO_LINE2 = 0x00040000
PSTO_LINE3 = 0x00080000
PSTO_LINE4 = 0x00100000
PSTO_LINE5 = 0x00200000
PSTO_LINE6 = 0x00400000
PSTO_LINE7 = 0x00800000
SPC_PXISTARTRG_OUT_AVAILABLE = 40305
SPC_PXITRGIN = 40310
PTI_OFF = 0
PTI_LINE0 = 1
PTI_LINE1 = 2
PTI_LINE2 = 4
PTI_LINE3 = 8
PTI_LINE4 = 16
PTI_LINE5 = 32
PTI_LINE6 = 64
PTI_LINE7 = 128
PTI_LINESTAR = 256
SPC_PXITRGIN_AVAILABLE = 40311
SPC_PXI_DIVIDER_RESET_IN = 40320
SPC_PXI_DIVIDER_RESET_IN_AVAILABLE = 40321
SPC_TRIG_AVAILORMASK = 40400
SPC_TRIG_ORMASK = 40410
SPC_TRIG_AVAILANDMASK = 40420
SPC_TRIG_ANDMASK = 40430
SPC_TMASK_NONE = 0x00000000
SPC_TMASK_SOFTWARE = 0x00000001
SPC_TMASK_EXT0 = 0x00000002
SPC_TMASK_EXT1 = 0x00000004
SPC_TMASK_EXT2 = 0x00000008
SPC_TMASK_EXT3 = 0x00000010
SPC_TMASK_EXT4 = 0x00000020
SPC_TMASK_XIO0 = 0x00000100
SPC_TMASK_XIO1 = 0x00000200
SPC_TMASK_XIO2 = 0x00000400
SPC_TMASK_XIO3 = 0x00000800
SPC_TMASK_XIO4 = 0x00001000
SPC_TMASK_XIO5 = 0x00002000
SPC_TMASK_XIO6 = 0x00004000
SPC_TMASK_XIO7 = 0x00008000
SPC_TMASK_PXI0 = 0x00100000
SPC_TMASK_PXI1 = 0x00200000
SPC_TMASK_PXI2 = 0x00400000
SPC_TMASK_PXI3 = 0x00800000
SPC_TMASK_PXI4 = 0x01000000
SPC_TMASK_PXI5 = 0x02000000
SPC_TMASK_PXI6 = 0x04000000
SPC_TMASK_PXI7 = 0x08000000
SPC_TMASK_PXISTAR = 0x10000000
SPC_TMASK_PXIDSTARB = 0x20000000
SPC_TRIG_CH_AVAILORMASK0 = 40450
SPC_TRIG_CH_AVAILORMASK1 = 40451
SPC_TRIG_CH_ORMASK0 = 40460
SPC_TRIG_CH_ORMASK1 = 40461
SPC_TRIG_CH_AVAILANDMASK0 = 40470
SPC_TRIG_CH_AVAILANDMASK1 = 40471
SPC_TRIG_CH_ANDMASK0 = 40480
SPC_TRIG_CH_ANDMASK1 = 40481
SPC_TMASK0_NONE = 0x00000000
SPC_TMASK0_CH0 = 0x00000001
SPC_TMASK0_CH1 = 0x00000002
SPC_TMASK0_CH2 = 0x00000004
SPC_TMASK0_CH3 = 0x00000008
SPC_TMASK0_CH4 = 0x00000010
SPC_TMASK0_CH5 = 0x00000020
SPC_TMASK0_CH6 = 0x00000040
SPC_TMASK0_CH7 = 0x00000080
SPC_TMASK0_CH8 = 0x00000100
SPC_TMASK0_CH9 = 0x00000200
SPC_TMASK0_CH10 = 0x00000400
SPC_TMASK0_CH11 = 0x00000800
SPC_TMASK0_CH12 = 0x00001000
SPC_TMASK0_CH13 = 0x00002000
SPC_TMASK0_CH14 = 0x00004000
SPC_TMASK0_CH15 = 0x00008000
SPC_TMASK0_CH16 = 0x00010000
SPC_TMASK0_CH17 = 0x00020000
SPC_TMASK0_CH18 = 0x00040000
SPC_TMASK0_CH19 = 0x00080000
SPC_TMASK0_CH20 = 0x00100000
SPC_TMASK0_CH21 = 0x00200000
SPC_TMASK0_CH22 = 0x00400000
SPC_TMASK0_CH23 = 0x00800000
SPC_TMASK0_CH24 = 0x01000000
SPC_TMASK0_CH25 = 0x02000000
SPC_TMASK0_CH26 = 0x04000000
SPC_TMASK0_CH27 = 0x08000000
SPC_TMASK0_CH28 = 0x10000000
SPC_TMASK0_CH29 = 0x20000000
SPC_TMASK0_CH30 = 0x40000000
SPC_TMASK0_CH31 = 0x80000000
SPC_TMASK1_NONE = 0x00000000
SPC_TMASK1_CH32 = 0x00000001
SPC_TMASK1_CH33 = 0x00000002
SPC_TMASK1_CH34 = 0x00000004
SPC_TMASK1_CH35 = 0x00000008
SPC_TMASK1_CH36 = 0x00000010
SPC_TMASK1_CH37 = 0x00000020
SPC_TMASK1_CH38 = 0x00000040
SPC_TMASK1_CH39 = 0x00000080
SPC_TMASK1_CH40 = 0x00000100
SPC_TMASK1_CH41 = 0x00000200
SPC_TMASK1_CH42 = 0x00000400
SPC_TMASK1_CH43 = 0x00000800
SPC_TMASK1_CH44 = 0x00001000
SPC_TMASK1_CH45 = 0x00002000
SPC_TMASK1_CH46 = 0x00004000
SPC_TMASK1_CH47 = 0x00008000
SPC_TMASK1_CH48 = 0x00010000
SPC_TMASK1_CH49 = 0x00020000
SPC_TMASK1_CH50 = 0x00040000
SPC_TMASK1_CH51 = 0x00080000
SPC_TMASK1_CH52 = 0x00100000
SPC_TMASK1_CH53 = 0x00200000
SPC_TMASK1_CH54 = 0x00400000
SPC_TMASK1_CH55 = 0x00800000
SPC_TMASK1_CH56 = 0x01000000
SPC_TMASK1_CH57 = 0x02000000
SPC_TMASK1_CH58 = 0x04000000
SPC_TMASK1_CH59 = 0x08000000
SPC_TMASK1_CH60 = 0x10000000
SPC_TMASK1_CH61 = 0x20000000
SPC_TMASK1_CH62 = 0x40000000
SPC_TMASK1_CH63 = 0x80000000
SPC_TRIG_EXT_AVAILMODES = 40500
SPC_TRIG_EXT0_AVAILMODES = 40500
SPC_TRIG_EXT1_AVAILMODES = 40501
SPC_TRIG_EXT2_AVAILMODES = 40502
SPC_TRIG_EXT0_AVAILMODESOR = 40503
SPC_TRIG_EXT1_AVAILMODESOR = 40504
SPC_TRIG_EXT2_AVAILMODESOR = 40505
SPC_TRIG_EXT0_AVAILMODESAND = 40506
SPC_TRIG_EXT1_AVAILMODESAND = 40507
SPC_TRIG_EXT2_AVAILMODESAND = 40508
SPC_TRIG_EXT3_AVAILMODESAND = 40509
SPC_TRIG_EXT0_MODE = 40510
SPC_TRIG_EXT1_MODE = 40511
SPC_TRIG_EXT2_MODE = 40512
SPC_TRIG_EXT3_MODE = 40513
SPC_TRIG_EXT3_AVAILMODES = 40514
SPC_TRIG_EXT3_AVAILMODESOR = 40515
SPC_TRIG_EXT4_AVAILMODES = 40516
SPC_TRIG_EXT4_AVAILMODESOR = 40517
SPC_TRIG_EXT4_AVAILMODESAND = 40518
SPC_TRIG_EXT4_MODE = 40519
SPC_TRIG_EXT0_READFEATURES = 40520
SPC_TRIG_EXT1_READFEATURES = 40521
SPC_TRIG_EXT2_READFEATURES = 40522
SPC_TRIG_EXT3_READFEATURES = 40523
SPC_TRIG_EXT4_READFEATURES = 40524
SPCM_TRFEAT_TERM = 0x00000001
SPCM_TRFEAT_HIGHIMP = 0x00000002
SPCM_TRFEAT_DCCOUPLING = 0x00000004
SPCM_TRFEAT_ACCOUPLING = 0x00000008
SPCM_TRFEAT_SE = 0x00000010
SPCM_TRFEAT_DIFF = 0x00000020
SPCM_TRFEAT_LEVELPROG = 0x00000100
SPCM_TRFEAT_PROGTHRESHOLD = 0x00000200
SPC_LEGACY_X0_READFEATURES = 40530
SPC_LEGACY_X1_READFEATURES = 40531
SPC_LEGACY_X2_READFEATURES = 40532
SPC_LEGACY_X3_READFEATURES = 40533
SPC_LEGACY_X0_TERM = 40535
SPC_LEGACY_X1_TERM = 40536
SPC_LEGACY_X2_TERM = 40537
SPC_LEGACY_X3_TERM = 40538
SPC_TRIG_XIO_AVAILMODES = 40550
SPC_TRIG_XIO_AVAILMODESOR = 40551
SPC_TRIG_XIO_AVAILMODESAND = 40552
SPC_TRIG_XIO0_MODE = 40560
SPC_TRIG_XIO1_MODE = 40561
SPC_TM_MODEMASK = 0x00FFFFFF
SPC_TM_NONE = 0x00000000
SPC_TM_POS = 0x00000001
SPC_TM_NEG = 0x00000002
SPC_TM_BOTH = 0x00000004
SPC_TM_HIGH = 0x00000008
SPC_TM_LOW = 0x00000010
SPC_TM_WINENTER = 0x00000020
SPC_TM_WINLEAVE = 0x00000040
SPC_TM_INWIN = 0x00000080
SPC_TM_OUTSIDEWIN = 0x00000100
SPC_TM_SPIKE = 0x00000200
SPC_TM_PATTERN = 0x00000400
SPC_TM_STEEPPOS = 0x00000800
SPC_TM_STEEPNEG = 0x00001000
SPC_TM_EXTRAMASK = 0xFF000000
SPC_TM_REARM = 0x01000000
SPC_TM_PW_SMALLER = 0x02000000
SPC_TM_PW_GREATER = 0x04000000
SPC_TM_DOUBLEEDGE = 0x08000000
SPC_TM_PULSESTRETCH = 0x10000000
SPC_TM_HYSTERESIS = 0x20000000
SPC_TRIG_PATTERN_AVAILMODES = 40580
SPC_TRIG_PATTERN_MODE = 40590
SPC_TRIG_CH_AVAILMODES = 40600
SPC_TRIG_CH_AVAILMODESOR = 40601
SPC_TRIG_CH_AVAILMODESAND = 40602
SPC_TRIG_CH0_MODE = 40610
SPC_TRIG_CH1_MODE = 40611
SPC_TRIG_CH2_MODE = 40612
SPC_TRIG_CH3_MODE = 40613
SPC_TRIG_CH4_MODE = 40614
SPC_TRIG_CH5_MODE = 40615
SPC_TRIG_CH6_MODE = 40616
SPC_TRIG_CH7_MODE = 40617
SPC_TRIG_CH8_MODE = 40618
SPC_TRIG_CH9_MODE = 40619
SPC_TRIG_CH10_MODE = 40620
SPC_TRIG_CH11_MODE = 40621
SPC_TRIG_CH12_MODE = 40622
SPC_TRIG_CH13_MODE = 40623
SPC_TRIG_CH14_MODE = 40624
SPC_TRIG_CH15_MODE = 40625
SPC_TRIG_CH16_MODE = 40626
SPC_TRIG_CH17_MODE = 40627
SPC_TRIG_CH18_MODE = 40628
SPC_TRIG_CH19_MODE = 40629
SPC_TRIG_CH20_MODE = 40630
SPC_TRIG_CH21_MODE = 40631
SPC_TRIG_CH22_MODE = 40632
SPC_TRIG_CH23_MODE = 40633
SPC_TRIG_CH24_MODE = 40634
SPC_TRIG_CH25_MODE = 40635
SPC_TRIG_CH26_MODE = 40636
SPC_TRIG_CH27_MODE = 40637
SPC_TRIG_CH28_MODE = 40638
SPC_TRIG_CH29_MODE = 40639
SPC_TRIG_CH30_MODE = 40640
SPC_TRIG_CH31_MODE = 40641
SPC_TRIG_CH32_MODE = 40642
SPC_TRIG_CH33_MODE = 40643
SPC_TRIG_CH34_MODE = 40644
SPC_TRIG_CH35_MODE = 40645
SPC_TRIG_CH36_MODE = 40646
SPC_TRIG_CH37_MODE = 40647
SPC_TRIG_CH38_MODE = 40648
SPC_TRIG_CH39_MODE = 40649
SPC_TRIG_CH40_MODE = 40650
SPC_TRIG_CH41_MODE = 40651
SPC_TRIG_CH42_MODE = 40652
SPC_TRIG_CH43_MODE = 40653
SPC_TRIG_CH44_MODE = 40654
SPC_TRIG_CH45_MODE = 40655
SPC_TRIG_CH46_MODE = 40656
SPC_TRIG_CH47_MODE = 40657
SPC_TRIG_CH48_MODE = 40658
SPC_TRIG_CH49_MODE = 40659
SPC_TRIG_CH50_MODE = 40660
SPC_TRIG_CH51_MODE = 40661
SPC_TRIG_CH52_MODE = 40662
SPC_TRIG_CH53_MODE = 40663
SPC_TRIG_CH54_MODE = 40664
SPC_TRIG_CH55_MODE = 40665
SPC_TRIG_CH56_MODE = 40666
SPC_TRIG_CH57_MODE = 40667
SPC_TRIG_CH58_MODE = 40668
SPC_TRIG_CH59_MODE = 40669
SPC_TRIG_CH60_MODE = 40670
SPC_TRIG_CH61_MODE = 40671
SPC_TRIG_CH62_MODE = 40672
SPC_TRIG_CH63_MODE = 40673
SPC_TRIG_AVAILDELAY = 40800
SPC_TRIG_AVAILDELAY_STEP = 40801
SPC_TRIG_DELAY = 40810
SPC_TRIG_AVAILHOLDOFF = 40802
SPC_TRIG_AVAILHOLDOFF_STEP = 40803
SPC_TRIG_HOLDOFF = 40811
SPC_SINGLESHOT = 41000
SPC_OUTONTRIGGER = 41100
SPC_RESTARTCONT = 41200
SPC_SINGLERESTART = 41300
SPC_TRIGGERLEVEL = 42000
SPC_TRIGGERLEVEL0 = 42000
SPC_TRIGGERLEVEL1 = 42001
SPC_TRIGGERLEVEL2 = 42002
SPC_TRIGGERLEVEL3 = 42003
SPC_TRIGGERLEVEL4 = 42004
SPC_TRIGGERLEVEL5 = 42005
SPC_TRIGGERLEVEL6 = 42006
SPC_TRIGGERLEVEL7 = 42007
SPC_TRIGGERLEVEL8 = 42008
SPC_TRIGGERLEVEL9 = 42009
SPC_TRIGGERLEVEL10 = 42010
SPC_TRIGGERLEVEL11 = 42011
SPC_TRIGGERLEVEL12 = 42012
SPC_TRIGGERLEVEL13 = 42013
SPC_TRIGGERLEVEL14 = 42014
SPC_TRIGGERLEVEL15 = 42015
SPC_AVAILHIGHLEVEL_MIN = 41997
SPC_AVAILHIGHLEVEL_MAX = 41998
SPC_AVAILHIGHLEVEL_STEP = 41999
SPC_HIGHLEVEL0 = 42000
SPC_HIGHLEVEL1 = 42001
SPC_HIGHLEVEL2 = 42002
SPC_HIGHLEVEL3 = 42003
SPC_HIGHLEVEL4 = 42004
SPC_HIGHLEVEL5 = 42005
SPC_HIGHLEVEL6 = 42006
SPC_HIGHLEVEL7 = 42007
SPC_HIGHLEVEL8 = 42008
SPC_HIGHLEVEL9 = 42009
SPC_HIGHLEVEL10 = 42010
SPC_HIGHLEVEL11 = 42011
SPC_HIGHLEVEL12 = 42012
SPC_HIGHLEVEL13 = 42013
SPC_HIGHLEVEL14 = 42014
SPC_HIGHLEVEL15 = 42015
SPC_AVAILLOWLEVEL_MIN = 42097
SPC_AVAILLOWLEVEL_MAX = 42098
SPC_AVAILLOWLEVEL_STEP = 42099
SPC_LOWLEVEL0 = 42100
SPC_LOWLEVEL1 = 42101
SPC_LOWLEVEL2 = 42102
SPC_LOWLEVEL3 = 42103
SPC_LOWLEVEL4 = 42104
SPC_LOWLEVEL5 = 42105
SPC_LOWLEVEL6 = 42106
SPC_LOWLEVEL7 = 42107
SPC_LOWLEVEL8 = 42108
SPC_LOWLEVEL9 = 42109
SPC_LOWLEVEL10 = 42110
SPC_LOWLEVEL11 = 42111
SPC_LOWLEVEL12 = 42112
SPC_LOWLEVEL13 = 42113
SPC_LOWLEVEL14 = 42114
SPC_LOWLEVEL15 = 42115
SPC_TRIG_CH0_LEVEL0 = 42200
SPC_TRIG_CH1_LEVEL0 = 42201
SPC_TRIG_CH2_LEVEL0 = 42202
SPC_TRIG_CH3_LEVEL0 = 42203
SPC_TRIG_CH4_LEVEL0 = 42204
SPC_TRIG_CH5_LEVEL0 = 42205
SPC_TRIG_CH6_LEVEL0 = 42206
SPC_TRIG_CH7_LEVEL0 = 42207
SPC_TRIG_CH8_LEVEL0 = 42208
SPC_TRIG_CH9_LEVEL0 = 42209
SPC_TRIG_CH10_LEVEL0 = 42210
SPC_TRIG_CH11_LEVEL0 = 42211
SPC_TRIG_CH12_LEVEL0 = 42212
SPC_TRIG_CH13_LEVEL0 = 42213
SPC_TRIG_CH14_LEVEL0 = 42214
SPC_TRIG_CH15_LEVEL0 = 42215
SPC_TRIG_CH0_LEVEL1 = 42300
SPC_TRIG_CH1_LEVEL1 = 42301
SPC_TRIG_CH2_LEVEL1 = 42302
SPC_TRIG_CH3_LEVEL1 = 42303
SPC_TRIG_CH4_LEVEL1 = 42304
SPC_TRIG_CH5_LEVEL1 = 42305
SPC_TRIG_CH6_LEVEL1 = 42306
SPC_TRIG_CH7_LEVEL1 = 42307
SPC_TRIG_CH8_LEVEL1 = 42308
SPC_TRIG_CH9_LEVEL1 = 42309
SPC_TRIG_CH10_LEVEL1 = 42310
SPC_TRIG_CH11_LEVEL1 = 42311
SPC_TRIG_CH12_LEVEL1 = 42312
SPC_TRIG_CH13_LEVEL1 = 42313
SPC_TRIG_CH14_LEVEL1 = 42314
SPC_TRIG_CH15_LEVEL1 = 42315
SPC_TRIG_EXT0_LEVEL0 = 42320
SPC_TRIG_EXT1_LEVEL0 = 42321
SPC_TRIG_EXT2_LEVEL0 = 42322
SPC_TRIG_EXT0_LEVEL1 = 42330
SPC_TRIG_EXT1_LEVEL1 = 42331
SPC_TRIG_EXT2_LEVEL1 = 42332
SPC_TRIG_EXT_AVAIL0_MIN = 42340
SPC_TRIG_EXT_AVAIL0_MAX = 42341
SPC_TRIG_EXT_AVAIL0_STEP = 42342
SPC_TRIG_EXT_AVAIL1_MIN = 42345
SPC_TRIG_EXT_AVAIL1_MAX = 42346
SPC_TRIG_EXT_AVAIL1_STEP = 42347
SPC_THRESHOLD0 = 42400
SPC_THRESHOLD1 = 42401
SPC_THRESHOLD2 = 42402
SPC_THRESHOLD3 = 42403
SPC_CLOCK_THRESHOLD = 42410
SPC_TRIG_THRESHOLD = 42411
SPC_X0X1_THRESHOLD = 42412
SPC_STROBE_THRESHOLD = 42413
SPC_AVAILTHRESHOLD_MIN = 42420
SPC_AVAILTHRESHOLD_MAX = 42421
SPC_AVAILTHRESHOLD_STEP = 42422
SPC_AVAILAVRGMODES = 42429
SPC_AVRGMODE_CH0 = 42430
SPC_AVRGMODE_CH1 = 42431
SPC_AVRGMODE_CH2 = 42432
SPC_AVRGMODE_CH3 = 42433
AVRGMODE_NORMAL = 0x0
AVRGMODE_TDA_HIGH = 0x1
AVRGMODE_TDA_LOW = 0x2
SPC_TDA_THRESHOLD_CH0 = 42440
SPC_TDA_THRESHOLD_CH1 = 42441
SPC_TDA_THRESHOLD_CH2 = 42442
SPC_TDA_THRESHOLD_CH3 = 42443
SPC_TDA_REPLACEMENT_CH0 = 42450
SPC_TDA_REPLACEMENT_CH1 = 42451
SPC_TDA_REPLACEMENT_CH2 = 42452
SPC_TDA_REPLACEMENT_CH3 = 42453
SPC_CLOCK_AVAILTHRESHOLD_MIN = 42423
SPC_CLOCK_AVAILTHRESHOLD_MAX = 42424
SPC_CLOCK_AVAILTHRESHOLD_STEP = 42425
SPC_TRIG_AVAILTHRESHOLD_MIN = 42426
SPC_TRIG_AVAILTHRESHOLD_MAX = 42427
SPC_TRIG_AVAILTHRESHOLD_STEP = 42428
SPC_TRIGGERPATTERN = 43000
SPC_TRIGGERPATTERN0 = 43000
SPC_TRIGGERPATTERN1 = 43001
SPC_TRIGGERMASK = 43100
SPC_TRIGGERMASK0 = 43100
SPC_TRIGGERMASK1 = 43101
SPC_PULSEWIDTH = 44000
SPC_PULSEWIDTH0 = 44000
SPC_PULSEWIDTH1 = 44001
SPC_TRIG_CH_AVAILPULSEWIDTH = 44100
SPC_TRIG_CH_PULSEWIDTH = 44101
SPC_TRIG_CH0_PULSEWIDTH = 44101
SPC_TRIG_CH1_PULSEWIDTH = 44102
SPC_TRIG_CH2_PULSEWIDTH = 44103
SPC_TRIG_CH3_PULSEWIDTH = 44104
SPC_TRIG_CH4_PULSEWIDTH = 44105
SPC_TRIG_CH5_PULSEWIDTH = 44106
SPC_TRIG_CH6_PULSEWIDTH = 44107
SPC_TRIG_CH7_PULSEWIDTH = 44108
SPC_TRIG_CH8_PULSEWIDTH = 44109
SPC_TRIG_CH9_PULSEWIDTH = 44110
SPC_TRIG_CH10_PULSEWIDTH = 44111
SPC_TRIG_CH11_PULSEWIDTH = 44112
SPC_TRIG_CH12_PULSEWIDTH = 44113
SPC_TRIG_CH13_PULSEWIDTH = 44114
SPC_TRIG_CH14_PULSEWIDTH = 44115
SPC_TRIG_CH15_PULSEWIDTH = 44116
SPC_TRIG_EXT_AVAILPULSEWIDTH = 44200
SPC_TRIG_EXT0_PULSEWIDTH = 44210
SPC_TRIG_EXT1_PULSEWIDTH = 44211
SPC_TRIG_EXT2_PULSEWIDTH = 44212
SPC_TRIG_EXT3_PULSEWIDTH = 44213
SPC_READCLOCKDIVCOUNT = 44300
SPC_CLOCKDIV0 = 44301
SPC_CLOCKDIV1 = 44302
SPC_CLOCKDIV2 = 44303
SPC_CLOCKDIV3 = 44304
SPC_CLOCKDIV4 = 44305
SPC_CLOCKDIV5 = 44306
SPC_CLOCKDIV6 = 44307
SPC_CLOCKDIV7 = 44308
SPC_CLOCKDIV8 = 44309
SPC_CLOCKDIV9 = 44310
SPC_CLOCKDIV10 = 44311
SPC_CLOCKDIV11 = 44312
SPC_CLOCKDIV12 = 44313
SPC_CLOCKDIV13 = 44314
SPC_CLOCKDIV14 = 44315
SPC_CLOCKDIV15 = 44316
SPC_CLOCKDIV16 = 44317
SPC_READTROFFSET = 45000
SPC_TRIGGEREDGE = 46000
SPC_TRIGGEREDGE0 = 46000
SPC_TRIGGEREDGE1 = 46001
TE_POS = 10000
TE_NEG = 10010
TE_BOTH = 10020
TE_NONE = 10030
CH_TIMESTAMP = 9999
SPC_TIMESTAMP_CMD = 47000
TS_RESET = 0
TS_MODE_DISABLE = 10
TS_MODE_STARTRESET = 11
TS_MODE_STANDARD = 12
TS_MODE_REFCLOCK = 13
TS_MODE_TEST5555 = 90
TS_MODE_TESTAAAA = 91
TS_MODE_ZHTEST = 92
SPC_TIMESTAMP_AVAILMODES = 47001
SPC_TSMODE_DISABLE = 0x00000000
SPC_TS_RESET = 0x00000001
SPC_TSMODE_STANDARD = 0x00000002
SPC_TSMODE_STARTRESET = 0x00000004
SPC_TS_RESET_WAITREFCLK = 0x00000008
SPC_TSCNT_INTERNAL = 0x00000100
SPC_TSCNT_REFCLOCKPOS = 0x00000200
SPC_TSCNT_REFCLOCKNEG = 0x00000400
SPC_TSFEAT_NONE = 0x00000000
SPC_TSFEAT_STORE1STABA = 0x00010000
SPC_TSFEAT_INCRMODE = 0x00020000
SPC_TSFEAT_INCRMODE12 = 0x00040000
SPC_TSFEAT_TRGSRC = 0x00080000
SPC_TSXIOACQ_DISABLE = 0x00000000
SPC_TSXIOACQ_ENABLE = 0x00001000
SPC_TSXIOINC_ENABLE = 0x00002000
SPC_TSXIOINC12_ENABLE = 0x00004000
SPC_TSMODE_MASK = 0x000000FF
SPC_TSCNT_MASK = 0x00000F00
SPC_TSFEAT_MASK = 0x000F0000
SPC_TRGSRC_MASK_CH0 = 0x00000001
SPC_TRGSRC_MASK_CH1 = 0x00000002
SPC_TRGSRC_MASK_CH2 = 0x00000004
SPC_TRGSRC_MASK_CH3 = 0x00000008
SPC_TRGSRC_MASK_CH4 = 0x00000010
SPC_TRGSRC_MASK_CH5 = 0x00000020
SPC_TRGSRC_MASK_CH6 = 0x00000040
SPC_TRGSRC_MASK_CH7 = 0x00000080
SPC_TRGSRC_MASK_EXT0 = 0x00000100
SPC_TRGSRC_MASK_EXT1 = 0x00000200
SPC_TRGSRC_MASK_FORCE = 0x00000400
SPC_TRGSRC_MASK_PXI0 = 0x00010000
SPC_TRGSRC_MASK_PXI1 = 0x00020000
SPC_TRGSRC_MASK_PXI2 = 0x00040000
SPC_TRGSRC_MASK_PXI3 = 0x00080000
SPC_TRGSRC_MASK_PXI4 = 0x00100000
SPC_TRGSRC_MASK_PXI5 = 0x00200000
SPC_TRGSRC_MASK_PXI6 = 0x00400000
SPC_TRGSRC_MASK_PXI7 = 0x00800000
SPC_TRGSRC_MASK_PXISTAR = 0x01000000
SPC_TRGSRC_MASK_PXIDSTARB = 0x02000000
SPC_TRGSRC_MASK_X0 = 0x10000000
SPC_TRGSRC_MASK_X1 = 0x20000000
SPC_TRGSRC_MASK_X2 = 0x40000000
SPC_TRGSRC_MASK_X3 = 0x80000000
SPC_TIMESTAMP_STATUS = 47010
TS_FIFO_EMPTY = 0
TS_FIFO_LESSHALF = 1
TS_FIFO_MOREHALF = 2
TS_FIFO_OVERFLOW = 3
SPC_TIMESTAMP_SIZE = 47011
SPC_TIMESTAMP_COUNT = 47020
SPC_TIMESTAMP_STARTTIME = 47030
SPC_TIMESTAMP_STARTDATE = 47031
SPC_TIMESTAMP_FIFO = 47040
SPC_TIMESTAMP_TIMEOUT = 47045
SPC_TIMESTAMP_RESETMODE = 47050
TS_RESET_POS = 10
TS_RESET_NEG = 20
SPC_XIO_DIRECTION = 47100
XD_CH0_INPUT = 0
XD_CH0_OUTPUT = 1
XD_CH1_INPUT = 0
XD_CH1_OUTPUT = 2
XD_CH2_INPUT = 0
XD_CH2_OUTPUT = 4
SPC_XIO_DIGITALIO = 47110
SPC_XIO_ANALOGOUT0 = 47120
SPC_XIO_ANALOGOUT1 = 47121
SPC_XIO_ANALOGOUT2 = 47122
SPC_XIO_ANALOGOUT3 = 47123
SPC_XIO_WRITEDACS = 47130
SPCM_LEGACY_X0_MODE = 47200
SPCM_LEGACY_X1_MODE = 47201
SPCM_LEGACY_X2_MODE = 47202
SPCM_LEGACY_X3_MODE = 47203
SPCM_LEGACY_X0_AVAILMODES = 47210
SPCM_LEGACY_X1_AVAILMODES = 47211
SPCM_LEGACY_X2_AVAILMODES = 47212
SPCM_LEGACY_X3_AVAILMODES = 47213
SPCM_XMODE_DISABLE = 0x00000000
SPCM_XMODE_ASYNCIN = 0x00000001
SPCM_XMODE_ASYNCOUT = 0x00000002
SPCM_XMODE_DIGIN = 0x00000004
SPCM_XMODE_DDS = 0x00000004
SPCM_XMODE_DIGOUT = 0x00000008
SPCM_XMODE_TRIGIN = 0x00000010
SPCM_XMODE_TRIGOUT = 0x00000020
SPCM_XMODE_OVROUT = 0x00000040
SPCM_XMODE_DIGIN2BIT = 0x00000080
SPCM_XMODE_RUNSTATE = 0x00000100
SPCM_XMODE_ARMSTATE = 0x00000200
SPCM_XMODE_DIRECTTRIGOUT = 0x00000400
SPCM_XMODE_DIRECTTRIGOUT_LR = 0x00000800
SPCM_XMODE_REFCLKOUT = 0x00001000
SPCM_XMODE_CONTOUTMARK = 0x00002000
SPCM_XMODE_SYSCLKOUT = 0x00004000
SPCM_XMODE_CLKOUT = 0x00008000
SPCM_XMODE_SYNCARMSTATE = 0x00010000
SPCM_XMODE_OPTDIGIN2BIT = 0x00020000
SPCM_XMODE_OPTDIGIN4BIT = 0x00040000
SPCM_XMODE_PULSEGEN = 0x00080000
SPCM_XMODE_MODEMASK = 0x000FFFFF
SPCM_XMODE_DIGOUTSRC_CH0 = 0x01000000
SPCM_XMODE_DIGOUTSRC_CH1 = 0x02000000
SPCM_XMODE_DIGOUTSRC_CH2 = 0x04000000
SPCM_XMODE_DIGOUTSRC_CH3 = 0x08000000
SPCM_XMODE_DIGOUTSRC_CH4 = 0x10000000
SPCM_XMODE_DIGOUTSRC_CH5 = 0x20000000
SPCM_XMODE_DIGOUTSRC_CH6 = 0x40000000
SPCM_XMODE_DIGOUTSRC_CH7 = 0x80000000
SPCM_XMODE_DIGOUTSRC_CHMASK = 0xFF000000
SPCM_XMODE_DIGOUTSRC_BIT15 = 0x00100000
SPCM_XMODE_DIGOUTSRC_BIT14 = 0x00200000
SPCM_XMODE_DIGOUTSRC_BIT13 = 0x00400000
SPCM_XMODE_DIGOUTSRC_BIT12 = 0x00800000
SPCM_XMODE_DIGOUTSRC_BITMASK = 0x00F00000
SPCM_XMODE_DIGOUTSRC_BIT15_downto_0 = 0x00F00000
SPCM_XMODE_DIGOUTSRC_BIT15_downto_8 = 0x00700000
SPCM_XX_ASYNCIO = 47220
SPC_DIGMODE0 = 47250
SPC_DIGMODE1 = 47251
SPC_DIGMODE2 = 47252
SPC_DIGMODE3 = 47253
SPC_DIGMODE4 = 47254
SPC_DIGMODE5 = 47255
SPC_DIGMODE6 = 47256
SPC_DIGMODE7 = 47257
SPCM_DIGMODE_OFF = 0x00000000
SPCM_DIGMODE_X0 = 0x21084000
SPCM_DIGMODE_X1 = 0x294A5000
SPCM_DIGMODE_X2 = 0x318C6000
SPCM_DIGMODE_X3 = 0x39CE7000
SPCM_DIGMODE_X4 = 0x84210001
SPCM_DIGMODE_X5 = 0x8c631002
SPCM_DIGMODE_X6 = 0x94a52004
SPCM_DIGMODE_X7 = 0x9ce73008
SPCM_DIGMODE_X8 = 0xa5294010
SPCM_DIGMODE_X9 = 0xad6b5020
SPCM_DIGMODE_X10 = 0xb5ad6040
SPCM_DIGMODE_X11 = 0xbdef7080
SPCM_DIGMODE_X12 = 0xc6318100
SPCM_DIGMODE_X13 = 0xce739200
SPCM_DIGMODE_X14 = 0xd6b5a400
SPCM_DIGMODE_X15 = 0xdef7b800
SPCM_DIGMODE_X16 = 0xe739c000
SPCM_DIGMODE_X17 = 0xef7bd000
SPCM_DIGMODE_X18 = 0xf7bde000
SPCM_DIGMODE_X19 = 0xfffff000
DIGMODEMASK_BIT15 = 0xF8000000
DIGMODEMASK_BIT14 = 0x07C00000
DIGMODEMASK_BIT13 = 0x003E0000
DIGMODEMASK_BIT12 = 0x0001F000
DIGMODEMASK_BIT11 = 0x00000800
DIGMODEMASK_BIT10 = 0x00000400
DIGMODEMASK_BIT9 = 0x00000200
DIGMODEMASK_BIT8 = 0x00000100
DIGMODEMASK_BIT7 = 0x00000080
DIGMODEMASK_BIT6 = 0x00000040
DIGMODEMASK_BIT5 = 0x00000020
DIGMODEMASK_BIT4 = 0x00000010
DIGMODEMASK_BIT3 = 0x00000008
DIGMODEMASK_BIT2 = 0x00000004
DIGMODEMASK_BIT1 = 0x00000002
DIGMODEMASK_BIT0 = 0x00000001
SPCM_DIGMODE_CHREPLACE = 0xFFBBCFFF
SPC_PXITRG0_MODE = 47300
SPC_PXITRG1_MODE = 47301
SPC_PXITRG2_MODE = 47302
SPC_PXITRG3_MODE = 47303
SPC_PXITRG4_MODE = 47304
SPC_PXITRG5_MODE = 47305
SPC_PXITRG6_MODE = 47306
SPC_PXITRG7_MODE = 47307
SPC_PXISTAR_MODE = 47308
SPC_PXIDSTARC_MODE = 47309
SPC_PXITRG0_AVAILMODES = 47310
SPC_PXITRG1_AVAILMODES = 47311
SPC_PXITRG2_AVAILMODES = 47312
SPC_PXITRG3_AVAILMODES = 47313
SPC_PXITRG4_AVAILMODES = 47314
SPC_PXITRG5_AVAILMODES = 47315
SPC_PXITRG6_AVAILMODES = 47316
SPC_PXITRG7_AVAILMODES = 47317
SPC_PXISTAR_AVAILMODES = 47318
SPC_PXIDSTARC_AVAILMODES = 47319
SPC_PXITRG_ASYNCIO = 47320
SPCM_PXITRGMODE_DISABLE = 0x00000000
SPCM_PXITRGMODE_IN = 0x00000001
SPCM_PXITRGMODE_ASYNCOUT = 0x00000002
SPCM_PXITRGMODE_RUNSTATE = 0x00000004
SPCM_PXITRGMODE_ARMSTATE = 0x00000008
SPCM_PXITRGMODE_TRIGOUT = 0x00000010
SPCM_PXITRGMODE_REFCLKOUT = 0x00000020
SPCM_PXITRGMODE_CONTOUTMARK = 0x00000040
SPC_STARHUB_STATUS = 48010
SPC_STARHUB_ROUTE0 = 48100
SPC_STARHUB_ROUTE99 = 48199
SPC_SYNC_READ_SYNCCOUNT = 48990
SPC_SYNC_READ_NUMCONNECTORS = 48991
SPC_SYNC_READ_CARDIDX0 = 49000
SPC_SYNC_READ_CARDIDX1 = 49001
SPC_SYNC_READ_CARDIDX2 = 49002
SPC_SYNC_READ_CARDIDX3 = 49003
SPC_SYNC_READ_CARDIDX4 = 49004
SPC_SYNC_READ_CARDIDX5 = 49005
SPC_SYNC_READ_CARDIDX6 = 49006
SPC_SYNC_READ_CARDIDX7 = 49007
SPC_SYNC_READ_CARDIDX8 = 49008
SPC_SYNC_READ_CARDIDX9 = 49009
SPC_SYNC_READ_CARDIDX10 = 49010
SPC_SYNC_READ_CARDIDX11 = 49011
SPC_SYNC_READ_CARDIDX12 = 49012
SPC_SYNC_READ_CARDIDX13 = 49013
SPC_SYNC_READ_CARDIDX14 = 49014
SPC_SYNC_READ_CARDIDX15 = 49015
SPC_SYNC_READ_CABLECON0 = 49100
SPC_SYNC_READ_CABLECON1 = 49101
SPC_SYNC_READ_CABLECON2 = 49102
SPC_SYNC_READ_CABLECON3 = 49103
SPC_SYNC_READ_CABLECON4 = 49104
SPC_SYNC_READ_CABLECON5 = 49105
SPC_SYNC_READ_CABLECON6 = 49106
SPC_SYNC_READ_CABLECON7 = 49107
SPC_SYNC_READ_CABLECON8 = 49108
SPC_SYNC_READ_CABLECON9 = 49109
SPC_SYNC_READ_CABLECON10 = 49110
SPC_SYNC_READ_CABLECON11 = 49111
SPC_SYNC_READ_CABLECON12 = 49112
SPC_SYNC_READ_CABLECON13 = 49113
SPC_SYNC_READ_CABLECON14 = 49114
SPC_SYNC_READ_CABLECON15 = 49115
SPC_SYNC_ENABLEMASK = 49200
SPC_SYNC_NOTRIGSYNCMASK = 49210
SPC_SYNC_CLKMASK = 49220
SPC_SYNC_MODE = 49230
SPC_AVAILSYNC_MODES = 49231
SPC_SYNC_STANDARD = 0x00000001
SPC_SYNC_SYSTEMCLOCK = 0x00000002
SPC_SYNC_SYSTEMCLOCKTRIG = 0x00000004
SPC_SYNC_SYSTEMCLOCKTRIGN = 0x00000008
SPC_SYNC_SYSTEM_TRIGADJUST = 49240
SPC_ADJ_START = 50000
SPC_ADJ_LOAD = 50000
SPC_ADJ_SAVE = 50010
ADJ_DEFAULT = 0
ADJ_USER0 = 1
ADJ_USER1 = 2
ADJ_USER2 = 3
ADJ_USER3 = 4
ADJ_USER4 = 5
ADJ_USER5 = 6
ADJ_USER6 = 7
ADJ_USER7 = 8
SPC_ADJ_AUTOADJ = 50020
ADJ_ALL = 0
ADJ_CURRENT = 1
ADJ_EXTERNAL = 2
ADJ_1MOHM = 3
ADJ_CURRENT_CLOCK = 4
ADJ_CURRENT_IR = 8
ADJ_OFFSET_ONLY = 16
ADJ_SPECIAL_CLOCK = 32
SPC_ADJ_SOURCE_CALLBACK = 50021
SPC_ADJ_PROGRESS_CALLBACK = 50022
SPC_ADJ_SET = 50030
SPC_ADJ_FAILMASK = 50040
SPC_ADJ_CALIBSOURCE = 50050
ADJ_CALSRC_GAIN = 1
ADJ_CALSRC_OFF = 0
ADJ_CALSRC_GND = -1
ADJ_CALSRC_GNDOFFS = -2
ADJ_CALSRC_AC = 10
ADJ_CALSRC_ADC = 11
SPC_ADJ_CALIBVALUE0 = 50060
SPC_ADJ_CALIBVALUE1 = 50061
SPC_ADJ_CALIBVALUE2 = 50062
SPC_ADJ_CALIBVALUE3 = 50063
SPC_ADJ_CALIBVALUE4 = 50064
SPC_ADJ_CALIBVALUE5 = 50065
SPC_ADJ_CALIBVALUE6 = 50066
SPC_ADJ_CALIBVALUE7 = 50067
SPC_ADJ_OFFSET_CH0 = 50900
SPC_ADJ_OFFSET_CH1 = 50901
SPC_ADJ_OFFSET_CH2 = 50902
SPC_ADJ_OFFSET_CH3 = 50903
SPC_ADJ_OFFSET_CH4 = 50904
SPC_ADJ_OFFSET_CH5 = 50905
SPC_ADJ_OFFSET_CH6 = 50906
SPC_ADJ_OFFSET_CH7 = 50907
SPC_ADJ_OFFSET_CH8 = 50908
SPC_ADJ_OFFSET_CH9 = 50909
SPC_ADJ_OFFSET_CH10 = 50910
SPC_ADJ_OFFSET_CH11 = 50911
SPC_ADJ_OFFSET_CH12 = 50912
SPC_ADJ_OFFSET_CH13 = 50913
SPC_ADJ_OFFSET_CH14 = 50914
SPC_ADJ_OFFSET_CH15 = 50915
SPC_ADJ_GAIN_CH0 = 50916
SPC_ADJ_GAIN_CH1 = 50917
SPC_ADJ_GAIN_CH2 = 50918
SPC_ADJ_GAIN_CH3 = 50919
SPC_ADJ_GAIN_CH4 = 50920
SPC_ADJ_GAIN_CH5 = 50921
SPC_ADJ_GAIN_CH6 = 50922
SPC_ADJ_GAIN_CH7 = 50923
SPC_ADJ_GAIN_CH8 = 50924
SPC_ADJ_GAIN_CH9 = 50925
SPC_ADJ_GAIN_CH10 = 50926
SPC_ADJ_GAIN_CH11 = 50927
SPC_ADJ_GAIN_CH12 = 50928
SPC_ADJ_GAIN_CH13 = 50929
SPC_ADJ_GAIN_CH14 = 50930
SPC_ADJ_GAIN_CH15 = 50931
SPC_ADJ_OFFSET0 = 51000
SPC_ADJ_OFFSET999 = 51999
SPC_ADJ_GAIN0 = 52000
SPC_ADJ_GAIN999 = 52999
SPC_ADJ_CORRECT0 = 53000
SPC_ADJ_OFFS_CORRECT0 = 53000
SPC_ADJ_CORRECT999 = 53999
SPC_ADJ_OFFS_CORRECT999 = 53999
SPC_ADJ_XIOOFFS0 = 54000
SPC_ADJ_XIOOFFS1 = 54001
SPC_ADJ_XIOOFFS2 = 54002
SPC_ADJ_XIOOFFS3 = 54003
SPC_ADJ_XIOGAIN0 = 54010
SPC_ADJ_XIOGAIN1 = 54011
SPC_ADJ_XIOGAIN2 = 54012
SPC_ADJ_XIOGAIN3 = 54013
SPC_ADJ_GAIN_CORRECT0 = 55000
SPC_ADJ_GAIN_CORRECT999 = 55999
SPC_ADJ_OFFSCALIBCORRECT0 = 56000
SPC_ADJ_OFFSCALIBCORRECT999 = 56999
SPC_ADJ_GAINCALIBCORRECT0 = 57000
SPC_ADJ_GAINCALIBCORRECT999 = 57999
SPC_ADJ_ANALOGTRIGGER0 = 58000
SPC_ADJ_ANALOGTRIGGER99 = 58099
SPC_ADJ_CALIBSAMPLERATE0 = 58100
SPC_ADJ_CALIBSAMPLERATE99 = 58199
SPC_ADJ_CALIBSAMPLERATE_GAIN0 = 58200
SPC_ADJ_CALIBSAMPLERATE_GAIN99 = 58299
SPC_ADJ_REFCLOCK = 58300
SPC_ADJ_STARHUB_REFCLOCK = 58301
SPC_ADJ_TIMING_CH0 = 58400
SPC_ADJ_TIMING_CH1 = 58401
SPC_ADJ_END = 59999
SPC_FIFO_BUFFERS = 60000
SPC_FIFO_BUFLEN = 60010
SPC_FIFO_BUFCOUNT = 60020
SPC_FIFO_BUFMAXCNT = 60030
SPC_FIFO_BUFADRCNT = 60040
SPC_FIFO_BUFREADY = 60050
SPC_FIFO_BUFFILLCNT = 60060
SPC_FIFO_BUFADR0 = 60100
SPC_FIFO_BUFADR1 = 60101
SPC_FIFO_BUFADR2 = 60102
SPC_FIFO_BUFADR3 = 60103
SPC_FIFO_BUFADR4 = 60104
SPC_FIFO_BUFADR5 = 60105
SPC_FIFO_BUFADR6 = 60106
SPC_FIFO_BUFADR7 = 60107
SPC_FIFO_BUFADR8 = 60108
SPC_FIFO_BUFADR9 = 60109
SPC_FIFO_BUFADR10 = 60110
SPC_FIFO_BUFADR11 = 60111
SPC_FIFO_BUFADR12 = 60112
SPC_FIFO_BUFADR13 = 60113
SPC_FIFO_BUFADR14 = 60114
SPC_FIFO_BUFADR15 = 60115
SPC_FIFO_BUFADR255 = 60355
SPC_FILTER = 100000
SPC_READNUMFILTERS = 100001
SPC_FILTERFREQUENCY0 = 100002
SPC_FILTERFREQUENCY1 = 100003
SPC_FILTERFREQUENCY2 = 100004
SPC_FILTERFREQUENCY3 = 100005
SPC_DIGITALBWFILTER = 100100
SPC_PATTERNENABLE = 110000
SPC_READDIGITAL = 110100
SPC_DIGITALMODE0 = 110200
SPC_DIGITALMODE1 = 110201
SPC_DIGITALMODE2 = 110202
SPC_DIGITALMODE3 = 110203
SPC_DIGITALMODE4 = 110204
SPC_DIGITALMODE5 = 110205
SPC_DIGITALMODE6 = 110206
SPC_DIGITALMODE7 = 110207
SPC_DIGITALMODE_OFF = 0
SPC_DIGITALMODE_2BIT = 1
SPC_DIGITALMODE_4BIT = 2
SPC_DIGITALMODE_CHREPLACE = 3
SPC_MISCDAC0 = 200000
SPC_MISCDAC1 = 200010
SPC_FACTORYMODE = 200020
SPC_DIRECTDAC = 200030
SPC_NOTRIGSYNC = 200040
SPC_DSPDIRECT = 200100
SPC_DMAPHYSICALADR = 200110
SPC_MICXCOMP_CLOSEBOARD = 200119
SPC_MICXCOMPATIBILITYMODE = 200120
SPC_TEST_FIFOSPEED = 200121
SPC_RELOADDEMO = 200122
SPC_OVERSAMPLINGFACTOR = 200123
SPC_ISMAPPEDCARD = 200124
SPCM_NOT_MAPPED = 0
SPCM_LOCAL_MAPPED = 1
SPCM_REMOTE_MAPPED = 2
SPC_GETTHREADHANDLE = 200130
SPC_GETKERNELHANDLE = 200131
SPC_XYZMODE = 200200
SPC_INVERTDATA = 200300
SPC_GATEMARKENABLE = 200400
SPC_GATE_LEN_ALIGNMENT = 200401
SPC_CONTOUTMARK = 200450
SPC_EXPANDINT32 = 200500
SPC_NOPRETRIGGER = 200600
SPC_RELAISWAITTIME = 200700
SPC_DACWAITTIME = 200710
SPC_DELAY_US = 200720
SPC_ILAMODE = 200800
SPC_NMDGMODE = 200810
SPC_CKADHALF_OUTPUT = 200820
SPC_LONGTRIG_OUTPUT = 200830
SPC_STOREMODAENDOFSEGMENT = 200840
SPC_COUNTERMODE = 200850
SPC_CNTMOD_MASK = 0x0000000F
SPC_CNTMOD_PARALLELDATA = 0x00000000
SPC_CNTMOD_8BITCNT = 0x00000001
SPC_CNTMOD_2x8BITCNT = 0x00000002
SPC_CNTMOD_16BITCNT = 0x00000003
SPC_CNT0_MASK = 0x000000F0
SPC_CNT0_CNTONPOSEDGE = 0x00000000
SPC_CNT0_CNTONNEGEDGE = 0x00000010
SPC_CNT0_RESETHIGHLVL = 0x00000000
SPC_CNT0_RESETLOWLVL = 0x00000020
SPC_CNT0_STOPATMAX = 0x00000000
SPC_CNT0_ROLLOVER = 0x00000040
SPC_CNT1_MASK = 0x00000F00
SPC_CNT1_CNTONPOSEDGE = 0x00000000
SPC_CNT1_CNTONNEGEDGE = 0x00000100
SPC_CNT1_RESETHIGHLVL = 0x00000000
SPC_CNT1_RESETLOWLVL = 0x00000200
SPC_CNT1_STOPATMAX = 0x00000000
SPC_CNT1_ROLLOVER = 0x00000400
SPC_CNTCMD_MASK = 0x0000F000
SPC_CNTCMD_RESETCNT0 = 0x00001000
SPC_CNTCMD_RESETCNT1 = 0x00002000
SPC_ENHANCEDSTATUS = 200900
SPC_ENHSTAT_OVERRANGE0 = 0x00000001
SPC_ENHSTAT_OVERRANGE1 = 0x00000002
SPC_ENHSTAT_OVERRANGE2 = 0x00000004
SPC_ENHSTAT_OVERRANGE3 = 0x00000008
SPC_ENHSTAT_OVERRANGE4 = 0x00000010
SPC_ENHSTAT_OVERRANGE5 = 0x00000020
SPC_ENHSTAT_OVERRANGE6 = 0x00000040
SPC_ENHSTAT_OVERRANGE7 = 0x00000080
SPC_ENHSTAT_COMPARATOR0 = 0x40000000
SPC_ENHSTAT_COMPARATOR1 = 0x80000000
SPC_ENHSTAT_COMPARATOR2 = 0x20000000
SPC_ENHSTAT_TRGCOMPARATOR = 0x40000000
SPC_ENHSTAT_CLKCOMPARATOR = 0x80000000
SPC_TRIGGERCOUNTER = 200905
SPC_FILLSIZEPROMILLE = 200910
SPC_OVERRANGEBIT = 201000
SPC_2CH8BITMODE = 201100
SPC_12BITMODE = 201200
SPC_HOLDLASTSAMPLE = 201300
SPC_DATACONVERSION = 201400
SPC_AVAILDATACONVERSION = 201401
SPCM_DC_NONE = 0x00000000
SPCM_DC_12BIT_TO_14BIT = 0x00000001
SPCM_DC_16BIT_TO_14BIT = 0x00000002
SPCM_DC_12BIT_TO_16BIT = 0x00000004
SPCM_DC_14BIT_TO_16BIT = 0x00000008
SPCM_DC_15BIT_TO_16BIT = 0x00000010
SPCM_DC_13BIT_TO_16BIT = 0x00000020
SPCM_DC_12BIT_TO_8BIT = 0x00000080
SPCM_DC_14BIT_TO_8BIT = 0x00000100
SPCM_DC_16BIT_TO_8BIT = 0x00000200
SPCM_DC_16BIT_TO_12BIT = 0x00000400
SPCM_DC_TO_OFFSETBINARY = 0x00000800
SPCM_DC_12BIT_TO_12BITPACKED = 0x00001000
SPC_CARDIDENTIFICATION = 201500
SPC_HANDSHAKE = 201600
SPC_CKSYNC0 = 202000
SPC_CKSYNC1 = 202001
SPC_DISABLEMOD0 = 203000
SPC_DISABLEMOD1 = 203010
SPC_ENABLEOVERRANGECHECK = 204000
SPC_OVERRANGESTATUS = 204010
SPC_BITMODE = 205000
SPC_READBACK = 206000
SPC_AVAILSTOPLEVEL = 206009
SPC_STOPLEVEL1 = 206010
SPC_STOPLEVEL0 = 206020
SPC_CH0_STOPLEVEL = 206020
SPC_CH1_STOPLEVEL = 206021
SPC_CH2_STOPLEVEL = 206022
SPC_CH3_STOPLEVEL = 206023
SPC_CH4_STOPLEVEL = 206024
SPC_CH5_STOPLEVEL = 206025
SPC_CH6_STOPLEVEL = 206026
SPC_CH7_STOPLEVEL = 206027
SPCM_STOPLVL_TRISTATE = 0x00000001
SPCM_STOPLVL_LOW = 0x00000002
SPCM_STOPLVL_HIGH = 0x00000004
SPCM_STOPLVL_HOLDLAST = 0x00000008
SPCM_STOPLVL_ZERO = 0x00000010
SPCM_STOPLVL_CUSTOM = 0x00000020
SPC_DIFFMODE = 206030
SPC_DACADJUST = 206040
SPC_CH0_CUSTOM_STOP = 206050
SPC_CH1_CUSTOM_STOP = 206051
SPC_CH2_CUSTOM_STOP = 206052
SPC_CH3_CUSTOM_STOP = 206053
SPC_CH4_CUSTOM_STOP = 206054
SPC_CH5_CUSTOM_STOP = 206055
SPC_CH6_CUSTOM_STOP = 206056
SPC_CH7_CUSTOM_STOP = 206057
SPC_AMP_MODE = 207000
SPCM_FW_CTRL = 210000
SPCM_FW_CTRL_GOLDEN = 210001
SPCM_FW_CTRL_ACTIVE = 210002
SPCM_FW_CLOCK = 210010
SPCM_FW_CONFIG = 210020
SPCM_FW_MODULEA = 210030
SPCM_FW_MODULEB = 210031
SPCM_FW_MODULEA_ACTIVE = 210032
SPCM_FW_MODULEB_ACTIVE = 210033
SPCM_FW_MODEXTRA = 210050
SPCM_FW_MODEXTRA_ACTIVE = 210052
SPCM_FW_POWER = 210060
SPCM_FW_POWER_ACTIVE = 210062
SPC_MULTI = 220000
SPC_DOUBLEMEM = 220100
SPC_MULTIMEMVALID = 220200
SPC_BANK = 220300
SPC_GATE = 220400
SPC_RELOAD = 230000
SPC_USEROUT = 230010
SPC_WRITEUSER0 = 230100
SPC_WRITEUSER1 = 230110
SPC_READUSER0 = 230200
SPC_READUSER1 = 230210
SPC_MUX = 240000
SPC_ADJADC = 241000
SPC_ADJOFFS0 = 242000
SPC_ADJOFFS1 = 243000
SPC_ADJGAIN0 = 244000
SPC_ADJGAIN1 = 245000
SPC_READEPROM = 250000
SPC_WRITEEPROM = 250010
SPC_DIRECTIO = 260000
SPC_DIRECT_MODA = 260010
SPC_DIRECT_MODB = 260020
SPC_DIRECT_EXT0 = 260030
SPC_DIRECT_EXT1 = 260031
SPC_DIRECT_EXT2 = 260032
SPC_DIRECT_EXT3 = 260033
SPC_DIRECT_EXT4 = 260034
SPC_DIRECT_EXT5 = 260035
SPC_DIRECT_EXT6 = 260036
SPC_DIRECT_EXT7 = 260037
SPC_MEMTEST = 270000
SPC_NODMA = 275000
SPC_NOCOUNTER = 275010
SPC_NOSCATTERGATHER = 275020
SPC_USER_RELAIS_OVERWRITE = 275030
SPCM_URO_ENABLE = 0x80000000
SPCM_URO_INVERT_10TO1REL = 0x00000001
SPC_RUNINTENABLE = 290000
SPC_XFERBUFSIZE = 295000
SPC_CHLX = 295010
SPC_SPECIALCLOCK = 295100
SPC_PLL0_ICP = 295105
SPCM_ICP0 = 0x00000000
SPCM_ICP7 = 0x00000007
SPC_STARTDELAY = 295110
SPC_BASISTTLTRIG = 295120
SPC_TIMEOUT = 295130
SPC_SWL_INFO = 295140
SPC_SWD_INFO = 295141
SPC_SWD_DOWN = 295142
SPC_SWL_EXTRAINFO = 295143
SPC_SPECIALCLOCK_ADJUST0 = 295150
SPC_SPECIALCLOCK_ADJUST1 = 295151
SPC_SPECIALCLOCK_ADJUST2 = 295152
SPC_SPECIALCLOCK_ADJUST3 = 295153
SPCM_SPECIALCLOCK_ADJUST_SHIFT = 1000000
SPC_REGACC_CONTMEM = 299000
SPC_REGACC_MEMORYUSAGE = 299001
SPC_REINITLOGSETTINGS = 299998
SPC_LOGDLLCALLS = 299999
SPC_FREQUENCE = 300000
SPC_DELTAFREQUENCE = 300010
SPC_PINHIGH = 300100
SPC_PINLOW = 300110
SPC_PINDELTA = 300120
SPC_STOPLEVEL = 300200
SPC_PINRELAIS = 300210
SPC_EXTERNLEVEL = 300300
SPC_COUNTER0 = 310000
SPC_COUNTER1 = 310001
SPC_COUNTER2 = 310002
SPC_COUNTER3 = 310003
SPC_COUNTER4 = 310004
SPC_COUNTER5 = 310005
SPC_MODE0 = 310100
SPC_MODE1 = 310101
SPC_MODE2 = 310102
SPC_MODE3 = 310103
SPC_MODE4 = 310104
SPC_MODE5 = 310105
CM_SINGLE = 1
CM_MULTI = 2
CM_POSEDGE = 4
CM_NEGEDGE = 8
CM_HIGHPULSE = 16
CM_LOWPULSE = 32
SPC_SEQUENCERESET = 320000
SPC_SEQUENCEADD = 320010
SEQ_IR_10000MV = 0
SEQ_IR_5000MV = 1
SEQ_IR_2000MV = 2
SEQ_IR_1000MV = 3
SEQ_IR_500MV = 4
SEQ_CH0 = 0
SEQ_CH1 = 8
SEQ_CH2 = 16
SEQ_CH3 = 24
SEQ_CH4 = 32
SEQ_CH5 = 40
SEQ_CH6 = 48
SEQ_CH7 = 56
SEQ_CH8 = 64
SEQ_CH9 = 72
SEQ_CH10 = 80
SEQ_CH11 = 88
SEQ_CH12 = 96
SEQ_CH13 = 104
SEQ_CH14 = 112
SEQ_CH15 = 120
SEQ_TRIGGER = 128
SEQ_START = 256
SPC_CA_MODE = 330000
CAMODE_OFF = 0
CAMODE_CDM = 1
CAMODE_KW = 2
CAMODE_OT = 3
CAMODE_CDMMUL = 4
SPC_CA_TRIGDELAY = 330010
SPC_CA_CKDIV = 330020
SPC_CA_PULS = 330030
SPC_CA_CKMUL = 330040
SPC_CA_DREHZAHLFORMAT = 330050
CADREH_4X4 = 0
CADREH_1X16 = 1
SPC_CA_KWINVERT = 330060
SPC_CA_OUTA = 330100
SPC_CA_OUTB = 330110
CAOUT_TRISTATE = 0
CAOUT_LOW = 1
CAOUT_HIGH = 2
CAOUT_CDM = 3
CAOUT_OT = 4
CAOUT_KW = 5
CAOUT_TRIG = 6
CAOUT_CLK = 7
CAOUT_KW60 = 8
CAOUT_KWGAP = 9
CAOUT_TRDLY = 10
CAOUT_INVERT = 16
SPC_SEQMODE_STEPMEM0 = 340000
SPC_SEQMODE_STEPMEM8191 = 348191
SPCSEQ_SEGMENTMASK = 0x0000FFFF
SPCSEQ_NEXTSTEPMASK = 0xFFFF0000
SPCSEQ_LOOPMASK = 0x000FFFFF
SPCSEQ_ENDLOOPALWAYS = 0x00000000
SPCSEQ_ENDLOOPONTRIG = 0x40000000
SPCSEQ_END = 0x80000000
SPC_SEQMODE_AVAILMAXSEGMENT = 349900
SPC_SEQMODE_AVAILMAXSTEPS = 349901
SPC_SEQMODE_AVAILMAXLOOP = 349902
SPC_SEQMODE_AVAILFEATURES = 349903
SPC_SEQMODE_MAXSEGMENTS = 349910
SPC_SEQMODE_WRITESEGMENT = 349920
SPC_SEQMODE_STARTSTEP = 349930
SPC_SEQMODE_SEGMENTSIZE = 349940
SPC_SEQMODE_STATUS = 349950
SEQSTAT_STEPCHANGE = 0x80000000
SPC_NETBOX_TYPE = 400000
NETBOX_SERIES_MASK = 0xFF000000
NETBOX_FAMILY_MASK = 0x00FF0000
NETBOX_SPEED_MASK = 0x0000FF00
NETBOX_CHANNEL_MASK = 0x000000FF
NETBOX_SERIES_DN2 = 0x02000000
NETBOX_SERIES_DN6 = 0x06000000
NETBOX_FAMILY_20 = 0x00200000
NETBOX_FAMILY_22 = 0x00220000
NETBOX_FAMILY_44 = 0x00440000
NETBOX_FAMILY_46 = 0x00460000
NETBOX_FAMILY_47 = 0x00470000
NETBOX_FAMILY_48 = 0x00480000
NETBOX_FAMILY_49 = 0x00490000
NETBOX_FAMILY_59 = 0x00590000
NETBOX_FAMILY_60 = 0x00600000
NETBOX_FAMILY_65 = 0x00650000
NETBOX_FAMILY_66 = 0x00660000
NETBOX_FAMILY_8X = 0x00800000
NETBOX_FAMILY_80 = 0x00800000
NETBOX_FAMILY_81 = 0x00810000
NETBOX_FAMILY_82 = 0x00820000
NETBOX_FAMILY_83 = 0x00830000
NETBOX_SPEED_1 = 0x00000100
NETBOX_SPEED_2 = 0x00000200
NETBOX_SPEED_3 = 0x00000300
NETBOX_SPEED_4 = 0x00000400
NETBOX_SPEED_5 = 0x00000500
NETBOX_SPEED_6 = 0x00000600
NETBOX_SPEED_7 = 0x00000700
NETBOX_SPEED_8 = 0x00000800
NETBOX_CHANNELS_2 = 0x00000002
NETBOX_CHANNELS_4 = 0x00000004
NETBOX_CHANNELS_6 = 0x00000006
NETBOX_CHANNELS_8 = 0x00000008
NETBOX_CHANNELS_10 = 0x0000000A
NETBOX_CHANNELS_12 = 0x0000000C
NETBOX_CHANNELS_16 = 0x00000010
NETBOX_CHANNELS_20 = 0x00000014
NETBOX_CHANNELS_24 = 0x00000018
NETBOX_CHANNELS_32 = 0x00000020
NETBOX_CHANNELS_40 = 0x00000028
NETBOX_CHANNELS_48 = 0x00000030
SPC_NETBOX_SERIALNO = 400001
SPC_NETBOX_PRODUCTIONDATE = 400002
SPC_NETBOX_HWVERSION = 400003
SPC_NETBOX_SWVERSION = 400004
SPC_NETBOX_FEATURES = 400005
NETBOX_FEAT_DCPOWER = 0x1
NETBOX_FEAT_BOOTATPOWERON = 0x2
NETBOX_FEAT_EMBEDDEDSERVER = 0x4
SPC_NETBOX_CUSTOM = 400006
SPC_NETBOX_WAKEONLAN = 400007
SPC_NETBOX_MACADDRESS = 400008
SPC_NETBOX_LANIDFLASH = 400009
SPC_NETBOX_TEMPERATURE = 400010
SPC_NETBOX_SHUTDOWN = 400011
SPC_NETBOX_RESTART = 400012
SPC_NETBOX_FANSPEED0 = 400013
SPC_NETBOX_FANSPEED1 = 400014
SPC_NETBOX_TEMPERATURE_K = 400010
SPC_NETBOX_TEMPERATURE_C = 400015
SPC_NETBOX_TEMPERATURE_F = 400016
SPC_NETBOX_TEMPERATURE1_K = 400017
SPC_NETBOX_TEMPERATURE1_C = 400018
SPC_NETBOX_TEMPERATURE1_F = 400019
SPC_NETBOX_TEMPERATURE2_K = 400020
SPC_NETBOX_TEMPERATURE2_C = 400021
SPC_NETBOX_TEMPERATURE2_F = 400022
SPC_MON_V_PCIE_BUS = 500000
SPC_MON_V_CONNECTOR = 500001
SPC_MON_CARD_PWRSOURCE = 500002
CARD_PWRSOURCE_BUS = 0
CARD_PWRSOURCE_CONNECTOR = 1
SPC_MON_V_CARD_IN = 500003
SPC_MON_I_CARD_IN = 500004
SPC_MON_P_CARD_IN = 500005
SPC_MON_V_3V3 = 500006
SPC_MON_V_2V5 = 500007
SPC_MON_V_CORE = 500008
SPC_MON_V_AVTT = 500009
SPC_MON_V_AVCC = 500010
SPC_MON_V_MEMVCC = 500011
SPC_MON_V_MEMVTT = 500012
SPC_MON_V_CP_POS = 500013
SPC_MON_V_CP_NEG = 500014
SPC_MON_V_5VA = 500015
SPC_MON_V_ADCA = 500016
SPC_MON_V_ADCD = 500017
SPC_MON_V_OP_POS = 500018
SPC_MON_V_OP_NEG = 500019
SPC_MON_V_COMP_NEG = 500020
SPC_MON_V_COMP_POS = 500021
SPC_MON_T_BASE_CTRL = 500022
SPC_MON_T_MODULE_0 = 500023
SPC_MON_T_MODULE_1 = 500024
SPC_MON_TK_BASE_CTRL = 500022
SPC_MON_TK_MODULE_0 = 500023
SPC_MON_TK_MODULE_1 = 500024
SPC_MON_TC_BASE_CTRL = 500025
SPC_MON_TC_MODULE_0 = 500026
SPC_MON_TC_MODULE_1 = 500027
SPC_MON_TF_BASE_CTRL = 500028
SPC_MON_TF_MODULE_0 = 500029
SPC_MON_TF_MODULE_1 = 500030
SPC_MON_V_1V8_BASE = 500031
SPC_MON_V_1V8_MOD = 500032
SPC_MON_V_MODA_0 = 500033
SPC_MON_V_MODA_1 = 500034
SPC_MON_V_MODB_0 = 500035
SPC_MON_V_MODB_1 = 500037
SPC_MON_TK_MODA_0 = 500023
SPC_MON_TK_MODA_1 = 500038
SPC_MON_TK_MODA_2 = 500039
SPC_MON_TK_MODA_3 = 500040
SPC_MON_TK_MODA_4 = 500041
SPC_MON_TK_MODB_0 = 500024
SPC_MON_TK_MODB_1 = 500042
SPC_MON_TK_MODB_2 = 500043
SPC_MON_TK_MODB_3 = 500044
SPC_MON_TK_MODB_4 = 500045
SPC_MON_TC_MODA_0 = 500026
SPC_MON_TC_MODA_1 = 500046
SPC_MON_TC_MODA_2 = 500047
SPC_MON_TC_MODA_3 = 500048
SPC_MON_TC_MODA_4 = 500049
SPC_MON_TC_MODB_0 = 500027
SPC_MON_TC_MODB_1 = 500050
SPC_MON_TC_MODB_2 = 500051
SPC_MON_TC_MODB_3 = 500052
SPC_MON_TC_MODB_4 = 500053
SPC_MON_TF_MODA_0 = 500029
SPC_MON_TF_MODA_1 = 500054
SPC_MON_TF_MODA_2 = 500055
SPC_MON_TF_MODA_3 = 500056
SPC_MON_TF_MODA_4 = 500057
SPC_MON_TF_MODB_0 = 500030
SPC_MON_TF_MODB_1 = 500058
SPC_MON_TF_MODB_2 = 500059
SPC_MON_TF_MODB_3 = 500060
SPC_MON_TF_MODB_4 = 500061
SPC_MON_I_MODA_0 = 500062
SPC_MON_I_MODA_1 = 500063
SPC_MON_I_MODA_2 = 500064
SPC_MON_I_MODA_3 = 500065
SPC_MON_I_MODB_0 = 500066
SPC_MON_I_MODB_1 = 500067
SPC_MON_I_MODB_2 = 500068
SPC_MON_I_MODB_3 = 500069
SPC_MON_MOD_FAULT = 500070
SPC_CLR_MOD_FAULT = 500071
SPC_MON_TK_MODA_5 = 500072
SPC_MON_TK_MODB_5 = 500073
SPC_MON_TC_MODA_5 = 500074
SPC_MON_TC_MODB_5 = 500075
SPC_MON_TF_MODA_5 = 500076
SPC_MON_TF_MODB_5 = 500077
SPC_MON_V_MOD_0 = 500078
SPC_MON_V_MOD_1 = 500079
SPC_MON_V_MOD_2 = 500080
SPC_MON_V_MOD_3 = 500081
SPC_MON_V_MOD_4 = 500082
SPC_MON_V_MOD_5 = 500083
SPC_MON_V_MOD_6 = 500084
SPC_MON_V_MOD_7 = 500085
SPC_MON_V_MOD_8 = 500086
SPC_MON_V_MOD_9 = 500087
SPC_MON_V_MOD_10 = 500088
SPC_MON_TK_MODULE_2 = 500089
SPC_MON_TC_MODULE_2 = 500090
SPC_MON_TF_MODULE_2 = 500091
SPC_MON_RPM_FAN0 = 500092
SPC_MON_RPM_FAN1 = 500093
SPC_MON_V_MEMVTTA = 500094
SPC_MON_V_MEMVTTB = 500095
SPC_MON_V_VCCAUX = 500096
SPC_MON_V_MEMAUXA = 500097
SPC_MON_V_MEMAUXB = 500098
SPC_MON_TK_BASE_0 = 500099
SPC_MON_TC_BASE_0 = 500100
SPC_MON_TF_BASE_0 = 500101
SPC_MON_TK_BASE_1 = 500102
SPC_MON_TC_BASE_1 = 500103
SPC_MON_TF_BASE_1 = 500104
SPC_MON_I_CORE = 500105
SPC_MON_V_CORE_REMOTE = 500106
SPC_AVAILMONITORS = 510000
SPCM_MON_T_BASE_CTRL = 0x0000000000000001
SPCM_MON_T_MODULE_0 = 0x0000000000000002
SPCM_MON_T_MODULE_1 = 0x0000000000000004
SPCM_MON_T_MODULE_2 = 0x0000000000000008
SPCM_MON_V_PCIE_BUS = 0x0000000000000010
SPCM_MON_V_CONNECTOR = 0x0000000000000020
SPCM_MON_CARD_PWRSOURCE = 0x0000000000000040
SPCM_MON_V_CARD_IN = 0x0000000000000080
SPCM_MON_I_CARD_IN = 0x0000000000000100
SPCM_MON_P_CARD_IN = 0x0000000000000200
SPCM_MON_V_3V3 = 0x0000000000000400
SPCM_MON_V_2V5 = 0x0000000000000800
SPCM_MON_V_CORE = 0x0000000000001000
SPCM_MON_V_AVTT = 0x0000000000002000
SPCM_MON_V_AVCC = 0x0000000000004000
SPCM_MON_V_MEMVCC = 0x0000000000008000
SPCM_MON_V_MEMVTT = 0x0000000000010000
SPCM_MON_V_CP_POS = 0x0000000000020000
SPCM_MON_V_CP_NEG = 0x0000000000040000
SPCM_MON_V_5VA = 0x0000000000080000
SPCM_MON_V_ADCA = 0x0000000000100000
SPCM_MON_V_ADCD = 0x0000000000200000
SPCM_MON_V_OP_POS = 0x0000000000400000
SPCM_MON_V_OP_NEG = 0x0000000000800000
SPCM_MON_V_COMP_NEG = 0x0000000001000000
SPCM_MON_V_COMP_POS = 0x0000000002000000
SPCM_MON_V_1V8_BASE = 0x0000000004000000
SPCM_MON_V_1V8_MOD = 0x0000000008000000
SPCM_MON_V_MODA_0 = 0x0000000010000000
SPCM_MON_V_MODA_1 = 0x0000000020000000
SPCM_MON_V_MODB_0 = 0x0000000040000000
SPCM_MON_V_MODB_1 = 0x0000000080000000
SPCM_MON_T_MODA_0 = 0x0000000000000002
SPCM_MON_T_MODA_1 = 0x0000000100000000
SPCM_MON_T_MODA_2 = 0x0000000200000000
SPCM_MON_T_MODA_3 = 0x0000000400000000
SPCM_MON_T_MODA_4 = 0x0000000800000000
SPCM_MON_T_MODB_0 = 0x0000000000000004
SPCM_MON_T_MODB_1 = 0x0000001000000000
SPCM_MON_T_MODB_2 = 0x0000002000000000
SPCM_MON_T_MODB_3 = 0x0000004000000000
SPCM_MON_T_MODB_4 = 0x0000008000000000
SPCM_MON_I_MODA_0 = 0x0000010000000000
SPCM_MON_I_MODA_1 = 0x0000020000000000
SPCM_MON_I_MODA_2 = 0x0000040000000000
SPCM_MON_I_MODA_3 = 0x0000080000000000
SPCM_MON_I_MODB_0 = 0x0000100000000000
SPCM_MON_I_MODB_1 = 0x0000200000000000
SPCM_MON_I_MODB_2 = 0x0000400000000000
SPCM_MON_I_MODB_3 = 0x0000800000000000
SPCM_MON_T_MODA_5 = 0x0001000000000000
SPCM_MON_T_MODB_5 = 0x0002000000000000
SPCM_MON_V_MEMVTTA = 0x0004000000000000
SPCM_MON_V_MEMVTTB = 0x0008000000000000
SPCM_MON_V_MEMAUXA = 0x0010000000000000
SPCM_MON_V_MEMAUXB = 0x0020000000000000
SPCM_MON_V_VCCAUX = 0x0040000000000000
SPCM_MON_T_BASE_0 = 0x0080000000000000
SPCM_MON_T_BASE_1 = 0x0100000000000000
SPCM_MON_RPM_FAN0 = 0x0200000000000000
SPCM_MON_RPM_FAN1 = 0x0400000000000000
SPCM_MON_I_CORE = 0x0800000000000000
SPCM_MON_V_CORE_REMOTE = 0x1000000000000000
SPC_AVAILMONITORS1 = 510001
SPCM_MON_V_MOD_0 = 0x0000000000000001
SPCM_MON_V_MOD_1 = 0x0000000000000002
SPCM_MON_V_MOD_2 = 0x0000000000000004
SPCM_MON_V_MOD_3 = 0x0000000000000008
SPCM_MON_V_MOD_4 = 0x0000000000000010
SPCM_MON_V_MOD_5 = 0x0000000000000020
SPCM_MON_V_MOD_6 = 0x0000000000000040
SPCM_MON_V_MOD_7 = 0x0000000000000080
SPCM_MON_V_MOD_8 = 0x0000000000000100
SPCM_MON_V_MOD_9 = 0x0000000000000200
SPCM_MON_V_MOD_10 = 0x0000000000000400
SPC_X0_READFEATURES = 600000
SPC_X1_READFEATURES = 600001
SPC_X2_READFEATURES = 600002
SPC_X3_READFEATURES = 600003
SPC_X4_READFEATURES = 600004
SPC_X5_READFEATURES = 600005
SPC_X6_READFEATURES = 600006
SPC_X7_READFEATURES = 600007
SPC_X8_READFEATURES = 600008
SPC_X9_READFEATURES = 600009
SPC_X10_READFEATURES = 600010
SPC_X11_READFEATURES = 600011
SPC_X12_READFEATURES = 600012
SPC_X13_READFEATURES = 600013
SPC_X14_READFEATURES = 600014
SPC_X15_READFEATURES = 600015
SPC_X16_READFEATURES = 600016
SPC_X17_READFEATURES = 600017
SPC_X18_READFEATURES = 600018
SPC_X19_READFEATURES = 600019
SPCM_XFEAT_TERM = 0x00000001
SPCM_XFEAT_HIGHIMP = 0x00000002
SPCM_XFEAT_DCCOUPLING = 0x00000004
SPCM_XFEAT_ACCOUPLING = 0x00000008
SPCM_XFEAT_SE = 0x00000010
SPCM_XFEAT_DIFF = 0x00000020
SPCM_XFEAT_PROGTHRESHOLD = 0x00000040
SPC_X0_TERM = 600100
SPC_X1_TERM = 600101
SPC_X2_TERM = 600102
SPC_X3_TERM = 600103
SPC_X4_TERM = 600104
SPC_X5_TERM = 600105
SPC_X6_TERM = 600106
SPC_X7_TERM = 600107
SPC_X8_TERM = 600108
SPC_X9_TERM = 600109
SPC_X10_TERM = 600110
SPC_X11_TERM = 600111
SPC_X12_TERM = 600112
SPC_X13_TERM = 600113
SPC_X14_TERM = 600114
SPC_X15_TERM = 600115
SPC_X16_TERM = 600116
SPC_X17_TERM = 600117
SPC_X18_TERM = 600118
SPC_X19_TERM = 600119
SPCM_X0_MODE = 600200
SPCM_X1_MODE = 600201
SPCM_X2_MODE = 600202
SPCM_X3_MODE = 600203
SPCM_X4_MODE = 600204
SPCM_X5_MODE = 600205
SPCM_X6_MODE = 600206
SPCM_X7_MODE = 600207
SPCM_X8_MODE = 600208
SPCM_X9_MODE = 600209
SPCM_X10_MODE = 600210
SPCM_X11_MODE = 600211
SPCM_X12_MODE = 600212
SPCM_X13_MODE = 600213
SPCM_X14_MODE = 600214
SPCM_X15_MODE = 600215
SPCM_X16_MODE = 600216
SPCM_X17_MODE = 600217
SPCM_X18_MODE = 600218
SPCM_X19_MODE = 600219
SPCM_X0_AVAILMODES = 600300
SPCM_X1_AVAILMODES = 600301
SPCM_X2_AVAILMODES = 600302
SPCM_X3_AVAILMODES = 600303
SPCM_X4_AVAILMODES = 600304
SPCM_X5_AVAILMODES = 600305
SPCM_X6_AVAILMODES = 600306
SPCM_X7_AVAILMODES = 600307
SPCM_X8_AVAILMODES = 600308
SPCM_X9_AVAILMODES = 600309
SPCM_X10_AVAILMODES = 600310
SPCM_X11_AVAILMODES = 600311
SPCM_X12_AVAILMODES = 600312
SPCM_X13_AVAILMODES = 600313
SPCM_X14_AVAILMODES = 600314
SPCM_X15_AVAILMODES = 600315
SPCM_X16_AVAILMODES = 600316
SPCM_X17_AVAILMODES = 600317
SPCM_X18_AVAILMODES = 600318
SPCM_X19_AVAILMODES = 600319
SPC_NUM_XIO_LINES = 600400
SPC_XIO_PULSEGEN0_MODE = 601000
SPC_XIO_PULSEGEN1_MODE = 601100
SPC_XIO_PULSEGEN2_MODE = 601200
SPC_XIO_PULSEGEN3_MODE = 601300
SPCM_PULSEGEN_MODE_GATED = 1
SPCM_PULSEGEN_MODE_TRIGGERED = 2
SPCM_PULSEGEN_MODE_SINGLESHOT = 3
SPC_XIO_PULSEGEN0_LEN = 601001
SPC_XIO_PULSEGEN1_LEN = 601101
SPC_XIO_PULSEGEN2_LEN = 601201
SPC_XIO_PULSEGEN3_LEN = 601301
SPC_XIO_PULSEGEN0_HIGH = 601002
SPC_XIO_PULSEGEN1_HIGH = 601102
SPC_XIO_PULSEGEN2_HIGH = 601202
SPC_XIO_PULSEGEN3_HIGH = 601302
SPC_XIO_PULSEGEN0_DELAY = 601003
SPC_XIO_PULSEGEN1_DELAY = 601103
SPC_XIO_PULSEGEN2_DELAY = 601203
SPC_XIO_PULSEGEN3_DELAY = 601303
SPC_XIO_PULSEGEN0_LOOPS = 601004
SPC_XIO_PULSEGEN1_LOOPS = 601104
SPC_XIO_PULSEGEN2_LOOPS = 601204
SPC_XIO_PULSEGEN3_LOOPS = 601304
SPC_XIO_PULSEGEN0_MUX1_SRC = 601005
SPC_XIO_PULSEGEN1_MUX1_SRC = 601105
SPC_XIO_PULSEGEN2_MUX1_SRC = 601205
SPC_XIO_PULSEGEN3_MUX1_SRC = 601305
SPCM_PULSEGEN_MUX1_SRC_UNUSED = 0
SPCM_PULSEGEN_MUX1_SRC_RUN = 1
SPCM_PULSEGEN_MUX1_SRC_ARM = 2
SPC_XIO_PULSEGEN0_MUX2_SRC = 601006
SPC_XIO_PULSEGEN1_MUX2_SRC = 601106
SPC_XIO_PULSEGEN2_MUX2_SRC = 601206
SPC_XIO_PULSEGEN3_MUX2_SRC = 601306
SPCM_PULSEGEN_MUX2_SRC_UNUSED = 0
SPCM_PULSEGEN_MUX2_SRC_SOFTWARE = 1
SPCM_PULSEGEN_MUX2_SRC_CARDTRIGGER = 2
SPCM_PULSEGEN_MUX2_SRC_PULSEGEN0 = 3
SPCM_PULSEGEN_MUX2_SRC_PULSEGEN1 = 4
SPCM_PULSEGEN_MUX2_SRC_PULSEGEN2 = 5
SPCM_PULSEGEN_MUX2_SRC_PULSEGEN3 = 6
SPCM_PULSEGEN_MUX2_SRC_XIO0 = 7
SPCM_PULSEGEN_MUX2_SRC_XIO1 = 8
SPCM_PULSEGEN_MUX2_SRC_XIO2 = 9
SPCM_PULSEGEN_MUX2_SRC_XIO3 = 10
SPC_XIO_PULSEGEN0_CONFIG = 601007
SPC_XIO_PULSEGEN1_CONFIG = 601107
SPC_XIO_PULSEGEN2_CONFIG = 601207
SPC_XIO_PULSEGEN3_CONFIG = 601307
SPCM_PULSEGEN_CONFIG_MUX1_INVERT = 0x1
SPCM_PULSEGEN_CONFIG_MUX2_INVERT = 0x2
SPCM_PULSEGEN_CONFIG_INVERT = 0x4
SPCM_PULSEGEN_CONFIG_HIGH = 0x8
SPC_XIO_PULSEGEN_ENABLE = 601500
SPCM_PULSEGEN_ENABLE0 = 0x01
SPCM_PULSEGEN_ENABLE1 = 0x02
SPCM_PULSEGEN_ENABLE2 = 0x04
SPCM_PULSEGEN_ENABLE3 = 0x08
SPC_XIO_PULSEGEN_COMMAND = 601501
SPCM_PULSEGEN_CMD_FORCE = 0x1
SPC_XIO_PULSEGEN_CLOCK = 602000
SPC_XIO_PULSEGEN_AVAILLEN_MIN = 602001
SPC_XIO_PULSEGEN_AVAILLEN_MAX = 602002
SPC_XIO_PULSEGEN_AVAILLEN_STEP = 602003
SPC_XIO_PULSEGEN_AVAILHIGH_MIN = 602004
SPC_XIO_PULSEGEN_AVAILHIGH_MAX = 602005
SPC_XIO_PULSEGEN_AVAILHIGH_STEP = 602006
SPC_XIO_PULSEGEN_AVAILDELAY_MIN = 602007
SPC_XIO_PULSEGEN_AVAILDELAY_MAX = 602008
SPC_XIO_PULSEGEN_AVAILDELAY_STEP = 602009
SPC_XIO_PULSEGEN_AVAILLOOPS_MIN = 602010
SPC_XIO_PULSEGEN_AVAILLOOPS_MAX = 602011
SPC_XIO_PULSEGEN_AVAILLOOPS_STEP = 602012
SPC_DDS_CORE0_FREQ = 603000
SPC_DDS_CORE1_FREQ = 603001
SPC_DDS_CORE2_FREQ = 603002
SPC_DDS_CORE3_FREQ = 603003
SPC_DDS_CORE4_FREQ = 603004
SPC_DDS_CORE5_FREQ = 603005
SPC_DDS_CORE6_FREQ = 603006
SPC_DDS_CORE7_FREQ = 603007
SPC_DDS_CORE8_FREQ = 603008
SPC_DDS_CORE9_FREQ = 603009
SPC_DDS_CORE10_FREQ = 603010
SPC_DDS_CORE11_FREQ = 603011
SPC_DDS_CORE12_FREQ = 603012
SPC_DDS_CORE13_FREQ = 603013
SPC_DDS_CORE14_FREQ = 603014
SPC_DDS_CORE15_FREQ = 603015
SPC_DDS_CORE16_FREQ = 603016
SPC_DDS_CORE17_FREQ = 603017
SPC_DDS_CORE18_FREQ = 603018
SPC_DDS_CORE19_FREQ = 603019
SPC_DDS_CORE20_FREQ = 603020
SPC_DDS_CORE21_FREQ = 603021
SPC_DDS_CORE22_FREQ = 603022
SPC_DDS_CORE0_FREQ_SLOPE = 604000
SPC_DDS_CORE1_FREQ_SLOPE = 604001
SPC_DDS_CORE2_FREQ_SLOPE = 604002
SPC_DDS_CORE3_FREQ_SLOPE = 604003
SPC_DDS_CORE4_FREQ_SLOPE = 604004
SPC_DDS_CORE5_FREQ_SLOPE = 604005
SPC_DDS_CORE6_FREQ_SLOPE = 604006
SPC_DDS_CORE7_FREQ_SLOPE = 604007
SPC_DDS_CORE8_FREQ_SLOPE = 604008
SPC_DDS_CORE9_FREQ_SLOPE = 604009
SPC_DDS_CORE10_FREQ_SLOPE = 604010
SPC_DDS_CORE11_FREQ_SLOPE = 604011
SPC_DDS_CORE12_FREQ_SLOPE = 604012
SPC_DDS_CORE13_FREQ_SLOPE = 604013
SPC_DDS_CORE14_FREQ_SLOPE = 604014
SPC_DDS_CORE15_FREQ_SLOPE = 604015
SPC_DDS_CORE16_FREQ_SLOPE = 604016
SPC_DDS_CORE17_FREQ_SLOPE = 604017
SPC_DDS_CORE18_FREQ_SLOPE = 604018
SPC_DDS_CORE19_FREQ_SLOPE = 604019
SPC_DDS_CORE20_FREQ_SLOPE = 604020
SPC_DDS_CORE21_FREQ_SLOPE = 604021
SPC_DDS_CORE22_FREQ_SLOPE = 604022
SPC_DDS_CORE0_AMP = 605000
SPC_DDS_CORE1_AMP = 605001
SPC_DDS_CORE2_AMP = 605002
SPC_DDS_CORE3_AMP = 605003
SPC_DDS_CORE4_AMP = 605004
SPC_DDS_CORE5_AMP = 605005
SPC_DDS_CORE6_AMP = 605006
SPC_DDS_CORE7_AMP = 605007
SPC_DDS_CORE8_AMP = 605008
SPC_DDS_CORE9_AMP = 605009
SPC_DDS_CORE10_AMP = 605010
SPC_DDS_CORE11_AMP = 605011
SPC_DDS_CORE12_AMP = 605012
SPC_DDS_CORE13_AMP = 605013
SPC_DDS_CORE14_AMP = 605014
SPC_DDS_CORE15_AMP = 605015
SPC_DDS_CORE16_AMP = 605016
SPC_DDS_CORE17_AMP = 605017
SPC_DDS_CORE18_AMP = 605018
SPC_DDS_CORE19_AMP = 605019
SPC_DDS_CORE20_AMP = 605020
SPC_DDS_CORE21_AMP = 605021
SPC_DDS_CORE22_AMP = 605022
SPC_DDS_CORE0_AMP_SLOPE = 606000
SPC_DDS_CORE1_AMP_SLOPE = 606001
SPC_DDS_CORE2_AMP_SLOPE = 606002
SPC_DDS_CORE3_AMP_SLOPE = 606003
SPC_DDS_CORE4_AMP_SLOPE = 606004
SPC_DDS_CORE5_AMP_SLOPE = 606005
SPC_DDS_CORE6_AMP_SLOPE = 606006
SPC_DDS_CORE7_AMP_SLOPE = 606007
SPC_DDS_CORE8_AMP_SLOPE = 606008
SPC_DDS_CORE9_AMP_SLOPE = 606009
SPC_DDS_CORE10_AMP_SLOPE = 606010
SPC_DDS_CORE11_AMP_SLOPE = 606011
SPC_DDS_CORE12_AMP_SLOPE = 606012
SPC_DDS_CORE13_AMP_SLOPE = 606013
SPC_DDS_CORE14_AMP_SLOPE = 606014
SPC_DDS_CORE15_AMP_SLOPE = 606015
SPC_DDS_CORE16_AMP_SLOPE = 606016
SPC_DDS_CORE17_AMP_SLOPE = 606017
SPC_DDS_CORE18_AMP_SLOPE = 606018
SPC_DDS_CORE19_AMP_SLOPE = 606019
SPC_DDS_CORE20_AMP_SLOPE = 606020
SPC_DDS_CORE21_AMP_SLOPE = 606021
SPC_DDS_CORE22_AMP_SLOPE = 606022
SPC_DDS_CORE0_PHASE = 607000
SPC_DDS_CORE1_PHASE = 607001
SPC_DDS_CORE2_PHASE = 607002
SPC_DDS_CORE3_PHASE = 607003
SPC_DDS_CORE4_PHASE = 607004
SPC_DDS_CORE5_PHASE = 607005
SPC_DDS_CORE6_PHASE = 607006
SPC_DDS_CORE7_PHASE = 607007
SPC_DDS_CORE8_PHASE = 607008
SPC_DDS_CORE9_PHASE = 607009
SPC_DDS_CORE10_PHASE = 607010
SPC_DDS_CORE11_PHASE = 607011
SPC_DDS_CORE12_PHASE = 607012
SPC_DDS_CORE13_PHASE = 607013
SPC_DDS_CORE14_PHASE = 607014
SPC_DDS_CORE15_PHASE = 607015
SPC_DDS_CORE16_PHASE = 607016
SPC_DDS_CORE17_PHASE = 607017
SPC_DDS_CORE18_PHASE = 607018
SPC_DDS_CORE19_PHASE = 607019
SPC_DDS_CORE20_PHASE = 607020
SPC_DDS_CORE21_PHASE = 607021
SPC_DDS_CORE22_PHASE = 607022
SPC_DDS_TRG_SRC = 608000
SPCM_DDS_TRG_SRC_NONE = 0
SPCM_DDS_TRG_SRC_TIMER = 1
SPCM_DDS_TRG_SRC_CARD = 2
SPC_DDS_TRG_TIMER = 608001
SPC_DDS_PHASE_BEHAVIOUR = 608002
SPCM_DDS_PHASE_JUMP = 0
SPCM_DDS_PHASE_SHIFT = 1
SPC_DDS_CMD = 608003
SPCM_DDS_CMD_RESET = 0x01
SPCM_DDS_CMD_EXEC_AT_TRG = 0x02
SPCM_DDS_CMD_EXEC_NOW = 0x04
SPCM_DDS_CMD_WRITE_TO_CARD = 0x08
SPCM_DDS_CMD_NO_NOP_FILL = 0x10
SPC_DDS_STATUS = 608004
SPCM_DDS_STAT_WAITING_FOR_TRG = 0x01
SPCM_DDS_STAT_QUEUE_UNDERRUN = 0x02
SPC_DDS_QUEUE_CMD_MAX = 608005
SPC_DDS_QUEUE_CMD_COUNT = 608006
SPC_DDS_NUM_CORES = 608007
SPC_DDS_FREQ_RAMP_STEPSIZE = 608008
SPC_DDS_AMP_RAMP_STEPSIZE = 608009
SPC_DDS_X_MANUAL_OUTPUT = 608010
SPCM_DDS_X0 = 0x1
SPCM_DDS_X1 = 0x2
SPCM_DDS_X2 = 0x4
SPC_DDS_NUM_QUEUED_CMD_IN_SW = 608011
SPC_DDS_DATA_TRANSFER_MODE = 608012
SPCM_DDS_DTM_SINGLE = 0
SPCM_DDS_DTM_DMA = 1
SPC_DDS_TRG_COUNT = 608013
SPC_DDS_CORES_ON_CH0 = 608040
SPC_DDS_CORES_ON_CH1 = 608041
SPC_DDS_CORES_ON_CH2 = 608042
SPC_DDS_CORES_ON_CH3 = 608043
SPCM_DDS_CORE_NONE = 0x000000
SPCM_DDS_CORE0 = 0x000001
SPCM_DDS_CORE1 = 0x000002
SPCM_DDS_CORE2 = 0x000004
SPCM_DDS_CORE3 = 0x000008
SPCM_DDS_CORE4 = 0x000010
SPCM_DDS_CORE5 = 0x000020
SPCM_DDS_CORE6 = 0x000040
SPCM_DDS_CORE7 = 0x000080
SPCM_DDS_CORE8 = 0x000100
SPCM_DDS_CORE9 = 0x000200
SPCM_DDS_CORE10 = 0x000400
SPCM_DDS_CORE11 = 0x000800
SPCM_DDS_CORE12 = 0x001000
SPCM_DDS_CORE13 = 0x002000
SPCM_DDS_CORE14 = 0x004000
SPCM_DDS_CORE15 = 0x008000
SPCM_DDS_CORE16 = 0x010000
SPCM_DDS_CORE17 = 0x020000
SPCM_DDS_CORE18 = 0x040000
SPCM_DDS_CORE19 = 0x080000
SPCM_DDS_CORE20 = 0x100000
SPCM_DDS_CORE21 = 0x200000
SPCM_DDS_CORE22 = 0x400000
SPC_DDS_X0_MODE = 608060
SPC_DDS_X1_MODE = 608061
SPC_DDS_X2_MODE = 608062
SPCM_DDS_XMODE_MANUAL = 1
SPCM_DDS_XMODE_WAITING_FOR_TRG = 2
SPCM_DDS_XMODE_EXEC = 3
SPC_DDS_AVAIL_FREQ_MIN = 608500
SPC_DDS_AVAIL_FREQ_MAX = 608501
SPC_DDS_AVAIL_FREQ_STEP = 608502
SPC_DDS_AVAIL_FREQ_SLOPE_MIN = 608503
SPC_DDS_AVAIL_FREQ_SLOPE_MAX = 608504
SPC_DDS_AVAIL_FREQ_SLOPE_STEP = 608505
SPC_DDS_AVAIL_AMP_MIN = 608506
SPC_DDS_AVAIL_AMP_MAX = 608507
SPC_DDS_AVAIL_AMP_STEP = 608508
SPC_DDS_AVAIL_AMP_SLOPE_MIN = 608509
SPC_DDS_AVAIL_AMP_SLOPE_MAX = 608510
SPC_DDS_AVAIL_AMP_SLOPE_STEP = 608511
SPC_DDS_AVAIL_PHASE_MIN = 608512
SPC_DDS_AVAIL_PHASE_MAX = 608513
SPC_DDS_AVAIL_PHASE_STEP = 608514
SPC_REG0x00 = 900000
SPC_REG0x02 = 900010
SPC_REG0x04 = 900020
SPC_REG0x06 = 900030
SPC_REG0x08 = 900040
SPC_REG0x0A = 900050
SPC_REG0x0C = 900060
SPC_REG0x0E = 900070
SPC_DEBUGREG0 = 900100
SPC_DEBUGREG15 = 900115
SPC_DEBUGVALUE0 = 900200
SPC_DEBUGVALUE15 = 900215
SPC_MI_ISP = 901000
ISP_TMS_0 = 0
ISP_TMS_1 = 1
ISP_TDO_0 = 0
ISP_TDO_1 = 2
SPC_EE_RWAUTH = 901100
SPC_EE_REG = 901110
SPC_EE_RESETCOUNTER = 901120
SPC_TEST_BASE = 902000
SPC_TEST_LOCAL_START = 902100
SPC_TEST_LOCAL_END = 902356
SPC_TEST_PLX_START = 902400
SPC_TEST_PLX_END = 902656
SPC_FUNCTION_DEFTRANSFER = 100000000
