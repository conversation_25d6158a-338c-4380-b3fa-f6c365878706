import time

from line_profiler import profile

from .pyspcm import *
import numpy as np
from .pyspcm import _U
from modules.config import Config, LOADED_PARAMETER


class SpcmAWG:
    """
    打开 star-hub 的逻辑：
    1. 打开两个卡, 系统会自动探测两个卡上的 star-hub
    2. 打开 star-hub

    """

    def __init__(self, IP="**************"):
        """
        双卡初始化
        1. 通过 IP 构造两个 Visa 地址，分别打开两个卡
        2. 打开 star-hub
        3. 设置卡的同步

        """
        # self.parameter = Config(DEFAULT_PARAMETER)
        # self.parameter = Config()
        self.parameter = LOADED_PARAMETER()
        if not isinstance(IP, bytes):
            # 将 ip 转换成二进制字节流
            IP = IP.encode("utf-8")
        address_0 = b"TCPIP::" + IP + b"::inst%d::INSTR" % 0
        address_1 = b"TCPIP::" + IP + b"::inst%d::INSTR" % 1
        print("Opening card_0 %s..." % address_0)
        self.card_0 = AwgCard(address=address_0)
        print("Opening card_1 %s..." % address_1)
        self.card_1 = AwgCard(address=address_1)
        # 打开 star-hub
        self.star_hub = spcm_hOpen(b"sync0")
        # 设置两个卡的同步
        self.init_hub()
        print("Init AWG hub success")

    def close(self):
        """
        关闭 star-hub
        """
        self.stop()  # 先调用停止波形的代码
        spcm_vClose(self.star_hub)
        self.card_0.close()
        self.card_1.close()

    def start(self):
        """
        启动 star-hub
        """

        self._set_star_hub_register(
            SPC_M2CMD, M2CMD_CARD_START | M2CMD_CARD_ENABLETRIGGER
        )

    def stop(self):
        """
        停止 star-hub
        """
        self.card_0.set_card_stop()
        self.card_1.set_card_stop()
        self._set_star_hub_register(SPC_M2CMD, M2CMD_CARD_STOP)

    def hub_force_trigger(self):
        """调试用的强制软触发"""
        self._set_star_hub_register(SPC_M2CMD, M2CMD_CARD_FORCETRIGGER)

    def init_hub(self):
        """
        初始化 hub 的状态，包括仪器初始化和同步时钟初始化
        """
        # 0. 初始化仪器
        # self.init_card()
        # config 文件里设置的单位是 v，而这里的单位是mv
        self.card_0.init_config(
            full_voltage=self.parameter.Light_554.AOM_AWG_full_Voltage * 1000
        )
        self.card_1.init_config(
            full_voltage=self.parameter.Light_554.AOD_AWG_full_Voltage * 1000
        )
        print("DDS AOM TOP Voltage:", self.parameter.Light_554.AOM_AWG_full_Voltage)
        print("DDS AOD TOP Voltage:", self.parameter.Light_554.AOD_AWG_full_Voltage)
        # 1. 设置
        self._set_star_hub_register(SPC_SYNC_ENABLEMASK, 3)  # 二进制对应 0011 两个卡 mask
        # 2. 设置卡 1 的时钟源为外部时钟源(卡 1 是 star-hub 所在)
        self.card_1.set_card_clock_mode(
            clock_mode=0
        )  # 1 是内部时钟源, 0 是外部时钟源
        # 3. 设置外部时钟的参考频率
        self.card_1.set_card_reference_clock(
            10000000
        )  # 设置参考时钟，被用于计算内部时钟频率， 如果设置的时钟源是内部时钟源，该指令无效

        # 4. 设置卡的 触发 模式
        # 1 号卡触发模式设置为 trig 0 号口的外部触发
        self.card_1.set_card_ORMask_source(source=0)  # ext_trig 0
        self.card_0.set_card_ORMask_source(source=2)  # None

        # 0 号和 1 号卡的 ORMASK 触发模式设置
        self.card_1.set_card_ext_trigger_mode(mode=1)  # 1 号卡设置检测上升沿
        # self.card_0.set_card_ext_trigger_mode(mode=0)  # 0 号卡设置无不检测外部触发，共用同步系统中的 1 号卡触发

        # # 1 号卡的 ANDMASK 模式设置为不检测（感觉不需要这个）
        self.card_1.set_card_ANDMask_source(source=2)  # 取消 AND mask 的作用
        # 将设置写入卡中
        self.card_1.set_card_write_setup()
        self.card_0.set_card_write_setup()
        print("dds mode_0", self.card_0.get_dds_data_transfer_mode())
        print("dds mode_1", self.card_0.get_dds_data_transfer_mode())

        self.start()
        # self.set_trigger_sourse()
        # self.hub_force_trigger()

    def write_dds_cmd_to_card(self):
        """
        向卡写入命令
        :return:
        """
        self.card_0.set_dds_cmd_write_to_card()
        self.card_1.set_dds_cmd_write_to_card()

    def get_max_connected_card(self):
        """
        获取两个卡的连接状态
        """
        ret = self._get_star_hub_register(SPC_SYNC_READ_NUMCONNECTORS)
        print("max connected card: {}".format(ret))

    def get_connected_card(self):
        """
        获取已连接的卡数量
        :return:
        """
        ret = self._get_star_hub_register(SPC_SYNC_READ_SYNCCOUNT)
        print("connected card: {}".format(ret))

    def _get_star_hub_register(self, register):
        """
        获取 star-hub 的信息
        """
        data_buffer = c_uint64(0)
        ret = spcm_dwGetParam_i64(self.star_hub, register, byref(data_buffer))
        if ret != 0:
            print(self._get_param_error_code())
            raise Exception("can't get register {}".format(register))
        else:
            return data_buffer.value

    def _set_star_hub_register(self, register, value):
        """
        设置 star-hub 的参数
        :param register:
        :param value:
        :return:
        """
        ret = spcm_dwSetParam_i64(self.star_hub, register, value)
        if ret != 0:
            # print(ret)
            print(self._get_param_error_code())
            raise Exception("can't set register {}".format(register))
        else:
            pass

    def _get_param_error_code(self):
        """获取错误码"""
        error = create_string_buffer(ERRORTEXTLEN)
        spcm_dwGetErrorInfo_i32(self.star_hub, None, None, error)
        return error.value

    def get_dds_status(self):
        """
        获取 DDS 的状态
        :return:
        """
        status1 = self.card_0.get_dds_status()
        status2 = self.card_1.get_dds_status()
        return status1, status2

    def set_trigger_sourse(self):
        # 6. 设置触发源，第一个触发源是 card 触发
        self.card_1.set_dds_trigger_source(source=2)
        self.card_0.set_dds_trigger_source(source=2)
        self.card_1.set_dds_cmd_exec_at_trigger()
        self.card_0.set_dds_cmd_exec_at_trigger()
        self.card_1.set_dds_cmd_exec_now()  # 触发源更改
        self.card_0.set_dds_cmd_exec_now()  # 触发源更改


class AwgCard:
    """
    本模块中的指令分为三个层级
    0. 参数设置和获取的基本指令
    1. card
    2. channel
    3. dds
    """

    # 通道与 DDS core 的映射关系
    channel_core_map = [
        [
            SPCM_DDS_CORE0,
            SPCM_DDS_CORE1,
            SPCM_DDS_CORE2,
            SPCM_DDS_CORE3,
            SPCM_DDS_CORE4,
        ],
        [
            SPCM_DDS_CORE8,
            SPCM_DDS_CORE9,
            SPCM_DDS_CORE10,
            SPCM_DDS_CORE11,
            SPCM_DDS_CORE20,
        ],
    ]

    channel_core_map_index = [
        [
            0,
            1,
            2,
            3,
            4,
        ],
        [
            8,
            9,
            10,
            11,
            12,
        ],
    ]

    def __init__(self, address=b"TCPIP::**************::inst0::INSTR"):
        # 1. 打开 VISA 地址所对应的仪器
        self.used_core = 2
        self.card_handle = spcm_hOpen(create_string_buffer(address))
        if self.card_handle is None:
            raise Exception("can't open card")
        else:
            print("get card handle, card open success")

    def init_trigger_config(self):
        self.set_card_ORMask_source()
        self.set_card_ANDMask_source()

    def init_config(self, full_voltage=100):
        """初始化仪器配置"""
        # time_1 = time.time()
        # 2. 设置成 dds 模式
        self.set_card_work_mode()
        # 3. 通道使能
        self.set_channels_enable()
        # time_3 = time.time()
        # 4. 输出电压
        self.set_channels_amplitude(amplitude=full_voltage)  # 单位是 mv
        # time_4 = time.time()
        # 4.1 设置卡的采样率
        # self.set_card_sample_rate(sample_rate=int(1e9))
        self.set_card_trig_term(1)
        # print(f"trig impedance config: {self.get_card_trig_term()}")

        # print(f"trig couple config: {self.get_card_trig_couple()}")
        self.set_card_trig_couple("DC")
        # print(f"trig couple config: {self.get_card_trig_couple()}")

        # print(f"trig level: {self.get_card_trig_level(500)}")

        self.set_card_trig_level(1000)
        # print(f"trig level: {self.get_card_trig_level()}")
        # 时钟设置，设置为 star-hub 的外部触发时钟
        self.set_card_write_setup()  # 将前面设定的指令全部写入卡中
        # time_5 = time.time()
        # 5. 重置所有 dds 相关的寄存器
        self.set_dds_reset()
        # time_6 = time.time()

        # time_7 = time.time()
        # 7. 分配 dds core
        self.set_dds_core_distribution()
        # time_8 = time.time()
        # 8. 相位模式
        self.set_dds_phase_mode(mode=0)  # 绝对相位
        # time_9 = time.time()
        # 9. 数据传输模式
        self.set_dds_data_transfer_mode(mode=1)
        # print("dds mode", self.get_dds_data_transfer_mode())
        # time_10 = time.time()
        # end. 设置写入卡中
        # self.set_dds_cmd_exec_at_trigger()
        # self.set_dds_cmd_exec_now()
        self.set_dds_cmd_write_to_card()
        # self.set_card_force_trigger()
        # self.set_card_force_trigger()

    def close(self):
        spcm_vClose(self.card_handle)

    def _set_param_i64(self, register, value):
        """设置某项参数"""
        ret = spcm_dwSetParam_i64(self.card_handle, register, value)
        if ret != 0:
            print(self._get_param_error_code())
            raise Exception("set {} failed".format(register))
        else:

            pass

    def _set_param_i32(self, register, value):
        """设置某个短参数
        目前用于 Spectrum 卡级别的指令

        """
        ret = spcm_dwSetParam_i32(self.card_handle, register, value)
        if ret != 0:
            print(self._get_param_error_code())
            raise Exception("set {} failed".format(register))
        else:

            print("set {} to {}".format(register, value))

    def _set_param_d64(self, register, value):
        """设置 dds 参数，有些是双精度浮点数"""

        ret = spcm_dwSetParam_d64(self.card_handle, register, value)
        if ret != 0:
            print(self._get_param_error_code())
            raise Exception("set {} failed".format(register))
        else:
            # print("set {} to {}".format(register, value))
            pass

    def _get_param_i64(self, register):
        """获取某项参数"""
        data_buffer = c_uint64(0)
        ret = spcm_dwGetParam_i64(self.card_handle, register, byref(data_buffer))
        if ret != 0:
            print(self._get_param_error_code())
            raise Exception("can't get register {}".format(register))
        else:
            return data_buffer.value

    def _get_param_d64(self, register):
        data_buffer = c_double(0)
        ret = spcm_dwGetParam_d64(self.card_handle, register, byref(data_buffer))
        if ret != 0:
            print(self._get_param_error_code())
            raise Exception("get {} failed".format(register))
        else:
            return data_buffer.value

    def _get_param_error_code(self):
        """获取错误码"""
        error = create_string_buffer(ERRORTEXTLEN)
        spcm_dwGetErrorInfo_i32(self.card_handle, None, None, error)
        return error.value

    def set_card_work_mode(self, card_mode=SPC_REP_STD_DDS):
        """设置卡的工作模式。设置为 DDS 模式"""
        self._set_param_i64(SPC_CARDMODE, card_mode)

    def set_card_clock_mode(self, clock_mode=0):
        """设置时钟模式"""
        if clock_mode == 1:
            self._set_param_i32(SPC_CLOCKMODE, SPC_CM_INTPLL)  # 内部时钟源
        elif clock_mode == 0:
            self._set_param_i32(SPC_CLOCKMODE, SPC_CM_EXTREFCLOCK)  # 外部参考时钟
        else:
            raise Exception("clock mode error")

    def set_card_reference_clock(self, clock_frequency=10000000):
        """设置时钟频率"""
        self._set_param_i64(SPC_REFERENCECLOCK, clock_frequency)

    def set_card_sample_rate(self, sample_rate=1e9):
        """设置采样率"""
        self._set_param_i64(SPC_SAMPLERATE, sample_rate)

    def get_card_sample_rate(self):
        """获取采样率"""
        return self._get_param_i64(SPC_SAMPLERATE)

    def set_card_trig_term(self, value):
        """设置触发阻抗
        1. 50 Ohm
        0. high termination
        """
        self._set_param_i64(SPC_TRIG_TERM, value)

    def get_card_trig_term(self):
        """获取此时的触发阻抗
        """
        return self._get_param_i64(SPC_TRIG_TERM)

    def set_card_trig_couple(self, value):
        """设置当前的触发耦合"""
        if value == "DC":
            value = COUPLING_DC
        elif value == "AC":
            value = COUPLING_AC
        else:
            raise Exception("Unsupported trigger couple")
        self._set_param_i64(SPC_TRIG_EXT0_ACDC, value)

    def get_card_trig_couple(self):
        return self._get_param_i64(SPC_TRIG_EXT0_ACDC)

    def set_card_trig_level(self, value):
        """设置触发电平"""
        self._set_param_i64(SPC_TRIG_EXT0_LEVEL0, value)

    def get_card_trig_level(self):
        """获取当前触发电平"""
        return self._get_param_i64(SPC_TRIG_EXT0_LEVEL0)

    def set_card_ORMask_source(self, source=0):
        """
        设置 or mask 触发的模式，该设置要在双卡同步函数中设置
        :param source:
        :return:
        """
        if source == 0:
            self._set_param_i64(SPC_TRIG_ORMASK, SPC_TMASK_EXT0)  # 0 号口接收触发

        elif source == 1:
            self._set_param_i64(SPC_TRIG_ORMASK, SPC_TMASK_EXT1)  # 1 号口触发

        elif source == 2:
            self._set_param_i64(SPC_TRIG_ORMASK, SPC_TMASK_NONE)

        elif source == 3:
            self._set_param_i64(SPC_TRIG_ORMASK, SPC_TMASK_SOFTWARE)

        else:
            raise Exception("trigger mode error")

    def set_card_ANDMask_source(self, source=2):
        """
        设置 and mask 触发的模式，该设置要在双卡同步函数中设置
        :param source:
        :return:
        """
        if source == 0:
            self._set_param_i64(SPC_TRIG_ANDMASK, SPC_TMASK_EXT0)
        elif source == 1:
            self._set_param_i64(SPC_TRIG_ANDMASK, SPC_TMASK_EXT1)
        elif source == 2:
            self._set_param_i64(SPC_TRIG_ANDMASK, SPC_TM_NONE)

    def get_card_ANDMask_source(self):
        return self._get_param_i64(SPC_TRIG_ANDMASK)

    def set_card_ext_trigger_mode(self, mode=1):
        """设置外部触发的电平检测模式"""
        if mode == 0:
            self._set_param_i64(SPC_TRIG_EXT0_MODE, SPC_TM_NONE)  # 检测无电平
        elif mode == 1:
            self._set_param_i64(SPC_TRIG_EXT0_MODE, SPC_TM_POS)  # 检测高电平
        else:
            raise Exception("trigger mode error")

    def set_card_write_setup(self):
        """将前面设定卡基本特性写入"""
        self._set_param_i64(SPC_M2CMD, M2CMD_CARD_WRITESETUP)

    def set_card_start(self):
        """使得卡可以工作，并设置等待触发状态
        M2CMD_CARD_ENABLETRIGGER: 使得触发可用
        M2CMD_CARD_FORCETRIGGER：使得紧跟着一个软触发
        """
        self._set_param_i64(SPC_M2CMD, M2CMD_CARD_START | M2CMD_CARD_ENABLETRIGGER)

    def set_card_force_trigger(self):
        """使能触发"""
        self._set_param_i64(SPC_M2CMD, M2CMD_CARD_FORCETRIGGER)

    def set_card_stop(self):
        """停止卡工作"""
        self._set_param_i64(SPC_M2CMD, M2CMD_CARD_STOP)

    def get_card_mode(self):
        """检查卡的工作模式。"""
        return self._get_param_i64(SPC_CARDMODE)

    def get_card_available_mode(self):
        """获取卡支持的工作模式"""
        return bin(self._get_param_i64(SPC_AVAILCARDMODES))

    def get_card_status(self):
        """检查卡的状态"""
        return self._get_param_i64(SPC_M2STATUS)

    def get_card_trigger_count(self):
        """返回当前接收到的 trigger 数量"""
        return self._get_param_i64(SPC_TRIGGERCOUNTER)

    def set_dds_data_transfer_mode(self, mode=1):
        """设置数据传输模式"""
        if mode == 0:
            self._set_param_i64(SPC_DDS_DATA_TRANSFER_MODE, SPCM_DDS_DTM_SINGLE)  # 单次模式
        elif mode == 1:
            self._set_param_i64(SPC_DDS_DATA_TRANSFER_MODE, SPCM_DDS_DTM_DMA)  # DMA 模式
        else:
            raise ValueError("mode must be 0 or 1")

    def get_dds_data_transfer_mode(self):
        return self._get_param_i64(SPC_DDS_DATA_TRANSFER_MODE)

    def set_channels_enable(self):
        """通道使能
        每张卡的两个通道都设定为可用
        """
        self._set_param_i64(SPC_ENABLEOUT0, 1)
        self._set_param_i64(SPC_ENABLEOUT1, 1)

    def set_channels_amplitude(self, amplitude):
        """设置通道电压值，单位是 mV
        最大值 2000
        最小值 80
        """
        if amplitude < 80 or amplitude > 2000:
            raise ValueError("电压值必须在80到2000毫伏之间。")

        self._set_param_d64(SPC_AMP0, amplitude)
        self._set_param_d64(SPC_AMP1, amplitude)

    def get_dds_queue_max_size_of_card(self):
        """获取卡能传入的 dds 指令的最大长度"""
        data = self._get_param_i64(SPC_DDS_QUEUE_CMD_MAX)
        return data

    def get_dds_queue_size_in_card(self):
        """返回当前 dds 机箱中，queue 的实际长度"""
        data = self._get_param_i64(SPC_DDS_QUEUE_CMD_COUNT)
        return data

    def get_dds_queue_size_in_pc(self):
        """返回 PC library 之中的 cmd 长度
        这些指令是还没有写入 dds 卡的
        """
        data = self._get_param_i64(SPC_DDS_NUM_QUEUED_CMD_IN_SW)
        return data

    def get_dds_num_of_cores(self):
        """返回卡的 dds 模块数量"""
        data = self._get_param_i64(SPC_DDS_NUM_CORES)
        return data

    def get_dds_trigger_count(self):
        """返回自 dds reset 之后，机子收到了多少个触发"""
        dara = self._get_param_i64(SPC_DDS_TRG_COUNT)
        return dara

    def get_dds_core_status(self):
        """返回 dds 模块的状态"""
        channel0 = self._get_param_i64(SPC_DDS_CORES_ON_CH0)
        channel1 = self._get_param_i64(SPC_DDS_CORES_ON_CH1)
        # channel2 = self.get_param(SPC_DDS_CORES_ON_CH2)
        # channel3 = self.get_param(SPC_DDS_CORES_ON_CH3)
        # 将所有数据转化成 2 进制 bitmap
        # data = [bin(channel0), bin(channel1), bin(channel2), bin(channel3)]
        data = [bin(channel0), bin(channel1)]
        return data

    def set_dds_core_distribution(self):
        """设置 dds core 对通道的分布"""
        # 1 通道设置 8、9、10、11、20 五个 core
        self._set_param_i64(
            SPC_DDS_CORES_ON_CH1,
            SPCM_DDS_CORE8
            | SPCM_DDS_CORE9
            | SPCM_DDS_CORE10
            | SPCM_DDS_CORE11
            | SPCM_DDS_CORE20,
        )

    def set_dds_reset(self):
        """重置 dds 相关的所有寄存器，在配置dds之前的初始化行为"""
        self._set_param_i64(SPC_DDS_CMD, SPCM_DDS_CMD_RESET)

    def set_dds_cmd_exec_now(self):
        """立即执行 dds 指令"""
        self._set_param_i64(SPC_DDS_CMD, SPCM_DDS_CMD_EXEC_NOW)

    def set_dds_amp_phase_fre(self, channel, core_index, amp, phase, fre):
        """设置 dds 指令的 amp、fre、phase
        从通道和 core 匹配机制中获取实际 index 参数

        前：设置 amp、fre、phase
        后：等待触发
        """
        # 定位到 core 编号
        core = self.channel_core_map_index[channel][core_index]
        # 设置该 core 的振幅频率相位
        self._set_core_amp(core, amp)
        self._set_core_phase(core, phase)
        self._set_core_fre(core, fre)

    def set_dds_multi_amp_phase_fre(self, channel, waveform_list):
        """
        设置单个通道中，多个 DDS core 的波形行为

        waveform_list 的格式：
        np.array([[t, amp_1, phase_1, fre_1],
                  [t, amp_2, phase_2, fre_2],
                  ...])
        :param channel:
        :param waveform_list:
        :return:
        """
        # 获取 waveform_list 的行数，行大于 5 时报错
        row_num = waveform_list.shape[0]
        if row_num > 5:
            raise ValueError("单个通道只允许使用 5 个 core")

        # 获取多个 core 的amp 的和，大于 1 时报错
        amp_sum = np.sum(waveform_list[:, 1])

        if amp_sum > 1:
            raise ValueError("单个通道的 amp 的和必须小于等于 1")
        # 每一行都对应一个 core 的行为
        for i in range(self.used_core):
            # 获取该行数据, 不 care

            if i < row_num:
                _, amp, phase, fre = waveform_list[i]

                self.set_dds_amp_phase_fre(channel, i, amp, phase, fre)
            else:
                self.set_dds_amp_phase_fre(channel, i, 0, 0, 0)

    def set_card_waveform(self, waveform_channel_0, waveform_channel_1):
        """设置两个通道的 dds 波形

        单卡最顶层的波形导入函数。
        :param waveform_channel_0:
        :param waveform_channel_1:
        1. 确认两个 waveform_list 的长度是否相同
        2. 遍历 waveform_list, 将每个小波形组件提取出来，设置其振幅、频率、相位
        """
        self.used_core = 2
        self.set_dds_trigger_source(source=1)  # 更改触发模式为 timer
        if len(waveform_channel_0) != len(waveform_channel_1):
            raise ValueError("two list should have same length")
        # 获取波形片段长度
        wave_num = len(waveform_channel_0)
        # 遍历每一个小段
        for wave_index in range(wave_num):
            # 如果该小段的长度为 0，则跳过
            if waveform_channel_0[wave_index][0][0] == 0:
                continue
            # 遍历每一个小段
            self.set_dds_multi_amp_phase_fre(0, waveform_channel_0[wave_index])
            self.set_dds_multi_amp_phase_fre(1, waveform_channel_1[wave_index])
            self.set_dds_timer(waveform_channel_0[wave_index][0][0])  # 获取该段波形的长度
            self.set_dds_cmd_exec_at_trigger()
        # 最后，将两个通道的 core 参数全部设置为 0
        self.set_dds_param_to_zero()
        self.set_dds_trigger_source(source=2)  # 更改触发模式为外部触发
        self.set_dds_cmd_exec_at_trigger()
        # 没有下一个 trigger 了

    def set_dds_param_to_zero(self):
        """指令结束之后，将所有的参数设置为0"""
        self.set_dds_multi_amp_phase_fre(
            0,
            np.array(
                [
                    [0, 0, 0, 0],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0],
                ]
            ),
        )
        self.set_dds_multi_amp_phase_fre(
            1,
            np.array(
                [
                    [0, 0, 0, 0],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0],
                ]
            ),
        )

    def get_dds_amp_fre_phase(self, channel, core_index):
        """获取 dds 指令的 amp、fre、phase
        从通道和 core 匹配机制中获取实际 index 参数
        """
        # 定位到 core 编号
        core = self.channel_core_map_index[channel][core_index]
        # 获取该 core 的振幅频率相位
        amp = self.get_core_amp(core)
        fre = self.get_core_fre(core)
        phase = self.get_core_phase(core)
        return amp, phase, fre

    def get_all_core_amp_phase_fre(self):
        """获取所有 core 的幅度频率相位，组织成一个  10 * 3 array，并打印"""
        parameter = np.zeros([10, 3])
        for i in range(5):
            parameter[i, :] = np.array(self.get_dds_amp_fre_phase(0, i))
            parameter[i + 5, :] = np.array(self.get_dds_amp_fre_phase(1, i))

        return parameter

    def _set_core_amp(self, core, amp):
        """指定 core index 设置 amp"""
        core_amp_index = SPC_DDS_CORE0_AMP + core
        self._set_param_d64(core_amp_index, amp)  # 设置它的 amp

    def get_core_amp(self, core):
        """指定 core index 获取 amp"""
        core_amp_index = SPC_DDS_CORE0_AMP + core
        return self._get_param_d64(core_amp_index)

    def get_core_fre(self, core):
        """指定 core index 获取 fre"""
        core_fre_index = SPC_DDS_CORE0_FREQ + core
        return self._get_param_d64(core_fre_index)

    def get_core_phase(self, core):
        """指定 core index 获取 phase"""
        core_phase_index = SPC_DDS_CORE0_PHASE + core
        return self._get_param_d64(core_phase_index)

    def _set_core_fre(self, core, fre):
        """指定 core index 设置 fre"""
        core_fre_index = SPC_DDS_CORE0_FREQ + core
        self._set_param_d64(core_fre_index, fre)

    def _set_core_phase(self, core, phase):
        """指定 core index 设置 phase"""
        core_phase_index = SPC_DDS_CORE0_PHASE + core
        self._set_param_d64(core_phase_index, phase)

    def set_dds_cmd_exec_at_trigger(self):
        """设置一段 dds 指令在触发时执行

        本命令是分段 dds 指令的核心功能
        两个卡情况下，需要对每一个卡调用这个指令

        前：一段 dds 指令，对应一个时间段内的 waveform_list 执行
        后：下一段 dds 指令
        """
        self._set_param_i64(SPC_DDS_CMD, SPCM_DDS_CMD_EXEC_AT_TRG)

    def set_dds_cmd_write_to_card(self):
        """将 PC dds 指令缓存空间中所有的指令写入到卡中，
        前：写指令
        后：等待触发
        """
        self._set_param_i64(SPC_DDS_CMD, SPCM_DDS_CMD_WRITE_TO_CARD)

    def set_dds_trigger_source(self, source=1):
        """设置 dds 触发源
        前：设置触发源
        后：等待触发
        """
        if source == 0:
            source = SPCM_DDS_TRG_SRC_NONE  # 无触发
        elif source == 1:
            source = SPCM_DDS_TRG_SRC_TIMER  # 内部时间触发
        elif source == 2:
            source = SPCM_DDS_TRG_SRC_CARD  # 外部触发
        self._set_param_i64(SPC_DDS_TRG_SRC, source)

    def set_dds_timer(self, timer):
        """设置 dds 触发源为 timer
        前：设置触发源
        后：等待触发
        """
        self._set_param_d64(SPC_DDS_TRG_TIMER, timer)

    def get_dds_timer(self):
        """获取刚才设置的触发时间"""
        return self._get_param_d64(SPC_DDS_TRG_TIMER)

    def set_dds_phase_mode(self, mode=1):
        """设置 dds 触发源为 timer
        前：设置触发源
        后：等待触发
        """
        if mode == 1:
            mode = SPCM_DDS_PHASE_SHIFT  # 相位追踪
        elif mode == 0:
            mode = SPCM_DDS_PHASE_JUMP  #
        self._set_param_i64(SPC_DDS_PHASE_BEHAVIOUR, mode)

    def get_dds_phase_behavior(self):
        """获取刚才设置的触发时间"""
        return self._get_param_i64(SPC_DDS_PHASE_BEHAVIOUR)

    # def set_dds_amp_phase_fre_new(self, channel, core_index, amp, phase, fre):
    #     """ """
    #     ast_list = (ST_LIST_PARAM) * 3()
    #     core = self.channel_core_map[channel][core_index]
    #     # fre
    #     ast_list[0] = ST_LIST_PARAM(lReg=SPC_DDS_CORE0_FREQ + core - 1, lType=TYPE_DOUBLE, Value=)
    #
    def get_dds_status(self):
        """获取 dds 状态"""
        return self._get_param_i64(SPC_DDS_STATUS)

    def set_parameters_cell(self, astList):
        """导入一个 cell 的参数"""
        # 调用 C 函数
        ret = spcm_dwSetParam_ptr(
            self.card_handle,
            SPC_REGISTER_LIST,
            pointer(astList),
            len(astList) * sizeof(ST_LIST_PARAM),
        )
        if ret != 0:
            print(self._get_param_error_code())
            raise Exception("can't set astList {}".format(astList))
        else:
            pass

    @staticmethod
    def construct_dds_phase_mode(phase_mode):
        """相位模式设置
        占用: 1 ST_LIST_PARAM"""
        if phase_mode == 1:
            mode = SPCM_DDS_PHASE_SHIFT  # 相位追踪
        elif phase_mode == 0:
            mode = SPCM_DDS_PHASE_JUMP  #
        else:
            raise Exception("phase_mode must be 0 or 1")

        return ST_LIST_PARAM(
            lReg=SPC_DDS_PHASE_BEHAVIOUR, lType=TYPE_INT64, Value=_U(llValue=mode)
        )

    @staticmethod
    def construct_dds_fre_cmd(core, fre):
        """单 core 的频率设置
        占用: 1 ST_LIST_PARAM"""
        core_fre_index = SPC_DDS_CORE0_FREQ + core
        return ST_LIST_PARAM(
            lReg=core_fre_index, lType=TYPE_DOUBLE, Value=_U(dValue=fre)
        )

    @staticmethod
    def construct_dds_cmd_timer(timer):
        """timer 指令
        占用: 1 ST_LIST_PARAM"""
        return ST_LIST_PARAM(
            lReg=SPC_DDS_TRG_TIMER, lType=TYPE_DOUBLE, Value=_U(dValue=timer)
        )

    @staticmethod
    def construct_dds_amp_cmd(core, amp):
        """单 core 的幅度设置
        占用: 1 ST_LIST_PARAM"""
        core_amp_index = SPC_DDS_CORE0_AMP + core
        return ST_LIST_PARAM(
            lReg=core_amp_index, lType=TYPE_DOUBLE, Value=_U(dValue=amp)
        )

    @staticmethod
    def construct_dds_phase_cmd(core, phase):
        """单 core 的相位设置
        占用: 1 ST_LIST_PARAM"""
        core_phase_index = SPC_DDS_CORE0_PHASE + core
        return ST_LIST_PARAM(
            lReg=core_phase_index, lType=TYPE_DOUBLE, Value=_U(dValue=phase)
        )

    def construct_dds_cmd_amp_phase_fre(self, channel, core_index, amp, phase, fre):
        """构造单 core 幅度、频率、相位的指令
        占用: 3 ST_LIST_PARAM"""
        core = self.channel_core_map_index[channel][core_index]
        ast_list = (ST_LIST_PARAM * 3)()
        ast_list[0] = self.construct_dds_amp_cmd(core, amp)
        ast_list[1] = self.construct_dds_phase_cmd(core, phase)
        ast_list[2] = self.construct_dds_fre_cmd(core, fre)
        return ast_list

    @staticmethod
    def construct_dds_cmd_exec_at_trigger():
        """触发执行指令
        占用: 1 ST_LIST_PARAM"""
        return ST_LIST_PARAM(
            lReg=SPC_DDS_CMD,
            lType=TYPE_INT64,
            Value=_U(llValue=SPCM_DDS_CMD_EXEC_AT_TRG),
        )

    def construct_dds_cmd_single_channel(self, channel, waveform_list):
        """
        设置单个通道中，多个 DDS core 的波形行为
        波形序列之中，每个 DDS 指令都拉出来用，算一个内存块
        那么总共多少内存块呢？
        timer+ 1
        amp、fre、phase *3
        exec_at_trigger + 1

        waveform_list 的格式：
        np.array([[t, amp_1, phase_1, fre_1],
                  [t, amp_2, phase_2, fre_2],
                  ...])
        :param channel:
        :param waveform_list:
        :return:
        """
        # 获取 waveform_list 的行数，行大于 5 时报错
        row_num = waveform_list.shape[0]
        if row_num > 5:
            raise ValueError("单个通道只允许使用 5 个 core")

        # 获取多个 core 的amp 的和，大于 1 时报错
        # 获取多个 core 的amp 的和，大于 1 时报错
        amp_sum = np.sum(waveform_list[:, 1])

        if amp_sum > 1:
            raise ValueError("单个通道的 amp 的和必须小于等于 1")

        num_of_dds_cmd = 15  # 5 个 core 的情况下，有十五个指令，直接按最大的来
        astList = (ST_LIST_PARAM * num_of_dds_cmd)()  # 创建一个ST_LIST_PARAM数组

        # 每一行都对应一个 core 的行为
        for i in range(5):
            # 获取该行数据, 不 care
            if i < row_num:
                _, amp, phase, fre = waveform_list[i]
                astList[i * 3: i * 3 + 3] = self.construct_dds_cmd_amp_phase_fre(
                    channel, i, amp, phase, fre
                )
            else:
                # 其他没有调用的 core, 在这里设置为 0
                astList[i * 3: i * 3 + 3] = self.construct_dds_cmd_amp_phase_fre(
                    channel, i, 0, 0, 0
                )

        return astList

    def construct_dds_cmd_two_channel(self, waveform_list_0, waveform_list_1):
        """设置两个通道中，多个 DDS core 的波形行为"""
        astList = (ST_LIST_PARAM * 30)()
        astList[0:15] = self.construct_dds_cmd_single_channel(0, waveform_list_0)
        # self.set_parameters_cell(astList)
        astList[15:30] = self.construct_dds_cmd_single_channel(1, waveform_list_1)
        # self.set_parameters_cell(astList)
        return astList

    @staticmethod
    def construct_dds_trigger_source(source=1):
        """设置触发源"""
        if source == 0:
            source = SPCM_DDS_TRG_SRC_NONE  # 无触发
        elif source == 1:
            source = SPCM_DDS_TRG_SRC_TIMER  # 内部时间触发
        elif source == 2:
            source = SPCM_DDS_TRG_SRC_CARD  # 外部触发
        return ST_LIST_PARAM(
            lReg=SPC_DDS_TRG_SRC, lType=TYPE_INT64, Value=_U(llValue=source)
        )

    def construct_dds_set_zero(self):
        """将所有 core 的参数设置为 0
        33 个单位， set_parameters_cell 本身算一个
        """

        # 1. 初始化一个 cell: 32 个单位， 10 个 core 的参数设为 0 ， 1 个 core 改触发源，一个 core 设置触发
        astList = (ST_LIST_PARAM * 32)()

        astList[0:15] = self.construct_dds_cmd_single_channel(
            0,
            np.array(
                [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]]
            ),
        )
        astList[15:30] = self.construct_dds_cmd_single_channel(
            1,
            np.array(
                [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]]
            ),
        )

        astList[30] = self.construct_dds_trigger_source(source=2)
        astList[31] = self.construct_dds_cmd_exec_at_trigger()
        self.set_parameters_cell(astList)

    # @profile
    def construct_dds_cmd_double(self, waveform_channel_0, waveform_channel_1, repeat):
        """设置两个通道的 dds 波形
        一段波形序列张量 32 个 unit
        数量 = 32 * N + 33
        单卡最顶层的波形导入函数。
        :param waveform_channel_0:
        :param waveform_channel_1:
        1. 确认两个 waveform_list 的长度是否相同
        2. 遍历 waveform_list, 将每个小波形组件提取出来，设置其振幅、频率、相位

        Parameters
        ----------
        repeat
        repeat
        """
        # 1. 更改触发模式为 timer
        # self.set_dds_trigger_source(source=1)
        # self.set_dds_phase_mode(mode=1)
        for i in range(repeat):
            astList_1 = (ST_LIST_PARAM * 1)()
            astList_1[0] = self.construct_dds_trigger_source(source=1)
            # astList_1[1] = self.construct_dds_phase_mode(phase_mode=1)
            self.set_parameters_cell(astList_1)
            # 2. 比较 两个 waveform_list 的长度
            if len(waveform_channel_0) != len(waveform_channel_1):
                raise ValueError("two list should have same length")

            # 3. 获取波形片段的长度
            wave_num = len(waveform_channel_0)
            astList = (ST_LIST_PARAM * 32)()
            # 4. 遍历每一个波形片段，设置其振幅、频率、相位
            for wave_index in range(wave_num):
                # 如果该小段的持续时间为 0，则跳过
                if waveform_channel_0[wave_index][0][0] == 0:
                    continue
                astList[0:30] = self.construct_dds_cmd_two_channel(
                    waveform_channel_0[wave_index], waveform_channel_1[wave_index]
                )
                # astList[0:15] = self.construct_dds_cmd_single_channel(0, waveform_channel_0[wave_index])
                # astList[15:30] = self.construct_dds_cmd_single_channel(1, waveform_channel_1[wave_index])
                astList[30] = self.construct_dds_cmd_timer(
                    waveform_channel_0[wave_index][0][0]
                )
                astList[31] = self.construct_dds_cmd_exec_at_trigger()
                self.set_parameters_cell(astList)

            # 最后，将两个通道的 core 参数全部设置为 0
            # self.set_dds_phase_mode(mode=0)
            self.construct_dds_set_zero()
            # self.set_dds_phase_mode(mode=0)
            self.set_dds_cmd_write_to_card()

    def get_dds_fre_step(self):
        return self._get_param_d64(SPC_DDS_AVAIL_FREQ_STEP)

    def get_trigger_level(self):
        return self._get_param_d64(SPC_TRIG_EXT0_LEVEL0)

    def set_trigger_level(self, value):
        self._set_param_i64(SPC_TRIG_EXT0_LEVEL0, value)

    def construct_dds_cmd_locate(self, channel, core_index, apf, value):
        """
        本函数时一个路由函数, 输入通道 core 索引, 列, 值, 调用三个基本的幅度相位指令
        Returns
        -------

        """
        core = self.channel_core_map_index[channel][core_index]
        if apf == 1:
            return self.construct_dds_amp_cmd(core, value)
        elif apf == 2:
            # print(f"Phase Change{value}")
            return self.construct_dds_phase_cmd(core, value)
        elif apf == 3:
            return self.construct_dds_fre_cmd(core, value)
        else:
            # 报错
            raise ValueError("apf should be 1, 2 or 3")

    def construct_dds_cmd_single_array(self, single_array):
        """
        遍历一个 array, 将该 array 中的每一行解析, 调用 construct_dds_cmd_locate 对应到基本指令.
        一个 single array 对应单卡两通道的的 dds 指令

        Parameters
        ----------
        single_array: 其格式为 N * 4 的 np.array, 第 0 列对应的是 channel, 第一列对应的是 core_index, 第二列对应的是 amp/phase/fre , 第三列对应的是值.

        Returns
        -------

        """
        array_len = single_array.shape[0]
        astList = (ST_LIST_PARAM * array_len)()
        for i in range(array_len):
            astList[i] = self.construct_dds_cmd_locate(
                int(single_array[i, 0]),
                int(single_array[i, 1]),
                int(single_array[i, 2]),
                single_array[i, 3],
            )
        return astList

    # @profile
    # def construct_dds_cmd_all_array(self, all_array, time_array, repeat):
    #     """
    #     遍历一个经过变化处理的 all_array 对象, 对其中每个片段调用 construct_dds_cmd_single_array 函数, 同时加上 timer 和 trigger 函数.
    #     第一段额外增加 trigger source 设置为 1, phase mode 设置为 1, 第二段设置 trigger source 为 2, phase mode 设置为 0
    #     Parameters
    #     ----------
    #     repeat: 重复次数
    #     time_array: 从原始 waveform 构造的每段波形的持续时间长度, 其长度应该是 all_array 长度 -1
    #     all_array: 一个 list, 其中每个元素是一个 N * 4 的 np.array, 对应着一个波形片段中两个通道所有的参数变化.
    #
    #     Returns
    #     -------
    #
    #     """
    #     array_len = len(all_array)
    #     # print("all_arrays")
    #     # print(all_array)
    #     num_of_dds = 0
    #     # 波形编译执行 repeat 次
    #     for j in range(repeat):
    #         # 每一次遍历所有的波形片段
    #         # 尝试计算整个指令的长度, 然后把每个波形片段往这个里头填
    #         total_rows = sum(arr.shape[0] for arr in all_array) + 2 * array_len + 1
    #         for i in range(array_len):
    #             single_array = all_array[i]  # 获取 all_array 的一个片段
    #             single_array_len = single_array.shape[0]  # 获取该段长度
    #             num_of_dds += single_array_len  # dds 指令计数
    #             if i == 0:
    #                 # 处理第一段指令
    #                 astList = (ST_LIST_PARAM * (single_array_len + 3))()  # 构造 list
    #                 astList[0] = self.construct_dds_trigger_source(source=1)  # 设置触发源
    #                 astList[1:single_array_len + 1] = self.construct_dds_cmd_single_array(single_array)
    #                 astList[single_array_len + 1] = self.construct_dds_cmd_timer(time_array[i])  # 从每个片段中提取第一行第一列
    #                 astList[single_array_len + 2] = self.construct_dds_cmd_exec_at_trigger()  # 触发执行
    #
    #             elif i == array_len - 1:
    #                 # 处理最后一段指令
    #                 astList = (ST_LIST_PARAM * (single_array_len + 2))()  # 构造 list
    #                 astList[0] = self.construct_dds_trigger_source(source=2)  # 设置触发源
    #                 astList[1:single_array_len + 1] = self.construct_dds_cmd_single_array(single_array)
    #                 astList[single_array_len + 1] = self.construct_dds_cmd_exec_at_trigger()  # 触发执行
    #             else:
    #                 # 处理其他指令
    #                 astList = (ST_LIST_PARAM * (single_array_len + 2))()
    #                 astList[0: single_array_len] = self.construct_dds_cmd_single_array(single_array)
    #                 astList[single_array_len] = self.construct_dds_cmd_timer(time_array[i])
    #                 astList[single_array_len + 1] = self.construct_dds_cmd_exec_at_trigger()
    #
    #             self.set_parameters_cell(astList)
    #             if num_of_dds > 1000:
    #                 print(num_of_dds)
    #                 self.set_parameters_cell(astList)
    #                 # self.set_dds_cmd_write_to_card()
    #                 num_of_dds = 0
    #     self.set_parameters_cell(astList)
    #     self.set_dds_cmd_write_to_card()
    def construct_dds_cmd_all_array(self, all_array, time_array, repeat):
        """
        遍历一个经过变化处理的 all_array 对象, 对其中每个片段调用 construct_dds_cmd_single_array 函数, 同时加上 timer 和 trigger 函数.
        第一段额外增加 trigger source 设置为 1, phase mode 设置为 1, 第二段设置 trigger source 为 2, phase mode 设置为 0
        Parameters
        ----------
        repeat: 重复次数
        time_array: 从原始 waveform 构造的每段波形的持续时间长度, 其长度应该是 all_array 长度 -1
        all_array: 一个 list, 其中每个元素是一个 N * 4 的 np.array, 对应着一个波形片段中两个通道所有的参数变化.

        Returns
        -------

        """
        print("all_array is ", all_array)
        array_len = len(all_array)
        # 每一次遍历所有的波形片段
        # 尝试计算整个指令的长度, 然后把每个波形片段往这个里头填
        total_rows = sum(arr.shape[0] for arr in all_array) + 2 * array_len + 1
        Total_List = (ST_LIST_PARAM * total_rows)()  # 构造单次循环所需的 list
        current_index = 0
        # print("Construct DDS CMD")
        for i in range(array_len):
            single_array = all_array[i]  # 获取 all_array 的一个片段
            single_array_len = single_array.shape[0]  # 获取该段长度
            if i == 0:
                # 处理第一段指令
                Total_List[current_index] = self.construct_dds_trigger_source(source=1)  # 设置触发源
                Total_List[current_index + 1: current_index + single_array_len + 1] = self.construct_dds_cmd_single_array(single_array)
                Total_List[current_index + single_array_len + 1] = self.construct_dds_cmd_timer(time_array[i])  # 从每个片段中提取第一行第一列
                Total_List[current_index + single_array_len + 2] = self.construct_dds_cmd_exec_at_trigger()  # 触发执行
                current_index += single_array_len + 3

            elif i == array_len - 1:
                # 处理最后一段指令
                Total_List[current_index] = self.construct_dds_trigger_source(source=2)  # 设置触发源
                Total_List[current_index + 1: current_index + single_array_len + 1] = self.construct_dds_cmd_single_array(single_array)
                Total_List[current_index + single_array_len + 1] = self.construct_dds_cmd_exec_at_trigger()  # 触发执行
                current_index += single_array_len + 2

            else:
                # 处理其他指令
                Total_List[current_index: current_index + single_array_len] = self.construct_dds_cmd_single_array(single_array)
                Total_List[current_index + single_array_len] = self.construct_dds_cmd_timer(time_array[i])
                Total_List[current_index + single_array_len + 1] = self.construct_dds_cmd_exec_at_trigger()
                current_index += single_array_len + 2
        # print("current_index", current_index)
        # print("Total_List", total_rows)
        # print("start_write_to_card")
        dds_cmd_num = 0

        for i in range(repeat):
            dds_cmd_num += current_index
            # print("dds_cmd_num", dds_cmd_num)
            self.set_parameters_cell(Total_List)
            if dds_cmd_num >= 1000:
                # print(f"dds_cmd_num is {dds_cmd_num}, clean counts")
                self.set_dds_cmd_write_to_card()
                dds_cmd_num = 0
        self.set_dds_cmd_write_to_card()

    def construct_dds_cmd_compiler(self, waveform_channel_0, waveform_channel_1):
        """
        波形编译:
        1. 并将序列补齐到最大行, 假设所有 waveform 片段中,  np.array  最大行为 3, 就补齐到 3 行,
        2. 将两个波形的两端添加上全 0 的序列,
        3. 使用滑窗算法解析该序列, 生成两个通道波形的简洁表示

        Parameters
        ----------
        waveform_channel_0
        waveform_channel_1

        Returns
        -------

        """
        wave_0 = self.waveform_processing(waveform_channel_0)
        wave_1 = self.waveform_processing(waveform_channel_1)
        # print("process_wave:", wave_0)
        # print("process_wave:", wave_1)

        # 找出变化部分
        result_0 = self.find_differences(wave_0, channel=0)
        result_1 = self.find_differences(wave_1, channel=1)

        # 构造最终 list，将两个结果的对应项堆叠在一起
        final_result = []
        for r0, r1 in zip(result_0, result_1):
            stacked = np.vstack((r0, r1))  # 将两个 array 垂直堆叠
            final_result.append(stacked)

        time_list_0 = [array[0][0] for array in waveform_channel_0]
        time_list_1 = [array[0][0] for array in waveform_channel_1]
        # 验证两个时间 list 是否全同
        if time_list_0 != time_list_1:
            raise ValueError("Two time list should be same!")
        # 构造 DDS 指令
        self.construct_dds_cmd_all_array(all_array=final_result, time_array=time_list_0)

    @profile
    def construct_dds_cmd_compiler_repeat(self, waveform_channel_0, waveform_channel_1, repeat):
        """
        波形编译:
        1. 并将序列补齐到最大行, 假设所有 waveform 片段中,  np.array  最大行为 3, 就补齐到 3 行,
        2. 将两个波形的两端添加上全 0 的序列,
        3. 使用滑窗算法解析该序列, 生成两个通道波形的简洁表示

        Parameters
        ----------
        waveform_channel_0
        waveform_channel_1
        repeat

        Returns
        -------
        """
        # 1. 波形预处理
        print("process_wave")
        wave_0 = self.waveform_processing(waveform_channel_0)
        wave_1 = self.waveform_processing(waveform_channel_1)
        # print("process_wave:", wave_0)
        # print("process_wave:", wave_1)

        # print(wave_0)
        # print(wave_1)
        # 2. 找出变化部分, 并构造中间表示
        result_0 = self.find_differences(wave_0, channel=0)
        result_1 = self.find_differences(wave_1, channel=1)
        # print("find_differences", result_0)
        # print("find_differences", result_1)
        # 3. 构造最终 list，将两个结果的对应项堆叠在一起
        final_result = []
        for r0, r1 in zip(result_0, result_1):
            stacked = np.vstack((r0, r1))  # 将两个 array 垂直堆叠
            final_result.append(stacked)

        # 4. 构造时间 list, 同时需要去掉第一个 0 时间元素
        time_list_0 = [array[0][0] for array in wave_0][1:]
        time_list_1 = [array[0][0] for array in wave_1][1:]
        # print("time_list_0", time_list_0)
        # print("time_list_1", time_list_1)
        # 验证两个时间 list 是否全同
        if time_list_0 != time_list_1:
            raise ValueError("Two time list should be same!")
        # 构造 DDS 指令
        self.construct_dds_cmd_all_array(all_array=final_result, time_array=time_list_0, repeat=repeat)

    @staticmethod
    def waveform_processing(waveform):
        """
        波形预处理的功能:
        1. 将所有的波形片段补齐到最大行;
        2. 整个波形的最前面和最后面添加一个全 0 的 array, 用来跟第一个和最后一个片段比较变化;
        3. 清除掉持续时间为 0 的行

        Parameters
        ----------
        waveform

        Returns
        -------

        """
        # 1. 获取 waveform 的最大行
        max_rows = max(array.shape[0] for array in waveform)

        # 2. 将所有的 waveform 片段补齐到最大行
        # 补齐数组
        padded_arrays = []
        for array in waveform:
            # 当前数组行数
            current_rows = array.shape[0]
            if current_rows < max_rows:
                # 构造补齐行，第0列保持一致，1/2/3列为0
                padding = np.zeros((max_rows - current_rows, 4))
                padding[:, 0] = array[0, 0]  # 第0列与前面行保持一致
                # 拼接原数组和补齐部分
                padded_array = np.vstack((array, padding))
            else:
                padded_array = array
            padded_arrays.append(padded_array)

        # 3. 清除掉 array[0][0] 为 0 的数组
        padded_arrays = [array for array in padded_arrays if array[0][0] != 0]

        # 4. 将波形的前后添加一个全 0 的 np.array, 到这一步, waveform 的预处理就搞定了
        zero_array = np.zeros((max_rows, 4))
        n_one_array = -1000 * np.ones((max_rows, 4))
        padded_arrays.insert(0, n_one_array)  # 添加到开头
        padded_arrays.append(zero_array)  # 添加到结尾
        return padded_arrays

    @staticmethod
    def find_differences(waveform, channel):
        """
        找出数组变化部分并标记。
        """
        # 将输入 list 转换为 numpy array，方便批量操作
        stacked_array = np.stack(waveform)  # 形状为 (M, N, 4)，M 是 list 的长度

        # 比较相邻数组，识别变化
        diffs = stacked_array[1:, :, 1:] != stacked_array[:-1, :, 1:]  # 只比较第 1、2、3 列
        array_indices, row_indices, col_indices = np.where(diffs)  # 获取变化的数组对索引、行索引、列索引

        # 构造变化信息
        result_array = np.zeros((len(array_indices), 4))
        result_array[:, 0] = channel  # 第 0 列为指定值
        result_array[:, 1] = row_indices  # 第 1 列为变化的行索引
        result_array[:, 2] = col_indices + 1  # 第 2 列为变化的列索引（+1 因为从第 1 列开始比较）
        result_array[:, 3] = stacked_array[array_indices + 1, row_indices, col_indices + 1]  # 第 3 列为变化后的值

        # 按数组对拆分结果
        result_list = []
        for i in range(len(waveform) - 1):
            # 筛选当前数组对的变化
            changes = result_array[array_indices == i]
            result_list.append(changes)
        return result_list

    def get_dds_queue_FIFO_num(self):
        """获取卡能传入的 dds 指令的最大长度"""
        data = self._get_param_i64(SPC_FILLSIZEPROMILLE)
        return data

    def get_dds_queue_DMA_space(self):
        data = self._get_param_i64(SPC_DATA_AVAIL_USER_LEN)
        return data