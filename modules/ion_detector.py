import numpy as np
import json
from pathlib import Path
# 理论位置表（N个离子的相对位置）

THEORY_POSITIONS = {
    1: [0],
    2: [-0.62996, 0.62996],
    3: [-1.0772, 0, 1.0772],
    4: [-1.4368, -0.45438, 0.45438, 1.4368],
    5: [-1.7429, -0.8221, 0, 0.8221, 1.7429],
    6: [-2.0123, -1.1361, -0.36992, 0.36992, 1.1361, 2.0123],
    7: [-2.2545, -1.4129, -0.68694, 0, 0.68694, 1.4129, 2.2545],
    8: [-2.4758, -1.6621, -0.96701, -0.31802, 0.31802, 0.96701, 1.6621, 2.4758],
    9: [-2.6803, -1.8897, -1.2195, -0.59958, 0, 0.59958, 1.2195, 1.8897, 2.6803],
    10: [-2.8708, -2.10003, -1.4504, -0.85378, -0.2821, 0.2821, 0.85378, 1.4504, 2.10003, 2.8708],
    11: [-3.0497, -2.2961, -1.6639, -1.0865, -0.5372, 0, 0.5372, 1.0865, 1.6639, 2.2961, 3.0497],
    12: [-3.2187, -2.4801, -1.8630, -1.3019, -0.7710, -0.2554, 0.2554, 0.7710, 1.3019, 1.8630, 2.4801, 3.2187],
    13: [-3.3789, -2.6538, -2.0499, -1.5029, -0.9875, -0.4898, 0, 0.4898, 0.9875, 1.5029, 2.0499, 2.6538, 3.3789],
    14: [-3.5316, -2.8186, -2.2264, -1.6918, -1.1898, -0.7070, -0.2346, 0.2346, 0.7070, 1.1898, 1.6918, 2.2264, 2.8186, 3.5316],
    15: [-3.6776, -2.9755, -2.3939, -1.8701, -1.3798, -0.9100, -0.4522, 0, 0.4522, 0.9100, 1.3798, 1.8701, 2.3939,2.9755, 3.6776],
    16: [-3.8175, -3.1255, -2.5534, -2.0394, -1.5595, -1.1010, -0.6557, -0.2178, 0.2178, 0.6557, 1.1010, 1.5595, 2.0394, 2.5534, 3.1255, 3.8175],
    17: [-3.9521, -3.2692, -2.7058, -2.2006, -1.7300, -1.2815, -0.8472, -0.4216, 0, 0.4216, 0.8472, 1.2815, 1.7300,
         2.2006, 2.7058, 3.2692, 3.9521],
    18: [-4.0818, -3.4074, -2.8519, -2.3547, -1.8925, -1.4529, -1.0283, -0.6134, -0.2039, 0.2039, 0.6134, 1.0283,
         1.4529, 1.8925, 2.3547, 2.8519, 3.4074, 4.0818],
    19: [-4.2070, -3.5404, -2.9923, -2.5025, -2.0478, -1.6163, -1.2004, -0.7949, -0.3959, 0, 0.3959, 0.7949, 1.2004,
         1.6163, 2.0478, 2.5025, 2.9923, 3.5404, 4.2070],
    20: [-4.3281, -3.6689, -3.1276, -2.6445, -2.1968, -1.7726, -1.3645, -0.9675, -0.5777, -0.1921, 0.1921, 0.5777,
         0.9675, 1.3645, 1.7726, 2.1968, 2.6445, 3.1276, 3.6689, 4.3281]
}

# 像素与理论值的转换系数
PIXEL_TO_THEORY_RATIO = 0.116969


def detect_dark_ions(ion_centers, chain_center, tolerance=0.2, unmatched_weight=0.3):
    """
    根据离子中心坐标和链中心坐标判断暗离子

    参数:
        ion_centers: 每个离子的中心坐标数组，形状为(n_ions, 2)
        chain_center: 整个离子链的中心坐标，形状为(2,)
        tolerance: 匹配容差
        unmatched_weight: 未匹配位置的权重

    返回:
        result: 包含匹配结果的字典，包括:
            best_n: 最佳匹配的总离子数
            bright_ions: 亮离子信息列表
            dark_ions: 暗离子的理论位置列表
            match_score: 匹配分数
    """
    # 1. 计算每个离子相对于链中心的距离
    ion_info = []
    for center in ion_centers:
        # 计算距离（欧几里得距离）
        dx = center[0] - chain_center[0]
        dy = center[1] - chain_center[1]
        distance_pixels = np.sqrt(dx ** 2 + dy ** 2)
        # print("distance_pixels", distance_pixels)
        # 计算理论值（距离）
        theoretical_value = distance_pixels * PIXEL_TO_THEORY_RATIO

        # 计算方向（用于确定正负）
        direction = np.sign(dx) if abs(dx) > abs(dy) else np.sign(dy)
        signed_value = theoretical_value * direction

        ion_info.append({
            'pos': center,
            'distance_pixels': distance_pixels,
            'theoretical_value': signed_value
        })

    # 按理论值排序
    ion_info.sort(key=lambda x: x['theoretical_value'])
    detected_values = [ion['theoretical_value'] for ion in ion_info]

    # 2. 匹配理论位置
    best_match_score = -float('inf')
    best_match_n = None
    best_matches = {}

    # 尝试所有可能的离子链数量（从检测到的离子数开始）
    min_n = len(detected_values)
    max_n = min(20, min_n + 10)  # 最大尝试20个离子

    for n in range(min_n, max_n + 1):
        if n not in THEORY_POSITIONS:
            continue

        # 当前检测值和理论值
        current_detected = detected_values.copy()
        theory_values = THEORY_POSITIONS[n]

        matches = []
        unmatched_theory = []

        # 尝试匹配每个理论位置
        for theory_val in theory_values:
            closest_idx = None
            closest_diff = float('inf')

            for i, detected_val in enumerate(current_detected):
                diff = abs(detected_val - theory_val)
                if diff < closest_diff:
                    closest_diff = diff
                    closest_idx = i

            # 检查是否在容差范围内
            if closest_idx is not None and closest_diff < tolerance:
                matches.append({
                    'theory_val': theory_val,
                    'detected_val': current_detected[closest_idx],
                    'diff': closest_diff
                })
                # 移除已匹配的值
                current_detected.pop(closest_idx)
            else:
                unmatched_theory.append(theory_val)

        # 计算匹配分数
        match_count = len(matches)
        unmatch_count = len(unmatched_theory)
        score = match_count - unmatched_weight * unmatch_count

        # 更新最佳匹配
        if score > best_match_score:
            best_match_score = score
            best_match_n = n
            best_matches = {
                'matches': matches,
                'unmatched_theory': unmatched_theory,
                'score': score,
                'unmatched_detected': current_detected
            }

    # 3. 处理无匹配的情况
    if best_match_n is None:
        best_match_n = len(ion_info)
        best_matches = {
            'matches': [],
            'unmatched_theory': [],
            'score': 0,
            'unmatched_detected': detected_values.copy()
        }

    # 4. 整理结果
    bright_ions = []
    for match in best_matches['matches']:
        # 找到对应的原始离子信息
        for ion in ion_info:
            if abs(ion['theoretical_value'] - match['detected_val']) < 1e-5:
                bright_ions.append({
                    'pos': ion['pos'],
                    'distance_pixels': ion['distance_pixels'],
                    'theoretical_value': match['theory_val'],
                    'matching_error': match['diff']
                })
                break

    dark_ions = [{'theoretical_value': val} for val in best_matches['unmatched_theory']]

    # 添加未匹配的亮离子
    unmatched_bright_ions = []
    for val in best_matches['unmatched_detected']:
        for ion in ion_info:
            if abs(ion['theoretical_value'] - val) < 1e-5:
                unmatched_bright_ions.append({
                    'pos': ion['pos'],
                    'distance_pixels': ion['distance_pixels'],
                    'theoretical_value': ion['theoretical_value']
                })
                break

    return {
        'best_n': best_match_n,
        'bright_ions': bright_ions,
        'dark_ions': dark_ions,
        'unmatched_bright_ions': unmatched_bright_ions,
        'match_score': best_matches['score'],
        'total_ions': best_match_n,
        'bright_count': len(bright_ions),
        'dark_count': len(dark_ions)
    }

def calculate_ion_centers(roi_for_ions):
    """
    计算每个离子的中心坐标和整个离子链的中心坐标

    参数:
        roi_for_ions: 离子ROI列表，格式为[[[x1,y1], [x2,y2], ...], ...]

    返回:
        ion_centers: 每个离子的中心坐标数组，形状为(n_ions, 2)，保留两位小数
        chain_center: 整个离子链的中心坐标，形状为(2,)，保留两位小数
    """
    # 计算每个离子的中心坐标
    ion_centers = []
    for ion_roi in roi_for_ions:
        # 转换为numpy数组并计算均值，保留两位小数
        points = np.array(ion_roi)
        center = np.round(np.mean(points, axis=0), decimals=2)
        ion_centers.append(center)

    # 转换为numpy数组
    ion_centers = np.array(ion_centers)

    # 计算整个离子链的中心坐标
    chain_center = np.round(np.mean(ion_centers, axis=0), decimals=2)

    return ion_centers, chain_center


def load_roi_from_json(file_path):
    """
    从JSON文件加载离子ROI数据（JSON文件需包含"roi_for_ions"键）

    参数:
        file_path: JSON文件路径（字符串或Path对象）

    返回:
        roi_for_ions: 离子ROI列表，格式为[[[x1,y1], [x2,y2], ...], ...]

    异常:
        FileNotFoundError: 若文件不存在则抛出
        json.JSONDecodeError: 若JSON格式错误则抛出
        ValueError: 若数据格式不符合要求则抛出
    """
    # 转换为Path对象方便处理
    file_path = Path(file_path)

    # 检查文件是否存在
    if not file_path.exists():
        raise FileNotFoundError(f"ROI文件不存在: {file_path}")

    # 读取并解析JSON文件
    with open(file_path, 'r', encoding='utf-8') as f:
        try:
            json_data = json.load(f)  # 加载完整JSON数据
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON文件格式错误: {e}")

    # 检查是否包含"roi_for_ions"键
    if "roi_for_ions" not in json_data:
        raise ValueError("JSON文件必须包含'roi_for_ions'键")

    roi_data = json_data["roi_for_ions"]  # 提取ROI数据


    return roi_data


# 示例用法
if __name__ == "__main__":
    from config import LOADED_PARAMETER
    parameter = LOADED_PARAMETER()
    roi_for_ions = parameter.QCMOS.roi_for_ions
    # 调用函数计算中心坐标
    ion_centers, chain_center = calculate_ion_centers(roi_for_ions)


    # # 示例离子中心坐标
    # ion_centers = np.array([
    #     [21.5, 19.0], [27.5, 19.5], [32.5, 20.0], [37.5, 20.0],
    #     [42.0, 20.0], [46.0, 20.0], [50.5, 20.5], [55.0, 20.5],
    #     [59.0, 20.5], [63.5, 20.5], [68.5, 21.0], [75.0, 21.0]
    # ])


    # 链中心坐标
    chain_center = parameter.QCMOS.ion_chain_center

    # 检测暗离子
    result = detect_dark_ions(ion_centers, chain_center, tolerance=0.3)

    # 打印结果
    print(f"最佳匹配总离子数: {result['best_n']}")
    print(f"匹配分数: {result['match_score']:.2f}")
    print(f"亮离子数量: {result['bright_count']}")
    print(f"暗离子数量: {result['dark_count']}")

    print("\n匹配的亮离子信息:")
    for i, ion in enumerate(result['bright_ions'], 1):
        print(f"  亮离子{i}: 坐标{ion['pos']}, 距离: {ion['distance_pixels']:.2f}像素, "
              f"理论值: {ion['theoretical_value']:.5f}, 匹配误差: {ion['matching_error']:.5f}")

    if result['dark_ions']:
        print("\n暗离子理论位置:")
        for i, ion in enumerate(result['dark_ions'], 1):
            print(f"  暗离子{i}: 理论值 {ion['theoretical_value']:.4f}")

    if result['unmatched_bright_ions']:
        print("\n未匹配的亮离子:")
        for i, ion in enumerate(result['unmatched_bright_ions'], 1):
            print(f"  亮离子{i}: 坐标{ion['pos']}, 距离: {ion['distance_pixels']:.2f}像素, "
                  f"理论值: {ion['theoretical_value']:.4f}")