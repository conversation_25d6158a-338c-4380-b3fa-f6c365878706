import xmlrpc.client

class NewPort:
    def __init__(self, ip="http://localhost:8009/", idx="8742 102105"):
        """初始化并连接
        ip: 设备 IP
        idx: newport 电机唯一序列号
        """
        self.device = xmlrpc.client.ServerProxy(ip, allow_none=True)
        self.idx = idx
        
    def set_step(self, hub: int, scan_axis:int, step):
        """设置轴 scan_axis 移动 step 步
        
        hub: 1 - 左轴; 2 - 右轴
        scan_axis: 1. H; 2. V 
        """
        # 数据验证
        if hub not in (1, 2):
            raise ValueError("hub should be 1 or 2")
        if scan_axis not in (1, 2):
            raise ValueError("scan_axis should be 1 or 2")
        
        # 移动
        move_id = self.idx + f"_{hub}"
        # print("new_port id", id)
        self.device.call(move_id, "move_relative", int(scan_axis), int(step))
        print(f"hub_{hub} scan_axis_{scan_axis} move step {step}")