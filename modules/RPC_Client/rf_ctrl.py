import time
import xmlrpc.client
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)
class RFCtrl:
    def __init__(self, ip="http://localhost:8001/"):
        """初始化并连接 RF RPC 服务
        ip: 设备 IP
        idx: newport 电机唯一序列号
        """
        self.device = xmlrpc.client.ServerProxy(ip, allow_none=True)

    def shake(self, minus=8, delay_time=0.5):
        logger.info("RF shaking")
        self.device.shake(minus, delay_time)

    def rf_re_open(self):
        """重开 RF"""
        logger.info("RF ReOpen")
        self.device.set_amp_plus(-40)
        time.sleep(0.1)
        self.device.set_amp_plus(40)


if __name__ == "__main__":
    a = RFCtrl()
    a.shake()