import time
import socket


class MotorServer:
    def __init__(self, sever_ip="************", port=6666, ionization_channel=1) -> None:
        self.sever_ip = sever_ip
        self.port = port
        self.ionization_channel = ionization_channel
        self.socket_tcp = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

    def _send_message(self, message):
        self.socket_tcp = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            self.socket_tcp.connect((self.sever_ip, self.port))
        except Exception as e:
            print(f"Network error: {e}")
        try:
            self.socket_tcp.sendall(message.encode("utf-8"))
        except Exception as e:
            print(f"Network error: {e}")
        self.socket_tcp.close()

    def open_channel(self, channel):
        self._send_message(f"SetSteer_{channel}_0")

    def close_channel(self, channel):
        self._send_message(f"SetSteer_{channel}_90")

    def open_ionization(self, t=5):
        # print(1)
        self.open_channel(self.ionization_channel)
        time.sleep(t)
        self.close_channel(self.ionization_channel)

if __name__ == "__main__":
    a = MotorServer()
    a.open_ionization()