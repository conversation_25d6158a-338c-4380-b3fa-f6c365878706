"""
波形管理器
1. 维护一个 list, list 的每个元素是一个 np 二维矩阵,
   矩阵的每一行包含四个元素, 分别对应了一段 sin 波形的持续时间、幅度、相位和失谐.
   例如:
   np.array([[2e-6, 1, 0, 1e6],
   [2e-6, 0.5, np.pi, 2e6],
   [2e-6, 0.2, np.pi/2, 3e6]]), 每个二维矩阵代表着, 该矩阵所有行所对应的波形是同时播放的(同一个二维矩阵的第一个元素时间必定相同).
   而 list 层级的元素则代表着, 这些波形是按顺序播放的.
2. 提供一个函数generate_wave(base_fre, sampling_rate=1e9), 将整个 list 转化为一段一维的波形序列,
   默认采样率 1e9, 每一段波形的失谐加上 base_fre 为实际频率.


2024年4月30日新增:
将 waveform 规范成 Spectrum DDS 支持的形式
"""
import numpy as np
import matplotlib.pyplot as plt
from copy import deepcopy
from numba import jit, float64, int64,prange

@jit(nopython=True, parallel=True, fastmath=True)
def _fully_parallel_waveform(total_time_points, waveform_list, base_fre):
    # 预计算所有片段的起止时间 [优化关键点]
    n_segments = len(waveform_list)
    start_times = np.zeros(n_segments, dtype=np.float64)
    end_times = np.zeros(n_segments, dtype=np.float64)

    # 阶段1：串行计算时间分段（轻量级）
    cursor = 0.0
    for i in range(n_segments):
        duration = waveform_list[i][0, 0]
        start_times[i] = cursor
        end_times[i] = cursor + duration
        cursor = end_times[i]

    # 阶段2：并行处理所有片段 [核心加速部分]
    total_waveform = np.zeros(len(total_time_points), dtype=np.float64)

    for seg_idx in prange(n_segments):  # 并行化外层循环
        matrix = waveform_list[seg_idx]
        mask = (total_time_points >= start_times[seg_idx]) & \
               (total_time_points < end_times[seg_idx])
        time_segment = total_time_points[mask]
        segment_wave = np.zeros(len(time_segment), dtype=np.float64)

        # 内层频率计算（保持串行，自动向量化）
        for i in range(matrix.shape[0]):
            freq = base_fre + matrix[i, 3]
            amp = matrix[i, 1]
            phase = matrix[i, 2]
            segment_wave += amp * np.sin(2 * np.pi * freq * time_segment + phase)

        # 原子操作写入结果（自动处理）
        total_waveform[mask] = segment_wave

    return total_waveform

class Waveform:

    def __init__(self, duration=0.0, amplitude=0.0, phase=0.0, detuning=0.0, waveform_list: list = None):
        """支持两种初始化方法:

        1. 传入 duration, amplitude, phase, detuning, 生成一个单段波形的 waveform 类;
        2. 传入一个波形列表, 用于生成复杂的, 多段的 waveform 类

        先判断传入的是不是 waveform_list, 如果是, 则直接赋值给 self.waveform_list,
        否则前面四个参数起作用生成一个单段波形的 waveform 类.

        :param duration: 波形持续时间
        :param amplitude: 波形幅度
        :param phase: 波形相位
        :param detuning: 波形失谐
        :param waveform_list: 波形列表

        """
        if waveform_list is not None:
            self.waveform_list = waveform_list
        else:
            if duration < 0.0:
                raise ValueError("Duration must be greater than 0.")
            if amplitude < 0.0:
                raise ValueError("Amplitude cannot be negative.")
            self.waveform_list = [np.array([[duration, amplitude, phase % (2 * np.pi), detuning]])]  # 用于生成波形的列表

    def duration(self):
        """计算整个波形的持续时间
        list 中每个元素的第 0 行 0 列, 作为该段波形的持续时间

        :return: 波形的总持续时间
        """
        total_duration = sum(matrix[0, 0] for matrix in self.waveform_list)

        return total_duration

    def __mul__(self, other):
        """波形前后拼接
        将后面的波形所维护的 list 拼接到前面波形的 list 后面
        """
        if not isinstance(other, Waveform):
            raise ValueError("The second operand must be a Waveform object.")
        new_self_list = deepcopy(self.waveform_list)
        new_other_list = deepcopy(other.waveform_list)
        # new_waveform_list = self.waveform_list + other.waveform_list
        new_waveform_list = new_self_list + new_other_list
        return Waveform(waveform_list=new_waveform_list)

    def __add__(self, other):
        """波形并联
        检查两个 waveform 对象的每一个元素的持续时间是否相同, 如果不同, 则抛出异常,
        如果相同, 则将后一个 waveform 的 list 中每一个元素 (np.array) 添加到前一个 list 相应元素的 np.array 下面.
        """
        if not isinstance(other, Waveform):
            raise ValueError("The second operand must be a Waveform object.")

        if len(self.waveform_list) != len(other.waveform_list):
            raise ValueError("The two waveforms must have the same number of segments.")

        # 检查相应元素的 np.array 的第 0 行 0 列是否相同, 如果发现不同, 捕获是哪个片段不同, 抛出异常
        for i in range(len(self.waveform_list)):
            if self.waveform_list[i][0, 0] != other.waveform_list[i][0, 0]:
                raise ValueError("The duration of the {i}-th segment is different.".format(i=i))
        new_self_list = deepcopy(self.waveform_list)
        new_other_list = deepcopy(other.waveform_list)
        # new_waveform_list = [np.vstack([self.waveform_list[i], other.waveform_list[i]]) for i in
        #                      range(len(self.waveform_list))]
        new_waveform_list = [np.vstack([new_self_list[i], new_other_list[i]]) for i in
                             range(len(new_self_list))]

        # 生成一个新的 waveform 对象并返回
        return Waveform(waveform_list=new_waveform_list)

    # def generate_waveform(self, base_fre, sampling_rate=1e9):
    #     """波形生成
    #     1. 遍历所有的波形片段, 生成每个片段的波形;
    #     """
    #     total_duration = self.duration()  # 计算波形的总持续时间
    #     total_time_points = np.arange(0, total_duration, 1 / sampling_rate)  # 生成时间序列
    #     total_waveform = np.zeros_like(total_time_points)  # 生成初始波形序列
    #     start_time_cursor = 0  # 用于记录每个片段的起始时间
    #     for matrix in self.waveform_list:
    #         duration = matrix[0, 0]
    #         mask = (total_time_points >= start_time_cursor) & (total_time_points < start_time_cursor + duration)
    #
    #         # 频率、幅度、相位的向量, 注意要转化成列向量
    #         frequencies = (base_fre + matrix[:, 3])[:, np.newaxis]  # 生成频率的向量
    #         amplitudes = matrix[:, 1][:, np.newaxis]  # 生成幅度向量
    #         phases = matrix[:, 2][:, np.newaxis]  # 生成相位向量
    #
    #         # 使用 numpy 的广播计算所有波形的贡献
    #         waveforms = amplitudes * np.sin(2 * np.pi * frequencies * total_time_points[mask] + phases)
    #         total_waveform[mask] += np.sum(waveforms, axis=0)
    #         start_time_cursor += duration  # 更新起始时间
    #
    #     return total_waveform
    import numpy as np


    def generate_waveform(self, base_fre, sampling_rate=1e9):
        """优化后的波形生成方法"""
        # 将数据转换为numba友好格式
        waveform_list_np = [np.asarray(m, dtype=np.float64) for m in self.waveform_list]
        total_duration = sum(m[0,0] for m in waveform_list_np)
        time_points = np.arange(0, total_duration, 1/sampling_rate)

        # 调用并行版本
        return _fully_parallel_waveform(
            time_points,
            waveform_list_np,
            float(base_fre)
        )
    def generate_waveform_for_spectrum_dds(self):
        """将波形整理成 Spectrum DDS 支持的形式

        Spectrum DDS 的限制：
        1. 分辨率是 6.4 ns 的整数倍，非整数倍的取值会四舍五入到整数倍上;
        2. 最小的脉冲片段是 83.2 ns, 
        3. 相位需要转换为 Spectrum DDS 支持的单位（角度制);

        步骤:
        1. 遍历 waveform_list,计算所有波形的开始时间点，得到时间戳列表;
        2. 将时间戳对齐到 6.4 ns 的整数倍;
        3. 计算新的每段波形的持续时间，得到新的持续时间列表;
        4. 遍历新的持续时间列表，更新 waveform_list 中对应的持续时间和相位;
        """
        # 1. 计算波形的时间戳
        wave_time_stamps = [0]
        time_cursor = 0
        waveform_list = deepcopy(self.waveform_list)

        for matrix in waveform_list:
            time_cursor += matrix[0, 0]  # matrix[0, 0] 是波形片段的持续时间
            wave_time_stamps.append(time_cursor)  # 时间戳列表中添加该时间戳
        # 本段执行完, 将得到一个 shape = (N + 1, 1) 的 array.

        # 2. 将时间戳对齐到 6.4 ns 的整数倍
        wave_time_stamps = np.array(wave_time_stamps)  # shape = [1, N + 1], 时间点 list, 从 0 到 N
        wave_time_stamps = np.round(wave_time_stamps / 6.4e-9) * 6.4e-9  # 四舍五入到边界上 shape = [1, N +1]

        # 3. 计算新的每段波形的持续时间，得到一个列表  shape = [1, N]
        new_duration = [wave_time_stamps[i + 1] - wave_time_stamps[i] for i in range(len(wave_time_stamps) - 1)]
        # new_duration.append(wave_time_stamps[-1] - wave_time_stamps[-2])  # 添加最后一段
        # 将 matrix 的第一列（也即持续时间）更换成 new_duration 中对应的数
        # print("before", self.waveform_list)
        # 4. 更新 waveform_list 中的持续时间和相位
        for i in range(len(waveform_list)):
            # 更新持续时间
            waveform_list[i][:, 0] = new_duration[i]
            waveform_list[i][:, 2] = np.rad2deg((waveform_list[i][:, 2] + 2 * np.pi * (waveform_list[i][:, 3] * wave_time_stamps[i])) % (2 * np.pi))
        return Waveform(waveform_list=waveform_list)

    def generate_waveform_for_dds(self, base_fre, sampling_rate=1e9):
        """波形生成
        1. 遍历所有的波形片段, 生成每个片段的波形;
        """
        total_duration = self.duration()  # 计算波形的总持续时间
        total_time_points = np.arange(0, total_duration, 1 / sampling_rate)  # 生成时间序列
        total_waveform = np.zeros_like(total_time_points)  # 生成初始波形序列

        start_time_cursor = 0  # 用于记录每个片段的起始时间
        for matrix in self.waveform_list:
            duration = matrix[0, 0]
            mask = (total_time_points >= start_time_cursor) & (total_time_points < start_time_cursor + duration)
            # 频率、幅度、相位的向量, 注意要转化成 列向量
            # frequencies = (base_fre + matrix[:, 3])[:, np.newaxis]  # 生成频率的向量
            frequencies = (base_fre + matrix[:, 3])[:, np.newaxis]  # 生成频率的向量
            amplitudes = matrix[:, 1][:, np.newaxis]  # 生成幅度向量
            phases = (matrix[:, 2][:, np.newaxis])  # 生成相位向量

            # 使用 numpy 的广播计算所有波形的贡献(对于 DDS 而言, 每段波形的起始时间不再是总时间线中的一个值, 而是 0 , 相干性由手动维护的相位来实现)
            waveforms = amplitudes * np.sin(2 * np.pi * frequencies * (total_time_points[mask] - start_time_cursor) + phases)
            total_waveform[mask] += np.sum(waveforms, axis=0)
            start_time_cursor += duration  # 更新起始时间
        return total_waveform

    def add_base_fre(self, base_fre):
        waveform_list = deepcopy(self.waveform_list)
        for matrix in waveform_list:
            matrix[:, 3] += base_fre
        return Waveform(waveform_list=waveform_list)

    def plot_waveform_dds(self, base_fre, sampling_rate=1e9):
        """绘制 DDS 波形"""
        total_duration = self.duration()
        total_time_points = np.arange(0, total_duration, 1 / sampling_rate)  # 生成时间序列
        total_waveform = self.generate_waveform_for_dds(base_fre, sampling_rate)

        #
        plt.plot(total_time_points, total_waveform)
        plt.xlabel("Time (us)")
        plt.ylabel("Amplitude")
        plt.show()

    def plot_waveform(self, base_fre, sampling_rate=1e9):
        """绘制波形"""
        total_duration = self.duration()
        total_time_points = np.arange(0, total_duration, 1 / sampling_rate)  # 生成时间序列
        total_waveform = self.generate_waveform(base_fre, sampling_rate)

        #
        plt.plot(total_time_points, total_waveform)
        plt.xlabel("Time (us)")
        plt.ylabel("Amplitude")
        plt.show()


if __name__ == "__main__":
    # 生成一个波形
    # wave1 = Waveform(2e-6, 1, 0, 1e6)
    # wave2 = Waveform(2e-6, 0.5, 0, 2e6)
    # wave3 = Waveform(2e-6, 0.2, 0, 3e6)
    #
    # # 波形拼接
    # wave = wave1 * (wave2 +wave1) * wave3
    #
    # # 波形并联
    # # wave = wave + wave
    #
    # # 绘制波形
    # wave.plot_waveform(1e6)
    # # print(wave.duration())
    # wave_list = [np.array([[2e-6, 1, 0, 1e6], [2e-6, 0.5, 0, 2e6], [2e-6, 0.2, 0, 3e6]]),
    #              np.array([[2e-6, 1, 0, 1e6], [2e-6, 0.5, 0, 2e6], [2e-6, 0.2, 0, 3e6]])]
    #
    # wave = Waveform(waveform_list=wave_list)
    # wave.plot_waveform(1e6)

    # 生成 复杂的 wave_list, 测试之
    # wave_list = [np.array([[1.2e-6, 0.2, 0, 1e6],
    #                        [1.2e-6, 0.5, 0, 2e6],
    #                        [1.2e-6, 0.2, 0, 3e6]]),
    #              np.array([[2e-6, 1, 0, 1e6],
    #                        [2e-6, 0.5, 0, 2e6],
    #                        [2e-6, 0.2, 0, 3e6]]),
    #              np.array([[2e-6, 1, 0, 1e6],
    #                        [2e-6, 0.5, 0, 2e6],
    #                        [2e-6, 0.2, 0, 3e6]])
    #              ]
    wave_list = [np.array([[1.2e-7, 1, 0, 10e6]]),
                 np.array([[2e-7, 1, 0, 10e6]]),
                 np.array([[3e-7, 1, 0, 10e6]])
                 ]
    wave = Waveform(waveform_list=wave_list)
    wave.plot_waveform(base_fre=20e6, sampling_rate=1e9)

    wave1 = wave.add_base_fre(base_fre=20e6)
    wave1 = wave1.generate_waveform_for_spectrum_dds()
    wave1.plot_waveform_dds(base_fre=0, sampling_rate=1e9)
    print(wave1.waveform_list)
