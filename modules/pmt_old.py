"""pmt 管理器

功能：
1. 管理 pmt 硬件
2. 管理数据集的准备、绘图组件的提交和更新
3. 管理数据采集、数据处理逻辑
"""

from artiq.experiment import *

from modules.config import *
from modules.PMT_package.PMT_Control import *
from modules.pmt_base import PMT_Base

class PMT(PMT_Base):
    """PMT 真实硬件模块

    使用示例：
    class PMTExample(EnvExperiment):
        def build(self):
            self.setattr_device("core")
            self.pmt = PMT(self)

        def prepare(self):
            self.pmt.prepare()

        def run(self):
            self.core.reset()
            self.pmt.data_process()

    """

    def build(self):
        """
        1. 连仪器
        2. 拉参数
        """
        # 1. 连仪器
        print("PMT is building")
        self.setattr_device("ccb")
        self.setattr_device("PMT_window")  # 连接 artiq 的 PMT 通道
        self.setattr_device("core")

        # 2. 拉参数
        self.parameter = Config()  # 从配置文件获取参数

        self.repeat = self.parameter.Experiment.Repeat
        self.ions_nums = len(self.parameter.PMT.Select_channels)



        self.PMT_counts_temp = None  # 用于保存单次 PMT 读数的缓存
        self.PMT_data_temp = None  #用于保存两层循环中内循环的临时读数
        print("PMT is built")
    @rpc(flags={})
    def connect_pmt(self):
        """
        连接PMT硬件
        :return:
        """
        try:
            print("connecting_pmt")
            self.pmt_device = MultichannelPMT()
            self.pmt_device.resetPMT()
            print("PMT_connected")
        except Exception as e:
            print(f"Connect PMT failed: {e}")
    @rpc(flags={"async"})
    def read_one_data(self):
        """读取 PMT 数据一次

        返回：shape = (32,) 的数列
        """
        data = self.pmt_device.readPMT()
        return data
    @rpc(flags={"async"})
    def read_data(self):
        """读取 PMT 数据
        返回：shape = (repeat,32) 的数列
        """
        # 1. 创建空数据集，用于存放　repeat 次读数
        data_temp = np.zeros([self.repeat, 32])  # repeat * 32

        # 2. 读数
        for repeat in range(self.repeat):
            data_temp[repeat] = self.read_one_data()
            # data_temp[repeat][9] = 0
            data_temp[repeat][23] = 0
            data_temp[repeat][27] = 0
        return data_temp
    @rpc(flags={"async"})
    def close_pmt(self):
        """关闭 PMT"""
        self.pmt_device.closePMT()

    @rpc(flags={"async"})
    def resetPMT(self):
        """重置 PMT"""
        self.pmt_device.resetPMT()

    @kernel()
    def pmt_start_count(self):
        """开始 pmt 计数"""
        self.PMT_window.on()

    @kernel()
    def pmt_stop_count(self):
        """停止 pmt 计数"""
        self.PMT_window.off()

    @kernel()
    def initial(self):
        self.core.wait_until_mu(now_mu())
        self.connect_pmt()
        self.core.break_realtime()
        """pmt 通道初始化"""
        self.PMT_window.output()
        delay(10e-9)
        self.PMT_window.off()


if __name__ == '__main__':
    PMT = MultichannelPMT()
    PMT.resetPMT()
    time.sleep(1)

    for i in range(100):
        print('------------------------', i)
        start_time = time.time()
        data_single = PMT.readPMT()
        end_time = time.time()
        print(data_single)
        print('time used: ', end_time - start_time)
