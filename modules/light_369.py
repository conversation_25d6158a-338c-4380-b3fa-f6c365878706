from artiq.experiment import *
from modules.config import Config, LOADED_PARAMETER
import numpy as np


class Light369(HasEnvironment):
    """M2 369 光路模块


    用途:
    1. 连通 369 EOM, 实现 369 光在 Cooling, pumping, detecting 之间来回切换;
    """

    def build(self):
        # 1. 建立通道
        self.devices = [
            "core",

            "dds_for_CPD",
            "dds_for_EIT_sigma",
            "dds_for_EIT_pi",
            "dds_for_cooling_2",
            "dds_for_cooling_3",
            "dds_for_760",
            "dds_for_399",

            "EOM_cooling_sw",
            "EOM_pumping_sw",
            "EOM_41_V1",
            "EOM_41_V2",
            # "EOM_21_CTRL",
            # "ttl46"
        ]

        for item in self.devices:
            self.setattr_device(item)

        # 2. 参数提取
        self.parameters = Config()
        # self.parameter = Config(DEFAULT_PARAMETER).Light_369
        self.parameter = LOADED_PARAMETER().Light_369
        self.dds_for_C_fre = self.parameter.dds_for_C_fre
        self.dds_for_C_atte = self.parameter.dds_for_C_atte
        self.dds_for_C_amp = self.parameter.dds_for_C_amp

        self.dds_for_C2_fre = self.parameter.dds_for_C2_fre
        self.dds_for_C2_amp = self.parameter.dds_for_C2_amp
        self.dds_for_C2_atte = self.parameter.dds_for_C2_atte

        self.dds_for_C2_fre = self.parameter.dds_for_C3_fre
        self.dds_for_C3_amp = self.parameter.dds_for_C3_amp
        self.dds_for_C3_atte = self.parameter.dds_for_C3_atte

        self.dds_for_pre_C_fre = self.parameter.dds_for_pre_C_fre
        self.dds_for_pre_C_atte = self.parameter.dds_for_pre_C_atte
        self.dds_for_pre_C_amp = self.parameter.dds_for_pre_C_amp

        self.dds_for_pre_C2_fre = self.parameter.dds_for_pre_C2_fre
        self.dds_for_pre_C2_amp = self.parameter.dds_for_pre_C2_amp
        self.dds_for_pre_C2_atte = self.parameter.dds_for_pre_C2_atte

        self.dds_for_pre_C3_fre = self.parameter.dds_for_pre_C3_fre
        self.dds_for_pre_C3_amp = self.parameter.dds_for_pre_C3_amp
        self.dds_for_pre_C3_atte = self.parameter.dds_for_pre_C3_atte

        self.dds_for_P_fre = self.parameter.dds_for_P_fre
        self.dds_for_P_amp = self.parameter.dds_for_P_amp
        self.dds_for_P_atte = self.parameter.dds_for_P_atte

        self.dds_for_760_fre = self.parameter.dds_for_760_fre
        self.dds_for_760_amp = self.parameter.dds_for_760_amp
        self.dds_for_760_atte = self.parameter.dds_for_760_atte

        self.dds_for_399_fre = self.parameter.dds_for_399_fre
        self.dds_for_399_amp = self.parameter.dds_for_399_amp
        self.dds_for_399_atte = self.parameter.dds_for_399_atte


        self.dds_for_D_fre = self.parameter.dds_for_D_fre
        self.dds_for_D_amp = self.parameter.dds_for_D_amp
        self.dds_for_D_atte = self.parameter.dds_for_D_atte

        self.dds_for_EIT_sigma_fre = self.parameter.dds_for_EIT_sigma_fre
        self.dds_for_EIT_sigma_amp = self.parameter.dds_for_EIT_sigma_amp
        self.dds_for_EIT_sigma_atte = self.parameter.dds_for_EIT_sigma_atte
        #
        self.dds_for_EIT_pi_fre = self.parameter.dds_for_EIT_pi_fre
        self.dds_for_EIT_pi_amp = self.parameter.dds_for_EIT_pi_amp
        self.dds_for_EIT_pi_atte = self.parameter.dds_for_EIT_pi_atte

        self.idle_time = self.parameters.Light_554.AOD_time_before_AOM
        self.SBC_num = self.parameters.Light_554.SBC_num
        self.SBC_time = self.parameters.Light_554.SBC_time
        self.pumping_time = self.parameters.Experiment.Pumping_Time
        print("light_369 is built")

    @rpc(flags={"async"})
    def tprint(self, data):
        """用于异步打印信息，不会占用时序"""
        print(data)

    @kernel
    def initial(self):
        """initial to cooling"""
        self.core.break_realtime()

        self.EOM_cooling_sw.output()
        self.EOM_pumping_sw.output()
        self.EOM_41_V1.output()
        self.EOM_41_V2.output()
        # self.EOM_21_CTRL.output()
        delay_mu(np.int64(self.core.ref_multiplier))

        # initial DDS
        self.dds_for_CPD.cpld.init()
        self.dds_for_CPD.init()
        self.dds_for_EIT_sigma.init()
        self.dds_for_EIT_pi.init()
        self.dds_for_cooling_2.init()
        self.dds_for_cooling_3.init()

        delay_mu(np.int64(self.core.ref_multiplier))

        # set dds amp, fre, phase
        self.dds_for_CPD.set_att(self.dds_for_pre_C_atte)
        self.dds_for_CPD.set(frequency=self.dds_for_pre_C_fre, phase=0.0,
                             amplitude=self.dds_for_pre_C_amp)
        delay_mu(np.int64(self.core.ref_multiplier))

        self.dds_for_cooling_2.set_att(self.dds_for_pre_C2_atte)
        self.dds_for_cooling_2.set(frequency=self.dds_for_pre_C2_fre, phase=0.0,
                                   amplitude=self.dds_for_pre_C2_amp)

        delay_mu(np.int64(self.core.ref_multiplier))

        self.dds_for_cooling_3.set_att(self.dds_for_pre_C3_atte)
        self.dds_for_cooling_3.set(frequency=self.dds_for_pre_C3_fre, phase=0.0,
                                   amplitude=self.dds_for_pre_C3_amp)

        delay(1e-6)

        self.dds_for_760.set_att(self.dds_for_760_atte)
        self.dds_for_760.set(frequency=self.dds_for_760_fre, phase=0.0,
                                   amplitude=self.dds_for_760_amp)

        delay(1e-6)
        self.dds_for_399.set_att(self.dds_for_399_atte)
        self.dds_for_399.set(frequency=self.dds_for_399_fre, phase=0.0,
                                   amplitude=self.dds_for_399_amp)

        self.dds_for_EIT_pi.set_att(self.dds_for_EIT_pi_atte)
        self.dds_for_EIT_pi.set(frequency=self.dds_for_EIT_pi_fre, phase=0.0,
                                amplitude=self.dds_for_EIT_pi_amp)

        delay_mu(np.int64(self.core.ref_multiplier))

        self.dds_for_EIT_sigma.set_att(self.dds_for_EIT_sigma_atte)
        self.dds_for_EIT_sigma.set(frequency=self.dds_for_EIT_sigma_fre, phase=0.0,
                                   amplitude=self.dds_for_EIT_sigma_amp)

        delay_mu(np.int64(self.core.ref_multiplier))

        # set ttl to cooling
        self.EOM_cooling_sw.off()
        self.EOM_pumping_sw.on()
        self.EOM_41_V1.on()
        self.EOM_41_V2.on()
        # self.EOM_21_CTRL.on()
        delay_mu(np.int64(self.core.ref_multiplier))

        # set dds to off
        self.dds_for_CPD.cfg_sw(True)
        self.dds_for_cooling_2.cfg_sw(True)
        self.dds_for_cooling_3.cfg_sw(True)
        self.dds_for_760.cfg_sw(True)
        self.dds_for_399.cfg_sw(True)
        self.dds_for_EIT_sigma.cfg_sw(False)
        self.dds_for_EIT_pi.cfg_sw(False)
        delay(10 * ns)

    @kernel
    def switch_detect_to_pre_cooling(self):
        """detect -> off"""
        with parallel:
            self.EOM_cooling_sw.off()
            self.EOM_pumping_sw.on()
            self.EOM_41_V1.on()
            self.EOM_41_V2.on()
            # self.EOM_21_CTRL.on()
        self.dds_for_cooling_2.cfg_sw(True)
        self.dds_for_cooling_3.cfg_sw(True)

        self.set_dds_for_CPD(fre=self.dds_for_pre_C_fre, amp=self.dds_for_pre_C_amp, atten=self.dds_for_pre_C_atte)
        delay(2 * us)

    @kernel
    def switch_pre_cooling_to_cooling(self):
        self.set_dds_for_CPD(fre=self.dds_for_C_fre, amp=self.dds_for_C_amp, atten=self.dds_for_C_atte)


    @kernel
    def set_dds_for_CPD(self, fre: TFloat = 100e6, amp: TFloat = 0.0, atten: TFloat = 0.0, phase: TFloat = 0.0):
        """set dds_for_CPD"""
        delay_mu(np.int64(self.core.ref_multiplier))
        self.dds_for_CPD.set_att(atten)
        self.dds_for_CPD.set(frequency=float(fre), phase=float(phase),
                             amplitude=float(amp))
        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def set_dds_detuning(self, detuning):
        frequency = self.dds_for_D_fre + detuning  # 计算微波偏移
        amplitude = self.dds_for_D_amp
        phase = 0.0
        self.dds_for_CPD.set(float(frequency),
                             phase,
                             amplitude)
        delay(10 * us)

    def set_dds_detuning_for_d(self, detuning):
        frequency = self.dds_for_D_fre + detuning  # 计算微波偏移
        amplitude = self.dds_for_D_amp
        phase = 0.0
        self.dds_for_CPD.set(float(frequency),
                             phase,
                             amplitude)
        delay(10 * us)

    @kernel
    def switch_cool_to_pump(self):
        """cool -> pump"""
        self.set_dds_for_CPD(fre=self.dds_for_P_fre, amp=self.dds_for_P_amp, atten=self.dds_for_P_atte)

        delay(10 * us)

        with parallel:
            self.EOM_cooling_sw.on()
            self.EOM_pumping_sw.off()
            self.EOM_41_V1.off()
            self.EOM_41_V2.off()

        self.dds_for_cooling_2.cfg_sw(False)  # close cooling 2
        self.dds_for_cooling_3.cfg_sw(False)  # close cooling 2

        delay(10 * ns)

    @kernel
    def switch_cool_to_detect(self):
        """cool -> detect"""
        with (parallel):
            self.EOM_cooling_sw.off()
            self.EOM_41_V1.on()
            self.EOM_41_V2.off()
            # self.EOM_21_CTRL.off()
            self.dds_for_cooling_2.cfg_sw(False)
            self.dds_for_cooling_3.cfg_sw(False)

        self.set_dds_for_CPD(fre=self.dds_for_D_fre, amp=self.dds_for_D_amp, atten=self.dds_for_D_atte)
        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def switch_cool_to_eit(self):
        """cool -> eit"""
        # try:
        self.dds_for_cooling_2.cfg_sw(False)
        self.dds_for_cooling_3.cfg_sw(False)

        self.dds_for_CPD.cfg_sw(False)
        delay_mu(np.int64(self.core.ref_multiplier))

        # open two EIT
        self.dds_for_EIT_sigma.cfg_sw(True)
        self.dds_for_EIT_pi.cfg_sw(True)

        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def switch_eit_to_pump(self):
        """eit -> pump"""
        self.set_dds_for_CPD(fre=self.dds_for_P_fre, amp=self.dds_for_P_amp, atten=self.dds_for_P_atte)
        with parallel:
            # 1. TTL
            self.EOM_cooling_sw.on()
            self.EOM_pumping_sw.off()
            self.EOM_41_V1.off()
            self.EOM_41_V2.off()

        # 2. DDS
        self.dds_for_EIT_sigma.cfg_sw(False)
        self.dds_for_EIT_pi.cfg_sw(False)
        self.dds_for_CPD.cfg_sw(True)
        self.dds_for_cooling_2.cfg_sw(False)
        self.dds_for_cooling_3.cfg_sw(False)

        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def switch_pump_to_control(self):
        """pump -> control"""
        with parallel:
            self.EOM_cooling_sw.on()  # close cooling 1
            self.EOM_pumping_sw.on()  # open pumping
            self.EOM_41_V1.off()
            self.EOM_41_V2.on()
            # self.EOM_21_CTRL.off()
        self.dds_for_CPD.cfg_sw(False)
        delay(10 * ns)

    @kernel
    def switch_control_to_pump(self):
        """control -> pump"""
        with parallel:
            self.EOM_pumping_sw.on()
            self.EOM_41_V2.on()
            # self.EOM_21_CTRL.on()
        self.dds_for_CPD.cfg_sw(True)
        delay(10 * ns)

    @kernel
    def switch_pump_to_detect(self):
        """pump -> detect"""
        self.set_dds_for_CPD(fre=self.dds_for_D_fre, amp=self.dds_for_D_amp, atten=self.dds_for_D_atte)
        delay(200 * us)
        with parallel:
            self.EOM_cooling_sw.on()
            self.EOM_pumping_sw.on()
            self.EOM_41_V1.off()
            self.EOM_41_V2.on()
            # self.EOM_21_CTRL.off()

        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def switch_control_to_detect(self):
        """control -> detect"""
        with sequential:
            delay(200 * us)
            self.set_dds_for_CPD(fre=self.dds_for_D_fre, amp=self.dds_for_D_amp, atten=self.dds_for_D_atte)
            self.dds_for_CPD.cfg_sw(True)
            delay(10 * ns)

    @kernel
    def switch_detect_to_cool(self):
        """detect -> off"""
        with parallel:
            self.EOM_cooling_sw.off()
            self.EOM_pumping_sw.on()
            self.EOM_41_V1.on()
            self.EOM_41_V2.on()
            # self.EOM_21_CTRL.on()
        self.dds_for_cooling_2.cfg_sw(True)
        self.dds_for_cooling_3.cfg_sw(True)

        self.set_dds_for_CPD(fre=self.dds_for_C_fre, amp=self.dds_for_C_amp, atten=self.dds_for_C_atte)
        delay(2 * us)
    @kernel
    def switch_to_pump(self):
        self.set_dds_for_CPD(fre=self.dds_for_P_fre, amp=self.dds_for_P_amp, atten=self.dds_for_P_atte)
        with parallel:
            # 1. TTL
            self.EOM_cooling_sw.on()
            self.EOM_pumping_sw.off()
            self.EOM_41_V1.off()
            self.EOM_41_V2.off()

        self.dds_for_EIT_sigma.cfg_sw(False)
        self.dds_for_EIT_pi.cfg_sw(False)
        delay_mu(np.int64(self.core.ref_multiplier))

        self.dds_for_CPD.cfg_sw(True)
        self.dds_for_cooling_2.cfg_sw(False)
        self.dds_for_cooling_3.cfg_sw(False)

        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def switch_to_control(self):
        with parallel:
            # 1. TTL
            self.EOM_cooling_sw.on()
            self.EOM_pumping_sw.on()
            self.EOM_41_V1.off()
            self.EOM_41_V2.on()
        self.dds_for_EIT_sigma.cfg_sw(False)
        self.dds_for_EIT_pi.cfg_sw(False)
        delay_mu(np.int64(self.core.ref_multiplier))
        self.dds_for_CPD.cfg_sw(False)
        self.dds_for_cooling_2.cfg_sw(False)
        self.dds_for_cooling_3.cfg_sw(False)

        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def switch_to_detect(self):
        """pump -> detect"""
        self.set_dds_for_CPD(fre=self.dds_for_D_fre, amp=self.dds_for_D_amp, atten=self.dds_for_D_atte)
        delay(200 * us)
        with parallel:
            self.EOM_cooling_sw.on()
            self.EOM_pumping_sw.on()
            self.EOM_41_V1.off()
            self.EOM_41_V2.on()

        self.dds_for_EIT_sigma.cfg_sw(False)
        self.dds_for_EIT_pi.cfg_sw(False)
        delay_mu(np.int64(self.core.ref_multiplier))

        self.dds_for_CPD.cfg_sw(True)
        self.dds_for_cooling_2.cfg_sw(False)
        self.dds_for_cooling_3.cfg_sw(False)

        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def switch_to_eit(self):
        with parallel:
            # 1. TTL
            self.EOM_cooling_sw.off()
            self.EOM_pumping_sw.on()
            self.EOM_41_V1.on()
            self.EOM_41_V2.on()

        self.dds_for_cooling_2.cfg_sw(False)
        self.dds_for_cooling_3.cfg_sw(False)

        self.dds_for_CPD.cfg_sw(False)
        delay_mu(np.int64(self.core.ref_multiplier))

        # open two EIT
        self.dds_for_EIT_sigma.cfg_sw(True)
        self.dds_for_EIT_pi.cfg_sw(True)

        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def switch_to_pre_cooling(self):
        """detect -> off"""
        with parallel:
            self.EOM_cooling_sw.off()
            self.EOM_pumping_sw.on()
            self.EOM_41_V1.on()
            self.EOM_41_V2.on()
            # self.EOM_21_CTRL.on()
        self.set_dds_for_CPD(fre=self.dds_for_pre_C_fre, amp=self.dds_for_pre_C_amp, atten=self.dds_for_pre_C_atte)
        delay(2 * us)
        self.dds_for_EIT_sigma.cfg_sw(False)
        self.dds_for_EIT_pi.cfg_sw(False)
        delay_mu(np.int64(self.core.ref_multiplier))

        self.dds_for_CPD.cfg_sw(True)
        self.dds_for_cooling_2.cfg_sw(True)
        self.dds_for_cooling_3.cfg_sw(True)

        delay_mu(np.int64(self.core.ref_multiplier))

    @kernel
    def sbc_cooling(self, enable):
        """Sideband Cooling"""
        if enable:
            with sequential:
                delay(self.idle_time)
                for i in range(self.SBC_num):
                    delay(self.SBC_time)
                    self.switch_control_to_pump()
                    delay(self.pumping_time)
                    self.switch_pump_to_control()
                self.switch_control_to_pump()
                delay(self.pumping_time)
                self.switch_pump_to_control()
                delay(self.idle_time)
        else:
            pass

    @kernel
    def switch_off(self):
        """close all ttl"""
        self.EOM_cooling_sw.off()
        self.EOM_pumping_sw.off()
        self.EOM_41_V1.off()
        self.EOM_41_V2.off()
        # self.EOM_21_CTRL.off()

        self.dds_for_cooling_2.cfg_sw(False)
        self.dds_for_cooling_3.cfg_sw(False)


    @kernel
    def set_dds_for_C_amp(self, amp):
        delay_mu(np.int64(self.core.ref_multiplier))
        self.dds_for_CPD.set(frequency=self.dds_for_C_fre, phase=0.0, amplitude=float(amp))
        delay_mu(np.int64(self.core.ref_multiplier))

    # @kernel()
    # def switch_pre_cooling_to_cooling(self):
    #     pass
    # @kernel()
    # def switch_detect_to_pre_cooling(self):
    #     pass