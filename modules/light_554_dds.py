import time

from artiq.experiment import *
from modules.config import Config
from modules.operation_manager import Operation, Circuit
from spcmpkg import *


class Light554(HasEnvironment):

    def build(self):
        # 1. set AWG trigger
        self.devices = ["AWG_trigger", "core"]
        for item in self.devices:
            self.setattr_device(item)
        # 2. parameter
        self.parameter = Config()  # 从配置文件获取参数
        self.Voltage_AOM = self.parameter.Light_554.AOM_AWG_full_Voltage
        self.Voltage_AOD = self.parameter.Light_554.AOD_AWG_full_Voltage
        self.awg_device = None

    @rpc(flags={})
    def connect_awg(self):
        """connect to AWG"""

        print("connect_awg")
        self.awg_device = SpcmDDS(self.parameter.Light_554.AWG_address)
        self.awg_device.init_spcm_dds(
            voltage_0=self.Voltage_AOM * 1000,
            voltage_1=self.Voltage_AOD * 1000,
            clock_mode="External",
        )
        print("connected_awg")

    @kernel
    def initial(self):
        """554 initial"""
        self.AWG_trigger.output()
        delay(10e-9)
        self.AWG_trigger.off()
        delay(10e-9)
        self.core.wait_until_mu(now_mu())
        self.connect_awg()
        print("awg_device_address", self.awg_device)
        self.core.break_realtime()

    @rpc(flags={})
    def close_awg(self):
        """close AWG"""
        self.awg_device.close()

    @kernel
    def AWG_on(self):
        """trigger awg"""
        self.AWG_trigger.on()

    @kernel
    def AWG_off(self):
        """trigger awg off"""
        self.AWG_trigger.off()

    @rpc(flags={})
    def prepare_waveform(self, operation):
        """调用 AWG 的驱动, 准备波形"""

        # 1. 添加中频
        operation.add_base_fre(
            self.parameter.Light_554.AOM_middle_freq
            + self.parameter.Light_554.AOM_freq_LR,
            self.parameter.Light_554.AOM_middle_freq
            - self.parameter.Light_554.AOM_freq_LR,
            self.parameter.Light_554.AOD_middle_freq,
            self.parameter.Light_554.AOD_middle_freq,
        )

        # 2. 波形序列张量整理成 DDS 支持的形式
        operation.generate_waveform_for_spectrum_dds()

        # 3. 波形序列张量解析与导入
        self.awg_device.compiler(
            operation.AOM_L,
            operation.AOM_R,
            operation.AOD_L,
            operation.AOD_R,
            self.parameter.Experiment.Repeat,
            batch_size=20000
        )