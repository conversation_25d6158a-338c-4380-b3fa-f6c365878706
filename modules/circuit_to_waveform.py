from modules.operation_manager import Circuit
import json
import numpy as np
import copy

# PARAMETER_FILE_PATH = "C:Users\\Administrator\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\modules\\gate_parameter.json"
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)

class ShiftedCircuit(Circuit):
    def __init__(self,circuit_list = None):
        # with open(PARAMETER_FILE_PATH,'r') as f:
        #     self.gate_parameters = json.load(f)
        super().__init__(circuit_list)
    def get_qubits(self):
        qubits = []
        for layer in self.circuit_list:
            for gate in layer:
                for qubit in gate["qubit_index"]:
                    if qubit not in qubits:
                        qubits.append(qubit)
        return qubits
    def update_sequence(self):

        self.transpile_rzz2rxx()
        super().update_sequence()

        q1_shift = self.parameters.Light_554.q1_AC_Stark_shift * 2 * np.pi
        # ms_shift = np.array(self.gate_parameters["AC_Stark_shift"]) * 2 * np.pi
        ms_shift = np.array(self.parameters.Light_554.MS_AC_Stark_shift) * 2 * np.pi

        qubits = self.get_qubits()
        single_pi2 = self.parameters.Light_554.pi_2.Carrier
        single_qubit_delay = self.parameters.Light_554.AOM_phase_delay
        # ms_time = self.gate_parameters["gate_time"]
        ms_time = self.parameters.Light_554.MS_time
        AOD_idle = self.parameters.Light_554.AOD_time_before_AOM
        phase_shift  = np.zeros(max(qubits)+1)
        time_now = 0
        new_sequence = []
        for layer in self.circuit_list:
            new_layer = []
            for gate in layer:
                if gate["gate"] == "Idle":
                    time_now += gate.get("idle_time",AOD_idle)
                    new_layer.append(gate)
                if gate["gate"] == "Rphi":
                    qubit  = gate["qubit_index"][0]
                    theta = gate["theta"]
                    phi = gate["phi"]

                    pi2_time = single_pi2[qubit]
                    gate_time = (pi2_time- single_qubit_delay) * (abs(theta)/(np.pi/2)) + single_qubit_delay

                    phi += phase_shift[qubit] - q1_shift * time_now

                    gate_new  = {
                        "gate": gate["gate"],
                        "qubit_index": gate["qubit_index"],
                        "theta": gate["theta"],
                        "phi": phi,
                    }
                    new_layer.append(gate_new)

                    time_now += gate_time
                    phase_shift[qubit] += q1_shift * gate_time
                if gate["gate"] == "Rphi_":
                    qubit  = gate["qubit_index"][0]
                    theta = gate["theta"]
                    phi = gate["phi"]
                    pi2_time = single_pi2[qubit]
                    gate_time = (pi2_time- single_qubit_delay) * (abs(theta)/(np.pi/2)) + single_qubit_delay
                    phi += - q1_shift * time_now
                    gate_new  = {
                        "gate": "Rphi",
                        "qubit_index": gate["qubit_index"],
                        "theta": gate["theta"],
                        "phi": phi,
                    }
                    new_layer.append(gate_new)
                    time_now += gate_time
                    phase_shift[qubit] += q1_shift * gate_time

                # if gate["gate"] == "Rxx":
                if gate["gate"] == "MSOperation":
                    qubit0 = gate["qubit_index"][0]
                    qubit1 = gate["qubit_index"][1]

                    gate_time = ms_time[qubit0][qubit1]
                    gate_time_ms = gate_time
                    shift = ms_shift[qubit0][qubit1]
                    gate["phase"] = -shift * time_now

                    phase_shift[qubit0] += shift * gate_time
                    phase_shift[qubit1] += shift * gate_time
                    time_now += gate_time

                    new_layer.append(gate)

                if gate["gate"] == "MS_AM":
                    qubit0 = gate["qubit_index"][0]
                    qubit1 = gate["qubit_index"][1]

                    gate_time = ms_time[qubit0][qubit1]
                    gate_time_ms = gate_time
                    shift = ms_shift[qubit0][qubit1]
                    gate["phase"] = -shift * time_now

                    phase_shift[qubit0] += shift * gate_time
                    phase_shift[qubit1] += shift * gate_time
                    time_now += gate_time

                    new_layer.append(gate)

                if gate["gate"] == "Rphi__":
                    qubit  = gate["qubit_index"][0]
                    theta = gate["theta"]
                    phi = gate["phi"]

                    pi2_time = single_pi2[qubit]
                    gate_time = (pi2_time - single_qubit_delay) * (abs(theta)/(np.pi/2)) + single_qubit_delay

                    phi += shift * gate_time_ms - q1_shift * time_now
                    gate_new  = {
                        "gate": "Rphi",
                        "qubit_index": gate["qubit_index"],
                        "theta": gate["theta"],
                        "phi": phi,
                    }
                    new_layer.append(gate_new)

                    time_now += gate_time
                    phase_shift[qubit] += q1_shift * gate_time

            new_sequence.append(new_layer)
        # print(new_sequence)
        self.circuit_list = new_sequence
    def transpile_rzz2rxx(self):
        transpiled_circuit_list = []

        for layer in self.circuit_list:
            if layer[0]["gate"] == "Rzz":
                qubit_index = layer[0]["qubit_index"]
                idle_gate = {"gate": "Idle",
                             "qubit_index": (qubit_index[0],),
                             "idle_time": 0.5e-6
                             }
                transpiled_circuit_list.append([idle_gate])
                pi2_0 = {
                    "gate": "Rphi_",
                    "qubit_index": (qubit_index[0],),
                    "theta": np.pi/2,
                    "phi": -np.pi/2
                          }
                transpiled_circuit_list.append([pi2_0])

                pi2_1 = {
                    "gate": "Rphi_",
                    "qubit_index": (qubit_index[1],),
                    "theta": np.pi / 2,
                    "phi": -np.pi / 2
                }
                transpiled_circuit_list.append([pi2_1])

                # rxx = {
                #     "gate": "Rxx",
                #     "qubit_index": qubit_index,
                # }
                # transpiled_circuit_list.append([rxx])
                # ms = {
                #     "gate":"MSOperation",
                #     "qubit_index": qubit_index,
                # }
                ms = {
                    "gate":"MS_AM",
                    "qubit_index": qubit_index
                }
                transpiled_circuit_list.append([ms])

                pi2_0m = {
                    "gate": "Rphi__",
                    "qubit_index": (qubit_index[0],),
                    "theta": np.pi / 2,
                    "phi":  np.pi / 2
                }
                transpiled_circuit_list.append([pi2_0m])

                pi2_1m = {
                    "gate": "Rphi__",
                    "qubit_index": (qubit_index[1],),
                    "theta": np.pi / 2,
                    "phi":   np.pi / 2
                }
                transpiled_circuit_list.append([pi2_1m])
            else:
                qubit_index = layer[0]["qubit_index"]
                idle_gate = {"gate": "Idle",
                      "qubit_index": qubit_index,
                      "idle_time": 0.5e-6
                      }
                # 在每个门前添加Idle隔开，统一上升下降沿
                transpiled_circuit_list.append([idle_gate])
                transpiled_circuit_list.append(layer)
        self.circuit_list = transpiled_circuit_list


def string2circuit_list(circuit,parameter,qubit_index,initial_state,modulator,ms_phase):
    rabi_time0 = parameter.Light_554.pi_2.Carrier[qubit_index[0]]
    rabi_time1 = parameter.Light_554.pi_2.Carrier[qubit_index[1]]
    rabi_time = parameter.Light_554.pi_2for2ions.Carrier

    aom_amp = parameter.Light_554.AOM_AWG_amp_ms
    daom_amp = 0.000

    idle_AOD = parameter.Light_554.AOD_time_before_AOM
    idle_time = 0.5e-6

    # 1. 定义操作
    inite_circuit = {
        "11": [
            [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": 0}],
            [{"gate": "Idle", "qubit_index": qubit_index, "idle_time": idle_time}],
            [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": 0}]
        ],
        "10": [
            [{"gate": "Rpi2", "qubit_index": (qubit_index[0],), "rabi_time": rabi_time0}],
            [{"gate": "Idle", "qubit_index": (qubit_index[0],), "idle_time": idle_time}],
            [{"gate": "Rpi2", "qubit_index": (qubit_index[0],), "rabi_time": rabi_time0,"phase":0}],
        ],
        "01": [
            [{"gate": "Rpi2", "qubit_index": (qubit_index[1],), "rabi_time": rabi_time1}],
            [{"gate": "Idle", "qubit_index": (qubit_index[1],), "idle_time": idle_time}],
            [{"gate": "Rpi2", "qubit_index": (qubit_index[1],), "rabi_time": rabi_time1,"phase":0}],
        ],

        "00": []
    }
    circuit_list = inite_circuit.get(initial_state, [])
    circuit_list.append([{"gate": "Idle",
                          "qubit_index": qubit_index,
                          "idle_time": idle_time
                          }])

    inite_time = {
        "00": idle_AOD + idle_time,
        "01": idle_AOD * 2 + idle_time * 2 + rabi_time0 * 2,
        "10": idle_AOD * 2 + idle_time * 2 + rabi_time0 * 2,
        "11": idle_AOD + idle_time * 2 + rabi_time * 2
    }

    shift_ms = parameter.Light_554.MS_AC_Stark_shift * 2 * np.pi
    shift_q1 = parameter.Light_554.q1_AC_Stark_shift * 2 * np.pi

    phase0_MS = (0 - shift_ms) * inite_time.get(initial_state, 0)
    phase0_1q = (0 - shift_q1) * inite_time.get(initial_state, 0)

    ms_time = parameter.Light_554.MS_time

    gate_dict = {
        "rx": [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": 0 + phase0_1q}],
        "ry": [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": -np.pi / 2 + phase0_1q}],
        "-rx": [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": np.pi + phase0_1q}],
        "-ry": [{"gate": "RR", "qubit_index": qubit_index, "theta": np.pi / 2, "phase": np.pi / 2 + phase0_1q}],
        "rxx": [{"gate": "MSOperationPlus",
                 "qubit_index": qubit_index,
                 "rabi_time": ms_time,
                 "modulator": modulator,
                 "gate_type": "rxx",
                 "ms_phase": ms_phase + phase0_MS,
                "aom_amp":aom_amp
                 }],
        "ryy": [{"gate": "MSOperationPlus",
                 "qubit_index": qubit_index,
                 "rabi_time": ms_time,
                 "modulator": modulator,
                 "gate_type": "ryy",
                 "ms_phase": ms_phase + phase0_MS,
                 "aom_amp":aom_amp
                 }],
    }

    for gate in circuit:
        if gate == "Idle":
            continue

        circuit_list.append(copy.deepcopy(gate_dict[gate]))

        circuit_list.append([{"gate": "Idle",
                              "qubit_index": qubit_index,
                              "idle_time": idle_time
                              }])

        if gate in ["rxx", "ryy"]:  # 修正因做两比特门产生的相位,具体相位由实验校正
            for single_gate in ["rx", "-rx", "ry", "-ry"]:
                gate_dict[single_gate][0]["phase"] = gate_dict[single_gate][0]["phase"] + (shift_ms - shift_q1) * (
                            ms_time + idle_time)
            for twoqubit_gate in ["rxx", "ryy"]:
                gate_dict[twoqubit_gate][0]["ms_phase"] = gate_dict[twoqubit_gate][0]["ms_phase"] + 0
                gate_dict[twoqubit_gate][0]["aom_amp"] = gate_dict[twoqubit_gate][0]["aom_amp"] + daom_amp
        if gate in ["rx", "-rx", "ry", "-ry"]:
            for single_gate in ["rx", "-rx", "ry", "-ry"]:  # 单比特无相位偏移
                gate_dict[single_gate][0]["phase"] = gate_dict[single_gate][0]["phase"]
            for twoqubit_gate in ["rxx", "ryy"]:  # 两比特相位偏移为 (shift_q1-shift_ms) * 单比特门时间
                gate_dict[twoqubit_gate][0]["ms_phase"] = gate_dict[twoqubit_gate][0]["ms_phase"] + (
                            shift_q1 - shift_ms) * (rabi_time + idle_time)

    return circuit_list