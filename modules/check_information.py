from artiq.experiment import *
import re
import time
import random
import json
from pathlib import Path
from ionctrl_pkg.utils.log import get_logger
logger = get_logger(__name__)
SCRIPT_DIRECTORY = Path(__file__).resolve().parent  # 获取当前脚本的路径
JSON_FILE_PATH = (
    SCRIPT_DIRECTORY / "check_information.json"
)  # 获取当前脚本同路径下的 json 文件路径


JSON_FILE_PATH = str(JSON_FILE_PATH)

with open(str(JSON_FILE_PATH), "r") as f:
    check_information = json.load(f)
@rpc(flags={""})
def decode_item_index(cali_item)->TList(TInt32):
    # 使用正则表达式找到所有下划线之间的数字
    numbers = re.findall(r'_(\d+)(?=_|$)', '_' + cali_item + '_')
    # 将字符串数字转换为整数
    return [int(num) for num in numbers]


@rpc(flags={""})
def decode_item_name(cali_item)->TStr:
    y = cali_item.split("_")[0]  # 分割后取第一个元素
    return y

def get_last_time(para_name):
    return check_information["last_time"][para_name]

def update_last_time(para_name, index,set_time = None):
    if set_time is None:
        set_time = time.time()
    target = check_information["last_time"][para_name]

    if len(index) == 0:
        target = set_time
    elif len(index) == 1:
        target[index[0]] = set_time
    elif len(index) == 2:
        # 对称赋值
        target[index[0]][index[1]] = set_time
        target[index[1]][index[0]] = set_time

    # 更新回原字典
    if len(index) == 0:
        check_information["last_time"][para_name] = target
    try:
        with open(JSON_FILE_PATH, "w") as f:
            json.dump(check_information, f, indent=4)
    except Exception as e:
        # print("______________________________________________")
        logger.error(e)

def get_period(para_name):
    return check_information["period"][para_name]

def get_reference(para_name):
    return check_information["reference"][para_name]

