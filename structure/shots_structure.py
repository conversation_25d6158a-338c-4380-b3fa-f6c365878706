from artiq.experiment import *

@kernel()
def standard_shot(self,operation_time):
    pass
    # self.l369.switch_to_cooling2()
    # self.pmt.pmt_start_count()
    # delay(self.cooling2_time)
    #
    # self.l369.switch_to_cooling1()
    # delay(self.cooling_time)
    # self.pmt.pmt_stop_count()
    #
    # self.l369.switch_to_eit()
    # delay(self.eit_time)
    # self.l369.switch_to_eit2()
    # delay(self.eit2_time)
    #
    # self.l369.switch_to_pump()
    # delay(self.pumping_time)
    #
    # # with parallel:
    # self.l369.switch_to_control()
    # #
    # # self.wm.mw_on()
    # # delay(self.pi_2time*2)
    # # self.wm.mw_off()
    #
    # delay(0.0 * ms)
    # self.l554.AWG_on()
    #
    # with parallel:
    #     self.l369.sbc_cooling(self.enable_SBC)
    #     delay(operation_time)
    #
    # # delay(2*ms)
    #
    # with parallel:
    #     self.l554.AWG_off()
    #     self.pmt.pmt_start_count()
    #     self.l369.switch_to_detect()
    #
    # delay(self.detecting_time)
    #
    # with parallel:
    #     self.pmt.pmt_stop_count()
    #     self.l369.switch_to_cool()

@kernel()
def standard_shot_qcmos(self, operation_time):
    self.qcmos.qcmos_start_count()
    delay(self.pre_cooling_time)

    self.l369.switch_pre_cooling_to_cooling()
    delay(self.cooling_time)
    self.qcmos.qcmos_stop_count()
    self.l369.switch_cool_to_pump()
    self.l369.switch_cool_to_eit()
    delay(self.eit_time)

    self.l369.switch_eit_to_pump()
    delay(self.pumping_time)

    with parallel:
        self.l369.switch_pump_to_control()
        self.l554.AWG_on()

    with parallel:
        self.l369.sbc_cooling(self.enable_SBC)
        delay(operation_time)

    with parallel:
        self.l554.AWG_off()
        self.qcmos.qcmos_start_count()
        self.l369.switch_control_to_detect()

    delay(self.detecting_time)

    self.qcmos.qcmos_stop_count()

    delay(1 * ms)

    self.l369.switch_detect_to_pre_cooling()