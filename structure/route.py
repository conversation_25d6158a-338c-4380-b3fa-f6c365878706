import numpy as np


class Route:
    pass

class Rabi_scan_t(Route):
    def __init__(self,qubit_index,start = 50e-6,stop = 55e-6,n_points = 6):
        self.points_info = [{
            'points': np.linspace(start, stop, n_points),  # 原始扫描点
        }]

        self.route = []
        for t in self.points_info[0]['points']:
            shot = [
                {
                    "name": "<PERSON><PERSON>",
                    "args": {
                        "qubit_index": (qubit_index,),
                        "time": t
                    }
                },

            ]
            self.route.append(shot)
class AOD_scan_f(Route):
    def __init__(self,start=98.1e6, stop = 100.4e6,n_points = 51):
        self.points_info = [{
            'points': np.linspace(start, stop, n_points),  # 原始扫描点
        }]

        self.route = []
        for f in self.points_info[0]['points']:
            shot = [
                {
                    "name": "AODScan",
                    "args": {
                        "samplingdense":"frequency",
                        "time":8e-6,
                        "frequency": f
                    }
                },

            ]
            self.route.append(shot)

class Parity_scan_phi(Route):
    def __init__(self,qubit_index,start=0.0, stop=0.0, n_points=41, n_gate=1):
        self.points_info = [{
            "points":np.linspace(start, stop, n_points),
        }]
        self.route = []
        for phi in self.points_info[0]['points']:
            shot = []
            for _ in range(int(n_gate)):
                ms = {
                    "name":"MS",
                    "args":{
                    "qubit_index":(qubit_index[0],qubit_index[1]),
                }
                }
                idle= {
                    "name":"Idle",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                        "time":0.5e-6
                    }
                }
                shot.append(ms)
                shot.append(idle)
            pi2_0 = {
                "name":"Rphi",
                "args":{
                    "qubit_index":(qubit_index[0],),
                    "theta":np.pi/2,
                    "phi":phi
                }
            }

            pi2_1 = {
                "name":"Rphi",
                "args":{
                    "qubit_index":(qubit_index[1],),
                    "theta":np.pi/2,
                    "phi":phi
                }
            }

            shot.append(pi2_0)
            shot.append(pi2_1)
            self.route.append(shot)

class MS_scan_n(Route):
    def __init__(self,qubit_index,start = 1.0,stop = 5.0,n_points = 3,):
        self.points_info = [{
            "points":np.linspace(start, stop, n_points),
        }]
        self.route = []
        for n_gate in self.points_info[0]['points']:
            shot = []
            for _ in range(int(n_gate)):
                ms = {
                    "name":"MS",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                    }
                }
                idle= {
                    "name":"Idle",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                        "time":0.5e-6
                    }
                }
                shot.append(ms)
                shot.append(idle)
            self.route.append(shot)
class MS_scan_amp(Route):
    def __init__(self,qubit_index,start = 0.1,stop = 0.3,n_points = 11,n_gate = 1):
        self.points_info = [{
            'points': np.linspace(start, stop, n_points),  # 原始扫描点
        }]

        self.route = []
        for amp in self.points_info[0]["points"]:
            shot  = []
            ms = {
                    "name":"MS",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                        "aom_amp": amp
                    }
                }
            idle = {
                    "name":"Idle",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                        "time":0.5e-6
                    }
            }
            for _ in range(int(n_gate)):
                shot.append(ms)
                shot.append(idle)
            self.route.append(shot)
class MS_scan_phase(Route):
    def __init__(self,qubit_index,start = 0.0,stop = np.pi*2,n_points = 31):
        self.points_info = [{
            'points': np.linspace(start, stop, n_points),  # 原始扫描点
        }]

        self.route = []
        for phi in self.points_info[0]["points"]:
            shot  = [
                {
                    "name":"MS",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                        "phase":0.0
                    }
                },
                {
                    "name":"Idle",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                        "time":0.5e-6
                    }
                },
                {
                    "name":"MS",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                        "phase": phi
                    }
                }
            ]
            self.route.append(shot)
class Ramsey_MS_scan_phase(Route):
    def __init__(self,qubit_index,start = 0.0,stop = np.pi*2,n_points = 31):
        self.points_info = [{
            'points': np.linspace(start, stop, n_points),  # 原始扫描点
        }]

        self.route = []
        for phi in self.points_info[0]["points"]:
            shot = [
                {
                    "name":"Rabi",
                    "args":{
                        "qubit_index":(qubit_index[0],),
                        "theta":np.pi/2
                    }
                },
                {
                    "name":"Rabi",
                    "args":{
                        "qubit_index":(qubit_index[1],),
                        "theta":np.pi/2
                    }
                },
                {
                    "name":"MS",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                        "phase":np.pi/2
                    }
                },
                {
                    "name":"Rabi",
                    "args":{
                        "qubit_index":(qubit_index[0],),
                        "theta":np.pi/2,
                        "phase":phi
                    }
                },
                {
                    "name":"Rabi",
                    "args":{
                        "qubit_index":(qubit_index[1],),
                        "theta":np.pi/2,
                        "phase":phi
                    }
                },

            ]
            self.route.append(shot)
class Pi2MS_scan_phase(Route):
    def __init__(self,qubit_index,start = 0.0,stop = np.pi*2,n_points = 31):
        self.points_info = [{
            'points': np.linspace(start, stop, n_points),  # 原始扫描点
        }]

        self.route = []
        for phi in self.points_info[0]["points"]:
            shot = [
                {
                    "name":"Rabi",
                    "args":{
                        "qubit_index":(qubit_index[0],),
                        "theta":np.pi/2
                    }
                },
                {
                    "name":"Rabi",
                    "args":{
                        "qubit_index":(qubit_index[1],),
                        "theta":np.pi/2
                    }
                },
                {
                    "name":"MS",
                    "args":{
                        "qubit_index":(qubit_index[0],qubit_index[1]),
                        "phase": phi
                    }
                },

            ]
            self.route.append(shot)

class Raman_Ramsey_scan_t(Route):
    def __init__(self,qubit_index,start = 0e-6,stop = 1000e-6,n_points = 21,ramsey_choice="Carrier",phonon_frequency=0,eta=0.1):
        self.points_info = [{
            'points': np.linspace(start, stop, n_points),  # 原始扫描点
        }]

        self.route = []
        for t in self.points_info[0]['points']:
            shot = [
                {
                    "name": "Rabi",
                    "args": {
                        "qubit_index": (qubit_index,),
                        "theta": np.pi/2,
                        "rabi_choice":ramsey_choice,
                        "phonon_frequency":phonon_frequency,
                        "eta":eta
                    }
                },
                {
                    "name": "Idle",
                    "args":{
                        "qubit_index":(qubit_index,),
                        "time": t
                    }
                },
                {
                    "name": "Rabi",
                    "args": {
                        "qubit_index": (qubit_index,),
                        "theta": np.pi/2,
                        "rabi_choice":ramsey_choice,
                        "phonon_frequency":phonon_frequency,
                        "eta":eta
                    }
                },

            ]
            self.route.append(shot)