from operations import get_operation_class
import  numpy as np
from copy import deepcopy


def decode_route(route: list[list[dict[str, any]]]) -> list[list[any]]:
    """
    解析配置，返回操作序列和扫描参数信息

    返回值:
        points: 展开后的操作序列列表

    """
    points = []

    # 第一步：解析原始操作
    for shots in route:
        point = []
        for i, U in enumerate(shots):
            op_class = get_operation_class(U["name"])
            op = op_class(**U["args"])
            point.append(op)
        points.append(point)

    return points

if __name__ == "__main__":
    route = [[{
                 "name":"Idle",
                 "args":{
                      "qubit_index":(1,),
                      "idle_time":0
                 }
            }]]
    I = decode_route(route)
    print(I[0][0].idle_time)