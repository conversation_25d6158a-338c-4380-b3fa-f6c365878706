## 修改记录

为了调通一个实验文件, 将该实验文件对应的模块也纷纷调通.

本文档记录本仓库的文件新增过程.

## PMT TEST 1

- 新增 PMT Test 程序

```python
from artiq.experiment import *


class PMTTest1(EnvExperiment):
    """PMT_TEST_1

    用途:
    1. 给 PMT 提供方波触发信号, 用于测试 pmt_controller.py 中的测试脚本;
    2. 输出方波的代码示例.
    """

    def build(self):
        self.setattr_device("core")
        self.setattr_device("PMT_window")
        self.setattr_argument(
            "period",
            NumberValue(default=1e-3, precision=6, unit="s", step=1e-3),
            tooltip="采样信号周期",
        )
        print("Experiment Registered: PMT Test 1")

    def prepare(self):
        print("Prepare Done")

    @kernel()
    def run(self):
        # 0. initial
        self.core.reset()
        self.PMT_window.output()
        self.PMT_window.off()

        # 1. 给方波信号
        while True:
            self.PMT_window.on()
            delay(self.period / 2)
            self.PMT_window.off()
            delay(self.period / 2)

    def analyze(self):
        print("Analysis Done")


```

- 同时将新 PMT 的驱动放到 modules 文件夹下, 优化了 pmt_controller 测试程序.
- 在提交执行 PMT_Test_1 后, pmt_controller.py 可以直接运行, 得到不同阈值电压下的计数图.
- 配置代码格式化工具 black, 每次代码运行和提交, 都会自动将代码格式化.
- PMT test figure 添加到 git ignore.
- result 添加到 git ignore.

## PMT Test 2

- 新增和优化 PMT_test_2 代码

```python
import numpy as np
from artiq.experiment import *
from modules.pmt import PMT
from modules.config import LOADED_PARAMETER


class PMTForTest(PMT):
    def prepare_dataset_applets(self):
        # 1. PMT 32 通道直方图
        self.set_dataset(
            "PMT_channels", np.arange(0, 32), broadcast=True
        )  # PMT 32 通道的索引 从零开始
        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)

        self.ccb.issue(
            "create_applet",
            "PMT_counts_Plot",
            "${artiq_applet}green_plot_hist_8 PMT_counts --x PMT_channels",
        )

        # 2. PMT 32 通道计数总和
        self.set_dataset("Cooling_Count", 0, broadcast=True)
        self.ccb.issue(
            "create_applet", "Cooling_Count", "${artiq_applet}big_number Cooling_Count"
        )

        # 3. PMT 历史计数曲线
        self.set_dataset("Cooling_data", [], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "Cooling_Counts_History",
            "${artiq_applet}green_plot_single_xy_8 Cooling_data",
        )

    @rpc(flags={"async"})
    def data_process(self):
        # 1. 读数一次 shape = [repeat, 32]
        data_temp = self.read_data()

        # 2. 行求和, 得到 pmt hist 数据, shape = [1, 32]
        cooling_counts_32 = np.sum(data_temp, axis=0)
        self.set_dataset(
            "PMT_counts", cooling_counts_32, broadcast=True
        )  # 更新 PMT hist

        # 3. 列求和, 得到总计数, shape = [1,]
        cooling_count_sum = np.sum(cooling_counts_32)
        self.set_dataset(
            "Cooling_Count", cooling_count_sum, broadcast=True
        )  # 更新 big number
        self.append_to_dataset("Cooling_data", cooling_count_sum)  # 更新历史计数曲线


class PMTTest2(EnvExperiment):
    """PMT TEST 2

    用途:
    1. 测试 PMT 在某个固定增益电压和与之电压下的行为;
    2. 测试 pmt.py 中的数据集, 绘图组件, 数据处理是否正常工作;
    3. 测试 config.py 和 LOADED_PARAMETER 是否正常工作;
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time

        # 2. 仪器连接
        self.setattr_device("core")
        self.pmt = PMTForTest(self)
        print("Experiment Registered: PMT Test 2 ")

    def prepare(self):
        self.pmt.prepare_dataset_applets()
        print("Prepare Done")

    @kernel()
    def run(self):
        # 0. initial
        self.core.reset()
        self.pmt.initial()

        # 1. 给方波信号
        while True:
            for repeat in range(self.repeat):
                # repeat 此读数循环, 总积分时间是 cooling_time * repeat
                self.pmt.pmt_start_count()
                delay(self.cooling_time)
                self.pmt.pmt_stop_count()
                delay(self.cooling_time)

            self.core.wait_until_mu(now_mu())
            delay(100e-6)
            self.pmt.data_process()

    def analyze(self):
        print("Analysis Done")

```

- 本示例中, 将 PMT 数据处理的逻辑放在实验文件里.
- 新增 pmt.py 类, 封装基本指令, 并在实验模块中重新继承 pmt 类, 完善数据处理逻辑.
- 解决 pmt_hist 和 plot_xy_single 里面的显示 bug.

## Cooling Idle

- 新增 cooling_idle.py 实验文件,

```python
import numpy as np
from artiq.experiment import *
from modules.pmt import PMT
from modules.config import LOADED_PARAMETER
from modules.light_369 import Light369


class PMTForIdle(PMT):
    """面向 IDLE 类型的数据处理"""

    def prepare_dataset_applets(self):
        # 1. PMT 32 通道直方图
        self.set_dataset(
            "PMT_channels", np.arange(0, 32), broadcast=True
        )  # PMT 32 通道的索引 从零开始
        self.set_dataset("PMT_counts", np.full(32, np.nan), broadcast=True)

        self.ccb.issue(
            "create_applet",
            "PMT_counts_Plot",
            "${artiq_applet}green_plot_hist_8 PMT_counts --x PMT_channels",
        )

        # 2. PMT 32 通道计数总和
        self.set_dataset("Cooling_Count", 0, broadcast=True)
        self.ccb.issue(
            "create_applet", "Cooling_Count", "${artiq_applet}big_number Cooling_Count"
        )

        # 3. PMT 历史计数曲线
        self.set_dataset("Cooling_data", [], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "Cooling_Counts_History",
            "${artiq_applet}green_plot_single_xy_8 Cooling_data",
        )

    @rpc(flags={"async"})
    def data_process(self):
        # 1. 读数一次 shape = [repeat, 32]
        data_temp = self.read_data()

        # 2. 行求和, 得到 pmt hist 数据, shape = [1, 32]
        cooling_counts_32 = np.sum(data_temp, axis=0)
        self.set_dataset(
            "PMT_counts", cooling_counts_32, broadcast=True
        )  # 更新 PMT hist

        # 3. 列求和, 得到总计数, shape = [1,]
        cooling_count_sum = np.sum(cooling_counts_32)
        self.set_dataset(
            "Cooling_Count", cooling_count_sum, broadcast=True
        )  # 更新 big number
        self.append_to_dataset("Cooling_data", cooling_count_sum)  # 更新历史计数曲线


class CoolingIdle(EnvExperiment):
    """Cooling Idle

    用途:
    1. 控制 369 光路切换到 Cooling 状态
    2. 看 cooling 计数
    3. 将离子与 PMT 通道对准
    """

    def build(self):
        # 1. 参数拉取
        self.parameter = LOADED_PARAMETER()
        self.repeat = self.parameter.Experiment.Repeat
        self.cooling_time = self.parameter.Experiment.Cooling_Time

        # 2. 仪器连接
        self.setattr_device("core")
        self.pmt = PMTForIdle(self)
        self.l369 = Light369(self)
        print("Experiment Registered: PMT Test 2 ")

    def prepare(self):
        self.pmt.prepare_dataset_applets()
        print("Prepare Done")

    @kernel()
    def run(self):
        # 0. initial
        self.core.reset()
        self.pmt.initial()
        self.l369.initial()

        # 1. 给方波信号
        while True:
            for repeat in range(self.repeat):
                # repeat 此读数循环, 总积分时间是 cooling_time * repeat
                self.pmt.pmt_start_count()
                delay(self.cooling_time)
                self.pmt.pmt_stop_count()
                delay(self.cooling_time)

            self.core.wait_until_mu(now_mu())
            delay(100e-6)
            self.pmt.data_process()

    def analyze(self):
        print("Analysis Done")

```

- 新增 light_369 模块;
- 优化 light_369 中 artiq 通道的命名与 SigmaX 一致;
- 优化 device_db 中的通道命名和格式;


## Pumping Test

新增 Pumping_test 实验, 经跟 cz讨论,还是决定, 将 idle, x-scan, ms 门三个数据处理放到 pmt.py 中, 其他放到具体的实验文件中
