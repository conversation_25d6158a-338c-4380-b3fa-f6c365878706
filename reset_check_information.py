#!/usr/bin/env python3
"""
脚本用于将 check_information.json 文件中的所有数值设置为 0
保持原有的数据结构不变
"""

import json
import os
from typing import Any, Union


def reset_values_to_zero(data: Any) -> Any:
    """
    递归地将数据结构中的所有数值设置为 0
    
    Args:
        data: 要处理的数据，可以是字典、列表或其他类型
        
    Returns:
        处理后的数据，所有数值都被设置为 0
    """
    if isinstance(data, dict):
        # 如果是字典，递归处理每个值
        return {key: reset_values_to_zero(value) for key, value in data.items()}
    elif isinstance(data, list):
        # 如果是列表，递归处理每个元素
        return [reset_values_to_zero(item) for item in data]
    elif isinstance(data, (int, float)):
        # 如果是数值，设置为 0
        return 0
    else:
        # 其他类型（如字符串）保持不变
        return data


def main():
    """主函数"""
    # JSON 文件路径
    json_file_path = "modules/check_information.json"
    
    # 检查文件是否存在
    if not os.path.exists(json_file_path):
        print(f"错误: 文件 {json_file_path} 不存在")
        return
    
    try:
        # 读取 JSON 文件
        print(f"正在读取文件: {json_file_path}")
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # 创建备份文件
        backup_path = json_file_path + ".backup"
        print(f"创建备份文件: {backup_path}")
        with open(backup_path, 'w', encoding='utf-8') as backup_file:
            json.dump(data, backup_file, indent=4, ensure_ascii=False)
        
        # 将所有数值设置为 0
        print("正在将所有数值设置为 0...")
        reset_data = reset_values_to_zero(data)
        
        # 写回文件
        print(f"正在写入文件: {json_file_path}")
        with open(json_file_path, 'w', encoding='utf-8') as file:
            json.dump(reset_data, file, indent=4, ensure_ascii=False)
        
        print("✅ 操作完成！所有数值已设置为 0")
        print(f"📁 原文件备份保存在: {backup_path}")
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析错误: {e}")
    except Exception as e:
        print(f"❌ 发生错误: {e}")


if __name__ == "__main__":
    main()